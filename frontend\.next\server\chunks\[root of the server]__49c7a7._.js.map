{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/lib/mock-data.ts"], "sourcesContent": ["// بيانات تجريبية للتطوير بدون قاعدة بيانات\n\nexport interface User {\n  id: string;\n  email: string;\n  name: string;\n  role: 'ADMIN' | 'CLIENT' | 'CRAFTSMAN';\n  phone?: string;\n  location?: string;\n  bio?: string;\n  avatar?: string;\n  isActive: boolean;\n  isVerified: boolean;\n  createdAt: Date;\n}\n\nexport interface Project {\n  id: string;\n  title: string;\n  description: string;\n  category: string;\n  budget: string;\n  deadline: Date;\n  location: string;\n  priority: 'LOW' | 'MEDIUM' | 'HIGH';\n  status: 'OPEN' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';\n  clientId: string;\n  craftsmanId?: string;\n  images: string[];\n  materials?: string;\n  workType?: string;\n  requirements?: string;\n  views: number;\n  featured: boolean;\n  createdAt: Date;\n}\n\nexport interface Offer {\n  id: string;\n  projectId: string;\n  craftsmanId: string;\n  amount: number;\n  currency: string;\n  description: string;\n  estimatedDuration: number;\n  status: 'PENDING' | 'ACCEPTED' | 'REJECTED';\n  createdAt: Date;\n}\n\nexport interface Notification {\n  id: string;\n  userId: string;\n  title: string;\n  message: string;\n  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR';\n  isRead: boolean;\n  createdAt: Date;\n}\n\n// بيانات المستخدمين التجريبية\nexport const mockUsers: User[] = [\n  {\n    id: 'admin',\n    email: '<EMAIL>',\n    name: 'مدير النظام',\n    role: 'ADMIN',\n    phone: '+963 11 123 4567',\n    location: 'دمشق، سوريا',\n    bio: 'مدير منصة دوزان',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-01'),\n  },\n  {\n    id: 'client',\n    email: '<EMAIL>',\n    name: 'أحمد محمد',\n    role: 'CLIENT',\n    phone: '+963 123 456 789',\n    location: 'دمشق - المزة',\n    bio: 'عميل يبحث عن حرفيين مؤهلين',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-15'),\n  },\n  {\n    id: 'craftsman1',\n    email: '<EMAIL>',\n    name: 'محمد النجار',\n    role: 'CRAFTSMAN',\n    phone: '+963 987 654 321',\n    location: 'دمشق - كفرسوسة',\n    bio: 'نجار محترف مع خبرة 10 سنوات',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-10'),\n  },\n  {\n    id: 'craftsman2',\n    email: '<EMAIL>',\n    name: 'سارة الكهربائية',\n    role: 'CRAFTSMAN',\n    phone: '+963 555 123 456',\n    location: 'حلب - الفرقان',\n    bio: 'كهربائية متخصصة في الأنظمة الذكية',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-20'),\n  },\n];\n\n// بيانات المشاريع التجريبية\nexport const mockProjects: Project[] = [\n  {\n    id: 'project1',\n    title: 'تجديد مطبخ منزلي',\n    description: 'أحتاج إلى تجديد مطبخ منزلي بالكامل مع تغيير الخزائن والأرضية',\n    category: 'نجارة',\n    budget: '₺6,000 - ₺8,000',\n    deadline: new Date('2024-03-15'),\n    location: 'دمشق - المزة',\n    priority: 'HIGH',\n    status: 'OPEN',\n    clientId: 'client',\n    images: [],\n    materials: 'متوفرة جزئياً',\n    workType: 'تجديد',\n    requirements: 'يفضل استخدام خشب عالي الجودة',\n    views: 15,\n    featured: true,\n    createdAt: new Date('2024-02-01'),\n  },\n  {\n    id: 'project2',\n    title: 'تركيب نظام إضاءة ذكي',\n    description: 'تركيب نظام إضاءة ذكي في المنزل مع إمكانية التحكم عبر الهاتف',\n    category: 'كهرباء',\n    budget: '₺4,000 - ₺6,000',\n    deadline: new Date('2024-03-10'),\n    location: 'دمشق - المالكي',\n    priority: 'MEDIUM',\n    status: 'OPEN',\n    clientId: 'client',\n    images: [],\n    materials: 'غير متوفرة',\n    workType: 'تركيب',\n    requirements: 'نظام متوافق مع الهواتف الذكية',\n    views: 8,\n    featured: false,\n    createdAt: new Date('2024-02-05'),\n  },\n];\n\n// بيانات العروض التجريبية\nexport const mockOffers: Offer[] = [\n  {\n    id: 'offer1',\n    projectId: 'project1',\n    craftsmanId: 'craftsman1',\n    amount: 7000,\n    currency: 'TRY',\n    description: 'يمكنني تنفيذ هذا المشروع بجودة عالية وفي الوقت المحدد',\n    estimatedDuration: 14,\n    status: 'PENDING',\n    createdAt: new Date('2024-02-02'),\n  },\n  {\n    id: 'offer2',\n    projectId: 'project2',\n    craftsmanId: 'craftsman2',\n    amount: 5000,\n    currency: 'TRY',\n    description: 'متخصصة في أنظمة الإضاءة الذكية مع ضمان سنة كاملة',\n    estimatedDuration: 7,\n    status: 'PENDING',\n    createdAt: new Date('2024-02-06'),\n  },\n];\n\n// بيانات الإشعارات التجريبية\nexport const mockNotifications: Notification[] = [\n  {\n    id: 'notif1',\n    userId: 'client',\n    title: 'عرض جديد على مشروعك',\n    message: 'تلقيت عرضاً جديداً من محمد النجار على مشروع تجديد المطبخ',\n    type: 'INFO',\n    isRead: false,\n    createdAt: new Date('2024-02-02'),\n  },\n  {\n    id: 'notif2',\n    userId: 'craftsman1',\n    title: 'مشروع جديد متاح',\n    message: 'يوجد مشروع نجارة جديد في منطقتك',\n    type: 'INFO',\n    isRead: true,\n    createdAt: new Date('2024-02-01'),\n  },\n];\n\n// دوال مساعدة للبحث والفلترة\nexport const getUserById = (id: string): User | undefined => {\n  return mockUsers.find(user => user.id === id);\n};\n\nexport const getUserByEmail = (email: string): User | undefined => {\n  return mockUsers.find(user => user.email === email);\n};\n\nexport const getProjectsByUserId = (userId: string): Project[] => {\n  return mockProjects.filter(project => \n    project.clientId === userId || project.craftsmanId === userId\n  );\n};\n\nexport const getOffersByProjectId = (projectId: string): Offer[] => {\n  return mockOffers.filter(offer => offer.projectId === projectId);\n};\n\nexport const getOffersByCraftsmanId = (craftsmanId: string): Offer[] => {\n  return mockOffers.filter(offer => offer.craftsmanId === craftsmanId);\n};\n\nexport const getNotificationsByUserId = (userId: string): Notification[] => {\n  return mockNotifications.filter(notification => notification.userId === userId);\n};\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;;;;;;;;;AA4DpC,MAAM,YAAoB;IAC/B;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,EAAE;QACV,WAAW;QACX,UAAU;QACV,cAAc;QACd,OAAO;QACP,UAAU;QACV,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,EAAE;QACV,WAAW;QACX,UAAU;QACV,cAAc;QACd,OAAO;QACP,UAAU;QACV,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,aAAsB;IACjC;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,UAAU;QACV,aAAa;QACb,mBAAmB;QACnB,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,UAAU;QACV,aAAa;QACb,mBAAmB;QACnB,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,oBAAoC;IAC/C;QACE,IAAI;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;QACT,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;QACT,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AAC5C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAO,aAAa,MAAM,CAAC,CAAA,UACzB,QAAQ,QAAQ,KAAK,UAAU,QAAQ,WAAW,KAAK;AAE3D;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,WAAW,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,KAAK;AACxD;AAEO,MAAM,yBAAyB,CAAC;IACrC,OAAO,WAAW,MAAM,CAAC,CAAA,QAAS,MAAM,WAAW,KAAK;AAC1D;AAEO,MAAM,2BAA2B,CAAC;IACvC,OAAO,kBAAkB,MAAM,CAAC,CAAA,eAAgB,aAAa,MAAM,KAAK;AAC1E"}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/api/users/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { mockUsers, getProjectsByUserId, getOffersByCraftsmanId } from '@/lib/mock-data';\n\n// GET /api/users/[id] - جلب مستخدم محدد\nexport async function GET(\n  request: NextRequest,\n  context: { params: { id: string } }\n) {\n  try {\n    const { id } = context.params;\n    \n    const user = mockUsers.find(u => u.id === id);\n    if (!user) {\n      return NextResponse.json(\n        { error: 'المستخدم غير موجود' },\n        { status: 404 }\n      );\n    }\n\n    // إضافة إحصائيات المستخدم\n    const userProjects = getProjectsByUserId(id);\n    const userOffers = getOffersByCraftsmanId(id);\n\n    const userWithStats = {\n      ...user,\n      stats: {\n        totalProjects: userProjects.length,\n        completedProjects: userProjects.filter(p => p.status === 'COMPLETED').length,\n        totalOffers: userOffers.length,\n        acceptedOffers: userOffers.filter(o => o.status === 'ACCEPTED').length,\n      }\n    };\n\n    return NextResponse.json({ user: userWithStats });\n  } catch (error) {\n    console.error('Error fetching user:', error);\n    return NextResponse.json(\n      { error: 'خطأ في جلب المستخدم' },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT /api/users/[id] - تحديث مستخدم\nexport async function PUT(\n  request: NextRequest,\n  context: { params: { id: string } }\n) {\n  try {\n    const { id } = context.params;\n    const body = await request.json();\n\n    const userIndex = mockUsers.findIndex(u => u.id === id);\n    if (userIndex === -1) {\n      return NextResponse.json(\n        { error: 'المستخدم غير موجود' },\n        { status: 404 }\n      );\n    }\n\n    // تحديث المستخدم (منع تغيير بعض الحقول الحساسة)\n    const { id: _, email: __, role: ___, ...updateData } = body;\n    \n    const updatedUser = {\n      ...mockUsers[userIndex],\n      ...updateData,\n    };\n\n    mockUsers[userIndex] = updatedUser;\n\n    return NextResponse.json({\n      user: updatedUser,\n      message: 'تم تحديث الملف الشخصي بنجاح'\n    });\n  } catch (error) {\n    console.error('Error updating user:', error);\n    return NextResponse.json(\n      { error: 'خطأ في تحديث المستخدم' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,IACpB,OAAoB,EACpB,OAAmC;IAEnC,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,QAAQ,MAAM;QAE7B,MAAM,OAAO,4HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,CAAC,MAAM;YACT,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqB,GAC9B;gBAAE,QAAQ;YAAI;QAElB;QAEA,0BAA0B;QAC1B,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,sBAAmB,AAAD,EAAE;QACzC,MAAM,aAAa,CAAA,GAAA,4HAAA,CAAA,yBAAsB,AAAD,EAAE;QAE1C,MAAM,gBAAgB;YACpB,GAAG,IAAI;YACP,OAAO;gBACL,eAAe,aAAa,MAAM;gBAClC,mBAAmB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;gBAC5E,aAAa,WAAW,MAAM;gBAC9B,gBAAgB,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;YACxE;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,MAAM;QAAc;IACjD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAsB,GAC/B;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,OAAmC;IAEnC,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,QAAQ,MAAM;QAC7B,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,MAAM,YAAY,4HAAA,CAAA,YAAS,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,cAAc,CAAC,GAAG;YACpB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAqB,GAC9B;gBAAE,QAAQ;YAAI;QAElB;QAEA,gDAAgD;QAChD,MAAM,EAAE,IAAI,CAAC,EAAE,OAAO,EAAE,EAAE,MAAM,GAAG,EAAE,GAAG,YAAY,GAAG;QAEvD,MAAM,cAAc;YAClB,GAAG,4HAAA,CAAA,YAAS,CAAC,UAAU;YACvB,GAAG,UAAU;QACf;QAEA,4HAAA,CAAA,YAAS,CAAC,UAAU,GAAG;QAEvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,MAAM;YACN,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF"}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}