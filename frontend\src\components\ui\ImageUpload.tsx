'use client';

import React, { useState, useRef } from 'react';
import { Button } from './Button';

interface ImageUploadProps {
  onImagesChange: (images: File[]) => void;
  maxImages?: number;
  existingImages?: string[];
  label?: string;
  description?: string;
  required?: boolean;
}

const ImageUpload: React.FC<ImageUploadProps> = ({
  onImagesChange,
  maxImages = 5,
  existingImages = [],
  label = 'إضافة صور',
  description = 'اختر صور واضحة تظهر التفاصيل المطلوبة',
  required = false
}) => {
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [previewUrls, setPreviewUrls] = useState<string[]>(existingImages);
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleImageSelect = (files: FileList | null) => {
    if (!files) return;

    const newFiles = Array.from(files);
    const totalImages = selectedImages.length + previewUrls.length + newFiles.length;

    if (totalImages > maxImages) {
      alert(`يمكنك رفع ${maxImages} صور كحد أقصى`);
      return;
    }

    // التحقق من نوع الملفات
    const validFiles = newFiles.filter(file => {
      if (!file.type.startsWith('image/')) {
        alert(`${file.name} ليس ملف صورة صحيح`);
        return false;
      }
      if (file.size > 5 * 1024 * 1024) { // 5MB
        alert(`${file.name} حجم الملف كبير جداً (الحد الأقصى 5MB)`);
        return false;
      }
      return true;
    });

    if (validFiles.length === 0) return;

    const updatedImages = [...selectedImages, ...validFiles];
    setSelectedImages(updatedImages);
    onImagesChange(updatedImages);

    // إنشاء معاينة للصور الجديدة
    validFiles.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrls(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    const isExistingImage = index < existingImages.length;
    
    if (isExistingImage) {
      // إزالة صورة موجودة مسبقاً
      setPreviewUrls(prev => prev.filter((_, i) => i !== index));
    } else {
      // إزالة صورة جديدة
      const newImageIndex = index - existingImages.length;
      const updatedImages = selectedImages.filter((_, i) => i !== newImageIndex);
      setSelectedImages(updatedImages);
      onImagesChange(updatedImages);
      setPreviewUrls(prev => prev.filter((_, i) => i !== index));
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleImageSelect(e.dataTransfer.files);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          {label} {required && <span className="text-red-500">*</span>}
        </label>
        <p className="text-sm text-gray-500 mb-4">{description}</p>
      </div>

      {/* منطقة رفع الصور */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragActive
            ? 'border-teal bg-teal/5'
            : 'border-gray-300 hover:border-teal hover:bg-gray-50'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={(e) => handleImageSelect(e.target.files)}
          className="hidden"
        />
        
        <div className="space-y-4">
          <div className="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          
          <div>
            <p className="text-gray-600 mb-2">اسحب الصور هنا أو</p>
            <Button
              type="button"
              variant="outline"
              onClick={() => fileInputRef.current?.click()}
            >
              اختر الصور
            </Button>
          </div>
          
          <p className="text-xs text-gray-500">
            PNG, JPG, GIF حتى {maxImages} صور (حد أقصى 5MB لكل صورة)
          </p>
        </div>
      </div>

      {/* معاينة الصور */}
      {previewUrls.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {previewUrls.map((url, index) => (
            <div key={index} className="relative group">
              <img
                src={url}
                alt={`صورة ${index + 1}`}
                className="w-full h-32 object-cover rounded-lg border border-gray-200"
              />
              <button
                type="button"
                onClick={() => removeImage(index)}
                className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-sm hover:bg-red-600 transition-colors"
              >
                ×
              </button>
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center">
                <button
                  type="button"
                  onClick={() => window.open(url, '_blank')}
                  className="opacity-0 group-hover:opacity-100 bg-white text-gray-700 px-3 py-1 rounded-md text-sm transition-opacity"
                >
                  عرض
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* عداد الصور */}
      <div className="text-sm text-gray-500 text-center">
        {previewUrls.length} من {maxImages} صور
      </div>
    </div>
  );
};

export default ImageUpload;
