'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import FileUpload from '@/components/ui/FileUpload';
import { useFileUpload } from '@/hooks/useApi';

const TestUploadPage = () => {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadedUrls, setUploadedUrls] = useState<string[]>([]);
  const { uploadFiles, loading, error } = useFileUpload();

  const handleFileSelect = (files: File[]) => {
    setSelectedFiles(files);
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      alert('يرجى اختيار ملفات أولاً');
      return;
    }

    const urls = await uploadFiles(selectedFiles);
    if (urls) {
      setUploadedUrls(urls);
      setSelectedFiles([]);
      alert('تم رفع الملفات بنجاح!');
    }
  };

  const clearUploaded = () => {
    setUploadedUrls([]);
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-navy mb-4">اختبار رفع الملفات</h1>
              <p className="text-gray-600">اختبر نظام رفع الصور والملفات</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Upload Section */}
              <Card>
                <CardHeader>
                  <CardTitle>رفع الملفات</CardTitle>
                </CardHeader>
                <CardContent>
                  <FileUpload
                    onFileSelect={handleFileSelect}
                    accept="image/*"
                    multiple={true}
                    maxFiles={5}
                    maxSize={5}
                  />

                  {error && (
                    <div className="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                      {error}
                    </div>
                  )}

                  {selectedFiles.length > 0 && (
                    <div className="mt-6">
                      <Button
                        onClick={handleUpload}
                        disabled={loading}
                        className="w-full bg-gradient-to-r from-navy to-teal"
                      >
                        {loading ? 'جاري الرفع...' : `رفع ${selectedFiles.length} ملف`}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Uploaded Files */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>الملفات المرفوعة ({uploadedUrls.length})</span>
                    {uploadedUrls.length > 0 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={clearUploaded}
                      >
                        مسح الكل
                      </Button>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {uploadedUrls.length > 0 ? (
                    <div className="space-y-4">
                      {uploadedUrls.map((url, index) => (
                        <div key={index} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center space-x-4 space-x-reverse">
                            <img
                              src={url}
                              alt={`رفع ${index + 1}`}
                              className="w-16 h-16 object-cover rounded-lg"
                            />
                            <div className="flex-1">
                              <p className="font-medium text-gray-700">صورة {index + 1}</p>
                              <p className="text-sm text-gray-500 break-all">{url}</p>
                            </div>
                            <div className="flex space-x-2 space-x-reverse">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => window.open(url, '_blank')}
                              >
                                عرض
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => navigator.clipboard.writeText(url)}
                              >
                                نسخ الرابط
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <div className="text-4xl mb-4">📷</div>
                      <p className="text-gray-500">لم يتم رفع أي ملفات بعد</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Usage Examples */}
            <div className="mt-12">
              <Card>
                <CardHeader>
                  <CardTitle>أمثلة الاستخدام</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center p-6 bg-gray-50 rounded-lg">
                      <div className="text-3xl mb-3">🏗️</div>
                      <h3 className="font-semibold text-navy mb-2">صور المشاريع</h3>
                      <p className="text-sm text-gray-600">
                        رفع صور المشاريع عند إنشاء مشروع جديد
                      </p>
                    </div>

                    <div className="text-center p-6 bg-gray-50 rounded-lg">
                      <div className="text-3xl mb-3">🎨</div>
                      <h3 className="font-semibold text-navy mb-2">معرض الأعمال</h3>
                      <p className="text-sm text-gray-600">
                        رفع صور الأعمال السابقة للحرفيين
                      </p>
                    </div>

                    <div className="text-center p-6 bg-gray-50 rounded-lg">
                      <div className="text-3xl mb-3">👤</div>
                      <h3 className="font-semibold text-navy mb-2">الصور الشخصية</h3>
                      <p className="text-sm text-gray-600">
                        رفع الصور الشخصية للملفات الشخصية
                      </p>
                    </div>
                  </div>

                  <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-semibold text-navy mb-3">📋 معلومات تقنية:</h3>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• الصيغ المدعومة: JPEG, PNG, GIF, WebP</li>
                      <li>• الحد الأقصى للحجم: 5MB لكل ملف</li>
                      <li>• الحد الأقصى للعدد: 5 ملفات في المرة الواحدة</li>
                      <li>• يتم حفظ الملفات في مجلد /public/uploads</li>
                      <li>• يتم إنشاء أسماء فريدة لتجنب التضارب</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* API Test */}
            <div className="mt-8">
              <Card>
                <CardHeader>
                  <CardTitle>اختبار API</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-navy mb-2">POST /api/upload</h4>
                      <p className="text-sm text-gray-600 mb-2">
                        رفع ملفات متعددة باستخدام FormData
                      </p>
                      <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                        const formData = new FormData();<br/>
                        files.forEach(file => formData.append('files', file));<br/>
                        fetch('/api/upload', {'{'} method: 'POST', body: formData {'}'});
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-navy mb-2">Response</h4>
                      <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                        {'{'}<br/>
                        &nbsp;&nbsp;"message": "تم رفع الملفات بنجاح",<br/>
                        &nbsp;&nbsp;"files": ["/uploads/filename1.jpg", "/uploads/filename2.png"]<br/>
                        {'}'}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default TestUploadPage;
