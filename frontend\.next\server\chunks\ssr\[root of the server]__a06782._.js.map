{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/context/JobFormContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, ReactNode } from 'react';\n\nexport interface JobFormData {\n  // Step 1: Basic Details\n  title: string;\n  description: string;\n  category: string;\n  location: string;\n\n  // Step 2: Requirements\n  requirements: string;\n  skills: string[];\n  attachments: string[];\n  images: File[];\n\n  // Step 3: Budget and Timeline\n  budget: number;\n  deadline: string;\n\n  // Additional fields\n  status: 'draft' | 'open' | 'in_progress' | 'completed' | 'cancelled';\n  tags: string[];\n}\n\ninterface JobFormContextType {\n  formData: JobFormData;\n  updateFormData: (data: Partial<JobFormData>) => void;\n  resetForm: () => void;\n  currentStep: number;\n  setCurrentStep: (step: number) => void;\n  isSubmitting: boolean;\n  setIsSubmitting: (isSubmitting: boolean) => void;\n}\n\nconst defaultFormData: JobFormData = {\n  title: '',\n  description: '',\n  category: '',\n  location: '',\n  requirements: '',\n  skills: [],\n  attachments: [],\n  images: [],\n  budget: 0,\n  deadline: '',\n  status: 'draft',\n  tags: [],\n};\n\nconst JobFormContext = createContext<JobFormContextType | undefined>(undefined);\n\nexport const JobFormProvider = ({ children }: { children: ReactNode }) => {\n  const [formData, setFormData] = useState<JobFormData>(defaultFormData);\n  const [currentStep, setCurrentStep] = useState(1);\n  const [isSubmitting, setIsSubmitting] = useState(false);\n\n  const updateFormData = (data: Partial<JobFormData>) => {\n    setFormData((prev) => ({ ...prev, ...data }));\n  };\n\n  const resetForm = () => {\n    setFormData(defaultFormData);\n    setCurrentStep(1);\n  };\n\n  return (\n    <JobFormContext.Provider\n      value={{\n        formData,\n        updateFormData,\n        resetForm,\n        currentStep,\n        setCurrentStep,\n        isSubmitting,\n        setIsSubmitting,\n      }}\n    >\n      {children}\n    </JobFormContext.Provider>\n  );\n};\n\nexport const useJobForm = () => {\n  const context = useContext(JobFormContext);\n  if (context === undefined) {\n    throw new Error('useJobForm must be used within a JobFormProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAoCA,MAAM,kBAA+B;IACnC,OAAO;IACP,aAAa;IACb,UAAU;IACV,UAAU;IACV,cAAc;IACd,QAAQ,EAAE;IACV,aAAa,EAAE;IACf,QAAQ,EAAE;IACV,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,MAAM,EAAE;AACV;AAEA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAkC;AAE9D,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAA2B;IACnE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACtD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,iBAAiB,CAAC;QACtB,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,IAAI;YAAC,CAAC;IAC7C;IAEA,MAAM,YAAY;QAChB,YAAY;QACZ,eAAe;IACjB;IAEA,qBACE,8OAAC,eAAe,QAAQ;QACtB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,MAAM,aAAa;IACxB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT"}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// دالة تنسيق التاريخ بشكل ثابت لتجنب مشاكل Hydration\nexport function formatDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n\n    // التحقق من صحة التاريخ\n    if (isNaN(date.getTime())) {\n      return 'تاريخ غير صحيح';\n    }\n\n    // تنسيق ثابت للتاريخ\n    const year = date.getFullYear();\n    const month = date.getMonth() + 1;\n    const day = date.getDate();\n\n    // أسماء الشهور بالعربية\n    const monthNames = [\n      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',\n      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'\n    ];\n\n    return `${day} ${monthNames[month - 1]} ${year}`;\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق التاريخ النسبي (منذ كم يوم)\nexport function formatRelativeDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInMs = now.getTime() - date.getTime();\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\n\n    if (diffInDays === 0) {\n      return 'اليوم';\n    } else if (diffInDays === 1) {\n      return 'أمس';\n    } else if (diffInDays < 7) {\n      return `منذ ${diffInDays} أيام`;\n    } else if (diffInDays < 30) {\n      const weeks = Math.floor(diffInDays / 7);\n      return `منذ ${weeks} ${weeks === 1 ? 'أسبوع' : 'أسابيع'}`;\n    } else if (diffInDays < 365) {\n      const months = Math.floor(diffInDays / 30);\n      return `منذ ${months} ${months === 1 ? 'شهر' : 'أشهر'}`;\n    } else {\n      const years = Math.floor(diffInDays / 365);\n      return `منذ ${years} ${years === 1 ? 'سنة' : 'سنوات'}`;\n    }\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق الأرقام بالفواصل\nexport function formatNumber(num: number): string {\n  return num.toLocaleString('ar-SA');\n}\n\n// دالة للتحقق من صحة البريد الإلكتروني\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// دالة للتحقق من صحة رقم الهاتف السوري\nexport function isValidSyrianPhone(phone: string): boolean {\n  const phoneRegex = /^(\\+963|0)?[0-9]{9,10}$/;\n  return phoneRegex.test(phone);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,UAAkB;IAC3C,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QAEtB,wBAAwB;QACxB,IAAI,MAAM,KAAK,OAAO,KAAK;YACzB,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,KAAK,QAAQ,KAAK;QAChC,MAAM,MAAM,KAAK,OAAO;QAExB,wBAAwB;QACxB,MAAM,aAAa;YACjB;YAAS;YAAU;YAAQ;YAAS;YAAQ;YAC5C;YAAS;YAAS;YAAU;YAAU;YAAU;SACjD;QAED,OAAO,GAAG,IAAI,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,MAAM;IAClD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,mBAAmB,UAAkB;IACnD,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;QAC7C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG;YACpB,OAAO;QACT,OAAO,IAAI,eAAe,GAAG;YAC3B,OAAO;QACT,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO,CAAC,IAAI,EAAE,WAAW,KAAK,CAAC;QACjC,OAAO,IAAI,aAAa,IAAI;YAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,UAAU,UAAU;QAC3D,OAAO,IAAI,aAAa,KAAK;YAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,aAAa;YACvC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,WAAW,IAAI,QAAQ,QAAQ;QACzD,OAAO;YACL,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,QAAQ,SAAS;QACxD;IACF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,cAAc,CAAC;AAC5B;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,mBAAmB,KAAa;IAC9C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB"}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',\n  {\n    variants: {\n      variant: {\n        default: 'bg-navy text-white hover:bg-navy/90',\n        destructive: 'bg-red-500 text-white hover:bg-red-500/90',\n        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',\n        secondary: 'bg-teal text-white hover:bg-teal/90',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'underline-offset-4 hover:underline text-primary',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 rounded-md',\n        lg: 'h-11 px-8 rounded-md',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,wQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 249, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 255, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { Button } from '../ui/Button';\n\nconst Header = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex justify-between items-center\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"group flex items-center space-x-3 space-x-reverse\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-navy to-teal\">\n                دوزان\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:block\">\n            <ul className=\"flex space-x-8 space-x-reverse\">\n              <li>\n                <Link href=\"/\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الرئيسية\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  المشاريع\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الحرفيون\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  من نحن\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الأسئلة الشائعة\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  اتصل بنا\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n            </ul>\n          </nav>\n\n          {/* Desktop Buttons */}\n          <div className=\"hidden lg:flex items-center space-x-4 space-x-reverse\">\n            <Link href=\"/login\">\n              <Button variant=\"outline\" size=\"sm\" className=\"border-navy text-navy hover:bg-navy hover:text-white transition-all duration-300\">\n                تسجيل الدخول\n              </Button>\n            </Link>\n            <Link href=\"/register\">\n              <Button size=\"sm\" className=\"bg-gradient-to-r from-navy to-teal hover:from-teal hover:to-navy text-white shadow-lg hover:shadow-xl transition-all duration-300\">\n                إنشاء حساب\n              </Button>\n            </Link>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300\"\n            aria-label=\"فتح القائمة\"\n          >\n            <svg className=\"w-6 h-6 text-navy\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              {isMobileMenuOpen ? (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              ) : (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden mt-4 py-4 border-t border-gray-200\">\n            <nav className=\"space-y-4\">\n              <Link href=\"/\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الرئيسية\n              </Link>\n              <Link href=\"/jobs\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                المشاريع\n              </Link>\n              <Link href=\"/craftsmen\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الحرفيون\n              </Link>\n              <Link href=\"/about\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                من نحن\n              </Link>\n              <Link href=\"/faq\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الأسئلة الشائعة\n              </Link>\n              <Link href=\"/contact\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                اتصل بنا\n              </Link>\n            </nav>\n            <div className=\"mt-6 space-y-3\">\n              <Link href=\"/login\" className=\"block\">\n                <Button variant=\"outline\" className=\"w-full border-navy text-navy hover:bg-navy hover:text-white\">\n                  تسجيل الدخول\n                </Button>\n              </Link>\n              <Link href=\"/register\" className=\"block\">\n                <Button className=\"w-full bg-gradient-to-r from-navy to-teal text-white\">\n                  إنشاء حساب\n                </Button>\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDAA2L;;;;;;kDAG1M,8OAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;;;;;;sCAO1G,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAA0F;8DAEjH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;gDAA0F;8DAErH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;;gDAA0F;8DAE1H,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;;gDAA0F;8DAEtH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAO,WAAU;;gDAA0F;8DAEpH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;gDAA0F;8DAExH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAmF;;;;;;;;;;;8CAInI,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAAoI;;;;;;;;;;;;;;;;;sCAOpK,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAoB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC1E,iCACC,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;yDAErE,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAO5E,kCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAiF;;;;;;8CAG1G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAiF;;;;;;8CAG9G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAiF;;;;;;8CAGnH,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiF;;;;;;8CAG/G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAO,WAAU;8CAAiF;;;;;;8CAG7G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiF;;;;;;;;;;;;sCAInH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAC5B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAA8D;;;;;;;;;;;8CAIpG,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAC/B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzF;uCAEe"}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden\">\n      {/* خلفية هندسية متناسقة */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl\"></div>\n      </div>\n\n      {/* شبكة نقطية ناعمة */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',\n          backgroundSize: '30px 30px'\n        }}></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-16 relative z-10\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Logo and Description */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n              <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center text-white text-2xl font-bold shadow-lg\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-white\">\n                دوزان\n              </span>\n            </div>\n            <p className=\"text-white/90 leading-relaxed mb-6\">\n              منصة تربط بين أصحاب المشاريع والحرفيين المهرة لتنفيذ المشاريع بكفاءة وجودة عالية في جميع أنحاء سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              روابط سريعة\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الرئيسية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  المشاريع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الحرفيون\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  من نحن\n                </Link>\n              </li>\n            </ul>\n          </div>\n          {/* Support */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              الدعم\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/contact\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  اتصل بنا\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  شروط الاستخدام\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              تواصل معنا\n            </h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">البريد الإلكتروني</p>\n                  <a href=\"mailto:<EMAIL>\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">الهاتف</p>\n                  <a href=\"tel:+963112345678\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    +963 11 234 5678\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">العنوان</p>\n                  <p className=\"text-white font-medium\">دمشق، سوريا</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"mt-12 pt-8 border-t border-white/20\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-white/80 text-sm\">\n                &copy; {new Date().getFullYear()} دوزان. جميع الحقوق محفوظة.\n              </p>\n              <p className=\"text-white/60 text-xs mt-1\">\n                صُنع بـ ❤️ في سوريا\n              </p>\n            </div>\n\n            <div className=\"flex items-center space-x-6 space-x-reverse\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">2500+</div>\n                <div className=\"text-white/70 text-xs\">حرفي</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">15000+</div>\n                <div className=\"text-white/70 text-xs\">مشروع</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">98%</div>\n                <div className=\"text-white/70 text-xs\">رضا</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6H;;;;;;0DAG5I,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAIlD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAyQ,UAAS;;;;;;;;;;;;;;;;0DAGjT,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA8jD,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO1mD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;;sEACvB,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;;sEAC3B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;;sEAChC,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAO3H,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAO,WAAU;;sEAC1B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3H,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,MAAK;gEAAuB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAM3H,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,MAAK;gEAAoB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAMxH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EACjE,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;8EACrE,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;;gDAAwB;gDAC3B,IAAI,OAAO,WAAW;gDAAG;;;;;;;sDAEnC,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;uCAEe"}}, {"offset": {"line": 1540, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1546, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout = ({ children }: MainLayoutProps) => {\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      <Header />\n      <main className=\"flex-grow\">{children}</main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB;IAC/C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;0BAAa;;;;;;0BAC7B,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe"}}, {"offset": {"line": 1586, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1592, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/jobs/FormLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { ReactNode } from 'react';\nimport MainLayout from '../layout/MainLayout';\nimport Link from 'next/link';\n\ninterface StepIndicatorProps {\n  currentStep: number;\n  totalSteps: number;\n}\n\nconst StepIndicator = ({ currentStep, totalSteps }: StepIndicatorProps) => {\n  return (\n    <div className=\"mb-8\">\n      <div className=\"flex justify-between items-center\">\n        {Array.from({ length: totalSteps }).map((_, index) => (\n          <React.Fragment key={index}>\n            <div className=\"flex flex-col items-center\">\n              <div\n                className={`w-10 h-10 rounded-full flex items-center justify-center ${\n                  index + 1 <= currentStep ? 'bg-navy text-white' : 'bg-gray-200 text-gray-500'\n                }`}\n              >\n                {index + 1}\n              </div>\n              <span\n                className={`mt-2 text-sm ${\n                  index + 1 <= currentStep ? 'text-navy font-medium' : 'text-gray-500'\n                }`}\n              >\n                {index + 1 === 1 && 'التفاصيل الأساسية'}\n                {index + 1 === 2 && 'المتطلبات والمواصفات'}\n                {index + 1 === 3 && 'الميزانية والمدة'}\n                {index + 1 === 4 && 'المراجعة والنشر'}\n              </span>\n            </div>\n            {index < totalSteps - 1 && (\n              <div\n                className={`h-1 flex-1 mx-2 ${\n                  index + 1 < currentStep ? 'bg-navy' : 'bg-gray-200'\n                }`}\n              ></div>\n            )}\n          </React.Fragment>\n        ))}\n      </div>\n    </div>\n  );\n};\n\ninterface FormLayoutProps {\n  children: ReactNode;\n  currentStep: number;\n  totalSteps: number;\n  onNext?: () => void;\n  onPrevious?: () => void;\n  isLastStep?: boolean;\n  isSubmitting?: boolean;\n}\n\nconst FormLayout = ({\n  children,\n  currentStep,\n  totalSteps,\n  onNext,\n  onPrevious,\n  isLastStep = false,\n  isSubmitting = false,\n}: FormLayoutProps) => {\n  return (\n    <MainLayout>\n      <div className=\"bg-beige py-12\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-3xl mx-auto\">\n            <h1 className=\"text-3xl font-bold text-navy mb-6 text-center\">إنشاء مشروع جديد</h1>\n            <StepIndicator currentStep={currentStep} totalSteps={totalSteps} />\n            <div className=\"bg-white rounded-lg shadow-md p-8\">\n              {children}\n              <div className=\"flex justify-between mt-8\">\n                {currentStep > 1 ? (\n                  <button\n                    type=\"button\"\n                    onClick={onPrevious}\n                    className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                  >\n                    السابق\n                  </button>\n                ) : (\n                  <Link href=\"/\">\n                    <button\n                      type=\"button\"\n                      className=\"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50\"\n                    >\n                      إلغاء\n                    </button>\n                  </Link>\n                )}\n                <button\n                  type=\"button\"\n                  onClick={onNext}\n                  disabled={isSubmitting}\n                  className={`px-6 py-2 bg-navy text-white rounded-md hover:bg-navy/90 ${\n                    isSubmitting ? 'opacity-70 cursor-not-allowed' : ''\n                  }`}\n                >\n                  {isSubmitting ? (\n                    <span className=\"flex items-center\">\n                      <svg\n                        className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white\"\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        fill=\"none\"\n                        viewBox=\"0 0 24 24\"\n                      >\n                        <circle\n                          className=\"opacity-25\"\n                          cx=\"12\"\n                          cy=\"12\"\n                          r=\"10\"\n                          stroke=\"currentColor\"\n                          strokeWidth=\"4\"\n                        ></circle>\n                        <path\n                          className=\"opacity-75\"\n                          fill=\"currentColor\"\n                          d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                        ></path>\n                      </svg>\n                      جاري المعالجة...\n                    </span>\n                  ) : isLastStep ? (\n                    'نشر المشروع'\n                  ) : (\n                    'التالي'\n                  )}\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  );\n};\n\nexport default FormLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWA,MAAM,gBAAgB,CAAC,EAAE,WAAW,EAAE,UAAU,EAAsB;IACpE,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAW,GAAG,GAAG,CAAC,CAAC,GAAG,sBAC1C,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,WAAW,CAAC,wDAAwD,EAClE,QAAQ,KAAK,cAAc,uBAAuB,6BAClD;8CAED,QAAQ;;;;;;8CAEX,8OAAC;oCACC,WAAW,CAAC,aAAa,EACvB,QAAQ,KAAK,cAAc,0BAA0B,iBACrD;;wCAED,QAAQ,MAAM,KAAK;wCACnB,QAAQ,MAAM,KAAK;wCACnB,QAAQ,MAAM,KAAK;wCACnB,QAAQ,MAAM,KAAK;;;;;;;;;;;;;wBAGvB,QAAQ,aAAa,mBACpB,8OAAC;4BACC,WAAW,CAAC,gBAAgB,EAC1B,QAAQ,IAAI,cAAc,YAAY,eACtC;;;;;;;mBAxBa;;;;;;;;;;;;;;;AAgC/B;AAYA,MAAM,aAAa,CAAC,EAClB,QAAQ,EACR,WAAW,EACX,UAAU,EACV,MAAM,EACN,UAAU,EACV,aAAa,KAAK,EAClB,eAAe,KAAK,EACJ;IAChB,qBACE,8OAAC,0IAAA,CAAA,UAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgD;;;;;;sCAC9D,8OAAC;4BAAc,aAAa;4BAAa,YAAY;;;;;;sCACrD,8OAAC;4BAAI,WAAU;;gCACZ;8CACD,8OAAC;oCAAI,WAAU;;wCACZ,cAAc,kBACb,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;iEAID,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;sDAKL,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU;4CACV,WAAW,CAAC,yDAAyD,EACnE,eAAe,kCAAkC,IACjD;sDAED,6BACC,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;wDACC,WAAU;wDACV,OAAM;wDACN,MAAK;wDACL,SAAQ;;0EAER,8OAAC;gEACC,WAAU;gEACV,IAAG;gEACH,IAAG;gEACH,GAAE;gEACF,QAAO;gEACP,aAAY;;;;;;0EAEd,8OAAC;gEACC,WAAU;gEACV,MAAK;gEACL,GAAE;;;;;;;;;;;;oDAEA;;;;;;uDAGN,aACF,gBAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpB;uCAEe"}}, {"offset": {"line": 1812, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1818, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/ImageUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef } from 'react';\nimport { Button } from './Button';\n\ninterface ImageUploadProps {\n  onImagesChange: (images: File[]) => void;\n  maxImages?: number;\n  existingImages?: string[];\n  label?: string;\n  description?: string;\n  required?: boolean;\n}\n\nconst ImageUpload: React.FC<ImageUploadProps> = ({\n  onImagesChange,\n  maxImages = 5,\n  existingImages = [],\n  label = 'إضافة صور',\n  description = 'اختر صور واضحة تظهر التفاصيل المطلوبة',\n  required = false\n}) => {\n  const [selectedImages, setSelectedImages] = useState<File[]>([]);\n  const [previewUrls, setPreviewUrls] = useState<string[]>(existingImages);\n  const [dragActive, setDragActive] = useState(false);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleImageSelect = (files: FileList | null) => {\n    if (!files) return;\n\n    const newFiles = Array.from(files);\n    const totalImages = selectedImages.length + previewUrls.length + newFiles.length;\n\n    if (totalImages > maxImages) {\n      alert(`يمكنك رفع ${maxImages} صور كحد أقصى`);\n      return;\n    }\n\n    // التحقق من نوع الملفات\n    const validFiles = newFiles.filter(file => {\n      if (!file.type.startsWith('image/')) {\n        alert(`${file.name} ليس ملف صورة صحيح`);\n        return false;\n      }\n      if (file.size > 5 * 1024 * 1024) { // 5MB\n        alert(`${file.name} حجم الملف كبير جداً (الحد الأقصى 5MB)`);\n        return false;\n      }\n      return true;\n    });\n\n    if (validFiles.length === 0) return;\n\n    const updatedImages = [...selectedImages, ...validFiles];\n    setSelectedImages(updatedImages);\n    onImagesChange(updatedImages);\n\n    // إنشاء معاينة للصور الجديدة\n    validFiles.forEach(file => {\n      const reader = new FileReader();\n      reader.onload = (e) => {\n        setPreviewUrls(prev => [...prev, e.target?.result as string]);\n      };\n      reader.readAsDataURL(file);\n    });\n  };\n\n  const removeImage = (index: number) => {\n    const isExistingImage = index < existingImages.length;\n    \n    if (isExistingImage) {\n      // إزالة صورة موجودة مسبقاً\n      setPreviewUrls(prev => prev.filter((_, i) => i !== index));\n    } else {\n      // إزالة صورة جديدة\n      const newImageIndex = index - existingImages.length;\n      const updatedImages = selectedImages.filter((_, i) => i !== newImageIndex);\n      setSelectedImages(updatedImages);\n      onImagesChange(updatedImages);\n      setPreviewUrls(prev => prev.filter((_, i) => i !== index));\n    }\n  };\n\n  const handleDrag = (e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    if (e.type === 'dragenter' || e.type === 'dragover') {\n      setDragActive(true);\n    } else if (e.type === 'dragleave') {\n      setDragActive(false);\n    }\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    e.stopPropagation();\n    setDragActive(false);\n    \n    if (e.dataTransfer.files && e.dataTransfer.files[0]) {\n      handleImageSelect(e.dataTransfer.files);\n    }\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          {label} {required && <span className=\"text-red-500\">*</span>}\n        </label>\n        <p className=\"text-sm text-gray-500 mb-4\">{description}</p>\n      </div>\n\n      {/* منطقة رفع الصور */}\n      <div\n        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${\n          dragActive\n            ? 'border-teal bg-teal/5'\n            : 'border-gray-300 hover:border-teal hover:bg-gray-50'\n        }`}\n        onDragEnter={handleDrag}\n        onDragLeave={handleDrag}\n        onDragOver={handleDrag}\n        onDrop={handleDrop}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          multiple\n          accept=\"image/*\"\n          onChange={(e) => handleImageSelect(e.target.files)}\n          className=\"hidden\"\n        />\n        \n        <div className=\"space-y-4\">\n          <div className=\"mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center\">\n            <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n            </svg>\n          </div>\n          \n          <div>\n            <p className=\"text-gray-600 mb-2\">اسحب الصور هنا أو</p>\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => fileInputRef.current?.click()}\n            >\n              اختر الصور\n            </Button>\n          </div>\n          \n          <p className=\"text-xs text-gray-500\">\n            PNG, JPG, GIF حتى {maxImages} صور (حد أقصى 5MB لكل صورة)\n          </p>\n        </div>\n      </div>\n\n      {/* معاينة الصور */}\n      {previewUrls.length > 0 && (\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\">\n          {previewUrls.map((url, index) => (\n            <div key={index} className=\"relative group\">\n              <img\n                src={url}\n                alt={`صورة ${index + 1}`}\n                className=\"w-full h-32 object-cover rounded-lg border border-gray-200\"\n              />\n              <button\n                type=\"button\"\n                onClick={() => removeImage(index)}\n                className=\"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-sm hover:bg-red-600 transition-colors\"\n              >\n                ×\n              </button>\n              <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center\">\n                <button\n                  type=\"button\"\n                  onClick={() => window.open(url, '_blank')}\n                  className=\"opacity-0 group-hover:opacity-100 bg-white text-gray-700 px-3 py-1 rounded-md text-sm transition-opacity\"\n                >\n                  عرض\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n\n      {/* عداد الصور */}\n      <div className=\"text-sm text-gray-500 text-center\">\n        {previewUrls.length} من {maxImages} صور\n      </div>\n    </div>\n  );\n};\n\nexport default ImageUpload;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,cAA0C,CAAC,EAC/C,cAAc,EACd,YAAY,CAAC,EACb,iBAAiB,EAAE,EACnB,QAAQ,WAAW,EACnB,cAAc,uCAAuC,EACrD,WAAW,KAAK,EACjB;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,oBAAoB,CAAC;QACzB,IAAI,CAAC,OAAO;QAEZ,MAAM,WAAW,MAAM,IAAI,CAAC;QAC5B,MAAM,cAAc,eAAe,MAAM,GAAG,YAAY,MAAM,GAAG,SAAS,MAAM;QAEhF,IAAI,cAAc,WAAW;YAC3B,MAAM,CAAC,UAAU,EAAE,UAAU,aAAa,CAAC;YAC3C;QACF;QAEA,wBAAwB;QACxB,MAAM,aAAa,SAAS,MAAM,CAAC,CAAA;YACjC,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,MAAM,GAAG,KAAK,IAAI,CAAC,kBAAkB,CAAC;gBACtC,OAAO;YACT;YACA,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;gBAC/B,MAAM,GAAG,KAAK,IAAI,CAAC,sCAAsC,CAAC;gBAC1D,OAAO;YACT;YACA,OAAO;QACT;QAEA,IAAI,WAAW,MAAM,KAAK,GAAG;QAE7B,MAAM,gBAAgB;eAAI;eAAmB;SAAW;QACxD,kBAAkB;QAClB,eAAe;QAEf,6BAA6B;QAC7B,WAAW,OAAO,CAAC,CAAA;YACjB,MAAM,SAAS,IAAI;YACnB,OAAO,MAAM,GAAG,CAAC;gBACf,eAAe,CAAA,OAAQ;2BAAI;wBAAM,EAAE,MAAM,EAAE;qBAAiB;YAC9D;YACA,OAAO,aAAa,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,kBAAkB,QAAQ,eAAe,MAAM;QAErD,IAAI,iBAAiB;YACnB,2BAA2B;YAC3B,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACrD,OAAO;YACL,mBAAmB;YACnB,MAAM,gBAAgB,QAAQ,eAAe,MAAM;YACnD,MAAM,gBAAgB,eAAe,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;YAC5D,kBAAkB;YAClB,eAAe;YACf,eAAe,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACrD;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,IAAI,EAAE,IAAI,KAAK,eAAe,EAAE,IAAI,KAAK,YAAY;YACnD,cAAc;QAChB,OAAO,IAAI,EAAE,IAAI,KAAK,aAAa;YACjC,cAAc;QAChB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc;QAEd,IAAI,EAAE,YAAY,CAAC,KAAK,IAAI,EAAE,YAAY,CAAC,KAAK,CAAC,EAAE,EAAE;YACnD,kBAAkB,EAAE,YAAY,CAAC,KAAK;QACxC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;;4BACd;4BAAM;4BAAE,0BAAY,8OAAC;gCAAK,WAAU;0CAAe;;;;;;;;;;;;kCAEtD,8OAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;0BAI7C,8OAAC;gBACC,WAAW,CAAC,oEAAoE,EAC9E,aACI,0BACA,sDACJ;gBACF,aAAa;gBACb,aAAa;gBACb,YAAY;gBACZ,QAAQ;;kCAER,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,QAAO;wBACP,UAAU,CAAC,IAAM,kBAAkB,EAAE,MAAM,CAAC,KAAK;wBACjD,WAAU;;;;;;kCAGZ,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC/E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAIzE,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS,IAAM,aAAa,OAAO,EAAE;kDACtC;;;;;;;;;;;;0CAKH,8OAAC;gCAAE,WAAU;;oCAAwB;oCAChB;oCAAU;;;;;;;;;;;;;;;;;;;YAMlC,YAAY,MAAM,GAAG,mBACpB,8OAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,KAAK,sBACrB,8OAAC;wBAAgB,WAAU;;0CACzB,8OAAC;gCACC,KAAK;gCACL,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAG;gCACxB,WAAU;;;;;;0CAEZ,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM,YAAY;gCAC3B,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK;oCAChC,WAAU;8CACX;;;;;;;;;;;;uBAlBK;;;;;;;;;;0BA4BhB,8OAAC;gBAAI,WAAU;;oBACZ,YAAY,MAAM;oBAAC;oBAAK;oBAAU;;;;;;;;;;;;;AAI3C;uCAEe"}}, {"offset": {"line": 2113, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2119, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Dropdown.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\n\ninterface DropdownOption {\n  value: string;\n  label: string;\n  icon?: string;\n}\n\ninterface DropdownProps {\n  options: DropdownOption[];\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  className?: string;\n  disabled?: boolean;\n  error?: string;\n  label?: string;\n  required?: boolean;\n}\n\nconst Dropdown: React.FC<DropdownProps> = ({\n  options,\n  value,\n  onChange,\n  placeholder = 'اختر خيار',\n  className = '',\n  disabled = false,\n  error,\n  label,\n  required = false\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  const selectedOption = options.find(option => option.value === value);\n  \n  const filteredOptions = options.filter(option =>\n    option.label.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n        setSearchTerm('');\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleToggle = () => {\n    if (!disabled) {\n      setIsOpen(!isOpen);\n      if (!isOpen) {\n        setTimeout(() => {\n          inputRef.current?.focus();\n        }, 100);\n      }\n    }\n  };\n\n  const handleSelect = (optionValue: string) => {\n    onChange(optionValue);\n    setIsOpen(false);\n    setSearchTerm('');\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      if (filteredOptions.length > 0) {\n        handleSelect(filteredOptions[0].value);\n      }\n    } else if (e.key === 'Escape') {\n      setIsOpen(false);\n      setSearchTerm('');\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      {label && (\n        <label className=\"block text-gray-700 font-medium mb-2\">\n          {label}\n          {required && <span className=\"text-red-500 mr-1\">*</span>}\n        </label>\n      )}\n      \n      <div ref={dropdownRef} className=\"relative\">\n        <div\n          onClick={handleToggle}\n          className={`\n            relative w-full px-4 py-3 bg-white border rounded-xl cursor-pointer transition-all duration-300\n            ${isOpen \n              ? 'border-teal ring-2 ring-teal/20 shadow-lg' \n              : error \n                ? 'border-red-300 hover:border-red-400' \n                : 'border-gray-300 hover:border-gray-400'\n            }\n            ${disabled ? 'bg-gray-50 cursor-not-allowed opacity-60' : 'hover:shadow-md'}\n            ${className}\n          `}\n        >\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3 space-x-reverse flex-1\">\n              {selectedOption?.icon && (\n                <span className=\"text-lg flex-shrink-0\">{selectedOption.icon}</span>\n              )}\n              <span className={`block truncate ${selectedOption ? 'text-gray-900' : 'text-gray-500'}`}>\n                {selectedOption ? selectedOption.label : placeholder}\n              </span>\n            </div>\n            \n            {/* أيقونة السهم */}\n            <div className=\"flex-shrink-0 mr-3\">\n              <svg\n                className={`w-5 h-5 text-gray-400 transition-transform duration-300 ${\n                  isOpen ? 'rotate-180' : 'rotate-0'\n                }`}\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        {/* القائمة المنسدلة */}\n        {isOpen && (\n          <div className=\"absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-xl shadow-xl max-h-60 overflow-hidden\">\n            {/* حقل البحث */}\n            <div className=\"p-3 border-b border-gray-100\">\n              <input\n                ref={inputRef}\n                type=\"text\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                onKeyDown={handleKeyDown}\n                placeholder=\"ابحث...\"\n                className=\"w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal\"\n              />\n            </div>\n\n            {/* قائمة الخيارات */}\n            <div className=\"max-h-48 overflow-y-auto\">\n              {filteredOptions.length > 0 ? (\n                filteredOptions.map((option) => (\n                  <div\n                    key={option.value}\n                    onClick={() => handleSelect(option.value)}\n                    className={`\n                      flex items-center space-x-3 space-x-reverse px-4 py-3 cursor-pointer transition-colors duration-200\n                      ${value === option.value \n                        ? 'bg-gradient-to-r from-navy/10 to-teal/10 text-navy border-r-4 border-teal' \n                        : 'hover:bg-gray-50'\n                      }\n                    `}\n                  >\n                    {option.icon && (\n                      <span className=\"text-lg flex-shrink-0\">{option.icon}</span>\n                    )}\n                    <span className=\"block truncate\">{option.label}</span>\n                    {value === option.value && (\n                      <div className=\"flex-shrink-0 mr-auto\">\n                        <svg className=\"w-5 h-5 text-teal\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </div>\n                    )}\n                  </div>\n                ))\n              ) : (\n                <div className=\"px-4 py-3 text-gray-500 text-center\">\n                  لا توجد نتائج\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {error && (\n        <p className=\"mt-2 text-red-500 text-sm flex items-center\">\n          <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n          {error}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport default Dropdown;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,WAAoC,CAAC,EACzC,OAAO,EACP,KAAK,EACL,QAAQ,EACR,cAAc,WAAW,EACzB,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,KAAK,EACL,KAAK,EACL,WAAW,KAAK,EACjB;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;IAE/D,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SACrC,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;gBACV,cAAc;YAChB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI,CAAC,UAAU;YACb,UAAU,CAAC;YACX,IAAI,CAAC,QAAQ;gBACX,WAAW;oBACT,SAAS,OAAO,EAAE;gBACpB,GAAG;YACL;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,SAAS;QACT,UAAU;QACV,cAAc;IAChB;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,aAAa,eAAe,CAAC,EAAE,CAAC,KAAK;YACvC;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B,UAAU;YACV,cAAc;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAU;;oBACd;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,KAAK;gBAAa,WAAU;;kCAC/B,8OAAC;wBACC,SAAS;wBACT,WAAW,CAAC;;YAEV,EAAE,SACE,8CACA,QACE,wCACA,wCACL;YACD,EAAE,WAAW,6CAA6C,kBAAkB;YAC5E,EAAE,UAAU;UACd,CAAC;kCAED,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,gBAAgB,sBACf,8OAAC;4CAAK,WAAU;sDAAyB,eAAe,IAAI;;;;;;sDAE9D,8OAAC;4CAAK,WAAW,CAAC,eAAe,EAAE,iBAAiB,kBAAkB,iBAAiB;sDACpF,iBAAiB,eAAe,KAAK,GAAG;;;;;;;;;;;;8CAK7C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAW,CAAC,wDAAwD,EAClE,SAAS,eAAe,YACxB;wCACF,MAAK;wCACL,QAAO;wCACP,SAAQ;kDAER,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAO5E,wBACC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAK;oCACL,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAW;oCACX,aAAY;oCACZ,WAAU;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,MAAM,GAAG,IACxB,gBAAgB,GAAG,CAAC,CAAC,uBACnB,8OAAC;wCAEC,SAAS,IAAM,aAAa,OAAO,KAAK;wCACxC,WAAW,CAAC;;sBAEV,EAAE,UAAU,OAAO,KAAK,GACpB,8EACA,mBACH;oBACH,CAAC;;4CAEA,OAAO,IAAI,kBACV,8OAAC;gDAAK,WAAU;0DAAyB,OAAO,IAAI;;;;;;0DAEtD,8OAAC;gDAAK,WAAU;0DAAkB,OAAO,KAAK;;;;;;4CAC7C,UAAU,OAAO,KAAK,kBACrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAoB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC3E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;uCAjBtE,OAAO,KAAK;;;;8DAwBrB,8OAAC;oCAAI,WAAU;8CAAsC;;;;;;;;;;;;;;;;;;;;;;;YAS9D,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACtE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE;;;;;;;;;;;;;AAKX;uCAEe"}}, {"offset": {"line": 2414, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2420, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/jobs/Step1BasicDetails.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useJobForm } from '@/context/JobFormContext';\nimport ImageUpload from '../ui/ImageUpload';\nimport Dropdown from '../ui/Dropdown';\n\nconst categories = [\n  { value: 'carpentry', label: 'النجارة', icon: '🪚' },\n  { value: 'plumbing', label: 'السباكة', icon: '🔧' },\n  { value: 'electrical', label: 'الكهرباء', icon: '⚡' },\n  { value: 'painting', label: 'الدهان', icon: '🖌️' },\n  { value: 'construction', label: 'البناء', icon: '🧱' },\n  { value: 'hvac', label: 'التكييف', icon: '❄️' },\n  { value: 'metalwork', label: 'الحدادة', icon: '🔨' },\n  { value: 'landscaping', label: 'تنسيق الحدائق', icon: '🌱' },\n  { value: 'other', label: 'أخرى', icon: '🔧' },\n];\n\nconst Step1BasicDetails = () => {\n  const { formData, updateFormData } = useJobForm();\n  const [errors, setErrors] = useState({\n    title: '',\n    description: '',\n    category: '',\n  });\n\n  const validateForm = () => {\n    let isValid = true;\n    const newErrors = {\n      title: '',\n      description: '',\n      category: '',\n    };\n\n    if (!formData.title || !formData.title.trim()) {\n      newErrors.title = 'عنوان المشروع مطلوب';\n      isValid = false;\n    } else if (formData.title.length < 5) {\n      newErrors.title = 'عنوان المشروع يجب أن يكون 5 أحرف على الأقل';\n      isValid = false;\n    }\n\n    if (!formData.description || !formData.description.trim()) {\n      newErrors.description = 'وصف المشروع مطلوب';\n      isValid = false;\n    } else if (formData.description.length < 20) {\n      newErrors.description = 'وصف المشروع يجب أن يكون 20 حرف على الأقل';\n      isValid = false;\n    }\n\n    if (!formData.category) {\n      newErrors.category = 'فئة المشروع مطلوبة';\n      isValid = false;\n    }\n\n    setErrors(newErrors);\n    return isValid;\n  };\n\n  useEffect(() => {\n    // Add validation to parent form\n    const parentForm = document.getElementById('job-form');\n    if (parentForm) {\n      const originalSubmit = parentForm.onsubmit;\n      parentForm.onsubmit = (e) => {\n        if (!validateForm()) {\n          e.preventDefault();\n          return false;\n        }\n        if (originalSubmit) {\n          return originalSubmit.call(parentForm, e);\n        }\n      };\n    }\n  }, [formData]);\n\n  return (\n    <div>\n      <h2 className=\"text-xl font-bold text-navy mb-6\">التفاصيل الأساسية للمشروع</h2>\n\n      <div className=\"space-y-6\">\n        <div>\n          <label htmlFor=\"title\" className=\"block text-gray-700 font-medium mb-2\">\n            عنوان المشروع <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            type=\"text\"\n            id=\"title\"\n            value={formData.title}\n            onChange={(e) => updateFormData({ title: e.target.value })}\n            className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent\"\n            placeholder=\"مثال: تصميم وتنفيذ خزائن مطبخ خشبية\"\n          />\n          {errors.title && <p className=\"mt-1 text-red-500 text-sm\">{errors.title}</p>}\n        </div>\n\n        <div>\n          <Dropdown\n            label=\"فئة المشروع\"\n            options={categories}\n            value={formData.category}\n            onChange={(value) => updateFormData({ category: value })}\n            placeholder=\"اختر فئة المشروع\"\n            required={true}\n            error={errors.category}\n          />\n        </div>\n\n        <div>\n          <label htmlFor=\"location\" className=\"block text-gray-700 font-medium mb-2\">\n            الموقع\n          </label>\n          <input\n            type=\"text\"\n            id=\"location\"\n            value={formData.location}\n            onChange={(e) => updateFormData({ location: e.target.value })}\n            className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent\"\n            placeholder=\"مثال: دمشق، المزة\"\n          />\n        </div>\n\n        <div>\n          <label htmlFor=\"description\" className=\"block text-gray-700 font-medium mb-2\">\n            وصف المشروع <span className=\"text-red-500\">*</span>\n          </label>\n          <textarea\n            id=\"description\"\n            value={formData.description}\n            onChange={(e) => updateFormData({ description: e.target.value })}\n            rows={5}\n            className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent\"\n            placeholder=\"اشرح تفاصيل مشروعك بوضوح...\"\n          ></textarea>\n          {errors.description && <p className=\"mt-1 text-red-500 text-sm\">{errors.description}</p>}\n        </div>\n\n        {/* رفع صور المشروع */}\n        <div>\n          <ImageUpload\n            onImagesChange={(images) => updateFormData({ images })}\n            maxImages={5}\n            label=\"صور المشروع\"\n            description=\"أضف صور واضحة تظهر المشكلة أو المنطقة التي تحتاج للعمل عليها. هذا سيساعد الحرفيين على فهم المطلوب بشكل أفضل.\"\n            required={false}\n          />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Step1BasicDetails;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,aAAa;IACjB;QAAE,OAAO;QAAa,OAAO;QAAW,MAAM;IAAK;IACnD;QAAE,OAAO;QAAY,OAAO;QAAW,MAAM;IAAK;IAClD;QAAE,OAAO;QAAc,OAAO;QAAY,MAAM;IAAI;IACpD;QAAE,OAAO;QAAY,OAAO;QAAU,MAAM;IAAM;IAClD;QAAE,OAAO;QAAgB,OAAO;QAAU,MAAM;IAAK;IACrD;QAAE,OAAO;QAAQ,OAAO;QAAW,MAAM;IAAK;IAC9C;QAAE,OAAO;QAAa,OAAO;QAAW,MAAM;IAAK;IACnD;QAAE,OAAO;QAAe,OAAO;QAAiB,MAAM;IAAK;IAC3D;QAAE,OAAO;QAAS,OAAO;QAAQ,MAAM;IAAK;CAC7C;AAED,MAAM,oBAAoB;IACxB,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAC9C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IAEA,MAAM,eAAe;QACnB,IAAI,UAAU;QACd,MAAM,YAAY;YAChB,OAAO;YACP,aAAa;YACb,UAAU;QACZ;QAEA,IAAI,CAAC,SAAS,KAAK,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC7C,UAAU,KAAK,GAAG;YAClB,UAAU;QACZ,OAAO,IAAI,SAAS,KAAK,CAAC,MAAM,GAAG,GAAG;YACpC,UAAU,KAAK,GAAG;YAClB,UAAU;QACZ;QAEA,IAAI,CAAC,SAAS,WAAW,IAAI,CAAC,SAAS,WAAW,CAAC,IAAI,IAAI;YACzD,UAAU,WAAW,GAAG;YACxB,UAAU;QACZ,OAAO,IAAI,SAAS,WAAW,CAAC,MAAM,GAAG,IAAI;YAC3C,UAAU,WAAW,GAAG;YACxB,UAAU;QACZ;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;YACrB,UAAU;QACZ;QAEA,UAAU;QACV,OAAO;IACT;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gCAAgC;QAChC,MAAM,aAAa,SAAS,cAAc,CAAC;QAC3C,IAAI,YAAY;YACd,MAAM,iBAAiB,WAAW,QAAQ;YAC1C,WAAW,QAAQ,GAAG,CAAC;gBACrB,IAAI,CAAC,gBAAgB;oBACnB,EAAE,cAAc;oBAChB,OAAO;gBACT;gBACA,IAAI,gBAAgB;oBAClB,OAAO,eAAe,IAAI,CAAC,YAAY;gBACzC;YACF;QACF;IACF,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAAmC;;;;;;0BAEjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAQ,WAAU;;oCAAuC;kDACxD,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAE/C,8OAAC;gCACC,MAAK;gCACL,IAAG;gCACH,OAAO,SAAS,KAAK;gCACrB,UAAU,CAAC,IAAM,eAAe;wCAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oCAAC;gCACxD,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,KAAK,kBAAI,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK;;;;;;;;;;;;kCAGzE,8OAAC;kCACC,cAAA,8OAAC,oIAAA,CAAA,UAAQ;4BACP,OAAM;4BACN,SAAS;4BACT,OAAO,SAAS,QAAQ;4BACxB,UAAU,CAAC,QAAU,eAAe;oCAAE,UAAU;gCAAM;4BACtD,aAAY;4BACZ,UAAU;4BACV,OAAO,OAAO,QAAQ;;;;;;;;;;;kCAI1B,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAAuC;;;;;;0CAG3E,8OAAC;gCACC,MAAK;gCACL,IAAG;gCACH,OAAO,SAAS,QAAQ;gCACxB,UAAU,CAAC,IAAM,eAAe;wCAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAC3D,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAIhB,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAc,WAAU;;oCAAuC;kDAChE,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAE7C,8OAAC;gCACC,IAAG;gCACH,OAAO,SAAS,WAAW;gCAC3B,UAAU,CAAC,IAAM,eAAe;wCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAC9D,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,WAAW,kBAAI,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,WAAW;;;;;;;;;;;;kCAIrF,8OAAC;kCACC,cAAA,8OAAC,uIAAA,CAAA,UAAW;4BACV,gBAAgB,CAAC,SAAW,eAAe;oCAAE;gCAAO;4BACpD,WAAW;4BACX,OAAM;4BACN,aAAY;4BACZ,UAAU;;;;;;;;;;;;;;;;;;;;;;;AAMtB;uCAEe"}}, {"offset": {"line": 2730, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2736, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/jobs/Step2Requirements.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useJobForm } from '@/context/JobFormContext';\n\nconst Step2Requirements = () => {\n  const { formData, updateFormData } = useJobForm();\n  const [newSkill, setNewSkill] = useState('');\n  const [errors, setErrors] = useState({\n    requirements: '',\n    skills: '',\n  });\n\n  const handleAddSkill = () => {\n    if (newSkill.trim()) {\n      const updatedSkills = [...formData.skills, newSkill.trim()];\n      updateFormData({ skills: updatedSkills });\n      setNewSkill('');\n    }\n  };\n\n  const handleRemoveSkill = (index: number) => {\n    const updatedSkills = formData.skills.filter((_, i) => i !== index);\n    updateFormData({ skills: updatedSkills });\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      handleAddSkill();\n    }\n  };\n\n  const validateForm = () => {\n    let isValid = true;\n    const newErrors = {\n      requirements: '',\n      skills: '',\n    };\n\n    if (!formData.requirements || !formData.requirements.trim()) {\n      newErrors.requirements = 'متطلبات المشروع مطلوبة';\n      isValid = false;\n    }\n\n    if (!formData.skills || formData.skills.length === 0) {\n      newErrors.skills = 'يجب إضافة مهارة واحدة على الأقل';\n      isValid = false;\n    }\n\n    setErrors(newErrors);\n    return isValid;\n  };\n\n  // Mock file upload function\n  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const files = e.target.files;\n    if (files && files.length > 0) {\n      // In a real app, you would upload these files to a server\n      // For now, we'll just store the file names\n      const fileNames = Array.from(files).map(file => file.name);\n      updateFormData({ attachments: [...formData.attachments, ...fileNames] });\n    }\n  };\n\n  const handleRemoveAttachment = (index: number) => {\n    const updatedAttachments = formData.attachments.filter((_, i) => i !== index);\n    updateFormData({ attachments: updatedAttachments });\n  };\n\n  return (\n    <div>\n      <h2 className=\"text-xl font-bold text-navy mb-6\">متطلبات ومواصفات المشروع</h2>\n\n      <div className=\"space-y-6\">\n        <div>\n          <label htmlFor=\"requirements\" className=\"block text-gray-700 font-medium mb-2\">\n            متطلبات المشروع <span className=\"text-red-500\">*</span>\n          </label>\n          <textarea\n            id=\"requirements\"\n            value={formData.requirements}\n            onChange={(e) => updateFormData({ requirements: e.target.value })}\n            rows={5}\n            className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent\"\n            placeholder=\"اذكر المتطلبات والمواصفات التفصيلية للمشروع...\"\n          ></textarea>\n          {errors.requirements && <p className=\"mt-1 text-red-500 text-sm\">{errors.requirements}</p>}\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 font-medium mb-2\">\n            المهارات المطلوبة <span className=\"text-red-500\">*</span>\n          </label>\n          <div className=\"flex\">\n            <input\n              type=\"text\"\n              value={newSkill}\n              onChange={(e) => setNewSkill(e.target.value)}\n              onKeyDown={handleKeyDown}\n              className=\"flex-grow px-4 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent\"\n              placeholder=\"أضف مهارة...\"\n            />\n            <button\n              type=\"button\"\n              onClick={handleAddSkill}\n              className=\"px-4 py-2 bg-teal text-white rounded-l-md hover:bg-teal/90\"\n            >\n              إضافة\n            </button>\n          </div>\n          {errors.skills && <p className=\"mt-1 text-red-500 text-sm\">{errors.skills}</p>}\n\n          {formData.skills.length > 0 && (\n            <div className=\"mt-3 flex flex-wrap gap-2\">\n              {formData.skills.map((skill, index) => (\n                <div\n                  key={index}\n                  className=\"bg-skyblue px-3 py-1 rounded-full flex items-center\"\n                >\n                  <span className=\"text-navy\">{skill}</span>\n                  <button\n                    type=\"button\"\n                    onClick={() => handleRemoveSkill(index)}\n                    className=\"ml-2 text-navy hover:text-red-500\"\n                  >\n                    ×\n                  </button>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-gray-700 font-medium mb-2\">\n            المرفقات (اختياري)\n          </label>\n          <div className=\"border-2 border-dashed border-gray-300 rounded-md p-6 text-center\">\n            <input\n              type=\"file\"\n              id=\"file-upload\"\n              multiple\n              onChange={handleFileUpload}\n              className=\"hidden\"\n            />\n            <label\n              htmlFor=\"file-upload\"\n              className=\"cursor-pointer inline-flex items-center justify-center px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50\"\n            >\n              <svg\n                className=\"ml-2 -mr-1 h-5 w-5 text-gray-400\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n                viewBox=\"0 0 20 20\"\n                fill=\"currentColor\"\n                aria-hidden=\"true\"\n              >\n                <path\n                  fillRule=\"evenodd\"\n                  d=\"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z\"\n                  clipRule=\"evenodd\"\n                />\n              </svg>\n              اختر ملفات\n            </label>\n            <p className=\"mt-2 text-sm text-gray-500\">\n              يمكنك رفع صور، مستندات PDF، أو أي ملفات أخرى ذات صلة بالمشروع\n            </p>\n          </div>\n\n          {formData.attachments.length > 0 && (\n            <div className=\"mt-4\">\n              <h4 className=\"text-sm font-medium text-gray-700 mb-2\">الملفات المرفقة:</h4>\n              <ul className=\"space-y-2\">\n                {formData.attachments.map((file, index) => (\n                  <li key={index} className=\"flex items-center justify-between bg-gray-50 p-2 rounded\">\n                    <span className=\"text-sm text-gray-700\">{file}</span>\n                    <button\n                      type=\"button\"\n                      onClick={() => handleRemoveAttachment(index)}\n                      className=\"text-red-500 hover:text-red-700\"\n                    >\n                      حذف\n                    </button>\n                  </li>\n                ))}\n              </ul>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Step2Requirements;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,oBAAoB;IACxB,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,cAAc;QACd,QAAQ;IACV;IAEA,MAAM,iBAAiB;QACrB,IAAI,SAAS,IAAI,IAAI;YACnB,MAAM,gBAAgB;mBAAI,SAAS,MAAM;gBAAE,SAAS,IAAI;aAAG;YAC3D,eAAe;gBAAE,QAAQ;YAAc;YACvC,YAAY;QACd;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,gBAAgB,SAAS,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC7D,eAAe;YAAE,QAAQ;QAAc;IACzC;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,UAAU;QACd,MAAM,YAAY;YAChB,cAAc;YACd,QAAQ;QACV;QAEA,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,YAAY,CAAC,IAAI,IAAI;YAC3D,UAAU,YAAY,GAAG;YACzB,UAAU;QACZ;QAEA,IAAI,CAAC,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC,MAAM,KAAK,GAAG;YACpD,UAAU,MAAM,GAAG;YACnB,UAAU;QACZ;QAEA,UAAU;QACV,OAAO;IACT;IAEA,4BAA4B;IAC5B,MAAM,mBAAmB,CAAC;QACxB,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC7B,0DAA0D;YAC1D,2CAA2C;YAC3C,MAAM,YAAY,MAAM,IAAI,CAAC,OAAO,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;YACzD,eAAe;gBAAE,aAAa;uBAAI,SAAS,WAAW;uBAAK;iBAAU;YAAC;QACxE;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,qBAAqB,SAAS,WAAW,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACvE,eAAe;YAAE,aAAa;QAAmB;IACnD;IAEA,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAAmC;;;;;;0BAEjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAe,WAAU;;oCAAuC;kDAC7D,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEjD,8OAAC;gCACC,IAAG;gCACH,OAAO,SAAS,YAAY;gCAC5B,UAAU,CAAC,IAAM,eAAe;wCAAE,cAAc,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAC/D,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,OAAO,YAAY,kBAAI,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,YAAY;;;;;;;;;;;;kCAGvF,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;;oCAAuC;kDACpC,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEnD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;wCAC3C,WAAW;wCACX,WAAU;wCACV,aAAY;;;;;;kDAEd,8OAAC;wCACC,MAAK;wCACL,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;4BAIF,OAAO,MAAM,kBAAI,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,MAAM;;;;;;4BAExE,SAAS,MAAM,CAAC,MAAM,GAAG,mBACxB,8OAAC;gCAAI,WAAU;0CACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;wCAEC,WAAU;;0DAEV,8OAAC;gDAAK,WAAU;0DAAa;;;;;;0DAC7B,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,kBAAkB;gDACjC,WAAU;0DACX;;;;;;;uCARI;;;;;;;;;;;;;;;;kCAiBf,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAuC;;;;;;0CAGxD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,QAAQ;wCACR,UAAU;wCACV,WAAU;;;;;;kDAEZ,8OAAC;wCACC,SAAQ;wCACR,WAAU;;0DAEV,8OAAC;gDACC,WAAU;gDACV,OAAM;gDACN,SAAQ;gDACR,MAAK;gDACL,eAAY;0DAEZ,cAAA,8OAAC;oDACC,UAAS;oDACT,GAAE;oDACF,UAAS;;;;;;;;;;;4CAEP;;;;;;;kDAGR,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;4BAK3C,SAAS,WAAW,CAAC,MAAM,GAAG,mBAC7B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,8OAAC;wCAAG,WAAU;kDACX,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC;gDAAe,WAAU;;kEACxB,8OAAC;wDAAK,WAAU;kEAAyB;;;;;;kEACzC,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,uBAAuB;wDACtC,WAAU;kEACX;;;;;;;+CANM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkB3B;uCAEe"}}, {"offset": {"line": 3116, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3122, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/jobs/Step3BudgetTimeline.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useJobForm } from '@/context/JobFormContext';\n\nconst Step3BudgetTimeline = () => {\n  const { formData, updateFormData } = useJobForm();\n  const [errors, setErrors] = useState({\n    budget: '',\n    deadline: '',\n  });\n\n  const validateForm = () => {\n    let isValid = true;\n    const newErrors = {\n      budget: '',\n      deadline: '',\n    };\n\n    if (!formData.budget || formData.budget <= 0) {\n      newErrors.budget = 'يرجى تحديد ميزانية صالحة للمشروع';\n      isValid = false;\n    }\n\n    if (!formData.deadline) {\n      newErrors.deadline = 'يرجى تحديد الموعد النهائي للمشروع';\n      isValid = false;\n    } else {\n      const selectedDate = new Date(formData.deadline);\n      const today = new Date();\n      today.setHours(0, 0, 0, 0);\n\n      if (selectedDate < today) {\n        newErrors.deadline = 'يجب أن يكون الموعد النهائي في المستقبل';\n        isValid = false;\n      }\n    }\n\n    setErrors(newErrors);\n    return isValid;\n  };\n\n  // Get minimum date (today) for the deadline input\n  const getMinDate = () => {\n    const today = new Date();\n    return today.toISOString().split('T')[0];\n  };\n\n  return (\n    <div>\n      <h2 className=\"text-xl font-bold text-navy mb-6\">الميزانية والجدول الزمني</h2>\n\n      <div className=\"space-y-6\">\n        <div>\n          <label htmlFor=\"budget\" className=\"block text-gray-700 font-medium mb-2\">\n            ميزانية المشروع (بالليرة السورية) <span className=\"text-red-500\">*</span>\n          </label>\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n              <span className=\"text-gray-500\">ل.س</span>\n            </div>\n            <input\n              type=\"number\"\n              id=\"budget\"\n              value={formData.budget || ''}\n              onChange={(e) => updateFormData({ budget: parseFloat(e.target.value) || 0 })}\n              min=\"0\"\n              step=\"1000\"\n              className=\"w-full px-4 py-2 pr-12 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent\"\n              placeholder=\"أدخل الميزانية المتوقعة\"\n            />\n          </div>\n          {errors.budget && <p className=\"mt-1 text-red-500 text-sm\">{errors.budget}</p>}\n\n          <div className=\"mt-2\">\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <button\n                type=\"button\"\n                onClick={() => updateFormData({ budget: 50000 })}\n                className={`px-3 py-1 rounded-full text-sm ${\n                  formData.budget === 50000 ? 'bg-navy text-white' : 'bg-gray-100 text-gray-700'\n                }`}\n              >\n                50,000 ل.س\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => updateFormData({ budget: 100000 })}\n                className={`px-3 py-1 rounded-full text-sm ${\n                  formData.budget === 100000 ? 'bg-navy text-white' : 'bg-gray-100 text-gray-700'\n                }`}\n              >\n                100,000 ل.س\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => updateFormData({ budget: 250000 })}\n                className={`px-3 py-1 rounded-full text-sm ${\n                  formData.budget === 250000 ? 'bg-navy text-white' : 'bg-gray-100 text-gray-700'\n                }`}\n              >\n                250,000 ل.س\n              </button>\n              <button\n                type=\"button\"\n                onClick={() => updateFormData({ budget: 500000 })}\n                className={`px-3 py-1 rounded-full text-sm ${\n                  formData.budget === 500000 ? 'bg-navy text-white' : 'bg-gray-100 text-gray-700'\n                }`}\n              >\n                500,000 ل.س\n              </button>\n            </div>\n          </div>\n        </div>\n\n        <div>\n          <label htmlFor=\"deadline\" className=\"block text-gray-700 font-medium mb-2\">\n            الموعد النهائي للمشروع <span className=\"text-red-500\">*</span>\n          </label>\n          <input\n            type=\"date\"\n            id=\"deadline\"\n            value={formData.deadline}\n            onChange={(e) => updateFormData({ deadline: e.target.value })}\n            min={getMinDate()}\n            className=\"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent\"\n          />\n          {errors.deadline && <p className=\"mt-1 text-red-500 text-sm\">{errors.deadline}</p>}\n        </div>\n\n        <div className=\"bg-beige bg-opacity-50 p-4 rounded-md\">\n          <h3 className=\"text-navy font-medium mb-2\">نصائح لتحديد الميزانية والموعد النهائي:</h3>\n          <ul className=\"list-disc list-inside text-gray-700 space-y-1 text-sm\">\n            <li>حدد ميزانية واقعية تتناسب مع حجم وتعقيد المشروع</li>\n            <li>ضع في اعتبارك تكلفة المواد والعمالة والوقت المطلوب</li>\n            <li>امنح وقتاً كافياً لإنجاز المشروع بجودة عالية</li>\n            <li>ضع في الحسبان الوقت اللازم للتعديلات والمراجعات</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Step3BudgetTimeline;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,sBAAsB;IAC1B,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAC9C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,QAAQ;QACR,UAAU;IACZ;IAEA,MAAM,eAAe;QACnB,IAAI,UAAU;QACd,MAAM,YAAY;YAChB,QAAQ;YACR,UAAU;QACZ;QAEA,IAAI,CAAC,SAAS,MAAM,IAAI,SAAS,MAAM,IAAI,GAAG;YAC5C,UAAU,MAAM,GAAG;YACnB,UAAU;QACZ;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;YACrB,UAAU;QACZ,OAAO;YACL,MAAM,eAAe,IAAI,KAAK,SAAS,QAAQ;YAC/C,MAAM,QAAQ,IAAI;YAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;YAExB,IAAI,eAAe,OAAO;gBACxB,UAAU,QAAQ,GAAG;gBACrB,UAAU;YACZ;QACF;QAEA,UAAU;QACV,OAAO;IACT;IAEA,kDAAkD;IAClD,MAAM,aAAa;QACjB,MAAM,QAAQ,IAAI;QAClB,OAAO,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAC1C;IAEA,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAAmC;;;;;;0BAEjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAS,WAAU;;oCAAuC;kDACrC,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAEnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAAgB;;;;;;;;;;;kDAElC,8OAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO,SAAS,MAAM,IAAI;wCAC1B,UAAU,CAAC,IAAM,eAAe;gDAAE,QAAQ,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;4CAAE;wCAC1E,KAAI;wCACJ,MAAK;wCACL,WAAU;wCACV,aAAY;;;;;;;;;;;;4BAGf,OAAO,MAAM,kBAAI,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,MAAM;;;;;;0CAEzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,eAAe;oDAAE,QAAQ;gDAAM;4CAC9C,WAAW,CAAC,+BAA+B,EACzC,SAAS,MAAM,KAAK,QAAQ,uBAAuB,6BACnD;sDACH;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,eAAe;oDAAE,QAAQ;gDAAO;4CAC/C,WAAW,CAAC,+BAA+B,EACzC,SAAS,MAAM,KAAK,SAAS,uBAAuB,6BACpD;sDACH;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,eAAe;oDAAE,QAAQ;gDAAO;4CAC/C,WAAW,CAAC,+BAA+B,EACzC,SAAS,MAAM,KAAK,SAAS,uBAAuB,6BACpD;sDACH;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,eAAe;oDAAE,QAAQ;gDAAO;4CAC/C,WAAW,CAAC,+BAA+B,EACzC,SAAS,MAAM,KAAK,SAAS,uBAAuB,6BACpD;sDACH;;;;;;;;;;;;;;;;;;;;;;;kCAOP,8OAAC;;0CACC,8OAAC;gCAAM,SAAQ;gCAAW,WAAU;;oCAAuC;kDAClD,8OAAC;wCAAK,WAAU;kDAAe;;;;;;;;;;;;0CAExD,8OAAC;gCACC,MAAK;gCACL,IAAG;gCACH,OAAO,SAAS,QAAQ;gCACxB,UAAU,CAAC,IAAM,eAAe;wCAAE,UAAU,EAAE,MAAM,CAAC,KAAK;oCAAC;gCAC3D,KAAK;gCACL,WAAU;;;;;;4BAEX,OAAO,QAAQ,kBAAI,8OAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ;;;;;;;;;;;;kCAG/E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhB;uCAEe"}}, {"offset": {"line": 3437, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3443, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/jobs/Step4Review.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useJobForm } from '@/context/JobFormContext';\nimport { formatDate } from '@/lib/utils';\n\nconst Step4Review = () => {\n  const { formData } = useJobForm();\n\n\n\n  // Get category name from ID\n  const getCategoryName = (categoryId: string) => {\n    const categories: Record<string, string> = {\n      'carpentry': 'النجارة',\n      'plumbing': 'السباكة',\n      'electrical': 'الكهرباء',\n      'painting': 'الدهان',\n      'construction': 'البناء',\n      'hvac': 'التكييف',\n      'metalwork': 'الحدادة',\n      'landscaping': 'تنسيق الحدائق',\n      'other': 'أخرى',\n    };\n\n    return categories[categoryId] || categoryId;\n  };\n\n  return (\n    <div>\n      <h2 className=\"text-xl font-bold text-navy mb-6\">مراجعة تفاصيل المشروع</h2>\n\n      <div className=\"space-y-8\">\n        <div className=\"bg-beige bg-opacity-50 p-4 rounded-md\">\n          <p className=\"text-gray-700 mb-4\">\n            يرجى مراجعة تفاصيل مشروعك بعناية قبل النشر. بعد النشر، سيتمكن الحرفيون من الاطلاع على مشروعك وتقديم عروضهم.\n          </p>\n        </div>\n\n        <div className=\"border border-gray-200 rounded-md overflow-hidden\">\n          <div className=\"bg-gray-50 px-4 py-3 border-b border-gray-200\">\n            <h3 className=\"text-lg font-medium text-navy\">التفاصيل الأساسية</h3>\n          </div>\n          <div className=\"p-4 space-y-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-500\">عنوان المشروع</h4>\n                <p className=\"text-gray-900\">{formData.title || 'غير محدد'}</p>\n              </div>\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-500\">فئة المشروع</h4>\n                <p className=\"text-gray-900\">{getCategoryName(formData.category) || 'غير محدد'}</p>\n              </div>\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-500\">الموقع</h4>\n                <p className=\"text-gray-900\">{formData.location || 'غير محدد'}</p>\n              </div>\n            </div>\n            <div>\n              <h4 className=\"text-sm font-medium text-gray-500\">وصف المشروع</h4>\n              <p className=\"text-gray-900 whitespace-pre-line\">{formData.description || 'غير محدد'}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border border-gray-200 rounded-md overflow-hidden\">\n          <div className=\"bg-gray-50 px-4 py-3 border-b border-gray-200\">\n            <h3 className=\"text-lg font-medium text-navy\">المتطلبات والمواصفات</h3>\n          </div>\n          <div className=\"p-4 space-y-4\">\n            <div>\n              <h4 className=\"text-sm font-medium text-gray-500\">متطلبات المشروع</h4>\n              <p className=\"text-gray-900 whitespace-pre-line\">{formData.requirements || 'غير محدد'}</p>\n            </div>\n            <div>\n              <h4 className=\"text-sm font-medium text-gray-500\">المهارات المطلوبة</h4>\n              {formData.skills.length > 0 ? (\n                <div className=\"flex flex-wrap gap-2 mt-1\">\n                  {formData.skills.map((skill, index) => (\n                    <span key={index} className=\"bg-skyblue px-3 py-1 rounded-full text-navy text-sm\">\n                      {skill}\n                    </span>\n                  ))}\n                </div>\n              ) : (\n                <p className=\"text-gray-900\">غير محدد</p>\n              )}\n            </div>\n            <div>\n              <h4 className=\"text-sm font-medium text-gray-500\">المرفقات</h4>\n              {formData.attachments.length > 0 ? (\n                <ul className=\"list-disc list-inside text-gray-900\">\n                  {formData.attachments.map((file, index) => (\n                    <li key={index}>{file}</li>\n                  ))}\n                </ul>\n              ) : (\n                <p className=\"text-gray-900\">لا توجد مرفقات</p>\n              )}\n            </div>\n          </div>\n        </div>\n\n        <div className=\"border border-gray-200 rounded-md overflow-hidden\">\n          <div className=\"bg-gray-50 px-4 py-3 border-b border-gray-200\">\n            <h3 className=\"text-lg font-medium text-navy\">الميزانية والجدول الزمني</h3>\n          </div>\n          <div className=\"p-4\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-500\">ميزانية المشروع</h4>\n                <p className=\"text-gray-900\">{formData.budget ? `${formData.budget.toLocaleString()} ل.س` : 'غير محدد'}</p>\n              </div>\n              <div>\n                <h4 className=\"text-sm font-medium text-gray-500\">الموعد النهائي</h4>\n                <p className=\"text-gray-900\">{formatDate(formData.deadline)}</p>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-navy bg-opacity-5 p-4 rounded-md\">\n          <div className=\"flex items-start\">\n            <div className=\"flex-shrink-0 mt-0.5\">\n              <svg className=\"h-5 w-5 text-navy\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div className=\"mr-3\">\n              <p className=\"text-sm text-navy\">\n                بالضغط على \"نشر المشروع\"، أنت توافق على شروط وأحكام منصة دوزان وسياسة الخصوصية.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Step4Review;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,cAAc;IAClB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAI9B,4BAA4B;IAC5B,MAAM,kBAAkB,CAAC;QACvB,MAAM,aAAqC;YACzC,aAAa;YACb,YAAY;YACZ,cAAc;YACd,YAAY;YACZ,gBAAgB;YAChB,QAAQ;YACR,aAAa;YACb,eAAe;YACf,SAAS;QACX;QAEA,OAAO,UAAU,CAAC,WAAW,IAAI;IACnC;IAEA,qBACE,8OAAC;;0BACC,8OAAC;gBAAG,WAAU;0BAAmC;;;;;;0BAEjD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;kCAKpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAgC;;;;;;;;;;;0CAEhD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEAAiB,SAAS,KAAK,IAAI;;;;;;;;;;;;0DAElD,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEAAiB,gBAAgB,SAAS,QAAQ,KAAK;;;;;;;;;;;;0DAEtE,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEAAiB,SAAS,QAAQ,IAAI;;;;;;;;;;;;;;;;;;kDAGvD,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAqC,SAAS,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;kCAKhF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAgC;;;;;;;;;;;0CAEhD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,8OAAC;gDAAE,WAAU;0DAAqC,SAAS,YAAY,IAAI;;;;;;;;;;;;kDAE7E,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;4CACjD,SAAS,MAAM,CAAC,MAAM,GAAG,kBACxB,8OAAC;gDAAI,WAAU;0DACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;wDAAiB,WAAU;kEACzB;uDADQ;;;;;;;;;qEAMf,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;kDAGjC,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAoC;;;;;;4CACjD,SAAS,WAAW,CAAC,MAAM,GAAG,kBAC7B,8OAAC;gDAAG,WAAU;0DACX,SAAS,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC;kEAAgB;uDAAR;;;;;;;;;qEAIb,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAMrC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAgC;;;;;;;;;;;0CAEhD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAE,WAAU;8DAAiB,SAAS,MAAM,GAAG,GAAG,SAAS,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG;;;;;;;;;;;;sDAE9F,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;8DAClD,8OAAC;oDAAE,WAAU;8DAAiB,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE,SAAS,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMlE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAoB,OAAM;wCAA6B,SAAQ;wCAAY,MAAK;kDAC7F,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAmI,UAAS;;;;;;;;;;;;;;;;8CAG3K,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C;uCAEe"}}, {"offset": {"line": 3927, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3933, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/post-job/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { JobFormProvider, useJobForm } from '@/context/JobFormContext';\nimport FormLayout from '@/components/jobs/FormLayout';\nimport Step1BasicDetails from '@/components/jobs/Step1BasicDetails';\nimport Step2Requirements from '@/components/jobs/Step2Requirements';\nimport Step3BudgetTimeline from '@/components/jobs/Step3BudgetTimeline';\nimport Step4Review from '@/components/jobs/Step4Review';\n\nconst JobCreationForm = () => {\n  const router = useRouter();\n  const { formData, currentStep, setCurrentStep, isSubmitting, setIsSubmitting } = useJobForm();\n  const totalSteps = 4;\n\n  const validateStep = (step: number) => {\n    switch (step) {\n      case 1:\n        return (\n          formData.title && formData.title.trim().length >= 5 &&\n          formData.description && formData.description.trim().length >= 20 &&\n          formData.category && formData.category.trim() !== ''\n        );\n\n      case 2:\n        return (\n          formData.requirements && formData.requirements.trim() !== '' &&\n          formData.skills && formData.skills.length > 0\n        );\n\n      case 3:\n        const today = new Date();\n        today.setHours(0, 0, 0, 0);\n        const selectedDate = formData.deadline ? new Date(formData.deadline) : null;\n        return (\n          formData.budget && formData.budget > 0 &&\n          formData.deadline && formData.deadline.trim() !== '' &&\n          selectedDate && selectedDate >= today\n        );\n\n      default:\n        return true;\n    }\n  };\n\n  const handleNext = () => {\n    if (currentStep < totalSteps) {\n      if (validateStep(currentStep)) {\n        setCurrentStep(currentStep + 1);\n        window.scrollTo(0, 0);\n      } else {\n        // Show detailed validation errors based on current step\n        let errorMessage = 'يرجى إكمال جميع الحقول المطلوبة:\\n';\n\n        switch (currentStep) {\n          case 1:\n            if (!formData.title || formData.title.trim().length < 5) {\n              errorMessage += '- عنوان المشروع (5 أحرف على الأقل)\\n';\n            }\n            if (!formData.description || formData.description.trim().length < 20) {\n              errorMessage += '- وصف المشروع (20 حرف على الأقل)\\n';\n            }\n            if (!formData.category) {\n              errorMessage += '- فئة المشروع\\n';\n            }\n            break;\n          case 2:\n            if (!formData.requirements || formData.requirements.trim() === '') {\n              errorMessage += '- متطلبات المشروع\\n';\n            }\n            if (!formData.skills || formData.skills.length === 0) {\n              errorMessage += '- المهارات المطلوبة (مهارة واحدة على الأقل)\\n';\n            }\n            break;\n          case 3:\n            if (!formData.budget || formData.budget <= 0) {\n              errorMessage += '- ميزانية المشروع\\n';\n            }\n            if (!formData.deadline) {\n              errorMessage += '- الموعد النهائي للمشروع\\n';\n            }\n            break;\n        }\n\n        alert(errorMessage);\n      }\n    } else {\n      handleSubmit();\n    }\n  };\n\n  const handlePrevious = () => {\n    if (currentStep > 1) {\n      setCurrentStep(currentStep - 1);\n      window.scrollTo(0, 0);\n    }\n  };\n\n  const handleSubmit = async () => {\n    setIsSubmitting(true);\n\n    try {\n      // In a real app, you would send the form data to your API\n      console.log('Submitting job:', formData);\n\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // Redirect to success page\n      router.push('/job-posted-success');\n    } catch (error) {\n      console.error('Error submitting job:', error);\n      alert('حدث خطأ أثناء نشر المشروع. يرجى المحاولة مرة أخرى.');\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <FormLayout\n      currentStep={currentStep}\n      totalSteps={totalSteps}\n      onNext={handleNext}\n      onPrevious={handlePrevious}\n      isLastStep={currentStep === totalSteps}\n      isSubmitting={isSubmitting}\n    >\n      {currentStep === 1 && <Step1BasicDetails />}\n      {currentStep === 2 && <Step2Requirements />}\n      {currentStep === 3 && <Step3BudgetTimeline />}\n      {currentStep === 4 && <Step4Review />}\n    </FormLayout>\n  );\n};\n\nconst PostJobPage = () => {\n  return (\n    <JobFormProvider>\n      <JobCreationForm />\n    </JobFormProvider>\n  );\n};\n\nexport default PostJobPage;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;AAWA,MAAM,kBAAkB;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,cAAc,EAAE,YAAY,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD;IAC1F,MAAM,aAAa;IAEnB,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBACH,OACE,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,IAAI,GAAG,MAAM,IAAI,KAClD,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,IAAI,GAAG,MAAM,IAAI,MAC9D,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,IAAI,OAAO;YAGtD,KAAK;gBACH,OACE,SAAS,YAAY,IAAI,SAAS,YAAY,CAAC,IAAI,OAAO,MAC1D,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC,MAAM,GAAG;YAGhD,KAAK;gBACH,MAAM,QAAQ,IAAI;gBAClB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;gBACxB,MAAM,eAAe,SAAS,QAAQ,GAAG,IAAI,KAAK,SAAS,QAAQ,IAAI;gBACvE,OACE,SAAS,MAAM,IAAI,SAAS,MAAM,GAAG,KACrC,SAAS,QAAQ,IAAI,SAAS,QAAQ,CAAC,IAAI,OAAO,MAClD,gBAAgB,gBAAgB;YAGpC;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,cAAc,YAAY;YAC5B,IAAI,aAAa,cAAc;gBAC7B,eAAe,cAAc;gBAC7B,OAAO,QAAQ,CAAC,GAAG;YACrB,OAAO;gBACL,wDAAwD;gBACxD,IAAI,eAAe;gBAEnB,OAAQ;oBACN,KAAK;wBACH,IAAI,CAAC,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,IAAI,GAAG,MAAM,GAAG,GAAG;4BACvD,gBAAgB;wBAClB;wBACA,IAAI,CAAC,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,IAAI,GAAG,MAAM,GAAG,IAAI;4BACpE,gBAAgB;wBAClB;wBACA,IAAI,CAAC,SAAS,QAAQ,EAAE;4BACtB,gBAAgB;wBAClB;wBACA;oBACF,KAAK;wBACH,IAAI,CAAC,SAAS,YAAY,IAAI,SAAS,YAAY,CAAC,IAAI,OAAO,IAAI;4BACjE,gBAAgB;wBAClB;wBACA,IAAI,CAAC,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC,MAAM,KAAK,GAAG;4BACpD,gBAAgB;wBAClB;wBACA;oBACF,KAAK;wBACH,IAAI,CAAC,SAAS,MAAM,IAAI,SAAS,MAAM,IAAI,GAAG;4BAC5C,gBAAgB;wBAClB;wBACA,IAAI,CAAC,SAAS,QAAQ,EAAE;4BACtB,gBAAgB;wBAClB;wBACA;gBACJ;gBAEA,MAAM;YACR;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,cAAc,GAAG;YACnB,eAAe,cAAc;YAC7B,OAAO,QAAQ,CAAC,GAAG;QACrB;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB;QAEhB,IAAI;YACF,0DAA0D;YAC1D,QAAQ,GAAG,CAAC,mBAAmB;YAE/B,oBAAoB;YACpB,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,2BAA2B;YAC3B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,8OAAC,wIAAA,CAAA,UAAU;QACT,aAAa;QACb,YAAY;QACZ,QAAQ;QACR,YAAY;QACZ,YAAY,gBAAgB;QAC5B,cAAc;;YAEb,gBAAgB,mBAAK,8OAAC,+IAAA,CAAA,UAAiB;;;;;YACvC,gBAAgB,mBAAK,8OAAC,+IAAA,CAAA,UAAiB;;;;;YACvC,gBAAgB,mBAAK,8OAAC,iJAAA,CAAA,UAAmB;;;;;YACzC,gBAAgB,mBAAK,8OAAC,yIAAA,CAAA,UAAW;;;;;;;;;;;AAGxC;AAEA,MAAM,cAAc;IAClB,qBACE,8OAAC,iIAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC;;;;;;;;;;AAGP;uCAEe"}}, {"offset": {"line": 4086, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}