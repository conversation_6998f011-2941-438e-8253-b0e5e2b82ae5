import React from 'react';
import Link from 'next/link';
import { Button } from '../ui/Button';

const CTASection = () => {
  return (
    <section className="py-16 bg-navy text-white">
      <div className="container mx-auto px-4 text-center">
        <h2 className="text-3xl font-bold mb-4">جاهز للبدء؟</h2>
        <p className="text-lg mb-8 max-w-2xl mx-auto">
          انضم إلى الآلاف من أصحاب المشاريع والحرفيين على منصة دوزان واستفد من خدماتنا المميزة.
        </p>
        <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
          <Link href="/register">
            <Button size="lg" className="bg-white text-navy hover:bg-gray-100">
              إنشاء حساب
            </Button>
          </Link>
          <Link href="/post-job">
            <Button size="lg" variant="outline" className="border-white text-white hover:bg-navy-800">
              نشر مشروع
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default CTASection;
