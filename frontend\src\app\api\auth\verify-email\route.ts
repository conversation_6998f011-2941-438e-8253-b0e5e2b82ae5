import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getClientIP, getUserAgent } from '@/lib/security';
import { sendWelcomeEmail } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'رمز التأكيد مطلوب' },
        { status: 400 }
      );
    }

    // البحث عن المستخدم بالرمز
    const user = await prisma.user.findFirst({
      where: {
        emailVerificationToken: token,
        emailVerificationExpiry: {
          gt: new Date() // التأكد من أن الرمز لم ينته
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'رمز التأكيد غير صحيح أو منتهي الصلاحية' },
        { status: 400 }
      );
    }

    if (user.isVerified) {
      return NextResponse.json(
        { message: 'البريد الإلكتروني مؤكد بالفعل' },
        { status: 200 }
      );
    }

    // تأكيد البريد الإلكتروني
    await prisma.user.update({
      where: { id: user.id },
      data: {
        isVerified: true,
        emailVerifiedAt: new Date(),
        emailVerificationToken: null,
        emailVerificationExpiry: null
      }
    });

    // إرسال بريد الترحيب
    await sendWelcomeEmail(user.email, user.name, user.role);

    // تسجيل النشاط
    await prisma.auditLog.create({
      data: {
        userId: user.id,
        action: 'EMAIL_VERIFIED',
        entity: 'User',
        entityId: user.id,
        ipAddress: getClientIP(request),
        userAgent: getUserAgent(request)
      }
    });

    return NextResponse.json({
      message: 'تم تأكيد البريد الإلكتروني بنجاح',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        isVerified: true
      }
    });

  } catch (error) {
    console.error('خطأ في تأكيد البريد الإلكتروني:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

// GET endpoint للتحقق من صحة الرمز
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { error: 'رمز التأكيد مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من صحة الرمز دون تأكيد البريد
    const user = await prisma.user.findFirst({
      where: {
        emailVerificationToken: token,
        emailVerificationExpiry: {
          gt: new Date()
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        isVerified: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'رمز التأكيد غير صحيح أو منتهي الصلاحية' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      valid: true,
      user: {
        name: user.name,
        email: user.email,
        isVerified: user.isVerified
      }
    });

  } catch (error) {
    console.error('خطأ في التحقق من رمز التأكيد:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
