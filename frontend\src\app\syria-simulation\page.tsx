'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

const SyriaSimulationPage = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [userType, setUserType] = useState<'client' | 'craftsman' | null>(null);

  // محاكاة العميل السوري
  const clientJourney = [
    {
      title: "أحمد من دمشق - يحتاج إصلاح مطبخ",
      description: "أحمد يعيش في دمشق، المزة. مطبخه بحاجة لإصلاح بعد تضرره من انقطاع الكهرباء المتكرر",
      scenario: "الوضع الاقتصادي صعب، يبحث عن حرفي موثوق بسعر معقول",
      actions: [
        "يدخل للموقع من هاتفه (الإنترنت بطيء أحياناً)",
        "يسجل حساب جديد بالعربية",
        "ينشر مشروع إصلاح المطبخ",
        "يحدد الميزانية بالليرة السورية",
        "يستقبل عروض من حرفيين محليين"
      ],
      challenges: [
        "الإنترنت بطيء أو منقطع أحياناً",
        "يفضل التعامل بالليرة السورية",
        "يحتاج ضمانات على العمل",
        "يريد رؤية أعمال سابقة للحرفي",
        "التواصل يجب أن يكون سهل وواضح"
      ]
    },
    {
      title: "البحث عن حرفي مناسب",
      description: "أحمد يتصفح الحرفيين المتاحين في منطقته",
      scenario: "يريد حرفي قريب من منطقته لتوفير تكلفة المواصلات",
      actions: [
        "يفلتر الحرفيين حسب المنطقة (دمشق)",
        "يقرأ التقييمات والتعليقات",
        "يشاهد معرض أعمال الحرفيين",
        "يقارن الأسعار والمدة المطلوبة",
        "يتواصل مع 2-3 حرفيين"
      ],
      challenges: [
        "يريد رؤية صور حقيقية للأعمال السابقة",
        "يحتاج تأكيد أن الحرفي موثوق",
        "يفضل الحرفيين ذوي التقييمات العالية",
        "يريد ضمان على العمل والمواد"
      ]
    },
    {
      title: "اختيار العرض والتنفيذ",
      description: "أحمد يختار أفضل عرض ويبدأ العمل",
      scenario: "اختار محمد النجار لأنه من نفس المنطقة وله تقييمات ممتازة",
      actions: [
        "يقبل عرض محمد النجار",
        "يتفق على تفاصيل العمل والمواد",
        "يتابع تقدم العمل يومياً",
        "يستلم العمل ويفحصه",
        "يقيم الحرفي ويكتب تعليق"
      ],
      challenges: [
        "يريد تحديثات مستمرة عن التقدم",
        "يحتاج صور قبل وبعد العمل",
        "يريد فاتورة واضحة بالتكاليف",
        "يحتاج ضمان على العمل المنجز"
      ]
    }
  ];

  // محاكاة الحرفي السوري
  const craftsmanJourney = [
    {
      title: "محمد النجار من دمشق - يبحث عن عمل",
      description: "محمد نجار محترف، يعمل من منزله في كفرسوسة، يريد زيادة دخله",
      scenario: "الوضع الاقتصادي صعب، يحتاج مشاريع إضافية لإعالة أسرته",
      actions: [
        "يسجل في الموقع كحرفي",
        "يضع صور أعماله السابقة",
        "يحدد مناطق عمله (دمشق وريفها)",
        "يضع أسعاره بالليرة السورية",
        "يتصفح المشاريع المتاحة يومياً"
      ],
      challenges: [
        "المنافسة مع حرفيين آخرين",
        "العملاء يريدون أسعار منخفضة",
        "يحتاج إثبات خبرته ومهارته",
        "تكلفة المواد ترتفع باستمرار",
        "يريد ضمان الحصول على أجره"
      ]
    },
    {
      title: "تقديم عروض على المشاريع",
      description: "محمد يقدم عروض مدروسة على المشاريع المناسبة",
      scenario: "يركز على المشاريع في منطقته ومجال تخصصه",
      actions: [
        "يقرأ تفاصيل المشروع بعناية",
        "يحسب التكلفة والوقت المطلوب",
        "يكتب عرض مفصل ومقنع",
        "يضع صور أعمال مشابهة",
        "يتابع رد العميل على العرض"
      ],
      challenges: [
        "المنافسة على السعر مع حرفيين آخرين",
        "إقناع العميل بجودة عمله",
        "تحديد سعر عادل يغطي التكاليف",
        "ضمان جدية العميل في المشروع"
      ]
    },
    {
      title: "تنفيذ المشروع وبناء السمعة",
      description: "محمد ينفذ المشروع بجودة عالية لبناء سمعته",
      scenario: "يركز على الجودة والالتزام بالمواعيد لضمان تقييمات إيجابية",
      actions: [
        "يبدأ العمل في الموعد المحدد",
        "يرسل صور تقدم العمل للعميل",
        "يلتزم بالمواصفات المتفق عليها",
        "يسلم العمل في الوقت المحدد",
        "يحصل على تقييم إيجابي"
      ],
      challenges: [
        "ضمان جودة العمل رغم ضغط التكلفة",
        "التعامل مع تغيير أسعار المواد",
        "إدارة الوقت بين عدة مشاريع",
        "بناء علاقة ثقة مع العملاء"
      ]
    }
  ];

  const currentJourney = userType === 'client' ? clientJourney : craftsmanJourney;

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-navy mb-4">
                🇸🇾 محاكاة تجربة المستخدم السوري
              </h1>
              <p className="text-xl text-gray-600 mb-6">
                فهم احتياجات وتحديات المستخدمين في سوريا
              </p>
            </div>

            {!userType && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                <Card 
                  className="border-0 bg-white/90 backdrop-blur-md shadow-xl cursor-pointer hover:shadow-2xl transition-all"
                  onClick={() => setUserType('client')}
                >
                  <CardContent className="p-8 text-center">
                    <div className="text-6xl mb-4">👨‍💼</div>
                    <h2 className="text-2xl font-bold text-navy mb-4">محاكاة العميل</h2>
                    <p className="text-gray-600 mb-6">
                      تجربة أحمد من دمشق الذي يحتاج إصلاح مطبخه
                    </p>
                    <Button className="bg-gradient-to-r from-navy to-teal">
                      ابدأ محاكاة العميل
                    </Button>
                  </CardContent>
                </Card>

                <Card 
                  className="border-0 bg-white/90 backdrop-blur-md shadow-xl cursor-pointer hover:shadow-2xl transition-all"
                  onClick={() => setUserType('craftsman')}
                >
                  <CardContent className="p-8 text-center">
                    <div className="text-6xl mb-4">👨‍🔧</div>
                    <h2 className="text-2xl font-bold text-navy mb-4">محاكاة الحرفي</h2>
                    <p className="text-gray-600 mb-6">
                      تجربة محمد النجار الذي يبحث عن مشاريع إضافية
                    </p>
                    <Button className="bg-gradient-to-r from-navy to-teal">
                      ابدأ محاكاة الحرفي
                    </Button>
                  </CardContent>
                </Card>
              </div>
            )}

            {userType && (
              <>
                {/* Progress Bar */}
                <div className="mb-8">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-2xl font-bold text-navy">
                      {userType === 'client' ? '👨‍💼 رحلة العميل' : '👨‍🔧 رحلة الحرفي'}
                    </h2>
                    <Button 
                      onClick={() => {
                        setUserType(null);
                        setCurrentStep(0);
                      }}
                      variant="outline"
                    >
                      العودة للاختيار
                    </Button>
                  </div>
                  <div className="flex space-x-2 space-x-reverse">
                    {currentJourney.map((_, index) => (
                      <div
                        key={index}
                        className={`flex-1 h-2 rounded-full ${
                          index <= currentStep ? 'bg-teal' : 'bg-gray-200'
                        }`}
                      />
                    ))}
                  </div>
                  <div className="flex justify-between mt-2 text-sm text-gray-600">
                    <span>البداية</span>
                    <span>النهاية</span>
                  </div>
                </div>

                {/* Current Step */}
                <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
                  <CardHeader>
                    <CardTitle className="text-xl text-navy">
                      المرحلة {currentStep + 1}: {currentJourney[currentStep].title}
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                      {/* Scenario */}
                      <div>
                        <h3 className="font-semibold text-navy mb-3">📖 السيناريو:</h3>
                        <p className="text-gray-700 mb-4">{currentJourney[currentStep].description}</p>
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                          <p className="text-blue-800">{currentJourney[currentStep].scenario}</p>
                        </div>

                        <h3 className="font-semibold text-navy mb-3 mt-6">✅ الإجراءات:</h3>
                        <ul className="space-y-2">
                          {currentJourney[currentStep].actions.map((action, index) => (
                            <li key={index} className="flex items-start space-x-2 space-x-reverse">
                              <span className="text-green-500 mt-1">•</span>
                              <span className="text-gray-700">{action}</span>
                            </li>
                          ))}
                        </ul>
                      </div>

                      {/* Challenges */}
                      <div>
                        <h3 className="font-semibold text-navy mb-3">⚠️ التحديات والاعتبارات:</h3>
                        <ul className="space-y-3">
                          {currentJourney[currentStep].challenges.map((challenge, index) => (
                            <li key={index} className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                              <div className="flex items-start space-x-2 space-x-reverse">
                                <span className="text-yellow-600 mt-1">⚠️</span>
                                <span className="text-yellow-800">{challenge}</span>
                              </div>
                            </li>
                          ))}
                        </ul>

                        {/* Context Box */}
                        <div className="mt-6 bg-gray-50 border border-gray-200 rounded-lg p-4">
                          <h4 className="font-semibold text-navy mb-2">🇸🇾 السياق السوري:</h4>
                          <ul className="text-sm text-gray-700 space-y-1">
                            <li>• الوضع الاقتصادي الصعب يؤثر على القرارات</li>
                            <li>• انقطاع الإنترنت والكهرباء أحياناً</li>
                            <li>• تفضيل التعامل بالليرة السورية</li>
                            <li>• أهمية الثقة والضمانات</li>
                            <li>• التركيز على الجودة والسعر المناسب</li>
                          </ul>
                        </div>
                      </div>
                    </div>

                    {/* Navigation */}
                    <div className="flex justify-between mt-8">
                      <Button
                        onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
                        disabled={currentStep === 0}
                        variant="outline"
                      >
                        المرحلة السابقة
                      </Button>
                      
                      <Button
                        onClick={() => setCurrentStep(Math.min(currentJourney.length - 1, currentStep + 1))}
                        disabled={currentStep === currentJourney.length - 1}
                        className="bg-gradient-to-r from-navy to-teal"
                      >
                        المرحلة التالية
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                {/* Insights */}
                {currentStep === currentJourney.length - 1 && (
                  <Card className="border-0 bg-gradient-to-r from-green-500 to-green-600 text-white">
                    <CardHeader>
                      <CardTitle>🎯 الخلاصة والتوصيات</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="font-semibold mb-3">✅ نقاط القوة المطلوبة:</h3>
                          <ul className="space-y-2 text-white/90">
                            <li>• واجهة بسيطة وسريعة التحميل</li>
                            <li>• دعم كامل للغة العربية</li>
                            <li>• نظام تقييمات موثوق</li>
                            <li>• معرض أعمال بصور حقيقية</li>
                            <li>• نظام دفع آمن ومرن</li>
                          </ul>
                        </div>
                        <div>
                          <h3 className="font-semibold mb-3">🔧 التحسينات المطلوبة:</h3>
                          <ul className="space-y-2 text-white/90">
                            <li>• تحسين الأداء للإنترنت البطيء</li>
                            <li>• دعم العملات المحلية</li>
                            <li>• نظام ضمانات واضح</li>
                            <li>• تواصل سهل ومباشر</li>
                            <li>• دعم فني باللغة العربية</li>
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default SyriaSimulationPage;
