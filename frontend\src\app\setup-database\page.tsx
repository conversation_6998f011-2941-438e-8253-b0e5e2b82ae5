'use client';

import { useState } from 'react';
import supabase from '@/lib/supabase';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export default function SetupDatabase() {
  const [status, setStatus] = useState('جاهز لإعداد قاعدة البيانات');
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message: string, success: boolean, data?: any) => {
    setResults(prev => [...prev, {
      id: Date.now(),
      message,
      success,
      data,
      timestamp: new Date().toLocaleTimeString('ar-SA')
    }]);
  };

  const createUsersTable = async () => {
    setLoading(true);
    setStatus('جاري إنشاء جدول المستخدمين...');
    
    try {
      const { data, error } = await supabase.rpc('create_users_table');
      
      if (error) {
        // Try alternative method using SQL
        const { data: sqlData, error: sqlError } = await supabase
          .from('information_schema.tables')
          .select('*')
          .eq('table_name', 'users');
          
        if (sqlError) {
          addResult(`خطأ في إنشاء جدول المستخدمين: ${error.message}`, false, error);
        } else {
          addResult('جدول المستخدمين موجود مسبقاً أو تم إنشاؤه ✅', true, sqlData);
        }
      } else {
        addResult('تم إنشاء جدول المستخدمين بنجاح! ✅', true, data);
      }
    } catch (err: any) {
      addResult(`خطأ: ${err.message}`, false, err);
    }
    
    setLoading(false);
  };

  const createProjectsTable = async () => {
    setLoading(true);
    setStatus('جاري إنشاء جدول المشاريع...');
    
    try {
      // Create projects table using raw SQL
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS projects (
          id TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
          title TEXT NOT NULL,
          description TEXT NOT NULL,
          category TEXT NOT NULL,
          budget_min DECIMAL,
          budget_max DECIMAL,
          location TEXT NOT NULL,
          client_id TEXT NOT NULL,
          status TEXT DEFAULT 'OPEN',
          created_at TIMESTAMP DEFAULT NOW(),
          updated_at TIMESTAMP DEFAULT NOW()
        );
      `;
      
      const { data, error } = await supabase.rpc('exec_sql', { sql: createTableSQL });
      
      if (error) {
        addResult(`خطأ في إنشاء جدول المشاريع: ${error.message}`, false, error);
      } else {
        addResult('تم إنشاء جدول المشاريع بنجاح! ✅', true, data);
      }
    } catch (err: any) {
      addResult(`خطأ: ${err.message}`, false, err);
    }
    
    setLoading(false);
  };

  const testInsertData = async () => {
    setLoading(true);
    setStatus('جاري اختبار إدخال البيانات...');
    
    try {
      // Try to insert test data into projects
      const { data, error } = await supabase
        .from('projects')
        .insert([
          {
            title: 'مشروع تجريبي',
            description: 'هذا مشروع تجريبي لاختبار قاعدة البيانات',
            category: 'تجريبي',
            location: 'دمشق',
            client_id: 'test-client-id'
          }
        ])
        .select();
      
      if (error) {
        addResult(`خطأ في إدخال البيانات: ${error.message}`, false, error);
      } else {
        addResult('تم إدخال البيانات التجريبية بنجاح! ✅', true, data);
      }
    } catch (err: any) {
      addResult(`خطأ: ${err.message}`, false, err);
    }
    
    setLoading(false);
  };

  const checkTables = async () => {
    setLoading(true);
    setStatus('جاري فحص الجداول الموجودة...');
    
    try {
      // List all tables
      const { data, error } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public');
      
      if (error) {
        addResult(`خطأ في فحص الجداول: ${error.message}`, false, error);
      } else {
        addResult(`الجداول الموجودة: ${data?.length || 0} جدول ✅`, true, data);
      }
    } catch (err: any) {
      addResult(`خطأ: ${err.message}`, false, err);
    }
    
    setLoading(false);
  };

  const clearResults = () => {
    setResults([]);
    setStatus('جاهز لإعداد قاعدة البيانات');
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-navy mb-4">
                🗄️ إعداد قاعدة البيانات
              </h1>
              <p className="text-xl text-gray-600">
                إنشاء الجداول والبيانات الأساسية في Supabase
              </p>
            </div>

            {/* Status */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">الحالة الحالية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className={`text-2xl font-bold mb-4 ${
                    loading ? 'text-blue-600' : 'text-gray-600'
                  }`}>
                    {status}
                  </div>
                  {loading && (
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal mx-auto"></div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Setup Buttons */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">خطوات الإعداد</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Button 
                    onClick={checkTables}
                    disabled={loading}
                    className="bg-gradient-to-r from-blue-500 to-blue-600"
                  >
                    فحص الجداول
                  </Button>
                  
                  <Button 
                    onClick={createUsersTable}
                    disabled={loading}
                    className="bg-gradient-to-r from-green-500 to-green-600"
                  >
                    إنشاء جدول المستخدمين
                  </Button>
                  
                  <Button 
                    onClick={createProjectsTable}
                    disabled={loading}
                    className="bg-gradient-to-r from-purple-500 to-purple-600"
                  >
                    إنشاء جدول المشاريع
                  </Button>
                  
                  <Button 
                    onClick={testInsertData}
                    disabled={loading}
                    className="bg-gradient-to-r from-orange-500 to-orange-600"
                  >
                    اختبار البيانات
                  </Button>
                </div>
                
                <div className="mt-4">
                  <Button 
                    onClick={clearResults}
                    disabled={loading}
                    variant="outline"
                    className="w-full"
                  >
                    مسح النتائج
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Results */}
            {results.length > 0 && (
              <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
                <CardHeader>
                  <CardTitle className="text-2xl text-navy">نتائج الإعداد</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {results.map((result) => (
                      <div 
                        key={result.id}
                        className={`p-4 rounded-lg border ${
                          result.success 
                            ? 'bg-green-50 border-green-200 text-green-800' 
                            : 'bg-red-50 border-red-200 text-red-800'
                        }`}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <span className="font-medium">{result.message}</span>
                          <span className="text-xs opacity-75">{result.timestamp}</span>
                        </div>
                        {result.data && (
                          <pre className="text-xs bg-white/50 p-2 rounded overflow-auto">
                            {JSON.stringify(result.data, null, 2)}
                          </pre>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Instructions */}
            <Card className="border-0 bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">تعليمات مهمة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-yellow-100 border border-yellow-400 text-yellow-800 px-4 py-3 rounded">
                    <strong>ملاحظة:</strong> بما أن Prisma لا يستطيع الوصول للخادم المباشر، سنستخدم Supabase Client لإنشاء الجداول.
                  </div>
                  
                  <div>
                    <h3 className="font-semibold text-navy mb-2">الخطوات:</h3>
                    <ol className="space-y-1 text-gray-700 list-decimal list-inside">
                      <li>فحص الجداول الموجودة</li>
                      <li>إنشاء الجداول الأساسية</li>
                      <li>اختبار إدخال البيانات</li>
                      <li>التحقق من نجاح العملية</li>
                    </ol>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Navigation */}
            <div className="text-center mt-8 space-x-4 space-x-reverse">
              <a 
                href="/test-dashboard"
                className="inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all"
              >
                العودة للوحة الاختبار
              </a>
              <a 
                href="/test-simple-supabase"
                className="inline-block bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all"
              >
                اختبار الاتصال
              </a>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
