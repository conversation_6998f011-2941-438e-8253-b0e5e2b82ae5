'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';

const MessagesPage = () => {
  const [selectedChat, setSelectedChat] = useState(1);
  const [newMessage, setNewMessage] = useState('');

  const conversations = [
    {
      id: 1,
      name: 'أحمد النجار',
      role: 'حرفي',
      lastMessage: 'سأبدأ العمل غداً صباحاً إن شاء الله',
      lastMessageTime: '10:30 ص',
      unreadCount: 2,
      online: true,
      avatar: 'أ',
      projectTitle: 'تجديد المطبخ الرئيسي'
    },
    {
      id: 2,
      name: 'فاطمة أحمد',
      role: 'عميل',
      lastMessage: 'شكراً لك على العمل الممتاز',
      lastMessageTime: 'أمس',
      unreadCount: 0,
      online: false,
      avatar: 'ف',
      projectTitle: 'إصلاح نظام السباكة'
    },
    {
      id: 3,
      name: 'محمد الدهان',
      role: 'حرفي',
      lastMessage: 'هل يمكنني الحصول على المواد اليوم؟',
      lastMessageTime: 'أمس',
      unreadCount: 1,
      online: true,
      avatar: 'م',
      projectTitle: 'دهان الشقة الكاملة'
    },
    {
      id: 4,
      name: 'سارة الكهربائية',
      role: 'حرفي',
      lastMessage: 'تم إرسال العرض، أرجو المراجعة',
      lastMessageTime: '2024-02-10',
      unreadCount: 0,
      online: false,
      avatar: 'س',
      projectTitle: 'تركيب نظام كهرباء ذكي'
    }
  ];

  const messages = [
    {
      id: 1,
      senderId: 1,
      senderName: 'أحمد النجار',
      content: 'مرحباً، شكراً لقبول عرضي',
      timestamp: '2024-02-15 09:00',
      isMe: false
    },
    {
      id: 2,
      senderId: 'me',
      senderName: 'أنا',
      content: 'أهلاً وسهلاً، متى يمكنك البدء؟',
      timestamp: '2024-02-15 09:15',
      isMe: true
    },
    {
      id: 3,
      senderId: 1,
      senderName: 'أحمد النجار',
      content: 'يمكنني البدء غداً صباحاً. هل المواد جاهزة؟',
      timestamp: '2024-02-15 09:30',
      isMe: false
    },
    {
      id: 4,
      senderId: 'me',
      senderName: 'أنا',
      content: 'نعم، جميع المواد جاهزة. سأكون متواجداً في المنزل من الساعة 8 صباحاً',
      timestamp: '2024-02-15 10:00',
      isMe: true
    },
    {
      id: 5,
      senderId: 1,
      senderName: 'أحمد النجار',
      content: 'ممتاز، سأبدأ العمل غداً صباحاً إن شاء الله',
      timestamp: '2024-02-15 10:30',
      isMe: false
    }
  ];

  const selectedConversation = conversations.find(conv => conv.id === selectedChat);

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (newMessage.trim()) {
      // هنا سيتم إرسال الرسالة إلى الخادم
      console.log('Sending message:', newMessage);
      setNewMessage('');
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('ar-SA', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  return (
    <ProtectedRoute>
      <DashboardLayout 
        title="الرسائل"
        subtitle="تواصل مع العملاء والحرفيين"
      >
        <div className="h-[calc(100vh-200px)] bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
          <div className="flex h-full">
            {/* Conversations List */}
            <div className="w-1/3 border-l border-gray-200 flex flex-col">
              {/* Search */}
              <div className="p-4 border-b border-gray-200">
                <input
                  type="text"
                  placeholder="البحث في المحادثات..."
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                />
              </div>

              {/* Conversations */}
              <div className="flex-1 overflow-y-auto">
                {conversations.map((conversation) => (
                  <div
                    key={conversation.id}
                    onClick={() => setSelectedChat(conversation.id)}
                    className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors duration-200 ${
                      selectedChat === conversation.id ? 'bg-blue-50 border-l-4 border-l-navy' : ''
                    }`}
                  >
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="relative">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold ${
                          conversation.role === 'حرفي' 
                            ? 'bg-gradient-to-br from-navy to-teal' 
                            : 'bg-gradient-to-br from-blue-500 to-blue-600'
                        }`}>
                          {conversation.avatar}
                        </div>
                        {conversation.online && (
                          <div className="absolute bottom-0 left-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                        )}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <h3 className="font-medium text-gray-900 truncate">{conversation.name}</h3>
                          <span className="text-xs text-gray-500">{conversation.lastMessageTime}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <p className="text-sm text-gray-600 truncate">{conversation.lastMessage}</p>
                          {conversation.unreadCount > 0 && (
                            <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                              {conversation.unreadCount}
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{conversation.projectTitle}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Chat Area */}
            <div className="flex-1 flex flex-col">
              {selectedConversation ? (
                <>
                  {/* Chat Header */}
                  <div className="p-4 border-b border-gray-200 bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="relative">
                          <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-bold ${
                            selectedConversation.role === 'حرفي' 
                              ? 'bg-gradient-to-br from-navy to-teal' 
                              : 'bg-gradient-to-br from-blue-500 to-blue-600'
                          }`}>
                            {selectedConversation.avatar}
                          </div>
                          {selectedConversation.online && (
                            <div className="absolute bottom-0 left-0 w-3 h-3 bg-green-500 border-2 border-white rounded-full"></div>
                          )}
                        </div>
                        <div>
                          <h3 className="font-medium text-gray-900">{selectedConversation.name}</h3>
                          <p className="text-sm text-gray-600">{selectedConversation.projectTitle}</p>
                        </div>
                      </div>
                      
                      <div className="flex space-x-2 space-x-reverse">
                        <Button size="sm" variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                          📞 اتصال
                        </Button>
                        <Button size="sm" variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-100">
                          ⚙️ خيارات
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Messages */}
                  <div className="flex-1 overflow-y-auto p-4 space-y-4">
                    {messages.map((message) => (
                      <div
                        key={message.id}
                        className={`flex ${message.isMe ? 'justify-end' : 'justify-start'}`}
                      >
                        <div className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                          message.isMe
                            ? 'bg-navy text-white'
                            : 'bg-gray-100 text-gray-900'
                        }`}>
                          <p className="text-sm">{message.content}</p>
                          <p className={`text-xs mt-1 ${
                            message.isMe ? 'text-blue-200' : 'text-gray-500'
                          }`}>
                            {formatTime(message.timestamp)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Message Input */}
                  <div className="p-4 border-t border-gray-200">
                    <form onSubmit={handleSendMessage} className="flex space-x-4 space-x-reverse">
                      <input
                        type="text"
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        placeholder="اكتب رسالتك..."
                        className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                      />
                      <Button type="submit" className="bg-gradient-to-r from-navy to-teal">
                        إرسال
                      </Button>
                    </form>
                  </div>
                </>
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-6xl mb-4">💬</div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2">اختر محادثة</h3>
                    <p className="text-gray-600">اختر محادثة من القائمة لبدء التواصل</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">إجمالي المحادثات</p>
                <p className="text-2xl font-bold text-gray-900">{conversations.length}</p>
              </div>
              <div className="text-2xl text-blue-600">💬</div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">رسائل غير مقروءة</p>
                <p className="text-2xl font-bold text-gray-900">
                  {conversations.reduce((sum, conv) => sum + conv.unreadCount, 0)}
                </p>
              </div>
              <div className="text-2xl text-red-600">🔴</div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">متصل الآن</p>
                <p className="text-2xl font-bold text-gray-900">
                  {conversations.filter(conv => conv.online).length}
                </p>
              </div>
              <div className="text-2xl text-green-600">🟢</div>
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">متوسط الرد</p>
                <p className="text-2xl font-bold text-gray-900">15 دقيقة</p>
              </div>
              <div className="text-2xl text-purple-600">⏱️</div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default MessagesPage;
