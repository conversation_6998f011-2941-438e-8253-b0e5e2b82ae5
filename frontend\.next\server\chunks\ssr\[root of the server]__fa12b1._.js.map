{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useState } from 'react';\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  role: 'client' | 'craftsman';\n  phone?: string;\n}\n\nexport const useAuth = () => {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const login = async (credentials: LoginCredentials) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await signIn('credentials', {\n        email: credentials.email,\n        password: credentials.password,\n        redirect: false,\n      });\n\n      if (result?.error) {\n        setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');\n        return false;\n      }\n\n      if (result?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        setError(errorData.message || 'حدث خطأ أثناء إنشاء الحساب');\n        return false;\n      }\n\n      // تسجيل الدخول تلقائياً بعد التسجيل\n      const loginResult = await signIn('credentials', {\n        email: data.email,\n        password: data.password,\n        redirect: false,\n      });\n\n      if (loginResult?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء إنشاء الحساب');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    setIsLoading(true);\n    try {\n      await signOut({ redirect: false });\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithGoogle = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('google', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Google');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithFacebook = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('facebook', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Facebook');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const isAuthenticated = status === 'authenticated';\n  const isLoading_session = status === 'loading';\n  const user = session?.user;\n\n  // التحقق من الأدوار مع fallback\n  const isClient = user?.role === 'client';\n  const isCraftsman = user?.role === 'craftsman';\n  const isAdmin = user?.role === 'admin';\n\n  // التحقق من الصلاحيات\n  const hasRole = (role: string | string[]) => {\n    if (!user) return false;\n    if (Array.isArray(role)) {\n      return role.includes(user.role);\n    }\n    return user.role === role;\n  };\n\n  const canAccess = (requiredRoles: string | string[]) => {\n    return isAuthenticated && hasRole(requiredRoles);\n  };\n\n  return {\n    // Session data\n    user,\n    session,\n    isAuthenticated,\n    isLoading: isLoading || isLoading_session,\n    error,\n\n    // Role checks\n    isClient,\n    isCraftsman,\n    isAdmin,\n    hasRole,\n    canAccess,\n\n    // Auth methods\n    login,\n    register,\n    logout,\n    loginWithGoogle,\n    loginWithFacebook,\n\n    // Utility\n    setError,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;AAmBO,MAAM,UAAU;IACrB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,QAAQ,OAAO;QACnB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;gBAC9B,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;gBACT,OAAO;YACT;YAEA,IAAI,QAAQ,IAAI;gBACd,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,OAAO,IAAI;gBAC9B,OAAO;YACT;YAEA,oCAAoC;YACpC,MAAM,cAAc,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBAC9C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;YACZ;YAEA,IAAI,aAAa,IAAI;gBACnB,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;gBAAE,UAAU;YAAM;YAChC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBAAE,aAAa;YAAa;QACrD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,YAAY;gBAAE,aAAa;YAAa;QACvD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,WAAW;IACnC,MAAM,oBAAoB,WAAW;IACrC,MAAM,OAAO,SAAS;IAEtB,gCAAgC;IAChC,MAAM,WAAW,MAAM,SAAS;IAChC,MAAM,cAAc,MAAM,SAAS;IACnC,MAAM,UAAU,MAAM,SAAS;IAE/B,sBAAsB;IACtB,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI;QAChC;QACA,OAAO,KAAK,IAAI,KAAK;IACvB;IAEA,MAAM,YAAY,CAAC;QACjB,OAAO,mBAAmB,QAAQ;IACpC;IAEA,OAAO;QACL,eAAe;QACf;QACA;QACA;QACA,WAAW,aAAa;QACxB;QAEA,cAAc;QACd;QACA;QACA;QACA;QACA;QAEA,eAAe;QACf;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;IACF;AACF"}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/auth/AuthButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/hooks/useAuth';\nimport { Button } from '@/components/ui/Button';\nimport Link from 'next/link';\nimport { useState } from 'react';\n\nexport default function AuthButton() {\n  const { isAuthenticated, user, logout, isLoading } = useAuth();\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse\">\n        <div className=\"h-10 w-24 bg-gray-200 rounded\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"flex items-center space-x-3 space-x-reverse\">\n        <Link href=\"/login\">\n          <Button variant=\"outline\" size=\"sm\" className=\"border-navy text-navy hover:bg-navy hover:text-white\">\n            تسجيل الدخول\n          </Button>\n        </Link>\n        <Link href=\"/register\">\n          <Button size=\"sm\" className=\"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90\">\n            إنشاء حساب\n          </Button>\n        </Link>\n      </div>\n    );\n  }\n\n  const getRoleDisplayName = (role: string) => {\n    switch (role) {\n      case 'client':\n        return 'عميل';\n      case 'craftsman':\n        return 'حرفي';\n      case 'admin':\n        return 'مدير';\n      default:\n        return 'مستخدم';\n    }\n  };\n\n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'client':\n        return '👤';\n      case 'craftsman':\n        return '👨‍🔧';\n      case 'admin':\n        return '👑';\n      default:\n        return '👤';\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className=\"flex items-center space-x-2 space-x-reverse bg-white border border-gray-200 rounded-lg px-4 py-2 hover:bg-gray-50 transition-colors duration-200\"\n      >\n        <div className=\"text-lg\">{getRoleIcon(user?.role || '')}</div>\n        <div className=\"text-right\">\n          <div className=\"text-sm font-medium text-navy\">{user?.name || 'مستخدم'}</div>\n          <div className=\"text-xs text-gray-500\">{getRoleDisplayName(user?.role || '')}</div>\n        </div>\n        <svg\n          className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${\n            isDropdownOpen ? 'rotate-180' : ''\n          }`}\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n        </svg>\n      </button>\n\n      {isDropdownOpen && (\n        <>\n          {/* Backdrop */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsDropdownOpen(false)}\n          />\n\n          {/* Dropdown Menu */}\n          <div className=\"absolute left-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-20\">\n            <div className=\"p-4 border-b border-gray-100\">\n              <div className=\"flex items-center space-x-3 space-x-reverse\">\n                <div className=\"text-2xl\">{getRoleIcon(user?.role || '')}</div>\n                <div>\n                  <div className=\"font-medium text-navy\">{user?.name || 'مستخدم'}</div>\n                  <div className=\"text-sm text-gray-500\">{user?.email || 'بريد إلكتروني'}</div>\n                  <div className=\"text-xs text-teal font-medium\">\n                    {getRoleDisplayName(user?.role || '')}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"py-2\">\n              <Link\n                href=\"/dashboard\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>📊</span>\n                  <span>لوحة التحكم</span>\n                </div>\n              </Link>\n\n              <Link\n                href=\"/profile\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>⚙️</span>\n                  <span>الملف الشخصي</span>\n                </div>\n              </Link>\n\n              {user?.role === 'client' && (\n                <Link\n                  href=\"/projects/create\"\n                  className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                  onClick={() => setIsDropdownOpen(false)}\n                >\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span>➕</span>\n                    <span>إنشاء مشروع</span>\n                  </div>\n                </Link>\n              )}\n\n              {user?.role === 'craftsman' && (\n                <Link\n                  href=\"/offers\"\n                  className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                  onClick={() => setIsDropdownOpen(false)}\n                >\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span>💼</span>\n                    <span>عروضي</span>\n                  </div>\n                </Link>\n              )}\n\n              <Link\n                href=\"/messages\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>💬</span>\n                  <span>الرسائل</span>\n                </div>\n              </Link>\n            </div>\n\n            <div className=\"border-t border-gray-100 py-2\">\n              <button\n                onClick={() => {\n                  logout();\n                  setIsDropdownOpen(false);\n                }}\n                className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-red-50 transition-colors duration-200\"\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>🚪</span>\n                  <span>تسجيل الخروج</span>\n                </div>\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,WAAU;kCAAuD;;;;;;;;;;;8BAIvG,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,WAAU;kCAAyE;;;;;;;;;;;;;;;;;IAM7G;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCAAW,YAAY,MAAM,QAAQ;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAiC,MAAM,QAAQ;;;;;;0CAC9D,8OAAC;gCAAI,WAAU;0CAAyB,mBAAmB,MAAM,QAAQ;;;;;;;;;;;;kCAE3E,8OAAC;wBACC,WAAW,CAAC,wDAAwD,EAClE,iBAAiB,eAAe,IAChC;wBACF,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAIxE,gCACC;;kCAEE,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,kBAAkB;;;;;;kCAInC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAY,YAAY,MAAM,QAAQ;;;;;;sDACrD,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAyB,MAAM,QAAQ;;;;;;8DACtD,8OAAC;oDAAI,WAAU;8DAAyB,MAAM,SAAS;;;;;;8DACvD,8OAAC;oDAAI,WAAU;8DACZ,mBAAmB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAM1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAIV,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;oCAIT,MAAM,SAAS,0BACd,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;oCAKX,MAAM,SAAS,6BACd,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAKZ,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;0CAKZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;wCACP;wCACA,kBAAkB;oCACpB;oCACA,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB"}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useSession } from 'next-auth/react';\nimport { Button } from '../ui/Button';\nimport AuthButton from '../auth/AuthButton';\nimport NotificationBell from '../notifications/NotificationBell';\n\nconst Header = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const { data: session } = useSession();\n\n  // بيانات تجريبية للإشعارات\n  const mockNotifications = [\n    {\n      id: '1',\n      title: 'عرض جديد',\n      message: 'تم تقديم عرض جديد على مشروع تجديد المطبخ',\n      type: 'offer' as const,\n      read: false,\n      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n      actionUrl: '/client/offers'\n    },\n    {\n      id: '2',\n      title: 'رسالة جديدة',\n      message: 'رسالة جديدة من محمد النجار',\n      type: 'message' as const,\n      read: false,\n      timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),\n      actionUrl: '/messages'\n    },\n    {\n      id: '3',\n      title: 'تقييم جديد',\n      message: 'تم تقييم عملك بـ 5 نجوم',\n      type: 'review' as const,\n      read: true,\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      actionUrl: '/craftsman/reviews'\n    }\n  ];\n\n  const handleNotificationClick = (notification: any) => {\n    if (notification.actionUrl) {\n      window.location.href = notification.actionUrl;\n    }\n  };\n\n  const handleMarkAsRead = (notificationId: string) => {\n    // TODO: تنفيذ تحديث حالة الإشعار في قاعدة البيانات\n    console.log('Mark as read:', notificationId);\n  };\n\n  const handleMarkAllAsRead = () => {\n    // TODO: تنفيذ تحديد جميع الإشعارات كمقروءة\n    console.log('Mark all as read');\n  };\n\n  return (\n    <header className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex justify-between items-center\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"group flex items-center space-x-3 space-x-reverse\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-navy to-teal\">\n                دوزان\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:block\">\n            <ul className=\"flex space-x-8 space-x-reverse\">\n              <li>\n                <Link href=\"/\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الرئيسية\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  المشاريع\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الحرفيون\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  من نحن\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الأسئلة الشائعة\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  اتصل بنا\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n            </ul>\n          </nav>\n\n          {/* Desktop Auth */}\n          <div className=\"hidden lg:flex items-center\">\n            <AuthButton />\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300\"\n            aria-label=\"فتح القائمة\"\n          >\n            <svg className=\"w-6 h-6 text-navy\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              {isMobileMenuOpen ? (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              ) : (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden mt-4 py-4 border-t border-gray-200\">\n            <nav className=\"space-y-4\">\n              <Link href=\"/\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الرئيسية\n              </Link>\n              <Link href=\"/jobs\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                المشاريع\n              </Link>\n              <Link href=\"/craftsmen\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الحرفيون\n              </Link>\n              <Link href=\"/about\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                من نحن\n              </Link>\n              <Link href=\"/faq\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الأسئلة الشائعة\n              </Link>\n              <Link href=\"/contact\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                اتصل بنا\n              </Link>\n            </nav>\n            <div className=\"mt-6\">\n              <AuthButton />\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AASA,MAAM,SAAS;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAEnC,2BAA2B;IAC3B,MAAM,oBAAoB;QACxB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC5D,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC5D,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YAChE,WAAW;QACb;KACD;IAED,MAAM,0BAA0B,CAAC;QAC/B,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,QAAQ,CAAC,IAAI,GAAG,aAAa,SAAS;QAC/C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,mDAAmD;QACnD,QAAQ,GAAG,CAAC,iBAAiB;IAC/B;IAEA,MAAM,sBAAsB;QAC1B,2CAA2C;QAC3C,QAAQ,GAAG,CAAC;IACd;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDAA2L;;;;;;kDAG1M,8OAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;;;;;;sCAO1G,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAA0F;8DAEjH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;gDAA0F;8DAErH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;;gDAA0F;8DAE1H,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;;gDAA0F;8DAEtH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAO,WAAU;;gDAA0F;8DAEpH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;gDAA0F;8DAExH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wIAAA,CAAA,UAAU;;;;;;;;;;sCAIb,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAoB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC1E,iCACC,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;yDAErE,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAO5E,kCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAiF;;;;;;8CAG1G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAiF;;;;;;8CAG9G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAiF;;;;;;8CAGnH,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiF;;;;;;8CAG/G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAO,WAAU;8CAAiF;;;;;;8CAG7G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiF;;;;;;;;;;;;sCAInH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wIAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB;uCAEe"}}, {"offset": {"line": 1070, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden\">\n      {/* خلفية هندسية متناسقة */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl\"></div>\n      </div>\n\n      {/* شبكة نقطية ناعمة */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',\n          backgroundSize: '30px 30px'\n        }}></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-16 relative z-10\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Logo and Description */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n              <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center text-white text-2xl font-bold shadow-lg\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-white\">\n                دوزان\n              </span>\n            </div>\n            <p className=\"text-white/90 leading-relaxed mb-6\">\n              منصة تربط بين أصحاب المشاريع والحرفيين المهرة لتنفيذ المشاريع بكفاءة وجودة عالية في جميع أنحاء سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              روابط سريعة\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الرئيسية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  المشاريع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الحرفيون\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  من نحن\n                </Link>\n              </li>\n            </ul>\n          </div>\n          {/* Support */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              الدعم\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/contact\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  اتصل بنا\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  شروط الاستخدام\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              تواصل معنا\n            </h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">البريد الإلكتروني</p>\n                  <a href=\"mailto:<EMAIL>\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">الهاتف</p>\n                  <a href=\"tel:+963112345678\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    +963 11 234 5678\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">العنوان</p>\n                  <p className=\"text-white font-medium\">دمشق، سوريا</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"mt-12 pt-8 border-t border-white/20\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-white/80 text-sm\">\n                &copy; {new Date().getFullYear()} دوزان. جميع الحقوق محفوظة.\n              </p>\n              <p className=\"text-white/60 text-xs mt-1\">\n                صُنع بـ ❤️ في سوريا\n              </p>\n            </div>\n\n            <div className=\"flex items-center space-x-6 space-x-reverse\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">2500+</div>\n                <div className=\"text-white/70 text-xs\">حرفي</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">15000+</div>\n                <div className=\"text-white/70 text-xs\">مشروع</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">98%</div>\n                <div className=\"text-white/70 text-xs\">رضا</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6H;;;;;;0DAG5I,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAIlD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAyQ,UAAS;;;;;;;;;;;;;;;;0DAGjT,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA8jD,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO1mD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;;sEACvB,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;;sEAC3B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;;sEAChC,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAO3H,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAO,WAAU;;sEAC1B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3H,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,MAAK;gEAAuB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAM3H,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,MAAK;gEAAoB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAMxH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EACjE,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;8EACrE,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;;gDAAwB;gDAC3B,IAAI,OAAO,WAAW;gDAAG;;;;;;;sDAEnC,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;uCAEe"}}, {"offset": {"line": 1927, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1933, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout = ({ children }: MainLayoutProps) => {\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      <Header />\n      <main className=\"flex-grow\">{children}</main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB;IAC/C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;0BAAa;;;;;;0BAC7B,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe"}}, {"offset": {"line": 1973, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1979, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-white shadow-sm transition-all duration-200 hover:shadow-md',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('font-semibold leading-none tracking-tight text-navy', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-600', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,qBAAO,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACpE,GAAG,KAAK;kBAER;;;;;;AAGL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;AAGL,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;AAIP,WAAW,WAAW,GAAG"}}, {"offset": {"line": 2061, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2067, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/StarRating.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\n\ninterface StarRatingProps {\n  rating: number;\n  onRatingChange?: (rating: number) => void;\n  readonly?: boolean;\n  size?: 'sm' | 'md' | 'lg';\n  showValue?: boolean;\n  className?: string;\n}\n\nconst StarRating: React.FC<StarRatingProps> = ({\n  rating,\n  onRatingChange,\n  readonly = false,\n  size = 'md',\n  showValue = false,\n  className = ''\n}) => {\n  const [hoverRating, setHoverRating] = useState(0);\n\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-5 h-5',\n    lg: 'w-6 h-6'\n  };\n\n  const handleStarClick = (starRating: number) => {\n    if (!readonly && onRatingChange) {\n      onRatingChange(starRating);\n    }\n  };\n\n  const handleStarHover = (starRating: number) => {\n    if (!readonly) {\n      setHoverRating(starRating);\n    }\n  };\n\n  const handleMouseLeave = () => {\n    if (!readonly) {\n      setHoverRating(0);\n    }\n  };\n\n  const displayRating = hoverRating || rating;\n\n  return (\n    <div className={`flex items-center space-x-1 space-x-reverse ${className}`}>\n      <div className=\"flex space-x-1 space-x-reverse\" onMouseLeave={handleMouseLeave}>\n        {[1, 2, 3, 4, 5].map((star) => (\n          <button\n            key={star}\n            type=\"button\"\n            className={`${sizeClasses[size]} ${\n              readonly ? 'cursor-default' : 'cursor-pointer hover:scale-110'\n            } transition-transform`}\n            onClick={() => handleStarClick(star)}\n            onMouseEnter={() => handleStarHover(star)}\n            disabled={readonly}\n          >\n            <svg\n              className={`w-full h-full ${\n                star <= displayRating\n                  ? 'text-yellow-400 fill-current'\n                  : 'text-gray-300 fill-current'\n              }`}\n              viewBox=\"0 0 20 20\"\n            >\n              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n            </svg>\n          </button>\n        ))}\n      </div>\n      \n      {showValue && (\n        <span className=\"text-sm text-gray-600 mr-2\">\n          {rating.toFixed(1)} من 5\n        </span>\n      )}\n    </div>\n  );\n};\n\nexport default StarRating;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA,MAAM,aAAwC,CAAC,EAC7C,MAAM,EACN,cAAc,EACd,WAAW,KAAK,EAChB,OAAO,IAAI,EACX,YAAY,KAAK,EACjB,YAAY,EAAE,EACf;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,YAAY,gBAAgB;YAC/B,eAAe;QACjB;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,UAAU;YACb,eAAe;QACjB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;YACb,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB,eAAe;IAErC,qBACE,8OAAC;QAAI,WAAW,CAAC,4CAA4C,EAAE,WAAW;;0BACxE,8OAAC;gBAAI,WAAU;gBAAiC,cAAc;0BAC3D;oBAAC;oBAAG;oBAAG;oBAAG;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAC,qBACpB,8OAAC;wBAEC,MAAK;wBACL,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,EAC/B,WAAW,mBAAmB,iCAC/B,qBAAqB,CAAC;wBACvB,SAAS,IAAM,gBAAgB;wBAC/B,cAAc,IAAM,gBAAgB;wBACpC,UAAU;kCAEV,cAAA,8OAAC;4BACC,WAAW,CAAC,cAAc,EACxB,QAAQ,gBACJ,iCACA,8BACJ;4BACF,SAAQ;sCAER,cAAA,8OAAC;gCAAK,GAAE;;;;;;;;;;;uBAjBL;;;;;;;;;;YAuBV,2BACC,8OAAC;gBAAK,WAAU;;oBACb,OAAO,OAAO,CAAC;oBAAG;;;;;;;;;;;;;AAK7B;uCAEe"}}, {"offset": {"line": 2160, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2166, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/ReviewCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { Card, CardContent } from './Card';\nimport StarRating from './StarRating';\n\ninterface Review {\n  id: string;\n  rating: number;\n  comment: string;\n  reviewerName: string;\n  reviewerAvatar?: string;\n  projectTitle?: string;\n  createdAt: string;\n  helpful?: number;\n}\n\ninterface ReviewCardProps {\n  review: Review;\n  showProject?: boolean;\n  className?: string;\n}\n\nconst ReviewCard: React.FC<ReviewCardProps> = ({\n  review,\n  showProject = false,\n  className = ''\n}) => {\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString('ar-SA', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  return (\n    <Card className={`border-0 bg-white/90 backdrop-blur-md shadow-lg ${className}`}>\n      <CardContent className=\"p-6\">\n        <div className=\"flex items-start space-x-4 space-x-reverse\">\n          {/* Avatar */}\n          <div className=\"flex-shrink-0\">\n            {review.reviewerAvatar ? (\n              <img\n                src={review.reviewerAvatar}\n                alt={review.reviewerName}\n                className=\"w-12 h-12 rounded-full object-cover\"\n              />\n            ) : (\n              <div className=\"w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center\">\n                <span className=\"text-gray-600 font-medium\">\n                  {review.reviewerName.charAt(0)}\n                </span>\n              </div>\n            )}\n          </div>\n\n          {/* Content */}\n          <div className=\"flex-1\">\n            <div className=\"flex items-center justify-between mb-2\">\n              <div>\n                <h4 className=\"font-semibold text-navy\">{review.reviewerName}</h4>\n                {showProject && review.projectTitle && (\n                  <p className=\"text-sm text-gray-600\">مشروع: {review.projectTitle}</p>\n                )}\n              </div>\n              <div className=\"text-left\">\n                <StarRating rating={review.rating} readonly size=\"sm\" />\n                <p className=\"text-xs text-gray-500 mt-1\">\n                  {formatDate(review.createdAt)}\n                </p>\n              </div>\n            </div>\n\n            <p className=\"text-gray-700 leading-relaxed mb-4\">\n              {review.comment}\n            </p>\n\n            {/* Actions */}\n            <div className=\"flex items-center space-x-4 space-x-reverse text-sm\">\n              <button className=\"flex items-center space-x-1 space-x-reverse text-gray-500 hover:text-gray-700\">\n                <span>👍</span>\n                <span>مفيد ({review.helpful || 0})</span>\n              </button>\n              <button className=\"text-gray-500 hover:text-gray-700\">\n                رد\n              </button>\n              <button className=\"text-gray-500 hover:text-gray-700\">\n                إبلاغ\n              </button>\n            </div>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default ReviewCard;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAuBA,MAAM,aAAwC,CAAC,EAC7C,MAAM,EACN,cAAc,KAAK,EACnB,YAAY,EAAE,EACf;IACC,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,gDAAgD,EAAE,WAAW;kBAC7E,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACZ,OAAO,cAAc,iBACpB,8OAAC;4BACC,KAAK,OAAO,cAAc;4BAC1B,KAAK,OAAO,YAAY;4BACxB,WAAU;;;;;iDAGZ,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CACb,OAAO,YAAY,CAAC,MAAM,CAAC;;;;;;;;;;;;;;;;kCAOpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAA2B,OAAO,YAAY;;;;;;4CAC3D,eAAe,OAAO,YAAY,kBACjC,8OAAC;gDAAE,WAAU;;oDAAwB;oDAAQ,OAAO,YAAY;;;;;;;;;;;;;kDAGpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sIAAA,CAAA,UAAU;gDAAC,QAAQ,OAAO,MAAM;gDAAE,QAAQ;gDAAC,MAAK;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DACV,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;0CAKlC,8OAAC;gCAAE,WAAU;0CACV,OAAO,OAAO;;;;;;0CAIjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAO,WAAU;;0DAChB,8OAAC;0DAAK;;;;;;0DACN,8OAAC;;oDAAK;oDAAO,OAAO,OAAO,IAAI;oDAAE;;;;;;;;;;;;;kDAEnC,8OAAC;wCAAO,WAAU;kDAAoC;;;;;;kDAGtD,8OAAC;wCAAO,WAAU;kDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;uCAEe"}}, {"offset": {"line": 2370, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2376, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/AddReview.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from './Card';\nimport { Button } from './Button';\nimport StarRating from './StarRating';\n\ninterface AddReviewProps {\n  onSubmit: (review: { rating: number; comment: string }) => void;\n  loading?: boolean;\n  className?: string;\n}\n\nconst AddReview: React.FC<AddReviewProps> = ({\n  onSubmit,\n  loading = false,\n  className = ''\n}) => {\n  const [rating, setRating] = useState(0);\n  const [comment, setComment] = useState('');\n  const [errors, setErrors] = useState<Record<string, string>>({});\n\n  const validateForm = () => {\n    const newErrors: Record<string, string> = {};\n\n    if (rating === 0) {\n      newErrors.rating = 'يرجى اختيار تقييم';\n    }\n\n    if (!comment.trim()) {\n      newErrors.comment = 'يرجى كتابة تعليق';\n    } else if (comment.trim().length < 10) {\n      newErrors.comment = 'التعليق يجب أن يكون 10 أحرف على الأقل';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    onSubmit({\n      rating,\n      comment: comment.trim()\n    });\n\n    // إعادة تعيين النموذج\n    setRating(0);\n    setComment('');\n    setErrors({});\n  };\n\n  const handleRatingChange = (newRating: number) => {\n    setRating(newRating);\n    if (errors.rating) {\n      setErrors(prev => ({ ...prev, rating: '' }));\n    }\n  };\n\n  const handleCommentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {\n    setComment(e.target.value);\n    if (errors.comment) {\n      setErrors(prev => ({ ...prev, comment: '' }));\n    }\n  };\n\n  return (\n    <Card className={`border-0 bg-white/90 backdrop-blur-md shadow-lg ${className}`}>\n      <CardHeader>\n        <CardTitle className=\"text-lg\">إضافة تقييم</CardTitle>\n      </CardHeader>\n      <CardContent>\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {/* Rating */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-3\">\n              التقييم *\n            </label>\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              <StarRating\n                rating={rating}\n                onRatingChange={handleRatingChange}\n                size=\"lg\"\n              />\n              <span className=\"text-sm text-gray-600\">\n                {rating === 0 ? 'اختر تقييمك' : \n                 rating === 1 ? 'سيء جداً' :\n                 rating === 2 ? 'سيء' :\n                 rating === 3 ? 'متوسط' :\n                 rating === 4 ? 'جيد' : 'ممتاز'}\n              </span>\n            </div>\n            {errors.rating && (\n              <p className=\"text-red-500 text-xs mt-1\">{errors.rating}</p>\n            )}\n          </div>\n\n          {/* Comment */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              التعليق *\n            </label>\n            <textarea\n              value={comment}\n              onChange={handleCommentChange}\n              rows={4}\n              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal resize-none ${\n                errors.comment ? 'border-red-500' : 'border-gray-300'\n              }`}\n              placeholder=\"شارك تجربتك مع هذا الحرفي... ما الذي أعجبك؟ ما الذي يمكن تحسينه؟\"\n            />\n            <div className=\"flex justify-between items-center mt-1\">\n              {errors.comment ? (\n                <p className=\"text-red-500 text-xs\">{errors.comment}</p>\n              ) : (\n                <p className=\"text-gray-500 text-xs\">\n                  اكتب تعليقاً مفيداً للمستخدمين الآخرين\n                </p>\n              )}\n              <span className=\"text-xs text-gray-400\">\n                {comment.length}/500\n              </span>\n            </div>\n          </div>\n\n          {/* Guidelines */}\n          <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n            <h4 className=\"font-medium text-navy mb-2\">💡 نصائح لكتابة تقييم مفيد:</h4>\n            <ul className=\"text-sm text-gray-700 space-y-1\">\n              <li>• اذكر تفاصيل محددة عن جودة العمل</li>\n              <li>• تحدث عن الالتزام بالمواعيد والتواصل</li>\n              <li>• اذكر ما إذا كان السعر مناسباً للجودة</li>\n              <li>• كن صادقاً وموضوعياً في تقييمك</li>\n            </ul>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex space-x-3 space-x-reverse\">\n            <Button\n              type=\"submit\"\n              disabled={loading}\n              className=\"bg-gradient-to-r from-navy to-teal text-white px-6 py-2\"\n            >\n              {loading ? 'جاري النشر...' : 'نشر التقييم'}\n            </Button>\n            \n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => {\n                setRating(0);\n                setComment('');\n                setErrors({});\n              }}\n              disabled={loading}\n            >\n              إلغاء\n            </Button>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default AddReview;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaA,MAAM,YAAsC,CAAC,EAC3C,QAAQ,EACR,UAAU,KAAK,EACf,YAAY,EAAE,EACf;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAE9D,MAAM,eAAe;QACnB,MAAM,YAAoC,CAAC;QAE3C,IAAI,WAAW,GAAG;YAChB,UAAU,MAAM,GAAG;QACrB;QAEA,IAAI,CAAC,QAAQ,IAAI,IAAI;YACnB,UAAU,OAAO,GAAG;QACtB,OAAO,IAAI,QAAQ,IAAI,GAAG,MAAM,GAAG,IAAI;YACrC,UAAU,OAAO,GAAG;QACtB;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,SAAS;YACP;YACA,SAAS,QAAQ,IAAI;QACvB;QAEA,sBAAsB;QACtB,UAAU;QACV,WAAW;QACX,UAAU,CAAC;IACb;IAEA,MAAM,qBAAqB,CAAC;QAC1B,UAAU;QACV,IAAI,OAAO,MAAM,EAAE;YACjB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,QAAQ;gBAAG,CAAC;QAC5C;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,WAAW,EAAE,MAAM,CAAC,KAAK;QACzB,IAAI,OAAO,OAAO,EAAE;YAClB,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,SAAS;gBAAG,CAAC;QAC7C;IACF;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,gDAAgD,EAAE,WAAW;;0BAC7E,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;8BAAU;;;;;;;;;;;0BAEjC,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCAEtC,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sIAAA,CAAA,UAAU;4CACT,QAAQ;4CACR,gBAAgB;4CAChB,MAAK;;;;;;sDAEP,8OAAC;4CAAK,WAAU;sDACb,WAAW,IAAI,gBACf,WAAW,IAAI,aACf,WAAW,IAAI,QACf,WAAW,IAAI,UACf,WAAW,IAAI,QAAQ;;;;;;;;;;;;gCAG3B,OAAO,MAAM,kBACZ,8OAAC;oCAAE,WAAU;8CAA6B,OAAO,MAAM;;;;;;;;;;;;sCAK3D,8OAAC;;8CACC,8OAAC;oCAAM,WAAU;8CAA+C;;;;;;8CAGhE,8OAAC;oCACC,OAAO;oCACP,UAAU;oCACV,MAAM;oCACN,WAAW,CAAC,+FAA+F,EACzG,OAAO,OAAO,GAAG,mBAAmB,mBACpC;oCACF,aAAY;;;;;;8CAEd,8OAAC;oCAAI,WAAU;;wCACZ,OAAO,OAAO,iBACb,8OAAC;4CAAE,WAAU;sDAAwB,OAAO,OAAO;;;;;iEAEnD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;sDAIvC,8OAAC;4CAAK,WAAU;;gDACb,QAAQ,MAAM;gDAAC;;;;;;;;;;;;;;;;;;;sCAMtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAKR,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,UAAU,kBAAkB;;;;;;8CAG/B,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;wCACP,UAAU;wCACV,WAAW;wCACX,UAAU,CAAC;oCACb;oCACA,UAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;uCAEe"}}, {"offset": {"line": 2680, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2686, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/test-reviews/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport StarRating from '@/components/ui/StarRating';\nimport ReviewCard from '@/components/ui/ReviewCard';\nimport AddReview from '@/components/ui/AddReview';\n\nconst TestReviewsPage = () => {\n  const [reviews, setReviews] = useState([\n    {\n      id: '1',\n      rating: 5,\n      comment: 'عمل ممتاز ودقيق. الحرفي محترف جداً والتزم بالمواعيد المحددة. النتيجة فاقت توقعاتي بكثير. أنصح بالتعامل معه.',\n      reviewerName: 'أحمد محمد',\n      projectTitle: 'تجديد المطبخ',\n      createdAt: '2024-02-01',\n      helpful: 12\n    },\n    {\n      id: '2',\n      rating: 4,\n      comment: 'عمل جيد بشكل عام. هناك بعض التفاصيل الصغيرة التي كان يمكن تحسينها، لكن النتيجة النهائية مرضية.',\n      reviewerName: 'فاطمة أحمد',\n      projectTitle: 'إصلاح السباكة',\n      createdAt: '2024-01-28',\n      helpful: 8\n    },\n    {\n      id: '3',\n      rating: 3,\n      comment: 'العمل متوسط. تأخر قليلاً عن الموعد المحدد ولكن النتيجة مقبولة. السعر مناسب.',\n      reviewerName: 'محمد علي',\n      projectTitle: 'دهان الشقة',\n      createdAt: '2024-01-25',\n      helpful: 3\n    }\n  ]);\n\n  const [testRating, setTestRating] = useState(0);\n\n  const handleAddReview = (newReview: { rating: number; comment: string }) => {\n    const review = {\n      id: Date.now().toString(),\n      rating: newReview.rating,\n      comment: newReview.comment,\n      reviewerName: 'مستخدم تجريبي',\n      projectTitle: 'مشروع تجريبي',\n      createdAt: new Date().toISOString(),\n      helpful: 0\n    };\n\n    setReviews(prev => [review, ...prev]);\n    alert('تم إضافة التقييم بنجاح!');\n  };\n\n  const averageRating = reviews.length > 0 \n    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length \n    : 0;\n\n  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({\n    rating,\n    count: reviews.filter(r => r.rating === rating).length,\n    percentage: reviews.length > 0 \n      ? (reviews.filter(r => r.rating === rating).length / reviews.length) * 100 \n      : 0\n  }));\n\n  return (\n    <MainLayout>\n      <div className=\"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            {/* Header */}\n            <div className=\"mb-8\">\n              <h1 className=\"text-3xl font-bold text-navy mb-4\">اختبار نظام التقييمات</h1>\n              <p className=\"text-gray-600\">اختبر مكونات التقييمات والتعليقات</p>\n            </div>\n\n            <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n              {/* Left Column - Components Test */}\n              <div className=\"lg:col-span-1 space-y-6\">\n                {/* Star Rating Test */}\n                <Card>\n                  <CardHeader>\n                    <CardTitle>اختبار تقييم النجوم</CardTitle>\n                  </CardHeader>\n                  <CardContent className=\"space-y-4\">\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-700 mb-2\">تفاعلي:</p>\n                      <StarRating\n                        rating={testRating}\n                        onRatingChange={setTestRating}\n                        size=\"lg\"\n                        showValue\n                      />\n                    </div>\n\n                    <div>\n                      <p className=\"text-sm font-medium text-gray-700 mb-2\">للقراءة فقط - أحجام مختلفة:</p>\n                      <div className=\"space-y-2\">\n                        <div className=\"flex items-center space-x-2 space-x-reverse\">\n                          <StarRating rating={4.5} readonly size=\"sm\" />\n                          <span className=\"text-sm\">صغير</span>\n                        </div>\n                        <div className=\"flex items-center space-x-2 space-x-reverse\">\n                          <StarRating rating={4.5} readonly size=\"md\" />\n                          <span className=\"text-sm\">متوسط</span>\n                        </div>\n                        <div className=\"flex items-center space-x-2 space-x-reverse\">\n                          <StarRating rating={4.5} readonly size=\"lg\" showValue />\n                          <span className=\"text-sm\">كبير</span>\n                        </div>\n                      </div>\n                    </div>\n                  </CardContent>\n                </Card>\n\n                {/* Rating Summary */}\n                <Card>\n                  <CardHeader>\n                    <CardTitle>ملخص التقييمات</CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"text-center mb-6\">\n                      <div className=\"text-4xl font-bold text-navy mb-2\">\n                        {averageRating.toFixed(1)}\n                      </div>\n                      <StarRating rating={averageRating} readonly size=\"lg\" />\n                      <p className=\"text-gray-600 mt-2\">\n                        {reviews.length} تقييم\n                      </p>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      {ratingDistribution.map(({ rating, count, percentage }) => (\n                        <div key={rating} className=\"flex items-center space-x-2 space-x-reverse\">\n                          <span className=\"text-sm w-8\">{rating} ⭐</span>\n                          <div className=\"flex-1 bg-gray-200 rounded-full h-2\">\n                            <div\n                              className=\"bg-yellow-400 h-2 rounded-full\"\n                              style={{ width: `${percentage}%` }}\n                            />\n                          </div>\n                          <span className=\"text-sm text-gray-600 w-8\">{count}</span>\n                        </div>\n                      ))}\n                    </div>\n                  </CardContent>\n                </Card>\n              </div>\n\n              {/* Right Column - Reviews */}\n              <div className=\"lg:col-span-2 space-y-6\">\n                {/* Add Review */}\n                <AddReview onSubmit={handleAddReview} />\n\n                {/* Reviews List */}\n                <div>\n                  <h2 className=\"text-2xl font-bold text-navy mb-6\">\n                    التقييمات ({reviews.length})\n                  </h2>\n                  \n                  <div className=\"space-y-4\">\n                    {reviews.map((review) => (\n                      <ReviewCard\n                        key={review.id}\n                        review={review}\n                        showProject={true}\n                      />\n                    ))}\n                  </div>\n\n                  {reviews.length === 0 && (\n                    <Card>\n                      <CardContent className=\"p-12 text-center\">\n                        <div className=\"text-6xl mb-4\">⭐</div>\n                        <h3 className=\"text-xl font-semibold text-navy mb-2\">\n                          لا توجد تقييمات بعد\n                        </h3>\n                        <p className=\"text-gray-600\">\n                          كن أول من يقيم هذا الحرفي\n                        </p>\n                      </CardContent>\n                    </Card>\n                  )}\n                </div>\n              </div>\n            </div>\n\n            {/* Usage Examples */}\n            <div className=\"mt-12\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>أمثلة الاستخدام</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <div className=\"text-center p-6 bg-gray-50 rounded-lg\">\n                      <div className=\"text-3xl mb-3\">👨‍🔧</div>\n                      <h3 className=\"font-semibold text-navy mb-2\">تقييم الحرفيين</h3>\n                      <p className=\"text-sm text-gray-600\">\n                        تقييم الحرفيين بعد إنجاز المشاريع\n                      </p>\n                    </div>\n\n                    <div className=\"text-center p-6 bg-gray-50 rounded-lg\">\n                      <div className=\"text-3xl mb-3\">👤</div>\n                      <h3 className=\"font-semibold text-navy mb-2\">تقييم العملاء</h3>\n                      <p className=\"text-sm text-gray-600\">\n                        تقييم العملاء من قبل الحرفيين\n                      </p>\n                    </div>\n\n                    <div className=\"text-center p-6 bg-gray-50 rounded-lg\">\n                      <div className=\"text-3xl mb-3\">🏗️</div>\n                      <h3 className=\"font-semibold text-navy mb-2\">تقييم المشاريع</h3>\n                      <p className=\"text-sm text-gray-600\">\n                        تقييم جودة المشاريع المنجزة\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg\">\n                    <h3 className=\"font-semibold text-navy mb-3\">📋 ميزات نظام التقييمات:</h3>\n                    <ul className=\"text-sm text-gray-700 space-y-1\">\n                      <li>• تقييم بالنجوم من 1 إلى 5</li>\n                      <li>• تعليقات نصية مفصلة</li>\n                      <li>• عرض متوسط التقييمات</li>\n                      <li>• توزيع التقييمات بالرسوم البيانية</li>\n                      <li>• إمكانية الإعجاب بالتقييمات</li>\n                      <li>• الرد على التقييمات</li>\n                      <li>• الإبلاغ عن التقييمات غير المناسبة</li>\n                    </ul>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  );\n};\n\nexport default TestReviewsPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,kBAAkB;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACrC;YACE,IAAI;YACJ,QAAQ;YACR,SAAS;YACT,cAAc;YACd,cAAc;YACd,WAAW;YACX,SAAS;QACX;QACA;YACE,IAAI;YACJ,QAAQ;YACR,SAAS;YACT,cAAc;YACd,cAAc;YACd,WAAW;YACX,SAAS;QACX;QACA;YACE,IAAI;YACJ,QAAQ;YACR,SAAS;YACT,cAAc;YACd,cAAc;YACd,WAAW;YACX,SAAS;QACX;KACD;IAED,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,kBAAkB,CAAC;QACvB,MAAM,SAAS;YACb,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,QAAQ,UAAU,MAAM;YACxB,SAAS,UAAU,OAAO;YAC1B,cAAc;YACd,cAAc;YACd,WAAW,IAAI,OAAO,WAAW;YACjC,SAAS;QACX;QAEA,WAAW,CAAA,OAAQ;gBAAC;mBAAW;aAAK;QACpC,MAAM;IACR;IAEA,MAAM,gBAAgB,QAAQ,MAAM,GAAG,IACnC,QAAQ,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,MAAM,EAAE,KAAK,QAAQ,MAAM,GACxE;IAEJ,MAAM,qBAAqB;QAAC;QAAG;QAAG;QAAG;QAAG;KAAE,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC;YACxD;YACA,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM;YACtD,YAAY,QAAQ,MAAM,GAAG,IACzB,AAAC,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QAAQ,MAAM,GAAG,QAAQ,MAAM,GAAI,MACrE;QACN,CAAC;IAED,qBACE,8OAAC,0IAAA,CAAA,UAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;8DAEb,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAyC;;;;;;8EACtD,8OAAC,sIAAA,CAAA,UAAU;oEACT,QAAQ;oEACR,gBAAgB;oEAChB,MAAK;oEACL,SAAS;;;;;;;;;;;;sEAIb,8OAAC;;8EACC,8OAAC;oEAAE,WAAU;8EAAyC;;;;;;8EACtD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,sIAAA,CAAA,UAAU;oFAAC,QAAQ;oFAAK,QAAQ;oFAAC,MAAK;;;;;;8FACvC,8OAAC;oFAAK,WAAU;8FAAU;;;;;;;;;;;;sFAE5B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,sIAAA,CAAA,UAAU;oFAAC,QAAQ;oFAAK,QAAQ;oFAAC,MAAK;;;;;;8FACvC,8OAAC;oFAAK,WAAU;8FAAU;;;;;;;;;;;;sFAE5B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,sIAAA,CAAA,UAAU;oFAAC,QAAQ;oFAAK,QAAQ;oFAAC,MAAK;oFAAK,SAAS;;;;;;8FACrD,8OAAC;oFAAK,WAAU;8FAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAQpC,8OAAC,gIAAA,CAAA,OAAI;;8DACH,8OAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;8DAEb,8OAAC,gIAAA,CAAA,cAAW;;sEACV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACZ,cAAc,OAAO,CAAC;;;;;;8EAEzB,8OAAC,sIAAA,CAAA,UAAU;oEAAC,QAAQ;oEAAe,QAAQ;oEAAC,MAAK;;;;;;8EACjD,8OAAC;oEAAE,WAAU;;wEACV,QAAQ,MAAM;wEAAC;;;;;;;;;;;;;sEAIpB,8OAAC;4DAAI,WAAU;sEACZ,mBAAmB,GAAG,CAAC,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBACpD,8OAAC;oEAAiB,WAAU;;sFAC1B,8OAAC;4EAAK,WAAU;;gFAAe;gFAAO;;;;;;;sFACtC,8OAAC;4EAAI,WAAU;sFACb,cAAA,8OAAC;gFACC,WAAU;gFACV,OAAO;oFAAE,OAAO,GAAG,WAAW,CAAC,CAAC;gFAAC;;;;;;;;;;;sFAGrC,8OAAC;4EAAK,WAAU;sFAA6B;;;;;;;mEARrC;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAiBpB,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC,qIAAA,CAAA,UAAS;4CAAC,UAAU;;;;;;sDAGrB,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;;wDAAoC;wDACpC,QAAQ,MAAM;wDAAC;;;;;;;8DAG7B,8OAAC;oDAAI,WAAU;8DACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,sIAAA,CAAA,UAAU;4DAET,QAAQ;4DACR,aAAa;2DAFR,OAAO,EAAE;;;;;;;;;;gDAOnB,QAAQ,MAAM,KAAK,mBAClB,8OAAC,gIAAA,CAAA,OAAI;8DACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,8OAAC;gEAAI,WAAU;0EAAgB;;;;;;0EAC/B,8OAAC;gEAAG,WAAU;0EAAuC;;;;;;0EAGrD,8OAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAWzC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,8OAAC,gIAAA,CAAA,cAAW;;0DACV,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAgB;;;;;;0EAC/B,8OAAC;gEAAG,WAAU;0EAA+B;;;;;;0EAC7C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAKvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAgB;;;;;;0EAC/B,8OAAC;gEAAG,WAAU;0EAA+B;;;;;;0EAC7C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAKvC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAgB;;;;;;0EAC/B,8OAAC;gEAAG,WAAU;0EAA+B;;;;;;0EAC7C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAMzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW1B;uCAEe"}}, {"offset": {"line": 3424, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}