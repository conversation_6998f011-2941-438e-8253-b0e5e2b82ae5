{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// دالة تنسيق التاريخ بشكل ثابت لتجنب مشاكل Hydration\nexport function formatDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n\n    // التحقق من صحة التاريخ\n    if (isNaN(date.getTime())) {\n      return 'تاريخ غير صحيح';\n    }\n\n    // تنسيق ثابت للتاريخ\n    const year = date.getFullYear();\n    const month = date.getMonth() + 1;\n    const day = date.getDate();\n\n    // أسماء الشهور بالعربية\n    const monthNames = [\n      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',\n      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'\n    ];\n\n    return `${day} ${monthNames[month - 1]} ${year}`;\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق التاريخ النسبي (منذ كم يوم)\nexport function formatRelativeDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInMs = now.getTime() - date.getTime();\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\n\n    if (diffInDays === 0) {\n      return 'اليوم';\n    } else if (diffInDays === 1) {\n      return 'أمس';\n    } else if (diffInDays < 7) {\n      return `منذ ${diffInDays} أيام`;\n    } else if (diffInDays < 30) {\n      const weeks = Math.floor(diffInDays / 7);\n      return `منذ ${weeks} ${weeks === 1 ? 'أسبوع' : 'أسابيع'}`;\n    } else if (diffInDays < 365) {\n      const months = Math.floor(diffInDays / 30);\n      return `منذ ${months} ${months === 1 ? 'شهر' : 'أشهر'}`;\n    } else {\n      const years = Math.floor(diffInDays / 365);\n      return `منذ ${years} ${years === 1 ? 'سنة' : 'سنوات'}`;\n    }\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق الأرقام بالفواصل\nexport function formatNumber(num: number): string {\n  return num.toLocaleString('ar-SA');\n}\n\n// دالة للتحقق من صحة البريد الإلكتروني\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// دالة للتحقق من صحة رقم الهاتف السوري\nexport function isValidSyrianPhone(phone: string): boolean {\n  const phoneRegex = /^(\\+963|0)?[0-9]{9,10}$/;\n  return phoneRegex.test(phone);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,UAAkB;IAC3C,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QAEtB,wBAAwB;QACxB,IAAI,MAAM,KAAK,OAAO,KAAK;YACzB,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,KAAK,QAAQ,KAAK;QAChC,MAAM,MAAM,KAAK,OAAO;QAExB,wBAAwB;QACxB,MAAM,aAAa;YACjB;YAAS;YAAU;YAAQ;YAAS;YAAQ;YAC5C;YAAS;YAAS;YAAU;YAAU;YAAU;SACjD;QAED,OAAO,GAAG,IAAI,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,MAAM;IAClD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,mBAAmB,UAAkB;IACnD,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;QAC7C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG;YACpB,OAAO;QACT,OAAO,IAAI,eAAe,GAAG;YAC3B,OAAO;QACT,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO,CAAC,IAAI,EAAE,WAAW,KAAK,CAAC;QACjC,OAAO,IAAI,aAAa,IAAI;YAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,UAAU,UAAU;QAC3D,OAAO,IAAI,aAAa,KAAK;YAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,aAAa;YACvC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,WAAW,IAAI,QAAQ,QAAQ;QACzD,OAAO;YACL,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,QAAQ,SAAS;QACxD;IACF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,cAAc,CAAC;AAC5B;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,mBAAmB,KAAa;IAC9C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB"}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',\n  {\n    variants: {\n      variant: {\n        default: 'bg-navy text-white hover:bg-navy/90',\n        destructive: 'bg-red-500 text-white hover:bg-red-500/90',\n        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',\n        secondary: 'bg-teal text-white hover:bg-teal/90',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'underline-offset-4 hover:underline text-primary',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 rounded-md',\n        lg: 'h-11 px-8 rounded-md',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,wQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { Button } from '../ui/Button';\n\nconst Header = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex justify-between items-center\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"group flex items-center space-x-3 space-x-reverse\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-navy to-teal\">\n                دوزان\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:block\">\n            <ul className=\"flex space-x-8 space-x-reverse\">\n              <li>\n                <Link href=\"/\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الرئيسية\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  المشاريع\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الحرفيون\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  من نحن\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الأسئلة الشائعة\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  اتصل بنا\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n            </ul>\n          </nav>\n\n          {/* Desktop Buttons */}\n          <div className=\"hidden lg:flex items-center space-x-4 space-x-reverse\">\n            <Link href=\"/login\">\n              <Button variant=\"outline\" size=\"sm\" className=\"border-navy text-navy hover:bg-navy hover:text-white transition-all duration-300\">\n                تسجيل الدخول\n              </Button>\n            </Link>\n            <Link href=\"/register\">\n              <Button size=\"sm\" className=\"bg-gradient-to-r from-navy to-teal hover:from-teal hover:to-navy text-white shadow-lg hover:shadow-xl transition-all duration-300\">\n                إنشاء حساب\n              </Button>\n            </Link>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300\"\n            aria-label=\"فتح القائمة\"\n          >\n            <svg className=\"w-6 h-6 text-navy\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              {isMobileMenuOpen ? (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              ) : (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden mt-4 py-4 border-t border-gray-200\">\n            <nav className=\"space-y-4\">\n              <Link href=\"/\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الرئيسية\n              </Link>\n              <Link href=\"/jobs\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                المشاريع\n              </Link>\n              <Link href=\"/craftsmen\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الحرفيون\n              </Link>\n              <Link href=\"/about\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                من نحن\n              </Link>\n              <Link href=\"/faq\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الأسئلة الشائعة\n              </Link>\n              <Link href=\"/contact\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                اتصل بنا\n              </Link>\n            </nav>\n            <div className=\"mt-6 space-y-3\">\n              <Link href=\"/login\" className=\"block\">\n                <Button variant=\"outline\" className=\"w-full border-navy text-navy hover:bg-navy hover:text-white\">\n                  تسجيل الدخول\n                </Button>\n              </Link>\n              <Link href=\"/register\" className=\"block\">\n                <Button className=\"w-full bg-gradient-to-r from-navy to-teal text-white\">\n                  إنشاء حساب\n                </Button>\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDAA2L;;;;;;kDAG1M,8OAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;;;;;;sCAO1G,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAA0F;8DAEjH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;gDAA0F;8DAErH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;;gDAA0F;8DAE1H,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;;gDAA0F;8DAEtH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAO,WAAU;;gDAA0F;8DAEpH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;gDAA0F;8DAExH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAmF;;;;;;;;;;;8CAInI,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAAoI;;;;;;;;;;;;;;;;;sCAOpK,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAoB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC1E,iCACC,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;yDAErE,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAO5E,kCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAiF;;;;;;8CAG1G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAiF;;;;;;8CAG9G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAiF;;;;;;8CAGnH,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiF;;;;;;8CAG/G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAO,WAAU;8CAAiF;;;;;;8CAG7G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiF;;;;;;;;;;;;sCAInH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAC5B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAA8D;;;;;;;;;;;8CAIpG,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAC/B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzF;uCAEe"}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 597, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden\">\n      {/* خلفية هندسية متناسقة */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl\"></div>\n      </div>\n\n      {/* شبكة نقطية ناعمة */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',\n          backgroundSize: '30px 30px'\n        }}></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-16 relative z-10\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Logo and Description */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n              <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center text-white text-2xl font-bold shadow-lg\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-white\">\n                دوزان\n              </span>\n            </div>\n            <p className=\"text-white/90 leading-relaxed mb-6\">\n              منصة تربط بين أصحاب المشاريع والحرفيين المهرة لتنفيذ المشاريع بكفاءة وجودة عالية في جميع أنحاء سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              روابط سريعة\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الرئيسية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  المشاريع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الحرفيون\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  من نحن\n                </Link>\n              </li>\n            </ul>\n          </div>\n          {/* Support */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              الدعم\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/contact\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  اتصل بنا\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  شروط الاستخدام\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              تواصل معنا\n            </h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">البريد الإلكتروني</p>\n                  <a href=\"mailto:<EMAIL>\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">الهاتف</p>\n                  <a href=\"tel:+963112345678\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    +963 11 234 5678\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">العنوان</p>\n                  <p className=\"text-white font-medium\">دمشق، سوريا</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"mt-12 pt-8 border-t border-white/20\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-white/80 text-sm\">\n                &copy; {new Date().getFullYear()} دوزان. جميع الحقوق محفوظة.\n              </p>\n              <p className=\"text-white/60 text-xs mt-1\">\n                صُنع بـ ❤️ في سوريا\n              </p>\n            </div>\n\n            <div className=\"flex items-center space-x-6 space-x-reverse\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">2500+</div>\n                <div className=\"text-white/70 text-xs\">حرفي</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">15000+</div>\n                <div className=\"text-white/70 text-xs\">مشروع</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">98%</div>\n                <div className=\"text-white/70 text-xs\">رضا</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6H;;;;;;0DAG5I,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAIlD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAyQ,UAAS;;;;;;;;;;;;;;;;0DAGjT,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA8jD,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO1mD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;;sEACvB,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;;sEAC3B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;;sEAChC,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAO3H,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAO,WAAU;;sEAC1B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3H,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,MAAK;gEAAuB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAM3H,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,MAAK;gEAAoB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAMxH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EACjE,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;8EACrE,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;;gDAAwB;gDAC3B,IAAI,OAAO,WAAW;gDAAG;;;;;;;sDAEnC,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;uCAEe"}}, {"offset": {"line": 1448, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1454, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout = ({ children }: MainLayoutProps) => {\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      <Header />\n      <main className=\"flex-grow\">{children}</main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB;IAC/C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;0BAAa;;;;;;0BAC7B,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe"}}, {"offset": {"line": 1494, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1500, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-white shadow-sm transition-all duration-200 hover:shadow-md',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('font-semibold leading-none tracking-tight text-navy', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-600', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,qBAAO,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACpE,GAAG,KAAK;kBAER;;;;;;AAGL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;AAGL,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;AAIP,WAAW,WAAW,GAAG"}}, {"offset": {"line": 1582, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1588, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/about/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport MainLayout from '@/components/layout/MainLayout';\nimport { Card, CardContent } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\n\nconst AboutPage = () => {\n  const features = [\n    {\n      icon: '🎯',\n      title: 'دقة في الاختيار',\n      description: 'نساعدك في العثور على الحرفي المناسب لمشروعك بدقة عالية'\n    },\n    {\n      icon: '⚡',\n      title: 'سرعة في التنفيذ',\n      description: 'منصة سريعة وفعالة لربط أصحاب المشاريع بالحرفيين'\n    },\n    {\n      icon: '🛡️',\n      title: 'ضمان الجودة',\n      description: 'نضمن جودة العمل من خلال نظام التقييمات والمراجعات'\n    },\n    {\n      icon: '💰',\n      title: 'أسعار منافسة',\n      description: 'احصل على أفضل الأسعار من خلال المنافسة بين الحرفيين'\n    }\n  ];\n\n  const team = [\n    {\n      name: 'أحمد محمد',\n      role: 'المؤسس والرئيس التنفيذي',\n      image: 'https://randomuser.me/api/portraits/men/1.jpg',\n      description: 'خبرة 15 عاماً في مجال التكنولوجيا والأعمال'\n    },\n    {\n      name: 'فاطمة أحمد',\n      role: 'مديرة التطوير',\n      image: 'https://randomuser.me/api/portraits/women/1.jpg',\n      description: 'متخصصة في تطوير المنصات الرقمية'\n    },\n    {\n      name: 'محمد علي',\n      role: 'مدير العمليات',\n      image: 'https://randomuser.me/api/portraits/men/2.jpg',\n      description: 'خبرة واسعة في إدارة المشاريع والعمليات'\n    }\n  ];\n\n  return (\n    <MainLayout>\n      <div className=\"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white relative overflow-hidden\">\n        {/* خلفية هندسية متناسقة */}\n        <div className=\"absolute inset-0 opacity-5\">\n          <div className=\"absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl\"></div>\n          <div className=\"absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl\"></div>\n        </div>\n\n        {/* شبكة نقطية ناعمة */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"w-full h-full\" style={{\n            backgroundImage: 'radial-gradient(circle, #567C8D 1px, transparent 1px)',\n            backgroundSize: '50px 50px'\n          }}></div>\n        </div>\n\n        <div className=\"container mx-auto px-4 py-8 relative z-10\">\n          {/* Hero Section */}\n          <div className=\"text-center mb-16\">\n            <div className=\"inline-block bg-gradient-to-r from-navy to-teal text-white px-8 py-3 rounded-full text-sm font-medium mb-6 shadow-lg\">\n              <span className=\"text-lg\">🏢</span>\n              <span className=\"mr-2\">من نحن</span>\n            </div>\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-navy mb-6 leading-tight\">\n              منصة\n              <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-teal via-navy to-teal\">\n                دوزان\n              </span>\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8\">\n              منصة دوزان هي الحل الأمثل لربط أصحاب المشاريع بالحرفيين المهرة في سوريا.\n              نسعى لتسهيل عملية العثور على الخدمات المطلوبة وتقديم تجربة موثوقة وآمنة للجميع.\n            </p>\n            <div className=\"w-24 h-1 bg-gradient-to-r from-teal to-navy mx-auto rounded-full\"></div>\n          </div>\n\n          {/* Mission Section */}\n          <div className=\"mb-16\">\n            <Card className=\"border-0 bg-white/90 backdrop-blur-md shadow-lg\">\n              <CardContent className=\"p-8 md:p-12\">\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n                  <div>\n                    <h2 className=\"text-3xl font-bold text-navy mb-6\">رسالتنا</h2>\n                    <p className=\"text-gray-700 leading-relaxed mb-6\">\n                      نؤمن بأن كل مشروع يستحق أفضل الحرفيين، وكل حرفي يستحق الفرصة المناسبة.\n                      لذلك أنشأنا منصة دوزان لتكون الجسر الذي يربط بين الطرفين بطريقة عادلة وشفافة.\n                    </p>\n                    <p className=\"text-gray-700 leading-relaxed mb-6\">\n                      نسعى لدعم الاقتصاد المحلي السوري من خلال توفير فرص عمل للحرفيين المهرة\n                      ومساعدة أصحاب المشاريع في الحصول على خدمات عالية الجودة بأسعار منافسة.\n                    </p>\n                    <p className=\"text-gray-700 leading-relaxed mb-6\">\n                      رؤيتنا هي أن نصبح المنصة الرائدة في سوريا لخدمات الحرف والمهن،\n                      ونساهم في بناء مجتمع من الثقة والتعاون المتبادل.\n                    </p>\n                    <Link href=\"/register\">\n                      <Button size=\"lg\" className=\"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90\">\n                        انضم إلينا الآن\n                      </Button>\n                    </Link>\n                  </div>\n                  <div className=\"relative\">\n                    <img\n                      src=\"https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=600&h=400&fit=crop\"\n                      alt=\"فريق العمل\"\n                      className=\"w-full h-80 object-cover rounded-2xl shadow-lg\"\n                    />\n                    <div className=\"absolute inset-0 bg-gradient-to-t from-navy/20 to-transparent rounded-2xl\"></div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Features Section */}\n          <div className=\"mb-16\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold text-navy mb-4\">لماذا دوزان؟</h2>\n              <p className=\"text-gray-600 max-w-2xl mx-auto\">\n                نقدم مجموعة من المميزات التي تجعل تجربتك معنا مميزة وموثوقة\n              </p>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              {features.map((feature, index) => (\n                <Card key={index} className=\"border-0 bg-white/90 backdrop-blur-md shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                  <CardContent className=\"p-6 text-center\">\n                    <div className=\"text-4xl mb-4\">{feature.icon}</div>\n                    <h3 className=\"text-lg font-bold text-navy mb-3\">{feature.title}</h3>\n                    <p className=\"text-gray-600 text-sm leading-relaxed\">{feature.description}</p>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          </div>\n\n          {/* Team Section */}\n          <div className=\"mb-16\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl font-bold text-navy mb-4\">فريق العمل</h2>\n              <p className=\"text-gray-600 max-w-2xl mx-auto\">\n                فريق متخصص ومتفاني يعمل على تطوير وتحسين منصة دوزان باستمرار\n              </p>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {team.map((member, index) => (\n                <Card key={index} className=\"border-0 bg-white/90 backdrop-blur-md shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105\">\n                  <CardContent className=\"p-6 text-center\">\n                    <img\n                      src={member.image}\n                      alt={member.name}\n                      className=\"w-24 h-24 rounded-full mx-auto mb-4 border-4 border-white shadow-lg object-cover\"\n                    />\n                    <h3 className=\"text-xl font-bold text-navy mb-2\">{member.name}</h3>\n                    <p className=\"text-teal font-medium mb-3\">{member.role}</p>\n                    <p className=\"text-gray-600 text-sm\">{member.description}</p>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          </div>\n\n          {/* Values Section */}\n          <div className=\"mb-16\">\n            <Card className=\"border-0 bg-gradient-to-r from-navy to-teal shadow-lg\">\n              <CardContent className=\"p-12 text-white\">\n                <div className=\"text-center mb-12\">\n                  <h2 className=\"text-3xl font-bold mb-4\">قيمنا</h2>\n                  <p className=\"text-white/90 max-w-2xl mx-auto\">\n                    القيم التي نؤمن بها وتوجه عملنا اليومي\n                  </p>\n                </div>\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n                  <div className=\"text-center\">\n                    <div className=\"text-4xl mb-4\">🤝</div>\n                    <h3 className=\"text-xl font-bold mb-2\">الثقة</h3>\n                    <p className=\"text-white/80\">\n                      نبني علاقات قائمة على الثقة المتبادلة بين جميع أطراف المنصة\n                    </p>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-4xl mb-4\">⭐</div>\n                    <h3 className=\"text-xl font-bold mb-2\">الجودة</h3>\n                    <p className=\"text-white/80\">\n                      نسعى دائماً لتقديم أعلى مستويات الجودة في الخدمات والمنتجات\n                    </p>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-4xl mb-4\">🚀</div>\n                    <h3 className=\"text-xl font-bold mb-2\">الابتكار</h3>\n                    <p className=\"text-white/80\">\n                      نطور حلولاً مبتكرة لتحسين تجربة المستخدمين باستمرار\n                    </p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Contact CTA */}\n          <div className=\"text-center\">\n            <Card className=\"border-0 bg-white/90 backdrop-blur-md shadow-lg\">\n              <CardContent className=\"p-12\">\n                <h2 className=\"text-3xl font-bold text-navy mb-4\">هل لديك أسئلة؟</h2>\n                <p className=\"text-gray-600 mb-8 max-w-2xl mx-auto text-lg\">\n                  نحن هنا لمساعدتك. تواصل معنا في أي وقت وسنكون سعداء للإجابة على استفساراتك\n                </p>\n                <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse\">\n                  <Link href=\"/contact\">\n                    <Button size=\"lg\" className=\"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90\">\n                      تواصل معنا\n                    </Button>\n                  </Link>\n                  <Button size=\"lg\" variant=\"outline\" className=\"border-navy text-navy hover:bg-navy hover:text-white\">\n                    <EMAIL>\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  );\n};\n\nexport default AboutPage;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;AAQA,MAAM,YAAY;IAChB,MAAM,WAAW;QACf;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,aAAa;QACf;KACD;IAED,MAAM,OAAO;QACX;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,8OAAC,0IAAA,CAAA,UAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAgB,OAAO;4BACpC,iBAAiB;4BACjB,gBAAgB;wBAClB;;;;;;;;;;;8BAGF,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAO;;;;;;;;;;;;8CAEzB,8OAAC;oCAAG,WAAU;;wCAA0E;sDAEtF,8OAAC;4CAAK,WAAU;sDAAkF;;;;;;;;;;;;8CAIpG,8OAAC;oCAAE,WAAU;8CAA+D;;;;;;8CAI5E,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAoC;;;;;;kEAClD,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAIlD,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAIlD,8OAAC;wDAAE,WAAU;kEAAqC;;;;;;kEAIlD,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;kEACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,WAAU;sEAAyE;;;;;;;;;;;;;;;;;0DAKzG,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,KAAI;wDACJ,KAAI;wDACJ,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;;;;;;;8CAIjD,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,gIAAA,CAAA,OAAI;4CAAa,WAAU;sDAC1B,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAI,WAAU;kEAAiB,QAAQ,IAAI;;;;;;kEAC5C,8OAAC;wDAAG,WAAU;kEAAoC,QAAQ,KAAK;;;;;;kEAC/D,8OAAC;wDAAE,WAAU;kEAAyC,QAAQ,WAAW;;;;;;;;;;;;2CAJlE;;;;;;;;;;;;;;;;sCAYjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;;;;;;;8CAIjD,8OAAC;oCAAI,WAAU;8CACZ,KAAK,GAAG,CAAC,CAAC,QAAQ,sBACjB,8OAAC,gIAAA,CAAA,OAAI;4CAAa,WAAU;sDAC1B,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDACC,KAAK,OAAO,KAAK;wDACjB,KAAK,OAAO,IAAI;wDAChB,WAAU;;;;;;kEAEZ,8OAAC;wDAAG,WAAU;kEAAoC,OAAO,IAAI;;;;;;kEAC7D,8OAAC;wDAAE,WAAU;kEAA8B,OAAO,IAAI;;;;;;kEACtD,8OAAC;wDAAE,WAAU;kEAAyB,OAAO,WAAW;;;;;;;;;;;;2CATjD;;;;;;;;;;;;;;;;sCAiBjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,8OAAC;oDAAE,WAAU;8DAAkC;;;;;;;;;;;;sDAIjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;4DAAG,WAAU;sEAAyB;;;;;;sEACvC,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;8DAI/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;4DAAG,WAAU;sEAAyB;;;;;;sEACvC,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;8DAI/B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgB;;;;;;sEAC/B,8OAAC;4DAAG,WAAU;sEAAyB;;;;;;sEACvC,8OAAC;4DAAE,WAAU;sEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUvC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAClD,8OAAC;4CAAE,WAAU;sDAA+C;;;;;;sDAG5D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;8DACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDAAC,MAAK;wDAAK,WAAU;kEAAyE;;;;;;;;;;;8DAIvG,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAK;oDAAK,SAAQ;oDAAU,WAAU;8DAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvH;uCAEe"}}, {"offset": {"line": 2298, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}