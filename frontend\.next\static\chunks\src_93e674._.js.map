{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useState } from 'react';\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  role: 'client' | 'craftsman';\n  phone?: string;\n}\n\nexport const useAuth = () => {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const login = async (credentials: LoginCredentials) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await signIn('credentials', {\n        email: credentials.email,\n        password: credentials.password,\n        redirect: false,\n      });\n\n      if (result?.error) {\n        setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');\n        return false;\n      }\n\n      if (result?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        setError(errorData.message || 'حدث خطأ أثناء إنشاء الحساب');\n        return false;\n      }\n\n      // تسجيل الدخول تلقائياً بعد التسجيل\n      const loginResult = await signIn('credentials', {\n        email: data.email,\n        password: data.password,\n        redirect: false,\n      });\n\n      if (loginResult?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء إنشاء الحساب');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    setIsLoading(true);\n    try {\n      await signOut({ redirect: false });\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithGoogle = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('google', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Google');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithFacebook = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('facebook', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Facebook');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const isAuthenticated = status === 'authenticated';\n  const isLoading_session = status === 'loading';\n  const user = session?.user;\n\n  // التحقق من الأدوار مع fallback\n  const isClient = user?.role === 'client';\n  const isCraftsman = user?.role === 'craftsman';\n  const isAdmin = user?.role === 'admin';\n\n  // التحقق من الصلاحيات\n  const hasRole = (role: string | string[]) => {\n    if (!user) return false;\n    if (Array.isArray(role)) {\n      return role.includes(user.role);\n    }\n    return user.role === role;\n  };\n\n  const canAccess = (requiredRoles: string | string[]) => {\n    return isAuthenticated && hasRole(requiredRoles);\n  };\n\n  return {\n    // Session data\n    user,\n    session,\n    isAuthenticated,\n    isLoading: isLoading || isLoading_session,\n    error,\n\n    // Role checks\n    isClient,\n    isCraftsman,\n    isAdmin,\n    hasRole,\n    canAccess,\n\n    // Auth methods\n    login,\n    register,\n    logout,\n    loginWithGoogle,\n    loginWithFacebook,\n\n    // Utility\n    setError,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;AAJA;;;;AAmBO,MAAM,UAAU;;IACrB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,QAAQ,OAAO;QACnB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;gBAC9B,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;gBACT,OAAO;YACT;YAEA,IAAI,QAAQ,IAAI;gBACd,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,OAAO,IAAI;gBAC9B,OAAO;YACT;YAEA,oCAAoC;YACpC,MAAM,cAAc,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBAC9C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;YACZ;YAEA,IAAI,aAAa,IAAI;gBACnB,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;gBAAE,UAAU;YAAM;YAChC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBAAE,aAAa;YAAa;QACrD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,YAAY;gBAAE,aAAa;YAAa;QACvD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,WAAW;IACnC,MAAM,oBAAoB,WAAW;IACrC,MAAM,OAAO,SAAS;IAEtB,gCAAgC;IAChC,MAAM,WAAW,MAAM,SAAS;IAChC,MAAM,cAAc,MAAM,SAAS;IACnC,MAAM,UAAU,MAAM,SAAS;IAE/B,sBAAsB;IACtB,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI;QAChC;QACA,OAAO,KAAK,IAAI,KAAK;IACvB;IAEA,MAAM,YAAY,CAAC;QACjB,OAAO,mBAAmB,QAAQ;IACpC;IAEA,OAAO;QACL,eAAe;QACf;QACA;QACA;QACA,WAAW,aAAa;QACxB;QAEA,cAAc;QACd;QACA;QACA;QACA;QACA;QAEA,eAAe;QACf;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;IACF;AACF;GA7Ja;;QACuB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS"}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/auth/AuthButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/hooks/useAuth';\nimport { Button } from '@/components/ui/Button';\nimport Link from 'next/link';\nimport { useState } from 'react';\n\nexport default function AuthButton() {\n  const { isAuthenticated, user, logout, isLoading } = useAuth();\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse\">\n        <div className=\"h-10 w-24 bg-gray-200 rounded\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"flex items-center space-x-3 space-x-reverse\">\n        <Link href=\"/login\">\n          <Button variant=\"outline\" size=\"sm\" className=\"border-navy text-navy hover:bg-navy hover:text-white\">\n            تسجيل الدخول\n          </Button>\n        </Link>\n        <Link href=\"/register\">\n          <Button size=\"sm\" className=\"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90\">\n            إنشاء حساب\n          </Button>\n        </Link>\n      </div>\n    );\n  }\n\n  const getRoleDisplayName = (role: string) => {\n    switch (role) {\n      case 'client':\n        return 'عميل';\n      case 'craftsman':\n        return 'حرفي';\n      case 'admin':\n        return 'مدير';\n      default:\n        return 'مستخدم';\n    }\n  };\n\n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'client':\n        return '👤';\n      case 'craftsman':\n        return '👨‍🔧';\n      case 'admin':\n        return '👑';\n      default:\n        return '👤';\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className=\"flex items-center space-x-2 space-x-reverse bg-white border border-gray-200 rounded-lg px-4 py-2 hover:bg-gray-50 transition-colors duration-200\"\n      >\n        <div className=\"text-lg\">{getRoleIcon(user?.role || '')}</div>\n        <div className=\"text-right\">\n          <div className=\"text-sm font-medium text-navy\">{user?.name || 'مستخدم'}</div>\n          <div className=\"text-xs text-gray-500\">{getRoleDisplayName(user?.role || '')}</div>\n        </div>\n        <svg\n          className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${\n            isDropdownOpen ? 'rotate-180' : ''\n          }`}\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n        </svg>\n      </button>\n\n      {isDropdownOpen && (\n        <>\n          {/* Backdrop */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsDropdownOpen(false)}\n          />\n\n          {/* Dropdown Menu */}\n          <div className=\"absolute left-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-20\">\n            <div className=\"p-4 border-b border-gray-100\">\n              <div className=\"flex items-center space-x-3 space-x-reverse\">\n                <div className=\"text-2xl\">{getRoleIcon(user?.role || '')}</div>\n                <div>\n                  <div className=\"font-medium text-navy\">{user?.name || 'مستخدم'}</div>\n                  <div className=\"text-sm text-gray-500\">{user?.email || 'بريد إلكتروني'}</div>\n                  <div className=\"text-xs text-teal font-medium\">\n                    {getRoleDisplayName(user?.role || '')}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"py-2\">\n              <Link\n                href=\"/dashboard\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>📊</span>\n                  <span>لوحة التحكم</span>\n                </div>\n              </Link>\n\n              <Link\n                href=\"/profile\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>⚙️</span>\n                  <span>الملف الشخصي</span>\n                </div>\n              </Link>\n\n              {user?.role === 'client' && (\n                <Link\n                  href=\"/projects/create\"\n                  className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                  onClick={() => setIsDropdownOpen(false)}\n                >\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span>➕</span>\n                    <span>إنشاء مشروع</span>\n                  </div>\n                </Link>\n              )}\n\n              {user?.role === 'craftsman' && (\n                <Link\n                  href=\"/offers\"\n                  className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                  onClick={() => setIsDropdownOpen(false)}\n                >\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span>💼</span>\n                    <span>عروضي</span>\n                  </div>\n                </Link>\n              )}\n\n              <Link\n                href=\"/messages\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>💬</span>\n                  <span>الرسائل</span>\n                </div>\n              </Link>\n            </div>\n\n            <div className=\"border-t border-gray-100 py-2\">\n              <button\n                onClick={() => {\n                  logout();\n                  setIsDropdownOpen(false);\n                }}\n                className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-red-50 transition-colors duration-200\"\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>🚪</span>\n                  <span>تسجيل الخروج</span>\n                </div>\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,WAAU;kCAAuD;;;;;;;;;;;8BAIvG,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,WAAU;kCAAyE;;;;;;;;;;;;;;;;;IAM7G;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCAAW,YAAY,MAAM,QAAQ;;;;;;kCACpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAiC,MAAM,QAAQ;;;;;;0CAC9D,6LAAC;gCAAI,WAAU;0CAAyB,mBAAmB,MAAM,QAAQ;;;;;;;;;;;;kCAE3E,6LAAC;wBACC,WAAW,CAAC,wDAAwD,EAClE,iBAAiB,eAAe,IAChC;wBACF,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAIxE,gCACC;;kCAEE,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,kBAAkB;;;;;;kCAInC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAY,YAAY,MAAM,QAAQ;;;;;;sDACrD,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAyB,MAAM,QAAQ;;;;;;8DACtD,6LAAC;oDAAI,WAAU;8DAAyB,MAAM,SAAS;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DACZ,mBAAmB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAM1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAIV,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;oCAIT,MAAM,SAAS,0BACd,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;oCAKX,MAAM,SAAS,6BACd,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAKZ,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;0CAKZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;wCACP;wCACA,kBAAkB;oCACpB;oCACA,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GArLwB;;QAC+B,0HAAA,CAAA,UAAO;;;KADtC"}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/notifications/NotificationBell.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '@/components/ui/Button';\n\ninterface Notification {\n  id: string;\n  title: string;\n  message: string;\n  type: 'message' | 'offer' | 'project' | 'review' | 'system';\n  read: boolean;\n  timestamp: string;\n  actionUrl?: string;\n}\n\ninterface NotificationBellProps {\n  notifications: Notification[];\n  onMarkAsRead: (notificationId: string) => void;\n  onMarkAllAsRead: () => void;\n  onNotificationClick: (notification: Notification) => void;\n}\n\nconst NotificationBell: React.FC<NotificationBellProps> = ({\n  notifications,\n  onMarkAsRead,\n  onMarkAllAsRead,\n  onNotificationClick\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'message': return '💬';\n      case 'offer': return '💰';\n      case 'project': return '🏗️';\n      case 'review': return '⭐';\n      case 'system': return '🔔';\n      default: return '📢';\n    }\n  };\n\n  const getNotificationColor = (type: string) => {\n    switch (type) {\n      case 'message': return 'text-blue-600';\n      case 'offer': return 'text-green-600';\n      case 'project': return 'text-purple-600';\n      case 'review': return 'text-yellow-600';\n      case 'system': return 'text-gray-600';\n      default: return 'text-gray-600';\n    }\n  };\n\n  const formatTime = (timestamp: string) => {\n    const now = new Date();\n    const notificationTime = new Date(timestamp);\n    const diffInMinutes = (now.getTime() - notificationTime.getTime()) / (1000 * 60);\n\n    if (diffInMinutes < 1) {\n      return 'الآن';\n    } else if (diffInMinutes < 60) {\n      return `منذ ${Math.floor(diffInMinutes)} دقيقة`;\n    } else if (diffInMinutes < 1440) {\n      return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;\n    } else {\n      return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`;\n    }\n  };\n\n  const handleNotificationClick = (notification: Notification) => {\n    if (!notification.read) {\n      onMarkAsRead(notification.id);\n    }\n    onNotificationClick(notification);\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* Bell Button */}\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2\"\n      >\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\" />\n        </svg>\n        \n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </Button>\n\n      {/* Dropdown */}\n      {isOpen && (\n        <div className=\"absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\">\n          {/* Header */}\n          <div className=\"p-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"font-semibold text-gray-900\">الإشعارات</h3>\n              {unreadCount > 0 && (\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={onMarkAllAsRead}\n                  className=\"text-xs\"\n                >\n                  تحديد الكل كمقروء\n                </Button>\n              )}\n            </div>\n          </div>\n\n          {/* Notifications List */}\n          <div className=\"max-h-96 overflow-y-auto\">\n            {notifications.length > 0 ? (\n              notifications.slice(0, 10).map((notification) => (\n                <div\n                  key={notification.id}\n                  onClick={() => handleNotificationClick(notification)}\n                  className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${\n                    !notification.read ? 'bg-blue-50' : ''\n                  }`}\n                >\n                  <div className=\"flex items-start space-x-3 space-x-reverse\">\n                    <div className={`text-lg ${getNotificationColor(notification.type)}`}>\n                      {getNotificationIcon(notification.type)}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center justify-between mb-1\">\n                        <h4 className={`text-sm font-medium ${\n                          !notification.read ? 'text-gray-900' : 'text-gray-700'\n                        }`}>\n                          {notification.title}\n                        </h4>\n                        {!notification.read && (\n                          <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                        )}\n                      </div>\n                      <p className=\"text-sm text-gray-600 line-clamp-2\">\n                        {notification.message}\n                      </p>\n                      <p className=\"text-xs text-gray-500 mt-1\">\n                        {formatTime(notification.timestamp)}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"p-8 text-center\">\n                <div className=\"text-4xl mb-2\">🔔</div>\n                <p className=\"text-gray-500\">لا توجد إشعارات</p>\n              </div>\n            )}\n          </div>\n\n          {/* Footer */}\n          {notifications.length > 10 && (\n            <div className=\"p-3 border-t border-gray-200 text-center\">\n              <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                عرض جميع الإشعارات\n              </Button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default NotificationBell;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAsBA,MAAM,mBAAoD,CAAC,EACzD,aAAa,EACb,YAAY,EACZ,eAAe,EACf,mBAAmB,EACpB;;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;iEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,UAAU;oBACZ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;8CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;qCAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,mBAAmB,IAAI,KAAK;QAClC,MAAM,gBAAgB,CAAC,IAAI,OAAO,KAAK,iBAAiB,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE/E,IAAI,gBAAgB,GAAG;YACrB,OAAO;QACT,OAAO,IAAI,gBAAgB,IAAI;YAC7B,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,eAAe,MAAM,CAAC;QACjD,OAAO,IAAI,gBAAgB,MAAM;YAC/B,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;QACrD,OAAO;YACL,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,gBAAgB,MAAM,IAAI,CAAC;QACtD;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,aAAa,IAAI,EAAE;YACtB,aAAa,aAAa,EAAE;QAC9B;QACA,oBAAoB;QACpB,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGtE,cAAc,mBACb,6LAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;YAMjC,wBACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8B;;;;;;gCAC3C,cAAc,mBACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,GAAG,IACtB,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,6BAC9B,6LAAC;gCAEC,SAAS,IAAM,wBAAwB;gCACvC,WAAW,CAAC,+EAA+E,EACzF,CAAC,aAAa,IAAI,GAAG,eAAe,IACpC;0CAEF,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,qBAAqB,aAAa,IAAI,GAAG;sDACjE,oBAAoB,aAAa,IAAI;;;;;;sDAExC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAW,CAAC,oBAAoB,EAClC,CAAC,aAAa,IAAI,GAAG,kBAAkB,iBACvC;sEACC,aAAa,KAAK;;;;;;wDAEpB,CAAC,aAAa,IAAI,kBACjB,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAGnB,6LAAC;oDAAE,WAAU;8DACV,aAAa,OAAO;;;;;;8DAEvB,6LAAC;oDAAE,WAAU;8DACV,WAAW,aAAa,SAAS;;;;;;;;;;;;;;;;;;+BAzBnC,aAAa,EAAE;;;;sDAgCxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;oBAMlC,cAAc,MAAM,GAAG,oBACtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAK,WAAU;sCAAS;;;;;;;;;;;;;;;;;;;;;;;AASrE;GApKM;KAAA;uCAsKS"}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useSession } from 'next-auth/react';\nimport { Button } from '../ui/Button';\nimport AuthButton from '../auth/AuthButton';\nimport NotificationBell from '../notifications/NotificationBell';\n\nconst Header = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const { data: session } = useSession();\n\n  // بيانات تجريبية للإشعارات\n  const mockNotifications = [\n    {\n      id: '1',\n      title: 'عرض جديد',\n      message: 'تم تقديم عرض جديد على مشروع تجديد المطبخ',\n      type: 'offer' as const,\n      read: false,\n      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n      actionUrl: '/client/offers'\n    },\n    {\n      id: '2',\n      title: 'رسالة جديدة',\n      message: 'رسالة جديدة من محمد النجار',\n      type: 'message' as const,\n      read: false,\n      timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),\n      actionUrl: '/messages'\n    },\n    {\n      id: '3',\n      title: 'تقييم جديد',\n      message: 'تم تقييم عملك بـ 5 نجوم',\n      type: 'review' as const,\n      read: true,\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      actionUrl: '/craftsman/reviews'\n    }\n  ];\n\n  const handleNotificationClick = (notification: any) => {\n    if (notification.actionUrl) {\n      window.location.href = notification.actionUrl;\n    }\n  };\n\n  const handleMarkAsRead = (notificationId: string) => {\n    // TODO: تنفيذ تحديث حالة الإشعار في قاعدة البيانات\n    console.log('Mark as read:', notificationId);\n  };\n\n  const handleMarkAllAsRead = () => {\n    // TODO: تنفيذ تحديد جميع الإشعارات كمقروءة\n    console.log('Mark all as read');\n  };\n\n  return (\n    <header className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex justify-between items-center\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"group flex items-center space-x-3 space-x-reverse\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-navy to-teal\">\n                دوزان\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:block\">\n            <ul className=\"flex space-x-8 space-x-reverse\">\n              <li>\n                <Link href=\"/\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الرئيسية\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  المشاريع\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الحرفيون\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  من نحن\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الأسئلة الشائعة\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  اتصل بنا\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n            </ul>\n          </nav>\n\n          {/* Desktop Auth */}\n          <div className=\"hidden lg:flex items-center space-x-4 space-x-reverse\">\n            {session && (\n              <>\n                <Link href=\"/messages\">\n                  <Button variant=\"outline\" size=\"sm\" className=\"relative\">\n                    💬\n                    <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center\">\n                      2\n                    </span>\n                  </Button>\n                </Link>\n                <NotificationBell\n                  notifications={mockNotifications}\n                  onMarkAsRead={handleMarkAsRead}\n                  onMarkAllAsRead={handleMarkAllAsRead}\n                  onNotificationClick={handleNotificationClick}\n                />\n              </>\n            )}\n            <AuthButton />\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300\"\n            aria-label=\"فتح القائمة\"\n          >\n            <svg className=\"w-6 h-6 text-navy\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              {isMobileMenuOpen ? (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              ) : (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden mt-4 py-4 border-t border-gray-200\">\n            <nav className=\"space-y-4\">\n              <Link href=\"/\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الرئيسية\n              </Link>\n              <Link href=\"/jobs\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                المشاريع\n              </Link>\n              <Link href=\"/craftsmen\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الحرفيون\n              </Link>\n              <Link href=\"/about\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                من نحن\n              </Link>\n              <Link href=\"/faq\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الأسئلة الشائعة\n              </Link>\n              <Link href=\"/contact\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                اتصل بنا\n              </Link>\n            </nav>\n            <div className=\"mt-6\">\n              <AuthButton />\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,SAAS;;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAEnC,2BAA2B;IAC3B,MAAM,oBAAoB;QACxB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC5D,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC5D,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YAChE,WAAW;QACb;KACD;IAED,MAAM,0BAA0B,CAAC;QAC/B,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,QAAQ,CAAC,IAAI,GAAG,aAAa,SAAS;QAC/C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,mDAAmD;QACnD,QAAQ,GAAG,CAAC,iBAAiB;IAC/B;IAEA,MAAM,sBAAsB;QAC1B,2CAA2C;QAC3C,QAAQ,GAAG,CAAC;IACd;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDAA2L;;;;;;kDAG1M,6LAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;;;;;;sCAO1G,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAA0F;8DAEjH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;gDAA0F;8DAErH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;;gDAA0F;8DAE1H,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;;gDAA0F;8DAEtH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAO,WAAU;;gDAA0F;8DAEpH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;gDAA0F;8DAExH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,6LAAC;4BAAI,WAAU;;gCACZ,yBACC;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;;oDAAW;kEAEvD,6LAAC;wDAAK,WAAU;kEAA+G;;;;;;;;;;;;;;;;;sDAKnI,6LAAC,0JAAA,CAAA,UAAgB;4CACf,eAAe;4CACf,cAAc;4CACd,iBAAiB;4CACjB,qBAAqB;;;;;;;;8CAI3B,6LAAC,2IAAA,CAAA,UAAU;;;;;;;;;;;sCAIb,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC;gCAAI,WAAU;gCAAoB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC1E,iCACC,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;yDAErE,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAO5E,kCACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAiF;;;;;;8CAG1G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAiF;;;;;;8CAG9G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAiF;;;;;;8CAGnH,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiF;;;;;;8CAG/G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAO,WAAU;8CAAiF;;;;;;8CAG7G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiF;;;;;;;;;;;;sCAInH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2IAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB;GAnLM;;QAEsB,iJAAA,CAAA,aAAU;;;KAFhC;uCAqLS"}}, {"offset": {"line": 1445, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1451, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden\">\n      {/* خلفية هندسية متناسقة */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl\"></div>\n      </div>\n\n      {/* شبكة نقطية ناعمة */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',\n          backgroundSize: '30px 30px'\n        }}></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-16 relative z-10\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Logo and Description */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n              <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center text-white text-2xl font-bold shadow-lg\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-white\">\n                دوزان\n              </span>\n            </div>\n            <p className=\"text-white/90 leading-relaxed mb-6\">\n              منصة تربط بين أصحاب المشاريع والحرفيين المهرة لتنفيذ المشاريع بكفاءة وجودة عالية في جميع أنحاء سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              روابط سريعة\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الرئيسية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  المشاريع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الحرفيون\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  من نحن\n                </Link>\n              </li>\n            </ul>\n          </div>\n          {/* Support */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              الدعم\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/contact\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  اتصل بنا\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  شروط الاستخدام\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              تواصل معنا\n            </h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">البريد الإلكتروني</p>\n                  <a href=\"mailto:<EMAIL>\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">الهاتف</p>\n                  <a href=\"tel:+963112345678\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    +963 11 234 5678\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">العنوان</p>\n                  <p className=\"text-white font-medium\">دمشق، سوريا</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"mt-12 pt-8 border-t border-white/20\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-white/80 text-sm\">\n                &copy; {new Date().getFullYear()} دوزان. جميع الحقوق محفوظة.\n              </p>\n              <p className=\"text-white/60 text-xs mt-1\">\n                صُنع بـ ❤️ في سوريا\n              </p>\n            </div>\n\n            <div className=\"flex items-center space-x-6 space-x-reverse\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">2500+</div>\n                <div className=\"text-white/70 text-xs\">حرفي</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">15000+</div>\n                <div className=\"text-white/70 text-xs\">مشروع</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">98%</div>\n                <div className=\"text-white/70 text-xs\">رضا</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA6H;;;;;;0DAG5I,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAIlD,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAyQ,UAAS;;;;;;;;;;;;;;;;0DAGjT,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA8jD,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO1mD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;;sEACvB,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;;sEAC3B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;;sEAChC,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAO3H,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAO,WAAU;;sEAC1B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3H,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,MAAK;gEAAuB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAM3H,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,MAAK;gEAAoB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAMxH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EACjE,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;8EACrE,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;gDAAwB;gDAC3B,IAAI,OAAO,WAAW;gDAAG;;;;;;;sDAEnC,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;KA5MM;uCA8MS"}}, {"offset": {"line": 2308, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2314, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout = ({ children }: MainLayoutProps) => {\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      <Header />\n      <main className=\"flex-grow\">{children}</main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB;IAC/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAK,WAAU;0BAAa;;;;;;0BAC7B,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;KARM;uCAUS"}}, {"offset": {"line": 2360, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-white shadow-sm transition-all duration-200 hover:shadow-md',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('font-semibold leading-none tracking-tight text-navy', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-600', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACpE,GAAG,KAAK;kBAER;;;;;;;AAGL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;;AAGL,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG"}}, {"offset": {"line": 2470, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2476, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Badge.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n  {\n    variants: {\n      variant: {\n        default: 'border-transparent bg-navy text-white hover:bg-navy/80',\n        secondary: 'border-transparent bg-teal text-white hover:bg-teal/80',\n        destructive: 'border-transparent bg-red-500 text-white hover:bg-red-500/80',\n        outline: 'text-navy border-navy',\n        success: 'border-transparent bg-green-500 text-white hover:bg-green-500/80',\n        warning: 'border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80',\n        info: 'border-transparent bg-skyblue text-navy hover:bg-skyblue/80',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  }\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS"}}, {"offset": {"line": 2522, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2528, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/DateDisplay.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { formatDate } from '@/lib/utils';\n\ninterface DateDisplayProps {\n  date: string;\n  className?: string;\n}\n\nconst DateDisplay: React.FC<DateDisplayProps> = ({ date, className = '' }) => {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  // عرض placeholder أثناء التحميل لتجنب مشاكل hydration\n  if (!mounted) {\n    return <span className={className}>تحميل...</span>;\n  }\n\n  return <span className={className}>{formatDate(date)}</span>;\n};\n\nexport default DateDisplay;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUA,MAAM,cAA0C,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE;;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;QACb;gCAAG,EAAE;IAEL,sDAAsD;IACtD,IAAI,CAAC,SAAS;QACZ,qBAAO,6LAAC;YAAK,WAAW;sBAAW;;;;;;IACrC;IAEA,qBAAO,6LAAC;QAAK,WAAW;kBAAY,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;;;;;;AACjD;GAbM;KAAA;uCAeS"}}, {"offset": {"line": 2575, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2581, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/portfolio/CompletedProject.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '../ui/Card';\nimport { Badge } from '../ui/Badge';\nimport DateDisplay from '../ui/DateDisplay';\n\ninterface CompletedProject {\n  id: number;\n  title: string;\n  description: string;\n  category: string;\n  completedAt: string;\n  duration: number; // بالأيام\n  budget: number;\n  beforeImages: string[];\n  afterImages: string[];\n  skills: string[];\n  clientRating: number;\n  clientReview: string;\n  client: {\n    name: string;\n    location: string;\n  };\n}\n\ninterface CompletedProjectProps {\n  project: CompletedProject;\n  showClientInfo?: boolean;\n}\n\nconst CompletedProject: React.FC<CompletedProjectProps> = ({\n  project,\n  showClientInfo = true\n}) => {\n  const [showBeforeAfter, setShowBeforeAfter] = useState(false);\n  const [selectedImageIndex, setSelectedImageIndex] = useState(0);\n\n  return (\n    <div className=\"group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-teal/30 hover:scale-105\">\n      {/* مقارنة قبل وبعد */}\n      <div className=\"relative h-64 overflow-hidden\">\n        {!showBeforeAfter ? (\n          // عرض مقارنة قبل وبعد\n          <div className=\"absolute inset-0 grid grid-cols-2\">\n            {/* صورة قبل */}\n            <div className=\"relative overflow-hidden\">\n              <img\n                src={project.beforeImages[0]}\n                alt=\"قبل\"\n                className=\"w-full h-full object-cover transition-transform duration-700 group-hover:scale-110\"\n              />\n              <div className=\"absolute top-4 left-4 bg-red-500/90 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                قبل\n              </div>\n            </div>\n\n            {/* صورة بعد */}\n            <div className=\"relative overflow-hidden\">\n              <img\n                src={project.afterImages[0]}\n                alt=\"بعد\"\n                className=\"w-full h-full object-cover transition-transform duration-700 group-hover:scale-110\"\n              />\n              <div className=\"absolute top-4 right-4 bg-green-500/90 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                بعد\n              </div>\n            </div>\n\n            {/* خط فاصل */}\n            <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-16 bg-white/80 rounded-full shadow-lg\"></div>\n\n            {/* أيقونة المقارنة */}\n            <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center\">\n              <svg className=\"w-5 h-5 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4\" />\n              </svg>\n            </div>\n          </div>\n        ) : (\n          // عرض الصور بشكل منفصل\n          <div className=\"relative\">\n            <img\n              src={showBeforeAfter ? project.afterImages[selectedImageIndex] : project.beforeImages[selectedImageIndex]}\n              alt={project.title}\n              className=\"w-full h-full object-cover transition-transform duration-700 group-hover:scale-110\"\n            />\n\n            {/* تأثير التدرج */}\n            <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent\"></div>\n          </div>\n        )}\n\n        {/* شارة الفئة */}\n        <div className=\"absolute top-4 right-4\">\n          <div className=\"bg-white/90 backdrop-blur-sm text-navy px-3 py-1 rounded-full text-sm font-medium shadow-lg\">\n            🏆 {project.category}\n          </div>\n        </div>\n\n        {/* شارة التقييم */}\n        <div className=\"absolute bottom-4 left-4\">\n          <div className=\"bg-yellow-500/90 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center\">\n            <svg className=\"w-4 h-4 ml-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n            </svg>\n            {project.clientRating}\n          </div>\n        </div>\n\n        {/* زر التبديل */}\n        <button\n          onClick={() => setShowBeforeAfter(!showBeforeAfter)}\n          className=\"absolute bottom-4 right-4 bg-white/90 hover:bg-white text-navy px-3 py-1 rounded-full text-sm font-medium shadow-lg transition-all duration-300 hover:scale-105\"\n        >\n          {showBeforeAfter ? 'عرض المقارنة' : 'عرض منفصل'}\n        </button>\n      </div>\n\n      {/* محتوى البطاقة */}\n      <div className=\"p-6\">\n        {/* العنوان والوصف */}\n        <div className=\"mb-6\">\n          <h3 className=\"text-xl font-bold text-navy mb-2 group-hover:text-teal transition-colors duration-300\">\n            {project.title}\n          </h3>\n          <p className=\"text-gray-600 line-clamp-2 leading-relaxed\">\n            {project.description}\n          </p>\n        </div>\n\n        {/* معلومات المشروع */}\n        <div className=\"grid grid-cols-3 gap-4 mb-6\">\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-green-600\">{project.budget.toLocaleString()}</div>\n            <div className=\"text-sm text-gray-500\">ل.س</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-blue-600\">{project.duration}</div>\n            <div className=\"text-sm text-gray-500\">يوم</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-2xl font-bold text-yellow-600\">{project.clientRating}</div>\n            <div className=\"text-sm text-gray-500\">تقييم</div>\n          </div>\n        </div>\n\n        {/* المهارات */}\n        <div className=\"flex flex-wrap gap-2 mb-6\">\n          {project.skills.slice(0, 3).map((skill, index) => (\n            <Badge key={index} variant=\"outline\" className=\"text-xs bg-gradient-to-r from-navy/5 to-teal/5 border-navy/20 text-navy\">\n              {skill}\n            </Badge>\n          ))}\n          {project.skills.length > 3 && (\n            <Badge variant=\"outline\" className=\"text-xs bg-gray-50 border-gray-200 text-gray-600\">\n              +{project.skills.length - 3}\n            </Badge>\n          )}\n        </div>\n\n        {/* تقييم العميل */}\n        {project.clientReview && (\n          <div className=\"bg-gradient-to-r from-gray-50 to-gray-100 p-4 rounded-xl mb-6 border border-gray-200/50\">\n            <div className=\"flex items-start\">\n              <svg className=\"w-5 h-5 text-gray-400 mt-1 ml-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                <path fillRule=\"evenodd\" d=\"M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z\" clipRule=\"evenodd\" />\n              </svg>\n              <div>\n                <p className=\"text-gray-700 text-sm italic leading-relaxed\">\"{project.clientReview}\"</p>\n                {showClientInfo && (\n                  <div className=\"mt-2 text-xs text-gray-500\">\n                    - {project.client.name}، {project.client.location}\n                  </div>\n                )}\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* معلومات إضافية */}\n        <div className=\"flex items-center justify-between pt-4 border-t border-gray-100\">\n          <div className=\"flex items-center text-sm text-gray-500\">\n            <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            اكتمل في <DateDisplay date={project.completedAt} />\n          </div>\n          <div className=\"flex items-center text-sm text-teal font-medium\">\n            <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            مشروع مكتمل\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default CompletedProject;\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;;;AALA;;;;AA+BA,MAAM,mBAAoD,CAAC,EACzD,OAAO,EACP,iBAAiB,IAAI,EACtB;;IACC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;oBACZ,CAAC,kBACA,sBAAsB;kCACtB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK,QAAQ,YAAY,CAAC,EAAE;wCAC5B,KAAI;wCACJ,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;kDAA4F;;;;;;;;;;;;0CAM7G,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,KAAK,QAAQ,WAAW,CAAC,EAAE;wCAC3B,KAAI;wCACJ,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;kDAA+F;;;;;;;;;;;;0CAMhH,6LAAC;gCAAI,WAAU;;;;;;0CAGf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC/E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;+BAK3E,uBAAuB;kCACvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,KAAK,kBAAkB,QAAQ,WAAW,CAAC,mBAAmB,GAAG,QAAQ,YAAY,CAAC,mBAAmB;gCACzG,KAAK,QAAQ,KAAK;gCAClB,WAAU;;;;;;0CAIZ,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAKnB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;gCAA8F;gCACvG,QAAQ,QAAQ;;;;;;;;;;;;kCAKxB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;oCAAe,MAAK;oCAAe,SAAQ;8CACxD,cAAA,6LAAC;wCAAK,GAAE;;;;;;;;;;;gCAET,QAAQ,YAAY;;;;;;;;;;;;kCAKzB,6LAAC;wBACC,SAAS,IAAM,mBAAmB,CAAC;wBACnC,WAAU;kCAET,kBAAkB,iBAAiB;;;;;;;;;;;;0BAKxC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK;;;;;;0CAEhB,6LAAC;gCAAE,WAAU;0CACV,QAAQ,WAAW;;;;;;;;;;;;kCAKxB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAqC,QAAQ,MAAM,CAAC,cAAc;;;;;;kDACjF,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAoC,QAAQ,QAAQ;;;;;;kDACnE,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAsC,QAAQ,YAAY;;;;;;kDACzE,6LAAC;wCAAI,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAK3C,6LAAC;wBAAI,WAAU;;4BACZ,QAAQ,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACtC,6LAAC,oIAAA,CAAA,QAAK;oCAAa,SAAQ;oCAAU,WAAU;8CAC5C;mCADS;;;;;4BAIb,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,6LAAC,oIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;oCAAmD;oCAClF,QAAQ,MAAM,CAAC,MAAM,GAAG;;;;;;;;;;;;;oBAM/B,QAAQ,YAAY,kBACnB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;oCAAkC,MAAK;oCAAe,SAAQ;8CAC3E,cAAA,6LAAC;wCAAK,UAAS;wCAAU,GAAE;wCAA6J,UAAS;;;;;;;;;;;8CAEnM,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;;gDAA+C;gDAAE,QAAQ,YAAY;gDAAC;;;;;;;wCAClF,gCACC,6LAAC;4CAAI,WAAU;;gDAA6B;gDACvC,QAAQ,MAAM,CAAC,IAAI;gDAAC;gDAAG,QAAQ,MAAM,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;kCAS7D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;kDACG,6LAAC,0IAAA,CAAA,UAAW;wCAAC,MAAM,QAAQ,WAAW;;;;;;;;;;;;0CAEjD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAvKM;KAAA;uCAyKS"}}, {"offset": {"line": 3097, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3103, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/<PERSON>zan%20Website/frontend/src/app/craftsmen/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useParams } from 'next/navigation';\nimport MainLayout from '@/components/layout/MainLayout';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { Badge } from '@/components/ui/Badge';\nimport { Button } from '@/components/ui/Button';\nimport CompletedProject from '@/components/portfolio/CompletedProject';\n\ninterface CompletedProject {\n  id: number;\n  title: string;\n  description: string;\n  category: string;\n  completedAt: string;\n  duration: number;\n  budget: number;\n  beforeImages: string[];\n  afterImages: string[];\n  skills: string[];\n  clientRating: number;\n  clientReview: string;\n  client: {\n    name: string;\n    location: string;\n  };\n}\n\nconst CraftsmanProfilePage = () => {\n  const params = useParams();\n  const [activeTab, setActiveTab] = useState<'about' | 'portfolio' | 'reviews'>('about');\n\n  // بيانات وهمية للحرفي\n  const craftsman = {\n    id: params.id,\n    name: 'محمد النجار',\n    profession: 'نجار محترف',\n    bio: 'نجار محترف مع خبرة 10 سنوات في تصميم وتنفيذ الأثاث المنزلي والمكتبي. متخصص في المطابخ والخزائن العصرية. أعمل بأحدث التقنيات وأضمن الجودة العالية في جميع أعمالي.',\n    location: 'دمشق، المزة',\n    rating: 4.9,\n    reviewsCount: 47,\n    completedJobs: 89,\n    hourlyRate: 2500,\n    skills: ['نجارة', 'تصميم أثاث', 'تركيب', 'ترميم', 'خزائن مطابخ', 'أثاث مكتبي'],\n    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',\n    isOnline: true,\n    responseTime: 'خلال ساعة',\n    joinedDate: '2022-03-15',\n    verified: true,\n    languages: ['العربية', 'الإنجليزية'],\n    workingHours: 'الأحد - الخميس: 8:00 - 18:00'\n  };\n\n  // مشاريع مكتملة مع صور قبل وبعد\n  const completedProjects: CompletedProject[] = [\n    {\n      id: 1,\n      title: 'تصميم وتنفيذ مطبخ عصري',\n      description: 'تصميم وتنفيذ مطبخ عصري بخزائن خشبية عالية الجودة مع جزيرة وسطية وإضاءة LED مدمجة.',\n      category: 'مطابخ',\n      completedAt: '2024-01-10',\n      duration: 14,\n      budget: 350000,\n      beforeImages: [\n        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop',\n        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&crop=entropy&cs=tinysrgb'\n      ],\n      afterImages: [\n        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&sat=2',\n        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&sat=2&crop=entropy'\n      ],\n      skills: ['نجارة', 'تصميم', 'تركيب'],\n      clientRating: 5,\n      clientReview: 'عمل ممتاز وجودة عالية. محمد حرفي محترف ومتقن لعمله. أنصح بالتعامل معه.',\n      client: {\n        name: 'أحمد محمد',\n        location: 'دمشق، المزة'\n      }\n    },\n    {\n      id: 2,\n      title: 'تجديد غرفة نوم كاملة',\n      description: 'تجديد وترميم غرفة نوم كاملة مع تصميم خزائن جديدة وتركيب أرضية خشبية.',\n      category: 'غرف نوم',\n      completedAt: '2023-12-20',\n      duration: 10,\n      budget: 280000,\n      beforeImages: [\n        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',\n        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=entropy'\n      ],\n      afterImages: [\n        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&sat=2',\n        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&sat=2&crop=entropy'\n      ],\n      skills: ['نجارة', 'ترميم', 'تركيب'],\n      clientRating: 5,\n      clientReview: 'تجاوز توقعاتي بكثير. العمل نظيف ومتقن والنتيجة رائعة.',\n      client: {\n        name: 'فاطمة أحمد',\n        location: 'دمشق، جرمانا'\n      }\n    },\n    {\n      id: 3,\n      title: 'مكتب منزلي مخصص',\n      description: 'تصميم وتنفيذ مكتب منزلي مخصص مع أرفف ووحدات تخزين متعددة.',\n      category: 'مكاتب',\n      completedAt: '2023-11-15',\n      duration: 7,\n      budget: 150000,\n      beforeImages: [\n        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=faces',\n      ],\n      afterImages: [\n        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=faces&sat=2',\n      ],\n      skills: ['نجارة', 'تصميم', 'أثاث مكتبي'],\n      clientRating: 4,\n      clientReview: 'عمل جيد ولكن كان هناك تأخير بسيط في التسليم.',\n      client: {\n        name: 'محمد علي',\n        location: 'دمشق، المالكي'\n      }\n    }\n  ];\n\n  const reviews = [\n    {\n      id: 1,\n      clientName: 'أحمد محمد',\n      rating: 5,\n      comment: 'عمل ممتاز وجودة عالية. محمد حرفي محترف ومتقن لعمله.',\n      date: '2024-01-12',\n      projectTitle: 'تصميم مطبخ عصري'\n    },\n    {\n      id: 2,\n      clientName: 'فاطمة أحمد',\n      rating: 5,\n      comment: 'تجاوز توقعاتي بكثير. العمل نظيف ومتقن والنتيجة رائعة.',\n      date: '2023-12-22',\n      projectTitle: 'تجديد غرفة نوم'\n    },\n    {\n      id: 3,\n      clientName: 'محمد علي',\n      rating: 4,\n      comment: 'عمل جيد ولكن كان هناك تأخير بسيط في التسليم.',\n      date: '2023-11-18',\n      projectTitle: 'مكتب منزلي مخصص'\n    }\n  ];\n\n  return (\n    <MainLayout>\n      <div className=\"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white relative overflow-hidden\">\n        {/* خلفية هندسية متناسقة */}\n        <div className=\"absolute inset-0 opacity-5\">\n          <div className=\"absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl\"></div>\n          <div className=\"absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl\"></div>\n        </div>\n\n        {/* شبكة نقطية ناعمة */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"w-full h-full\" style={{\n            backgroundImage: 'radial-gradient(circle, #567C8D 1px, transparent 1px)',\n            backgroundSize: '50px 50px'\n          }}></div>\n        </div>\n\n        <div className=\"container mx-auto px-4 py-8 relative z-10\">\n          {/* معلومات الحرفي */}\n          <Card className=\"border-0 bg-white/90 backdrop-blur-sm mb-8\">\n            <CardContent className=\"p-8\">\n              <div className=\"flex flex-col md:flex-row items-start md:items-center gap-6\">\n                <div className=\"relative\">\n                  <img\n                    src={craftsman.avatar}\n                    alt={craftsman.name}\n                    className=\"w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg\"\n                  />\n                  {craftsman.isOnline && (\n                    <div className=\"absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 border-4 border-white rounded-full\"></div>\n                  )}\n                  {craftsman.verified && (\n                    <div className=\"absolute -top-2 -left-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"flex-1\">\n                  <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-4\">\n                    <div>\n                      <h1 className=\"text-3xl font-bold text-navy mb-2\">{craftsman.name}</h1>\n                      <p className=\"text-lg text-gray-600 mb-2\">{craftsman.profession}</p>\n                      <div className=\"flex items-center text-gray-500 mb-2\">\n                        <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                        </svg>\n                        {craftsman.location}\n                      </div>\n                    </div>\n\n                    <div className=\"text-left\">\n                      <div className=\"flex items-center mb-2\">\n                        <div className=\"flex items-center\">\n                          {[...Array(5)].map((_, i) => (\n                            <svg\n                              key={i}\n                              className={`w-5 h-5 ${\n                                i < Math.floor(craftsman.rating) ? 'text-yellow-500' : 'text-gray-300'\n                              }`}\n                              fill=\"currentColor\"\n                              viewBox=\"0 0 20 20\"\n                            >\n                              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                            </svg>\n                          ))}\n                        </div>\n                        <span className=\"mr-2 font-semibold\">{craftsman.rating}</span>\n                        <span className=\"text-gray-500\">({craftsman.reviewsCount} تقييم)</span>\n                      </div>\n                      <div className=\"text-2xl font-bold text-green-600\">\n                        {craftsman.hourlyRate.toLocaleString()} ل.س/ساعة\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\">\n                    <div className=\"text-center\">\n                      <div className=\"text-xl font-bold text-navy\">{craftsman.completedJobs}</div>\n                      <div className=\"text-sm text-gray-600\">مشروع مكتمل</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-xl font-bold text-navy\">{craftsman.reviewsCount}</div>\n                      <div className=\"text-sm text-gray-600\">تقييم</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-xl font-bold text-navy\">10</div>\n                      <div className=\"text-sm text-gray-600\">سنوات خبرة</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-xl font-bold text-navy\">{craftsman.responseTime}</div>\n                      <div className=\"text-sm text-gray-600\">وقت الرد</div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex flex-wrap gap-2 mb-4\">\n                    {craftsman.skills.slice(0, 6).map((skill, index) => (\n                      <Badge key={index} variant=\"outline\">\n                        {skill}\n                      </Badge>\n                    ))}\n                  </div>\n\n                  <div className=\"flex space-x-3 space-x-reverse\">\n                    <Button className=\"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90\">\n                      تواصل معي\n                    </Button>\n                    <Button variant=\"outline\">\n                      احفظ الملف الشخصي\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* التبويبات */}\n          <div className=\"mb-8\">\n            <div className=\"flex border-b border-gray-200 bg-white rounded-t-lg\">\n              {[\n                { id: 'about', label: 'نبذة عني', count: null },\n                { id: 'portfolio', label: 'معرض الأعمال', count: completedProjects.length },\n                { id: 'reviews', label: 'التقييمات', count: reviews.length }\n              ].map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id as any)}\n                  className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${\n                    activeTab === tab.id\n                      ? 'border-teal text-teal'\n                      : 'border-transparent text-gray-500 hover:text-gray-700'\n                  }`}\n                >\n                  {tab.label}\n                  {tab.count !== null && (\n                    <span className=\"mr-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs\">\n                      {tab.count}\n                    </span>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* محتوى التبويبات */}\n          {activeTab === 'about' && (\n            <Card className=\"border-0 bg-white/90 backdrop-blur-sm\">\n              <CardHeader>\n                <CardTitle>نبذة عني</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-gray-700 leading-relaxed mb-6\">{craftsman.bio}</p>\n\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <h4 className=\"font-semibold text-navy mb-3\">معلومات إضافية</h4>\n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">تاريخ الانضمام:</span>\n                        <span>مارس 2022</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">اللغات:</span>\n                        <span>{craftsman.languages.join(', ')}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">ساعات العمل:</span>\n                        <span>{craftsman.workingHours}</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div>\n                    <h4 className=\"font-semibold text-navy mb-3\">المهارات</h4>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {craftsman.skills.map((skill, index) => (\n                        <Badge key={index} variant=\"secondary\">\n                          {skill}\n                        </Badge>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {activeTab === 'portfolio' && (\n            <div className=\"space-y-8\">\n              {/* عنوان معرض الأعمال */}\n              <div className=\"text-center\">\n                <div className=\"inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-2 rounded-full text-sm font-medium mb-4\">\n                  🏆 معرض الأعمال\n                </div>\n                <h3 className=\"text-3xl font-bold text-navy mb-4\">\n                  أعمال تحكي قصص\n                  <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-teal to-navy\">النجاح</span>\n                </h3>\n                <p className=\"text-gray-600 max-w-2xl mx-auto\">\n                  اكتشف مجموعة من أفضل المشاريع التي تم إنجازها بجودة استثنائية\n                </p>\n              </div>\n\n              {/* شبكة المشاريع */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n                {completedProjects.map((project, index) => (\n                  <div\n                    key={project.id}\n                    style={{\n                      animation: `fadeInUp 0.6s ease-out ${index * 0.1}s both`\n                    }}\n                  >\n                    <CompletedProject\n                      project={project}\n                      showClientInfo={true}\n                    />\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          {activeTab === 'reviews' && (\n            <div className=\"space-y-4\">\n              {reviews.map((review) => (\n                <Card key={review.id} className=\"border-0 bg-white/90 backdrop-blur-sm\">\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex items-start justify-between mb-4\">\n                      <div>\n                        <h4 className=\"font-semibold text-navy\">{review.clientName}</h4>\n                        <p className=\"text-sm text-gray-500\">{review.projectTitle}</p>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <div className=\"flex items-center ml-2\">\n                          {[...Array(5)].map((_, i) => (\n                            <svg\n                              key={i}\n                              className={`w-4 h-4 ${\n                                i < review.rating ? 'text-yellow-500' : 'text-gray-300'\n                              }`}\n                              fill=\"currentColor\"\n                              viewBox=\"0 0 20 20\"\n                            >\n                              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                            </svg>\n                          ))}\n                        </div>\n                        <span className=\"text-sm text-gray-500\">{review.date}</span>\n                      </div>\n                    </div>\n                    <p className=\"text-gray-700 italic\">\"{review.comment}\"</p>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          )}\n        </div>\n\n        {/* CSS Animations */}\n        <style jsx>{`\n          @keyframes fadeInUp {\n            from {\n              opacity: 0;\n              transform: translateY(30px);\n            }\n            to {\n              opacity: 1;\n              transform: translateY(0);\n            }\n          }\n        `}</style>\n      </div>\n    </MainLayout>\n  );\n};\n\nexport default CraftsmanProfilePage;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;;AA6BA,MAAM,uBAAuB;;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IAE9E,sBAAsB;IACtB,MAAM,YAAY;QAChB,IAAI,OAAO,EAAE;QACb,MAAM;QACN,YAAY;QACZ,KAAK;QACL,UAAU;QACV,QAAQ;QACR,cAAc;QACd,eAAe;QACf,YAAY;QACZ,QAAQ;YAAC;YAAS;YAAc;YAAS;YAAS;YAAe;SAAa;QAC9E,QAAQ;QACR,UAAU;QACV,cAAc;QACd,YAAY;QACZ,UAAU;QACV,WAAW;YAAC;YAAW;SAAa;QACpC,cAAc;IAChB;IAEA,gCAAgC;IAChC,MAAM,oBAAwC;QAC5C;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;YACR,cAAc;gBACZ;gBACA;aACD;YACD,aAAa;gBACX;gBACA;aACD;YACD,QAAQ;gBAAC;gBAAS;gBAAS;aAAQ;YACnC,cAAc;YACd,cAAc;YACd,QAAQ;gBACN,MAAM;gBACN,UAAU;YACZ;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;YACR,cAAc;gBACZ;gBACA;aACD;YACD,aAAa;gBACX;gBACA;aACD;YACD,QAAQ;gBAAC;gBAAS;gBAAS;aAAQ;YACnC,cAAc;YACd,cAAc;YACd,QAAQ;gBACN,MAAM;gBACN,UAAU;YACZ;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;YACR,cAAc;gBACZ;aACD;YACD,aAAa;gBACX;aACD;YACD,QAAQ;gBAAC;gBAAS;gBAAS;aAAa;YACxC,cAAc;YACd,cAAc;YACd,QAAQ;gBACN,MAAM;gBACN,UAAU;YACZ;QACF;KACD;IAED,MAAM,UAAU;QACd;YACE,IAAI;YACJ,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,MAAM;YACN,cAAc;QAChB;KACD;IAED,qBACE,6LAAC,6IAAA,CAAA,UAAU;kBACT,cAAA,6LAAC;sDAAc;;8BAEb,6LAAC;8DAAc;;sCACb,6LAAC;sEAAc;;;;;;sCACf,6LAAC;sEAAc;;;;;;sCACf,6LAAC;sEAAc;;;;;;;;;;;;8BAIjB,6LAAC;8DAAc;8BACb,cAAA,6LAAC;wBAA8B,OAAO;4BACpC,iBAAiB;4BACjB,gBAAgB;wBAClB;kEAHe;;;;;;;;;;;8BAMjB,6LAAC;8DAAc;;sCAEb,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;8EAAc;;sDACb,6LAAC;sFAAc;;8DACb,6LAAC;oDACC,KAAK,UAAU,MAAM;oDACrB,KAAK,UAAU,IAAI;8FACT;;;;;;gDAEX,UAAU,QAAQ,kBACjB,6LAAC;8FAAc;;;;;;gDAEhB,UAAU,QAAQ,kBACjB,6LAAC;8FAAc;8DACb,cAAA,6LAAC;wDAAmC,MAAK;wDAAe,SAAQ;kGAAjD;kEACb,cAAA,6LAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAkiB,UAAS;;;;;;;;;;;;;;;;;;;;;;;sDAM9kB,6LAAC;sFAAc;;8DACb,6LAAC;8FAAc;;sEACb,6LAAC;;;8EACC,6LAAC;8GAAa;8EAAqC,UAAU,IAAI;;;;;;8EACjE,6LAAC;8GAAY;8EAA8B,UAAU,UAAU;;;;;;8EAC/D,6LAAC;8GAAc;;sFACb,6LAAC;4EAA6B,MAAK;4EAAO,QAAO;4EAAe,SAAQ;sHAAzD;sFACb,cAAA,6LAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;;wEAEtE,UAAU,QAAQ;;;;;;;;;;;;;sEAIvB,6LAAC;sGAAc;;8EACb,6LAAC;8GAAc;;sFACb,6LAAC;sHAAc;sFACZ;mFAAI,MAAM;6EAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;oFAKC,MAAK;oFACL,SAAQ;8HAJG,CAAC,QAAQ,EAClB,IAAI,KAAK,KAAK,CAAC,UAAU,MAAM,IAAI,oBAAoB,iBACvD;8FAIF,cAAA,6LAAC;wFAAK,GAAE;;;;;;;mFAPH;;;;;;;;;;sFAWX,6LAAC;sHAAe;sFAAsB,UAAU,MAAM;;;;;;sFACtD,6LAAC;sHAAe;;gFAAgB;gFAAE,UAAU,YAAY;gFAAC;;;;;;;;;;;;;8EAE3D,6LAAC;8GAAc;;wEACZ,UAAU,UAAU,CAAC,cAAc;wEAAG;;;;;;;;;;;;;;;;;;;8DAK7C,6LAAC;8FAAc;;sEACb,6LAAC;sGAAc;;8EACb,6LAAC;8GAAc;8EAA+B,UAAU,aAAa;;;;;;8EACrE,6LAAC;8GAAc;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;sGAAc;;8EACb,6LAAC;8GAAc;8EAA+B,UAAU,YAAY;;;;;;8EACpE,6LAAC;8GAAc;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;sGAAc;;8EACb,6LAAC;8GAAc;8EAA8B;;;;;;8EAC7C,6LAAC;8GAAc;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;sGAAc;;8EACb,6LAAC;8GAAc;8EAA+B,UAAU,YAAY;;;;;;8EACpE,6LAAC;8GAAc;8EAAwB;;;;;;;;;;;;;;;;;;8DAI3C,6LAAC;8FAAc;8DACZ,UAAU,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACxC,6LAAC,oIAAA,CAAA,QAAK;4DAAa,SAAQ;sEACxB;2DADS;;;;;;;;;;8DAMhB,6LAAC;8FAAc;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;sEAAyE;;;;;;sEAG3F,6LAAC,qIAAA,CAAA,SAAM;4DAAC,SAAQ;sEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAUpC,6LAAC;sEAAc;sCACb,cAAA,6LAAC;0EAAc;0CACZ;oCACC;wCAAE,IAAI;wCAAS,OAAO;wCAAY,OAAO;oCAAK;oCAC9C;wCAAE,IAAI;wCAAa,OAAO;wCAAgB,OAAO,kBAAkB,MAAM;oCAAC;oCAC1E;wCAAE,IAAI;wCAAW,OAAO;wCAAa,OAAO,QAAQ,MAAM;oCAAC;iCAC5D,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;wCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;kFACvB,CAAC,2DAA2D,EACrE,cAAc,IAAI,EAAE,GAChB,0BACA,wDACJ;;4CAED,IAAI,KAAK;4CACT,IAAI,KAAK,KAAK,sBACb,6LAAC;0FAAe;0DACb,IAAI,KAAK;;;;;;;uCAXT,IAAI,EAAE;;;;;;;;;;;;;;;wBAoBlB,cAAc,yBACb,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;sFAAY;sDAAsC,UAAU,GAAG;;;;;;sDAEhE,6LAAC;sFAAc;;8DACb,6LAAC;;;sEACC,6LAAC;sGAAa;sEAA+B;;;;;;sEAC7C,6LAAC;sGAAc;;8EACb,6LAAC;8GAAc;;sFACb,6LAAC;sHAAe;sFAAgB;;;;;;sFAChC,6LAAC;;sFAAK;;;;;;;;;;;;8EAER,6LAAC;8GAAc;;sFACb,6LAAC;sHAAe;sFAAgB;;;;;;sFAChC,6LAAC;;sFAAM,UAAU,SAAS,CAAC,IAAI,CAAC;;;;;;;;;;;;8EAElC,6LAAC;8GAAc;;sFACb,6LAAC;sHAAe;sFAAgB;;;;;;sFAChC,6LAAC;;sFAAM,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;;;8DAKnC,6LAAC;;;sEACC,6LAAC;sGAAa;sEAA+B;;;;;;sEAC7C,6LAAC;sGAAc;sEACZ,UAAU,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC5B,6LAAC,oIAAA,CAAA,QAAK;oEAAa,SAAQ;8EACxB;mEADS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBAWzB,cAAc,6BACb,6LAAC;sEAAc;;8CAEb,6LAAC;8EAAc;;sDACb,6LAAC;sFAAc;sDAA6G;;;;;;sDAG5H,6LAAC;sFAAa;;gDAAoC;8DAEhD,6LAAC;8FAAe;8DAAmE;;;;;;;;;;;;sDAErF,6LAAC;sFAAY;sDAAkC;;;;;;;;;;;;8CAMjD,6LAAC;8EAAc;8CACZ,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC/B,6LAAC;4CAEC,OAAO;gDACL,WAAW,CAAC,uBAAuB,EAAE,QAAQ,IAAI,MAAM,CAAC;4CAC1D;;sDAEA,cAAA,6LAAC,sJAAA,CAAA,UAAgB;gDACf,SAAS;gDACT,gBAAgB;;;;;;2CAPb,QAAQ,EAAE;;;;;;;;;;;;;;;;wBAexB,cAAc,2BACb,6LAAC;sEAAc;sCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,mIAAA,CAAA,OAAI;oCAAiB,WAAU;8CAC9B,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;0FAAc;;kEACb,6LAAC;;;0EACC,6LAAC;0GAAa;0EAA2B,OAAO,UAAU;;;;;;0EAC1D,6LAAC;0GAAY;0EAAyB,OAAO,YAAY;;;;;;;;;;;;kEAE3D,6LAAC;kGAAc;;0EACb,6LAAC;0GAAc;0EACZ;uEAAI,MAAM;iEAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wEAKC,MAAK;wEACL,SAAQ;kHAJG,CAAC,QAAQ,EAClB,IAAI,OAAO,MAAM,GAAG,oBAAoB,iBACxC;kFAIF,cAAA,6LAAC;4EAAK,GAAE;;;;;;;uEAPH;;;;;;;;;;0EAWX,6LAAC;0GAAe;0EAAyB,OAAO,IAAI;;;;;;;;;;;;;;;;;;0DAGxD,6LAAC;0FAAY;;oDAAuB;oDAAE,OAAO,OAAO;oDAAC;;;;;;;;;;;;;mCAzB9C,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiDpC;GAnZM;;QACW,qIAAA,CAAA,YAAS;;;KADpB;uCAqZS"}}, {"offset": {"line": 4150, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}