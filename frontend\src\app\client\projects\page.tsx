'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';

const ClientProjectsPage = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const projects = [
    {
      id: 1,
      title: 'تجديد المطبخ الرئيسي',
      description: 'تجديد كامل للمطبخ مع تركيب خزائن جديدة وأجهزة حديثة',
      status: 'نشط',
      budget: '₺8,000',
      offers: 12,
      deadline: '2024-03-15',
      category: 'نجارة',
      priority: 'عالية',
      createdAt: '2024-02-01',
      location: 'دمشق - المزة'
    },
    {
      id: 2,
      title: 'إصلاح نظام السباكة',
      description: 'إصلاح تسريب في الحمام الرئيسي وتجديد الأنابيب',
      status: 'في الانتظار',
      budget: '₺2,500',
      offers: 8,
      deadline: '2024-02-28',
      category: 'سباكة',
      priority: 'متوسطة',
      createdAt: '2024-01-25',
      location: 'دمشق - كفرسوسة'
    },
    {
      id: 3,
      title: 'دهان الشقة الكاملة',
      description: 'دهان جميع الغرف بألوان حديثة مع تحضير الجدران',
      status: 'مكتمل',
      budget: '₺3,200',
      offers: 15,
      deadline: '2024-01-20',
      category: 'دهان',
      priority: 'منخفضة',
      createdAt: '2024-01-10',
      location: 'دمشق - أبو رمانة'
    },
    {
      id: 4,
      title: 'تركيب نظام كهرباء ذكي',
      description: 'تركيب نظام إضاءة ذكي ومفاتيح تحكم عن بعد',
      status: 'جاري التنفيذ',
      budget: '₺4,500',
      offers: 6,
      deadline: '2024-03-10',
      category: 'كهرباء',
      priority: 'عالية',
      createdAt: '2024-02-05',
      location: 'دمشق - المالكي'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'نشط':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'في الانتظار':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'مكتمل':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'جاري التنفيذ':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'ملغي':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'عالية':
        return 'text-red-600';
      case 'متوسطة':
        return 'text-yellow-600';
      case 'منخفضة':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  const filteredProjects = projects.filter(project => {
    const matchesFilter = activeFilter === 'all' || project.status === activeFilter;
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const stats = [
    {
      title: 'إجمالي المشاريع',
      value: projects.length.toString(),
      color: 'text-blue-600'
    },
    {
      title: 'المشاريع النشطة',
      value: projects.filter(p => p.status === 'نشط').length.toString(),
      color: 'text-green-600'
    },
    {
      title: 'المشاريع المكتملة',
      value: projects.filter(p => p.status === 'مكتمل').length.toString(),
      color: 'text-purple-600'
    },
    {
      title: 'إجمالي العروض',
      value: projects.reduce((sum, p) => sum + p.offers, 0).toString(),
      color: 'text-orange-600'
    }
  ];

  return (
    <ProtectedRoute requiredRoles="client">
      <DashboardLayout 
        title="مشاريعي"
        subtitle="إدارة ومتابعة جميع مشاريعك"
        actions={
          <Link href="/projects/create">
            <Button className="bg-gradient-to-r from-navy to-teal">
              إنشاء مشروع جديد
            </Button>
          </Link>
        }
      >
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className={`text-3xl ${stat.color}`}>
                    📊
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Filters and Search */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
              {/* Filters */}
              <div className="flex flex-wrap gap-2">
                {[
                  { id: 'all', label: 'الكل' },
                  { id: 'نشط', label: 'نشط' },
                  { id: 'في الانتظار', label: 'في الانتظار' },
                  { id: 'جاري التنفيذ', label: 'جاري التنفيذ' },
                  { id: 'مكتمل', label: 'مكتمل' }
                ].map((filter) => (
                  <button
                    key={filter.id}
                    onClick={() => setActiveFilter(filter.id)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                      activeFilter === filter.id
                        ? 'bg-navy text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {filter.label}
                  </button>
                ))}
              </div>

              {/* Search */}
              <div className="flex items-center space-x-4 space-x-reverse">
                <input
                  type="text"
                  placeholder="البحث في المشاريع..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                />
              </div>
            </div>
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredProjects.map((project) => (
              <div key={project.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{project.title}</h3>
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">{project.description}</p>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(project.status)}`}>
                    {project.status}
                  </span>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div>
                    <span className="text-gray-500">الميزانية:</span>
                    <span className="font-semibold text-gray-900 mr-2">{project.budget}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">العروض:</span>
                    <span className="font-semibold text-gray-900 mr-2">{project.offers}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">الموعد النهائي:</span>
                    <span className="font-semibold text-gray-900 mr-2">{project.deadline}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">الأولوية:</span>
                    <span className={`font-semibold mr-2 ${getPriorityColor(project.priority)}`}>{project.priority}</span>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-500">
                    <span className="bg-gray-100 px-2 py-1 rounded">{project.category}</span>
                    <span>📍 {project.location}</span>
                  </div>
                  <div className="flex space-x-2 space-x-reverse">
                    <Link href={`/projects/${project.id}`}>
                      <Button size="sm" variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                        عرض التفاصيل
                      </Button>
                    </Link>
                    {project.status === 'نشط' && (
                      <Link href={`/projects/${project.id}/offers`}>
                        <Button size="sm" className="bg-gradient-to-r from-navy to-teal">
                          العروض ({project.offers})
                        </Button>
                      </Link>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredProjects.length === 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-6xl mb-4">📋</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد مشاريع</h3>
              <p className="text-gray-600 mb-6">لم يتم العثور على مشاريع بالمعايير المحددة</p>
              <Link href="/projects/create">
                <Button className="bg-gradient-to-r from-navy to-teal">
                  إنشاء مشروع جديد
                </Button>
              </Link>
            </div>
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default ClientProjectsPage;
