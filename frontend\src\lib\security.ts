import bcrypt from 'bcryptjs';
import crypto from 'crypto';
import { NextRequest } from 'next/server';

// تشفير كلمة المرور
export async function hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return await bcrypt.hash(password, saltRounds);
}

// التحقق من كلمة المرور
export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return await bcrypt.compare(password, hashedPassword);
}

// إنشاء رمز عشوائي آمن
export function generateSecureToken(length: number = 32): string {
  return crypto.randomBytes(length).toString('hex');
}

// إنشاء رمز تأكيد البريد الإلكتروني
export function generateEmailVerificationToken(): string {
  return generateSecureToken(32);
}

// إنشاء رمز إعادة تعيين كلمة المرور
export function generatePasswordResetToken(): string {
  return generateSecureToken(32);
}

// تنظيف وتعقيم النصوص
export function sanitizeInput(input: string): string {
  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // إزالة script tags
    .replace(/javascript:/gi, '') // إزالة javascript:
    .replace(/on\w+\s*=/gi, ''); // إزالة event handlers
}

// التحقق من قوة كلمة المرور
export function validatePasswordStrength(password: string): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('كلمة المرور يجب أن تكون 8 أحرف على الأقل');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل');
  }

  if (!/[0-9]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رقم واحد على الأقل');
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// التحقق من صحة البريد الإلكتروني
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// التحقق من صحة رقم الهاتف السوري
export function validateSyrianPhone(phone: string): boolean {
  // أرقام الهواتف السورية
  const syrianPhoneRegex = /^(\+963|0)?9[0-9]{8}$/;
  return syrianPhoneRegex.test(phone.replace(/\s/g, ''));
}

// الحصول على IP address من الطلب
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return request.ip || 'unknown';
}

// الحصول على User Agent من الطلب
export function getUserAgent(request: NextRequest): string {
  return request.headers.get('user-agent') || 'unknown';
}

// تحديد معدل الطلبات (Rate Limiting)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(
  identifier: string, 
  maxRequests: number = 10, 
  windowMs: number = 60000 // دقيقة واحدة
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const windowStart = now - windowMs;
  
  // تنظيف الطلبات القديمة
  for (const [key, value] of rateLimitMap.entries()) {
    if (value.resetTime < windowStart) {
      rateLimitMap.delete(key);
    }
  }
  
  const current = rateLimitMap.get(identifier);
  
  if (!current) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return { allowed: true, remaining: maxRequests - 1, resetTime: now + windowMs };
  }
  
  if (current.resetTime < now) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return { allowed: true, remaining: maxRequests - 1, resetTime: now + windowMs };
  }
  
  if (current.count >= maxRequests) {
    return { allowed: false, remaining: 0, resetTime: current.resetTime };
  }
  
  current.count++;
  return { allowed: true, remaining: maxRequests - current.count, resetTime: current.resetTime };
}

// تشفير البيانات الحساسة
export function encryptSensitiveData(data: string, key?: string): string {
  const secretKey = key || process.env.ENCRYPTION_KEY || 'default-secret-key';
  const cipher = crypto.createCipher('aes-256-cbc', secretKey);
  let encrypted = cipher.update(data, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  return encrypted;
}

// فك تشفير البيانات الحساسة
export function decryptSensitiveData(encryptedData: string, key?: string): string {
  const secretKey = key || process.env.ENCRYPTION_KEY || 'default-secret-key';
  const decipher = crypto.createDecipher('aes-256-cbc', secretKey);
  let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  return decrypted;
}

// إنشاء CSRF token
export function generateCSRFToken(): string {
  return generateSecureToken(32);
}

// التحقق من CSRF token
export function verifyCSRFToken(token: string, sessionToken: string): boolean {
  return token === sessionToken;
}

// تسجيل محاولات تسجيل الدخول الفاشلة
const loginAttempts = new Map<string, { count: number; lastAttempt: number }>();

export function recordFailedLogin(identifier: string): void {
  const now = Date.now();
  const current = loginAttempts.get(identifier);
  
  if (!current) {
    loginAttempts.set(identifier, { count: 1, lastAttempt: now });
    return;
  }
  
  // إعادة تعيين العداد إذا مر أكثر من ساعة
  if (now - current.lastAttempt > 3600000) {
    loginAttempts.set(identifier, { count: 1, lastAttempt: now });
    return;
  }
  
  current.count++;
  current.lastAttempt = now;
}

export function isAccountLocked(identifier: string): boolean {
  const current = loginAttempts.get(identifier);
  if (!current) return false;
  
  const now = Date.now();
  const maxAttempts = 5;
  const lockDuration = 3600000; // ساعة واحدة
  
  return current.count >= maxAttempts && (now - current.lastAttempt) < lockDuration;
}

export function clearFailedLogins(identifier: string): void {
  loginAttempts.delete(identifier);
}

// التحقق من الملفات المرفوعة
export function validateUploadedFile(file: File): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];
  const maxSize = 5 * 1024 * 1024; // 5MB
  const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
  
  if (file.size > maxSize) {
    errors.push('حجم الملف يجب أن يكون أقل من 5MB');
  }
  
  if (!allowedTypes.includes(file.type)) {
    errors.push('نوع الملف غير مدعوم. الأنواع المدعومة: JPEG, PNG, GIF, WebP');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}
