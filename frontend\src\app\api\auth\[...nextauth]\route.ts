import NextAuth from 'next-auth';
import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import FacebookProvider from 'next-auth/providers/facebook';
import { prisma } from '@/lib/prisma';
import { verifyPassword, checkLoginAttempts, recordFailedLogin, resetLoginAttempts, logAuditAction } from '@/lib/security';

const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials, req) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // البحث عن المستخدم في قاعدة البيانات
          const user = await prisma.user.findUnique({
            where: { email: credentials.email },
            include: {
              craftsmanProfile: true,
            },
          });

          if (!user) {
            return null;
          }

          // التحقق من حالة الحساب
          if (!user.isActive) {
            throw new Error('الحساب غير نشط');
          }

          // التحقق من محاولات تسجيل الدخول
          const canLogin = await checkLoginAttempts(user.id);
          if (!canLogin) {
            throw new Error('تم قفل الحساب مؤقتاً بسبب محاولات تسجيل دخول متعددة');
          }

          // التحقق من كلمة المرور
          if (!user.password) {
            throw new Error('كلمة المرور غير محددة');
          }

          const isValidPassword = await verifyPassword(credentials.password, user.password);
          if (!isValidPassword) {
            await recordFailedLogin(user.id);
            await logAuditAction(
              user.id,
              'FAILED_LOGIN',
              'User',
              user.id,
              null,
              null,
              req?.headers?.['x-forwarded-for'] as string || req?.ip,
              req?.headers?.['user-agent'] as string
            );
            return null;
          }

          // تسجيل دخول ناجح
          await resetLoginAttempts(user.id);
          await logAuditAction(
            user.id,
            'LOGIN',
            'User',
            user.id,
            null,
            null,
            req?.headers?.['x-forwarded-for'] as string || req?.ip,
            req?.headers?.['user-agent'] as string
          );

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            avatar: user.avatar,
            isVerified: user.isVerified,
          };
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      }
    }),
    // تعطيل Google و Facebook مؤقتاً للتطوير
    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? [
      GoogleProvider({
        clientId: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      })
    ] : []),
    ...(process.env.FACEBOOK_CLIENT_ID && process.env.FACEBOOK_CLIENT_SECRET ? [
      FacebookProvider({
        clientId: process.env.FACEBOOK_CLIENT_ID,
        clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
      })
    ] : []),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user, account }) {
      // إضافة معلومات إضافية للـ token
      if (user) {
        token.role = user.role || 'client';
      }

      // للتسجيل عبر Google أو Facebook
      if (account && (account.provider === 'google' || account.provider === 'facebook')) {
        token.role = 'client'; // افتراضي للتطوير
      }

      return token;
    },
    async session({ session, token }) {
      // إضافة معلومات إضافية للـ session
      if (token && session.user) {
        session.user.id = token.sub || '1';
        session.user.role = (token.role as 'client' | 'craftsman' | 'admin') || 'client';
        // التأكد من وجود name
        if (!session.user.name && session.user.email) {
          const userName = session.user.email.split('@')[0];
          session.user.name = userName.charAt(0).toUpperCase() + userName.slice(1);
        }
      }
      return session;
    },
  },
  pages: {
    signIn: '/login',
    signUp: '/register',
    error: '/auth/error',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
