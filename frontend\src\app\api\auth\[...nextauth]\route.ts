import NextAuth from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';

const authOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials: any) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        // حسابات تجريبية ثابتة للتطوير
        const testAccounts = [
          { email: '<EMAIL>', password: 'Test123!@#', role: 'ADMIN', name: 'مدير النظام' },
          { email: '<EMAIL>', password: 'Test123!@#', role: 'CLIENT', name: 'أحمد محمد' },
          { email: '<EMAIL>', password: 'Test123!@#', role: 'CRAFTSMAN', name: 'محمد النجار' },
          { email: '<EMAIL>', password: 'Test123!@#', role: 'CRAFTSMAN', name: 'سارة الكهربائية' },
        ];

        const testUser = testAccounts.find(
          account => account.email === credentials.email && account.password === credentials.password
        );

        if (testUser) {
          return {
            id: testUser.email.split('@')[0],
            email: testUser.email,
            name: testUser.name,
            role: testUser.role,
            isVerified: true,
          };
        }

        return null;
      }
    }),
  ],
  callbacks: {
    async jwt({ token, user }: any) {
      if (user) {
        token.role = user.role;
        token.isVerified = user.isVerified;
      }
      return token;
    },
    async session({ session, token }: any) {
      if (token && session.user) {
        session.user.id = token.sub || '1';
        session.user.role = token.role;
        session.user.isVerified = token.isVerified;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };