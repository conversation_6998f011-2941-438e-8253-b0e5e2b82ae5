import NextAuth from 'next-auth';
import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import Facebook<PERSON>rovider from 'next-auth/providers/facebook';

const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // للتطوير: قبول أي بيانات اعتماد صحيحة
          // في الإنتاج: استدعاء API حقيقي
          if (credentials.email && credentials.password.length >= 6) {
            const userName = credentials.email.split('@')[0];
            // استخدام الدور المرسل أو افتراضي
            const role = (credentials as any).role || 'client';
            return {
              id: '1',
              email: credentials.email,
              name: userName.charAt(0).toUpperCase() + userName.slice(1),
              role: role,
            };
          }

          return null;
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      }
    }),
    // تعطيل Google و Facebook مؤقتاً للتطوير
    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? [
      GoogleProvider({
        clientId: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
      })
    ] : []),
    ...(process.env.FACEBOOK_CLIENT_ID && process.env.FACEBOOK_CLIENT_SECRET ? [
      FacebookProvider({
        clientId: process.env.FACEBOOK_CLIENT_ID,
        clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
      })
    ] : []),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user, account }) {
      // إضافة معلومات إضافية للـ token
      if (user) {
        token.role = user.role || 'client';
      }

      // للتسجيل عبر Google أو Facebook
      if (account && (account.provider === 'google' || account.provider === 'facebook')) {
        token.role = 'client'; // افتراضي للتطوير
      }

      return token;
    },
    async session({ session, token }) {
      // إضافة معلومات إضافية للـ session
      if (token && session.user) {
        session.user.id = token.sub || '1';
        session.user.role = (token.role as 'client' | 'craftsman' | 'admin') || 'client';
        // التأكد من وجود name
        if (!session.user.name && session.user.email) {
          const userName = session.user.email.split('@')[0];
          session.user.name = userName.charAt(0).toUpperCase() + userName.slice(1);
        }
      }
      return session;
    },
  },
  pages: {
    signIn: '/login',
    signUp: '/register',
    error: '/auth/error',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
