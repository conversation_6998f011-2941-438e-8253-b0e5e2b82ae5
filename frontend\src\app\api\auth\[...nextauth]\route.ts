import NextAuth from 'next-auth';
import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';

const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null;
        }

        try {
          // للتطوير: استخدام الحسابات التجريبية المحددة مسبقاً
          const testAccounts = [
            { email: '<EMAIL>', password: 'Test123!@#', role: 'ADMIN', name: 'مدير النظام' },
            { email: '<EMAIL>', password: 'Test123!@#', role: 'CLIENT', name: 'أحمد محمد' },
            { email: '<EMAIL>', password: 'Test123!@#', role: 'CRAFTSMAN', name: 'محمد النجار' },
            { email: '<EMAIL>', password: 'Test123!@#', role: 'CRAFTSMAN', name: 'سارة الكهربائية' },
          ];

          const testUser = testAccounts.find(
            account => account.email === credentials.email && account.password === credentials.password
          );

          if (testUser) {
            return {
              id: testUser.email.split('@')[0],
              email: testUser.email,
              name: testUser.name,
              role: testUser.role as 'ADMIN' | 'CLIENT' | 'CRAFTSMAN',
              isVerified: true,
            };
          }

          // للحسابات الجديدة: التحقق من قاعدة البيانات
          // هذا سيتم تطبيقه لاحقاً عندما تكون قاعدة البيانات جاهزة

          return null;
        } catch (error) {
          console.error('Auth error:', error);
          return null;
        }
      }
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role;
        token.isVerified = user.isVerified;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.sub || '1';
        session.user.role = token.role as 'CLIENT' | 'CRAFTSMAN' | 'ADMIN';
        session.user.isVerified = token.isVerified as boolean;
      }
      return session;
    },
  },
  pages: {
    signIn: '/auth/signin',
    error: '/auth/error',
  },
  secret: process.env.NEXTAUTH_SECRET,
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
