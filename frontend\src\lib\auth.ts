import { NextAuthOptions } from 'next-auth';
import CredentialsProvider from 'next-auth/providers/credentials';
import GoogleProvider from 'next-auth/providers/google';
import { prisma } from './prisma';
import { verifyPassword, hashPassword } from './security';

export const authOptions: NextAuthOptions = {
  providers: [
    // تسجيل الدخول بالبريد الإلكتروني وكلمة المرور
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'البريد الإلكتروني', type: 'email' },
        password: { label: 'كلمة المرور', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('البريد الإلكتروني وكلمة المرور مطلوبان');
        }

        try {
          // البحث عن المستخدم
          const user = await prisma.user.findUnique({
            where: { email: credentials.email },
            include: {
              craftsmanProfile: true
            }
          });

          if (!user) {
            throw new Error('البريد الإلكتروني أو كلمة المرور غير صحيحة');
          }

          // التحقق من حالة الحساب
          if (!user.isActive) {
            throw new Error('الحساب معطل. يرجى التواصل مع الدعم');
          }

          // التحقق من كلمة المرور
          if (!user.password) {
            throw new Error('يرجى استخدام تسجيل الدخول عبر Google');
          }

          const isValidPassword = await verifyPassword(credentials.password, user.password);
          if (!isValidPassword) {
            throw new Error('البريد الإلكتروني أو كلمة المرور غير صحيحة');
          }

          // تحديث آخر تسجيل دخول
          await prisma.user.update({
            where: { id: user.id },
            data: { 
              lastLoginAt: new Date(),
              loginAttempts: 0 // إعادة تعيين محاولات تسجيل الدخول الفاشلة
            }
          });

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            role: user.role,
            avatar: user.avatar,
            isVerified: user.isVerified,
            location: user.location
          };
        } catch (error: any) {
          throw new Error(error.message || 'حدث خطأ في تسجيل الدخول');
        }
      }
    }),

    // تسجيل الدخول عبر Google
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    })
  ],

  callbacks: {
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google') {
        try {
          // البحث عن المستخدم أو إنشاؤه
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email! }
          });

          if (existingUser) {
            // تحديث معلومات المستخدم من Google
            await prisma.user.update({
              where: { id: existingUser.id },
              data: {
                name: user.name || existingUser.name,
                avatar: user.image || existingUser.avatar,
                lastLoginAt: new Date(),
                emailVerifiedAt: existingUser.emailVerifiedAt || new Date(),
                isVerified: true
              }
            });
          } else {
            // إنشاء مستخدم جديد
            await prisma.user.create({
              data: {
                email: user.email!,
                name: user.name || 'مستخدم جديد',
                avatar: user.image,
                role: 'CLIENT', // الدور الافتراضي
                isActive: true,
                isVerified: true,
                emailVerifiedAt: new Date(),
                lastLoginAt: new Date()
              }
            });
          }

          return true;
        } catch (error) {
          console.error('خطأ في تسجيل الدخول عبر Google:', error);
          return false;
        }
      }

      return true;
    },

    async jwt({ token, user, account }) {
      if (user) {
        // إضافة معلومات إضافية للـ token
        const dbUser = await prisma.user.findUnique({
          where: { email: user.email! },
          include: {
            craftsmanProfile: true
          }
        });

        if (dbUser) {
          token.id = dbUser.id;
          token.role = dbUser.role;
          token.isVerified = dbUser.isVerified;
          token.location = dbUser.location;
          token.avatar = dbUser.avatar;
        }
      }

      return token;
    },

    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string;
        session.user.role = token.role as string;
        session.user.isVerified = token.isVerified as boolean;
        session.user.location = token.location as string;
        session.user.avatar = token.avatar as string;
      }

      return session;
    }
  },

  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error',
    verifyRequest: '/auth/verify-request',
    newUser: '/auth/welcome'
  },

  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 يوم
  },

  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 يوم
  },

  secret: process.env.NEXTAUTH_SECRET,

  debug: process.env.NODE_ENV === 'development',
};

// دالة مساعدة للتحقق من الأذونات
export function hasPermission(userRole: string, requiredRoles: string | string[]): boolean {
  if (typeof requiredRoles === 'string') {
    return userRole === requiredRoles;
  }
  
  return requiredRoles.includes(userRole);
}

// دالة مساعدة للتحقق من ملكية المورد
export async function isResourceOwner(userId: string, resourceType: string, resourceId: string): Promise<boolean> {
  try {
    switch (resourceType) {
      case 'project':
        const project = await prisma.project.findUnique({
          where: { id: resourceId }
        });
        return project?.clientId === userId;

      case 'offer':
        const offer = await prisma.offer.findUnique({
          where: { id: resourceId }
        });
        return offer?.craftsmanId === userId || offer?.clientId === userId;

      case 'message':
        const message = await prisma.message.findUnique({
          where: { id: resourceId }
        });
        return message?.senderId === userId || message?.receiverId === userId;

      default:
        return false;
    }
  } catch (error) {
    console.error('خطأ في التحقق من ملكية المورد:', error);
    return false;
  }
}

// دالة تسجيل الأنشطة
export async function logActivity(
  userId: string,
  action: string,
  entity: string,
  entityId?: string,
  oldValues?: any,
  newValues?: any,
  ipAddress?: string,
  userAgent?: string
) {
  try {
    await prisma.auditLog.create({
      data: {
        userId,
        action,
        entity,
        entityId,
        oldValues: oldValues ? JSON.stringify(oldValues) : null,
        newValues: newValues ? JSON.stringify(newValues) : null,
        ipAddress,
        userAgent
      }
    });
  } catch (error) {
    console.error('خطأ في تسجيل النشاط:', error);
  }
}
