import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { databaseConfig } from './config/database.config';
import { User } from './entities/user.entity';
import { Job } from './entities/job.entity';
import { Offer } from './entities/offer.entity';
import { Message } from './entities/message.entity';
import { Transaction } from './entities/transaction.entity';
import { Review } from './entities/review.entity';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { JobsModule } from './jobs/jobs.module';
import { OffersModule } from './offers/offers.module';
import { ChatModule } from './chat/chat.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    TypeOrmModule.forRoot({
      ...databaseConfig,
      entities: [User, Job, Offer, Message, Transaction, Review],
    }),
    AuthModule,
    UsersModule,
    JobsModule,
    OffersModule,
    ChatModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
