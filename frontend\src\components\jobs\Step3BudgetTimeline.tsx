'use client';

import React, { useState } from 'react';
import { useJobForm } from '@/context/JobFormContext';

const Step3BudgetTimeline = () => {
  const { formData, updateFormData } = useJobForm();
  const [errors, setErrors] = useState({
    budget: '',
    deadline: '',
  });

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      budget: '',
      deadline: '',
    };

    if (!formData.budget || formData.budget <= 0) {
      newErrors.budget = 'يرجى تحديد ميزانية صالحة للمشروع';
      isValid = false;
    }

    if (!formData.deadline) {
      newErrors.deadline = 'يرجى تحديد الموعد النهائي للمشروع';
      isValid = false;
    } else {
      const selectedDate = new Date(formData.deadline);
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (selectedDate < today) {
        newErrors.deadline = 'يجب أن يكون الموعد النهائي في المستقبل';
        isValid = false;
      }
    }

    setErrors(newErrors);
    return isValid;
  };

  // Get minimum date (today) for the deadline input
  const getMinDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  return (
    <div>
      <h2 className="text-xl font-bold text-navy mb-6">الميزانية والجدول الزمني</h2>

      <div className="space-y-6">
        <div>
          <label htmlFor="budget" className="block text-gray-700 font-medium mb-2">
            ميزانية المشروع (بالليرة السورية) <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <span className="text-gray-500">ل.س</span>
            </div>
            <input
              type="number"
              id="budget"
              value={formData.budget || ''}
              onChange={(e) => updateFormData({ budget: parseFloat(e.target.value) || 0 })}
              min="0"
              step="1000"
              className="w-full px-4 py-2 pr-12 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
              placeholder="أدخل الميزانية المتوقعة"
            />
          </div>
          {errors.budget && <p className="mt-1 text-red-500 text-sm">{errors.budget}</p>}

          <div className="mt-2">
            <div className="flex items-center space-x-4 space-x-reverse">
              <button
                type="button"
                onClick={() => updateFormData({ budget: 50000 })}
                className={`px-3 py-1 rounded-full text-sm ${
                  formData.budget === 50000 ? 'bg-navy text-white' : 'bg-gray-100 text-gray-700'
                }`}
              >
                50,000 ل.س
              </button>
              <button
                type="button"
                onClick={() => updateFormData({ budget: 100000 })}
                className={`px-3 py-1 rounded-full text-sm ${
                  formData.budget === 100000 ? 'bg-navy text-white' : 'bg-gray-100 text-gray-700'
                }`}
              >
                100,000 ل.س
              </button>
              <button
                type="button"
                onClick={() => updateFormData({ budget: 250000 })}
                className={`px-3 py-1 rounded-full text-sm ${
                  formData.budget === 250000 ? 'bg-navy text-white' : 'bg-gray-100 text-gray-700'
                }`}
              >
                250,000 ل.س
              </button>
              <button
                type="button"
                onClick={() => updateFormData({ budget: 500000 })}
                className={`px-3 py-1 rounded-full text-sm ${
                  formData.budget === 500000 ? 'bg-navy text-white' : 'bg-gray-100 text-gray-700'
                }`}
              >
                500,000 ل.س
              </button>
            </div>
          </div>
        </div>

        <div>
          <label htmlFor="deadline" className="block text-gray-700 font-medium mb-2">
            الموعد النهائي للمشروع <span className="text-red-500">*</span>
          </label>
          <input
            type="date"
            id="deadline"
            value={formData.deadline}
            onChange={(e) => updateFormData({ deadline: e.target.value })}
            min={getMinDate()}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
          />
          {errors.deadline && <p className="mt-1 text-red-500 text-sm">{errors.deadline}</p>}
        </div>

        <div className="bg-beige bg-opacity-50 p-4 rounded-md">
          <h3 className="text-navy font-medium mb-2">نصائح لتحديد الميزانية والموعد النهائي:</h3>
          <ul className="list-disc list-inside text-gray-700 space-y-1 text-sm">
            <li>حدد ميزانية واقعية تتناسب مع حجم وتعقيد المشروع</li>
            <li>ضع في اعتبارك تكلفة المواد والعمالة والوقت المطلوب</li>
            <li>امنح وقتاً كافياً لإنجاز المشروع بجودة عالية</li>
            <li>ضع في الحسبان الوقت اللازم للتعديلات والمراجعات</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Step3BudgetTimeline;
