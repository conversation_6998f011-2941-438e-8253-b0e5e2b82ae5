'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './Card';
import { Button } from './Button';
import StarRating from './StarRating';

interface AddReviewProps {
  onSubmit: (review: { rating: number; comment: string }) => void;
  loading?: boolean;
  className?: string;
}

const AddReview: React.FC<AddReviewProps> = ({
  onSubmit,
  loading = false,
  className = ''
}) => {
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (rating === 0) {
      newErrors.rating = 'يرجى اختيار تقييم';
    }

    if (!comment.trim()) {
      newErrors.comment = 'يرجى كتابة تعليق';
    } else if (comment.trim().length < 10) {
      newErrors.comment = 'التعليق يجب أن يكون 10 أحرف على الأقل';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    onSubmit({
      rating,
      comment: comment.trim()
    });

    // إعادة تعيين النموذج
    setRating(0);
    setComment('');
    setErrors({});
  };

  const handleRatingChange = (newRating: number) => {
    setRating(newRating);
    if (errors.rating) {
      setErrors(prev => ({ ...prev, rating: '' }));
    }
  };

  const handleCommentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setComment(e.target.value);
    if (errors.comment) {
      setErrors(prev => ({ ...prev, comment: '' }));
    }
  };

  return (
    <Card className={`border-0 bg-white/90 backdrop-blur-md shadow-lg ${className}`}>
      <CardHeader>
        <CardTitle className="text-lg">إضافة تقييم</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Rating */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              التقييم *
            </label>
            <div className="flex items-center space-x-4 space-x-reverse">
              <StarRating
                rating={rating}
                onRatingChange={handleRatingChange}
                size="lg"
              />
              <span className="text-sm text-gray-600">
                {rating === 0 ? 'اختر تقييمك' : 
                 rating === 1 ? 'سيء جداً' :
                 rating === 2 ? 'سيء' :
                 rating === 3 ? 'متوسط' :
                 rating === 4 ? 'جيد' : 'ممتاز'}
              </span>
            </div>
            {errors.rating && (
              <p className="text-red-500 text-xs mt-1">{errors.rating}</p>
            )}
          </div>

          {/* Comment */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              التعليق *
            </label>
            <textarea
              value={comment}
              onChange={handleCommentChange}
              rows={4}
              className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal resize-none ${
                errors.comment ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="شارك تجربتك مع هذا الحرفي... ما الذي أعجبك؟ ما الذي يمكن تحسينه؟"
            />
            <div className="flex justify-between items-center mt-1">
              {errors.comment ? (
                <p className="text-red-500 text-xs">{errors.comment}</p>
              ) : (
                <p className="text-gray-500 text-xs">
                  اكتب تعليقاً مفيداً للمستخدمين الآخرين
                </p>
              )}
              <span className="text-xs text-gray-400">
                {comment.length}/500
              </span>
            </div>
          </div>

          {/* Guidelines */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-navy mb-2">💡 نصائح لكتابة تقييم مفيد:</h4>
            <ul className="text-sm text-gray-700 space-y-1">
              <li>• اذكر تفاصيل محددة عن جودة العمل</li>
              <li>• تحدث عن الالتزام بالمواعيد والتواصل</li>
              <li>• اذكر ما إذا كان السعر مناسباً للجودة</li>
              <li>• كن صادقاً وموضوعياً في تقييمك</li>
            </ul>
          </div>

          {/* Submit Button */}
          <div className="flex space-x-3 space-x-reverse">
            <Button
              type="submit"
              disabled={loading}
              className="bg-gradient-to-r from-navy to-teal text-white px-6 py-2"
            >
              {loading ? 'جاري النشر...' : 'نشر التقييم'}
            </Button>
            
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setRating(0);
                setComment('');
                setErrors({});
              }}
              disabled={loading}
            >
              إلغاء
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default AddReview;
