'use client';

import React from 'react';
import Link from 'next/link';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';

const JobPostedSuccessPage = () => {
  return (
    <MainLayout>
      <div className="bg-beige py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto bg-white rounded-lg shadow-md p-8 text-center">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <svg
                className="w-10 h-10 text-green-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                ></path>
              </svg>
            </div>
            
            <h1 className="text-2xl font-bold text-navy mb-4">تم نشر مشروعك بنجاح!</h1>
            
            <p className="text-gray-700 mb-6">
              تهانينا! تم نشر مشروعك بنجاح وهو الآن متاح للحرفيين لتقديم عروضهم. سنقوم بإشعارك عندما تتلقى عروضاً جديدة.
            </p>
            
            <div className="bg-beige bg-opacity-50 p-4 rounded-md mb-8">
              <h2 className="font-medium text-navy mb-2">الخطوات التالية:</h2>
              <ul className="text-gray-700 text-right space-y-2">
                <li className="flex items-start">
                  <span className="ml-2 text-navy">1.</span>
                  <span>انتظر العروض من الحرفيين المؤهلين</span>
                </li>
                <li className="flex items-start">
                  <span className="ml-2 text-navy">2.</span>
                  <span>راجع العروض واختر الحرفي المناسب</span>
                </li>
                <li className="flex items-start">
                  <span className="ml-2 text-navy">3.</span>
                  <span>تواصل مع الحرفي وناقش تفاصيل المشروع</span>
                </li>
                <li className="flex items-start">
                  <span className="ml-2 text-navy">4.</span>
                  <span>تابع تقدم المشروع حتى الانتهاء</span>
                </li>
              </ul>
            </div>
            
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
              <Link href="/dashboard/jobs">
                <Button>
                  عرض مشاريعي
                </Button>
              </Link>
              <Link href="/">
                <Button variant="outline">
                  العودة للصفحة الرئيسية
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default JobPostedSuccessPage;
