'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';

const AdminProjectsPage = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const projects = [
    {
      id: 1,
      title: 'تجديد المطبخ الرئيسي',
      client: 'أحمد محمد',
      craftsman: 'أحمد النجار',
      category: 'نجارة',
      status: 'نشط',
      budget: '₺7,500',
      offers: 12,
      createdAt: '2024-02-01',
      deadline: '2024-03-15',
      location: 'دمشق - المزة',
      priority: 'عالية',
      commission: '₺375'
    },
    {
      id: 2,
      title: 'إصلاح نظام السباكة',
      client: 'فاطمة أحمد',
      craftsman: 'علي السباك',
      category: 'سباكة',
      status: 'مكتمل',
      budget: '₺2,200',
      offers: 8,
      createdAt: '2024-01-25',
      deadline: '2024-02-28',
      location: 'دمشق - كفرسوسة',
      priority: 'متوسطة',
      commission: '₺110'
    },
    {
      id: 3,
      title: 'دهان الشقة الكاملة',
      client: 'محمد علي',
      craftsman: 'محمد الدهان',
      category: 'دهان',
      status: 'جاري التنفيذ',
      budget: '₺3,200',
      offers: 15,
      createdAt: '2024-01-10',
      deadline: '2024-01-20',
      location: 'دمشق - أبو رمانة',
      priority: 'منخفضة',
      commission: '₺160'
    },
    {
      id: 4,
      title: 'تركيب نظام كهرباء ذكي',
      client: 'سارة خالد',
      craftsman: null,
      category: 'كهرباء',
      status: 'في الانتظار',
      budget: '₺4,500',
      offers: 6,
      createdAt: '2024-02-05',
      deadline: '2024-03-10',
      location: 'دمشق - المالكي',
      priority: 'عالية',
      commission: '₺225'
    },
    {
      id: 5,
      title: 'تركيب خزائن مطبخ',
      client: 'خالد أحمد',
      craftsman: null,
      category: 'نجارة',
      status: 'ملغي',
      budget: '₺6,000',
      offers: 3,
      createdAt: '2024-01-15',
      deadline: '2024-02-15',
      location: 'حلب - الفرقان',
      priority: 'متوسطة',
      commission: '₺0'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'نشط':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'في الانتظار':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'مكتمل':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'جاري التنفيذ':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'ملغي':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'عالية':
        return 'text-red-600';
      case 'متوسطة':
        return 'text-yellow-600';
      case 'منخفضة':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  const filteredProjects = projects.filter(project => {
    const matchesFilter = activeFilter === 'all' || project.status === activeFilter;
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.client.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (project.craftsman && project.craftsman.toLowerCase().includes(searchTerm.toLowerCase()));
    return matchesFilter && matchesSearch;
  });

  const stats = [
    {
      title: 'إجمالي المشاريع',
      value: projects.length.toString(),
      color: 'text-blue-600',
      icon: '📋'
    },
    {
      title: 'المشاريع النشطة',
      value: projects.filter(p => ['نشط', 'جاري التنفيذ'].includes(p.status)).length.toString(),
      color: 'text-green-600',
      icon: '🔄'
    },
    {
      title: 'المشاريع المكتملة',
      value: projects.filter(p => p.status === 'مكتمل').length.toString(),
      color: 'text-purple-600',
      icon: '✅'
    },
    {
      title: 'إجمالي العمولات',
      value: '₺870',
      color: 'text-orange-600',
      icon: '💰'
    }
  ];

  return (
    <ProtectedRoute requiredRoles="admin">
      <DashboardLayout 
        title="إدارة المشاريع"
        subtitle="مراقبة ومتابعة جميع المشاريع في المنصة"
      >
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className={`text-3xl ${stat.color}`}>
                    {stat.icon}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Filters and Search */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
              {/* Filters */}
              <div className="flex flex-wrap gap-2">
                {[
                  { id: 'all', label: 'جميع المشاريع' },
                  { id: 'نشط', label: 'نشطة' },
                  { id: 'في الانتظار', label: 'في الانتظار' },
                  { id: 'جاري التنفيذ', label: 'جاري التنفيذ' },
                  { id: 'مكتمل', label: 'مكتملة' },
                  { id: 'ملغي', label: 'ملغية' }
                ].map((filter) => (
                  <button
                    key={filter.id}
                    onClick={() => setActiveFilter(filter.id)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                      activeFilter === filter.id
                        ? 'bg-navy text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {filter.label}
                  </button>
                ))}
              </div>

              {/* Search */}
              <div className="flex items-center space-x-4 space-x-reverse">
                <input
                  type="text"
                  placeholder="البحث في المشاريع..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                />
              </div>
            </div>
          </div>

          {/* Projects Table */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">المشروع</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">العميل</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">الحرفي</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">الحالة</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">الميزانية</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">العمولة</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">الموعد النهائي</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProjects.map((project) => (
                    <tr key={project.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-6">
                        <div>
                          <div className="font-medium text-gray-900">{project.title}</div>
                          <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-500">
                            <span className="bg-gray-100 px-2 py-1 rounded">{project.category}</span>
                            <span>📍 {project.location}</span>
                            <span className={`font-medium ${getPriorityColor(project.priority)}`}>
                              {project.priority}
                            </span>
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            {project.client.charAt(0)}
                          </div>
                          <span className="font-medium text-gray-900">{project.client}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        {project.craftsman ? (
                          <div className="flex items-center space-x-3 space-x-reverse">
                            <div className="w-8 h-8 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white text-sm font-bold">
                              {project.craftsman.charAt(0)}
                            </div>
                            <span className="font-medium text-gray-900">{project.craftsman}</span>
                          </div>
                        ) : (
                          <span className="text-gray-500 text-sm">غير محدد</span>
                        )}
                      </td>
                      <td className="py-4 px-6">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(project.status)}`}>
                          {project.status}
                        </span>
                      </td>
                      <td className="py-4 px-6">
                        <span className="font-bold text-gray-900">{project.budget}</span>
                        <div className="text-xs text-gray-500">{project.offers} عرض</div>
                      </td>
                      <td className="py-4 px-6">
                        <span className="font-bold text-green-600">{project.commission}</span>
                      </td>
                      <td className="py-4 px-6 text-gray-600">
                        {project.deadline}
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex space-x-2 space-x-reverse">
                          <Button size="sm" variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                            عرض
                          </Button>
                          <Button size="sm" variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-100">
                            تعديل
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {filteredProjects.length === 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-6xl mb-4">📋</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد مشاريع</h3>
              <p className="text-gray-600 mb-6">لم يتم العثور على مشاريع بالمعايير المحددة</p>
              <Button 
                onClick={() => {
                  setActiveFilter('all');
                  setSearchTerm('');
                }}
                className="bg-gradient-to-r from-navy to-teal"
              >
                إعادة تعيين الفلاتر
              </Button>
            </div>
          )}

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h3>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start border-blue-300 text-blue-700 hover:bg-blue-100">
                  📊 تصدير تقرير المشاريع
                </Button>
                <Button variant="outline" className="w-full justify-start border-green-300 text-green-700 hover:bg-green-100">
                  📈 عرض الإحصائيات التفصيلية
                </Button>
                <Button variant="outline" className="w-full justify-start border-purple-300 text-purple-700 hover:bg-purple-100">
                  🔔 إرسال تذكيرات
                </Button>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">المشاريع المتأخرة</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                  <div>
                    <p className="font-medium text-red-900">دهان الشقة الكاملة</p>
                    <p className="text-sm text-red-600">متأخر 8 أيام</p>
                  </div>
                  <Button size="sm" className="bg-red-600 hover:bg-red-700">
                    متابعة
                  </Button>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">إحصائيات سريعة</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">معدل الإكمال:</span>
                  <span className="font-bold text-green-600">85%</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">متوسط المدة:</span>
                  <span className="font-bold text-blue-600">12 يوم</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">رضا العملاء:</span>
                  <span className="font-bold text-purple-600">4.8/5</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default AdminProjectsPage;
