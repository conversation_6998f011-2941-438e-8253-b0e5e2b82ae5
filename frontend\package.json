{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "db:reset": "prisma migrate reset"}, "dependencies": {"@prisma/client": "^6.8.2", "@tailwindcss/forms": "^0.5.10", "@tanstack/react-query": "^5.76.2", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "next": "15.1.8", "next-auth": "^4.24.11", "next-i18next": "^15.4.2", "prisma": "^6.8.2", "react": "^19.0.0", "react-dom": "^19.0.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.1.8", "postcss": "^8", "tailwindcss": "^3.4.1", "tsx": "^4.19.4", "typescript": "^5"}}