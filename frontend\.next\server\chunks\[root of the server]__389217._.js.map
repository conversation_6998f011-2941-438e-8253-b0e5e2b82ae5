{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/lib/mock-data.ts"], "sourcesContent": ["// بيانات تجريبية للتطوير بدون قاعدة بيانات\n\nexport interface User {\n  id: string;\n  email: string;\n  name: string;\n  role: 'ADMIN' | 'CLIENT' | 'CRAFTSMAN';\n  phone?: string;\n  location?: string;\n  bio?: string;\n  avatar?: string;\n  isActive: boolean;\n  isVerified: boolean;\n  createdAt: Date;\n}\n\nexport interface Project {\n  id: string;\n  title: string;\n  description: string;\n  category: string;\n  budget: string;\n  deadline: Date;\n  location: string;\n  priority: 'LOW' | 'MEDIUM' | 'HIGH';\n  status: 'OPEN' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';\n  clientId: string;\n  craftsmanId?: string;\n  images: string[];\n  materials?: string;\n  workType?: string;\n  requirements?: string;\n  views: number;\n  featured: boolean;\n  createdAt: Date;\n}\n\nexport interface Offer {\n  id: string;\n  projectId: string;\n  craftsmanId: string;\n  amount: number;\n  currency: string;\n  description: string;\n  estimatedDuration: number;\n  status: 'PENDING' | 'ACCEPTED' | 'REJECTED';\n  createdAt: Date;\n}\n\nexport interface Notification {\n  id: string;\n  userId: string;\n  title: string;\n  message: string;\n  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR';\n  isRead: boolean;\n  createdAt: Date;\n}\n\n// بيانات المستخدمين التجريبية\nexport const mockUsers: User[] = [\n  {\n    id: 'admin',\n    email: '<EMAIL>',\n    name: 'مدير النظام',\n    role: 'ADMIN',\n    phone: '+963 11 123 4567',\n    location: 'دمشق، سوريا',\n    bio: 'مدير منصة دوزان',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-01'),\n  },\n  {\n    id: 'client',\n    email: '<EMAIL>',\n    name: 'أحمد محمد',\n    role: 'CLIENT',\n    phone: '+963 123 456 789',\n    location: 'دمشق - المزة',\n    bio: 'عميل يبحث عن حرفيين مؤهلين',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-15'),\n  },\n  {\n    id: 'craftsman1',\n    email: '<EMAIL>',\n    name: 'محمد النجار',\n    role: 'CRAFTSMAN',\n    phone: '+963 987 654 321',\n    location: 'دمشق - كفرسوسة',\n    bio: 'نجار محترف مع خبرة 10 سنوات',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-10'),\n  },\n  {\n    id: 'craftsman2',\n    email: '<EMAIL>',\n    name: 'سارة الكهربائية',\n    role: 'CRAFTSMAN',\n    phone: '+963 555 123 456',\n    location: 'حلب - الفرقان',\n    bio: 'كهربائية متخصصة في الأنظمة الذكية',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-20'),\n  },\n];\n\n// بيانات المشاريع التجريبية\nexport const mockProjects: Project[] = [\n  {\n    id: 'project1',\n    title: 'تجديد مطبخ منزلي',\n    description: 'أحتاج إلى تجديد مطبخ منزلي بالكامل مع تغيير الخزائن والأرضية',\n    category: 'نجارة',\n    budget: '₺6,000 - ₺8,000',\n    deadline: new Date('2024-03-15'),\n    location: 'دمشق - المزة',\n    priority: 'HIGH',\n    status: 'OPEN',\n    clientId: 'client',\n    images: [],\n    materials: 'متوفرة جزئياً',\n    workType: 'تجديد',\n    requirements: 'يفضل استخدام خشب عالي الجودة',\n    views: 15,\n    featured: true,\n    createdAt: new Date('2024-02-01'),\n  },\n  {\n    id: 'project2',\n    title: 'تركيب نظام إضاءة ذكي',\n    description: 'تركيب نظام إضاءة ذكي في المنزل مع إمكانية التحكم عبر الهاتف',\n    category: 'كهرباء',\n    budget: '₺4,000 - ₺6,000',\n    deadline: new Date('2024-03-10'),\n    location: 'دمشق - المالكي',\n    priority: 'MEDIUM',\n    status: 'OPEN',\n    clientId: 'client',\n    images: [],\n    materials: 'غير متوفرة',\n    workType: 'تركيب',\n    requirements: 'نظام متوافق مع الهواتف الذكية',\n    views: 8,\n    featured: false,\n    createdAt: new Date('2024-02-05'),\n  },\n];\n\n// بيانات العروض التجريبية\nexport const mockOffers: Offer[] = [\n  {\n    id: 'offer1',\n    projectId: 'project1',\n    craftsmanId: 'craftsman1',\n    amount: 7000,\n    currency: 'TRY',\n    description: 'يمكنني تنفيذ هذا المشروع بجودة عالية وفي الوقت المحدد',\n    estimatedDuration: 14,\n    status: 'PENDING',\n    createdAt: new Date('2024-02-02'),\n  },\n  {\n    id: 'offer2',\n    projectId: 'project2',\n    craftsmanId: 'craftsman2',\n    amount: 5000,\n    currency: 'TRY',\n    description: 'متخصصة في أنظمة الإضاءة الذكية مع ضمان سنة كاملة',\n    estimatedDuration: 7,\n    status: 'PENDING',\n    createdAt: new Date('2024-02-06'),\n  },\n];\n\n// بيانات الإشعارات التجريبية\nexport const mockNotifications: Notification[] = [\n  {\n    id: 'notif1',\n    userId: 'client',\n    title: 'عرض جديد على مشروعك',\n    message: 'تلقيت عرضاً جديداً من محمد النجار على مشروع تجديد المطبخ',\n    type: 'INFO',\n    isRead: false,\n    createdAt: new Date('2024-02-02'),\n  },\n  {\n    id: 'notif2',\n    userId: 'craftsman1',\n    title: 'مشروع جديد متاح',\n    message: 'يوجد مشروع نجارة جديد في منطقتك',\n    type: 'INFO',\n    isRead: true,\n    createdAt: new Date('2024-02-01'),\n  },\n];\n\n// دوال مساعدة للبحث والفلترة\nexport const getUserById = (id: string): User | undefined => {\n  return mockUsers.find(user => user.id === id);\n};\n\nexport const getUserByEmail = (email: string): User | undefined => {\n  return mockUsers.find(user => user.email === email);\n};\n\nexport const getProjectsByUserId = (userId: string): Project[] => {\n  return mockProjects.filter(project => \n    project.clientId === userId || project.craftsmanId === userId\n  );\n};\n\nexport const getOffersByProjectId = (projectId: string): Offer[] => {\n  return mockOffers.filter(offer => offer.projectId === projectId);\n};\n\nexport const getOffersByCraftsmanId = (craftsmanId: string): Offer[] => {\n  return mockOffers.filter(offer => offer.craftsmanId === craftsmanId);\n};\n\nexport const getNotificationsByUserId = (userId: string): Notification[] => {\n  return mockNotifications.filter(notification => notification.userId === userId);\n};\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;;;;;;;;;AA4DpC,MAAM,YAAoB;IAC/B;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,EAAE;QACV,WAAW;QACX,UAAU;QACV,cAAc;QACd,OAAO;QACP,UAAU;QACV,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,EAAE;QACV,WAAW;QACX,UAAU;QACV,cAAc;QACd,OAAO;QACP,UAAU;QACV,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,aAAsB;IACjC;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,UAAU;QACV,aAAa;QACb,mBAAmB;QACnB,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,UAAU;QACV,aAAa;QACb,mBAAmB;QACnB,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,oBAAoC;IAC/C;QACE,IAAI;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;QACT,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;QACT,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AAC5C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAO,aAAa,MAAM,CAAC,CAAA,UACzB,QAAQ,QAAQ,KAAK,UAAU,QAAQ,WAAW,KAAK;AAE3D;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,WAAW,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,KAAK;AACxD;AAEO,MAAM,yBAAyB,CAAC;IACrC,OAAO,WAAW,MAAM,CAAC,CAAA,QAAS,MAAM,WAAW,KAAK;AAC1D;AAEO,MAAM,2BAA2B,CAAC;IACvC,OAAO,kBAAkB,MAAM,CAAC,CAAA,eAAgB,aAAa,MAAM,KAAK;AAC1E"}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/api/offers/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { mockOffers, mockUsers, mockProjects, Offer } from '@/lib/mock-data';\n\n// GET /api/offers - جلب العروض\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const projectId = searchParams.get('projectId');\n    const craftsmanId = searchParams.get('craftsmanId');\n    const status = searchParams.get('status');\n\n    let filteredOffers = [...mockOffers];\n\n    // تطبيق الفلاتر\n    if (projectId) {\n      filteredOffers = filteredOffers.filter(offer => offer.projectId === projectId);\n    }\n    if (craftsmanId) {\n      filteredOffers = filteredOffers.filter(offer => offer.craftsmanId === craftsmanId);\n    }\n    if (status) {\n      filteredOffers = filteredOffers.filter(offer => offer.status === status);\n    }\n\n    // إضافة بيانات الحرفي والمشروع لكل عرض\n    const offersWithDetails = filteredOffers.map(offer => {\n      const craftsman = mockUsers.find(user => user.id === offer.craftsmanId);\n      const project = mockProjects.find(project => project.id === offer.projectId);\n      \n      return {\n        ...offer,\n        craftsman: craftsman ? {\n          id: craftsman.id,\n          name: craftsman.name,\n          location: craftsman.location,\n          avatar: craftsman.avatar,\n          bio: craftsman.bio\n        } : null,\n        project: project ? {\n          id: project.id,\n          title: project.title,\n          category: project.category,\n          location: project.location,\n          budget: project.budget\n        } : null\n      };\n    });\n\n    return NextResponse.json({\n      offers: offersWithDetails,\n      total: offersWithDetails.length\n    });\n  } catch (error) {\n    console.error('Error fetching offers:', error);\n    return NextResponse.json(\n      { error: 'خطأ في جلب العروض' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/offers - إنشاء عرض جديد\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const {\n      projectId,\n      craftsmanId,\n      amount,\n      currency = 'TRY',\n      description,\n      estimatedDuration\n    } = body;\n\n    // التحقق من البيانات المطلوبة\n    if (!projectId || !craftsmanId || !amount || !description || !estimatedDuration) {\n      return NextResponse.json(\n        { error: 'جميع الحقول المطلوبة يجب ملؤها' },\n        { status: 400 }\n      );\n    }\n\n    // التحقق من وجود المشروع\n    const project = mockProjects.find(p => p.id === projectId);\n    if (!project) {\n      return NextResponse.json(\n        { error: 'المشروع غير موجود' },\n        { status: 404 }\n      );\n    }\n\n    // التحقق من وجود الحرفي\n    const craftsman = mockUsers.find(user => user.id === craftsmanId);\n    if (!craftsman || craftsman.role !== 'CRAFTSMAN') {\n      return NextResponse.json(\n        { error: 'الحرفي غير موجود' },\n        { status: 404 }\n      );\n    }\n\n    // التحقق من عدم وجود عرض سابق من نفس الحرفي على نفس المشروع\n    const existingOffer = mockOffers.find(\n      offer => offer.projectId === projectId && offer.craftsmanId === craftsmanId\n    );\n    if (existingOffer) {\n      return NextResponse.json(\n        { error: 'لقد قدمت عرضاً على هذا المشروع من قبل' },\n        { status: 400 }\n      );\n    }\n\n    // إنشاء عرض جديد\n    const newOffer: Offer = {\n      id: `offer_${Date.now()}`,\n      projectId,\n      craftsmanId,\n      amount: Number(amount),\n      currency,\n      description,\n      estimatedDuration: Number(estimatedDuration),\n      status: 'PENDING',\n      createdAt: new Date()\n    };\n\n    // إضافة العرض للبيانات التجريبية\n    mockOffers.push(newOffer);\n\n    return NextResponse.json({\n      offer: newOffer,\n      message: 'تم تقديم العرض بنجاح'\n    }, { status: 201 });\n\n  } catch (error) {\n    console.error('Error creating offer:', error);\n    return NextResponse.json(\n      { error: 'خطأ في تقديم العرض' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,YAAY,aAAa,GAAG,CAAC;QACnC,MAAM,cAAc,aAAa,GAAG,CAAC;QACrC,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,IAAI,iBAAiB;eAAI,4HAAA,CAAA,aAAU;SAAC;QAEpC,gBAAgB;QAChB,IAAI,WAAW;YACb,iBAAiB,eAAe,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,KAAK;QACtE;QACA,IAAI,aAAa;YACf,iBAAiB,eAAe,MAAM,CAAC,CAAA,QAAS,MAAM,WAAW,KAAK;QACxE;QACA,IAAI,QAAQ;YACV,iBAAiB,eAAe,MAAM,CAAC,CAAA,QAAS,MAAM,MAAM,KAAK;QACnE;QAEA,uCAAuC;QACvC,MAAM,oBAAoB,eAAe,GAAG,CAAC,CAAA;YAC3C,MAAM,YAAY,4HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,MAAM,WAAW;YACtE,MAAM,UAAU,4HAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK,MAAM,SAAS;YAE3E,OAAO;gBACL,GAAG,KAAK;gBACR,WAAW,YAAY;oBACrB,IAAI,UAAU,EAAE;oBAChB,MAAM,UAAU,IAAI;oBACpB,UAAU,UAAU,QAAQ;oBAC5B,QAAQ,UAAU,MAAM;oBACxB,KAAK,UAAU,GAAG;gBACpB,IAAI;gBACJ,SAAS,UAAU;oBACjB,IAAI,QAAQ,EAAE;oBACd,OAAO,QAAQ,KAAK;oBACpB,UAAU,QAAQ,QAAQ;oBAC1B,UAAU,QAAQ,QAAQ;oBAC1B,QAAQ,QAAQ,MAAM;gBACxB,IAAI;YACN;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,QAAQ;YACR,OAAO,kBAAkB,MAAM;QACjC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAoB,GAC7B;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,SAAS,EACT,WAAW,EACX,MAAM,EACN,WAAW,KAAK,EAChB,WAAW,EACX,iBAAiB,EAClB,GAAG;QAEJ,8BAA8B;QAC9B,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,UAAU,CAAC,eAAe,CAAC,mBAAmB;YAC/E,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,UAAU,4HAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,YAAY,4HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACrD,IAAI,CAAC,aAAa,UAAU,IAAI,KAAK,aAAa;YAChD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,4DAA4D;QAC5D,MAAM,gBAAgB,4HAAA,CAAA,aAAU,CAAC,IAAI,CACnC,CAAA,QAAS,MAAM,SAAS,KAAK,aAAa,MAAM,WAAW,KAAK;QAElE,IAAI,eAAe;YACjB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAwC,GACjD;gBAAE,QAAQ;YAAI;QAElB;QAEA,iBAAiB;QACjB,MAAM,WAAkB;YACtB,IAAI,CAAC,MAAM,EAAE,KAAK,GAAG,IAAI;YACzB;YACA;YACA,QAAQ,OAAO;YACf;YACA;YACA,mBAAmB,OAAO;YAC1B,QAAQ;YACR,WAAW,IAAI;QACjB;QAEA,iCAAiC;QACjC,4HAAA,CAAA,aAAU,CAAC,IAAI,CAAC;QAEhB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAqB,GAC9B;YAAE,QAAQ;QAAI;IAElB;AACF"}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}