'use client';

import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

const ProjectSummaryPage = () => {
  const features = [
    {
      phase: 'المرحلة 1: الأساسيات',
      status: '✅ مكتملة',
      items: [
        'إعداد Next.js 15 مع TypeScript',
        'تصميم واجهة المستخدم مع Tailwind CSS',
        'نظام المصادقة مع NextAuth.js',
        'تصميم قاعدة البيانات مع Prisma',
        'الصفحات الأساسية (الرئيسية، المشاريع، الحرفيون)',
        'نظام الأدوار (عميل، حرفي، مدير)'
      ]
    },
    {
      phase: 'المرحلة 2: ربط الواجهات بالـ API',
      status: '✅ مكتملة',
      items: [
        'إنشاء API Routes لجميع العمليات',
        'Hooks مخصصة للتفاعل مع API',
        'ربط صفحات المشاريع بقاعدة البيانات',
        'ربط لوحات التحكم بالبيانات الحقيقية',
        'معالجة حالات التحميل والأخطاء',
        'صفحات اختبار شاملة'
      ]
    },
    {
      phase: 'المرحلة 3: الميزات التفاعلية',
      status: '✅ مكتملة',
      items: [
        'نظام العروض المتقدم (تقديم، قبول، رفض)',
        'نظام رفع الملفات والصور',
        'نظام التقييمات والتعليقات',
        'صفحات إدارة العروض للحرفيين والعملاء',
        'مكونات UI متقدمة (StarRating, FileUpload)',
        'صفحات اختبار للميزات الجديدة'
      ]
    },
    {
      phase: 'المرحلة 4: التحسينات النهائية',
      status: '🚧 قيد التطوير',
      items: [
        'نظام الرسائل المباشرة',
        'نظام الإشعارات الفورية',
        'إعداد قاعدة البيانات الحقيقية',
        'تحسينات الأمان والأداء',
        'إعداد النشر والاستضافة',
        'اختبارات شاملة'
      ]
    }
  ];

  const techStack = [
    {
      category: 'Frontend',
      technologies: [
        'Next.js 15 (App Router)',
        'React 19',
        'TypeScript',
        'Tailwind CSS',
        'NextAuth.js'
      ]
    },
    {
      category: 'Backend',
      technologies: [
        'Next.js API Routes',
        'Prisma ORM',
        'PostgreSQL',
        'NextAuth.js',
        'File Upload API'
      ]
    },
    {
      category: 'UI/UX',
      technologies: [
        'Responsive Design',
        'Arabic RTL Support',
        'Modern Components',
        'Interactive Elements',
        'Professional Styling'
      ]
    },
    {
      category: 'Features',
      technologies: [
        'Authentication System',
        'Role-based Access',
        'Real-time Messaging',
        'File Upload',
        'Rating System'
      ]
    }
  ];

  const pages = [
    { name: 'الصفحة الرئيسية', url: '/', status: '✅' },
    { name: 'المشاريع', url: '/jobs', status: '✅' },
    { name: 'إنشاء مشروع', url: '/post-job', status: '✅' },
    { name: 'لوحة تحكم العميل', url: '/client/dashboard', status: '✅' },
    { name: 'لوحة تحكم الحرفي', url: '/craftsman/dashboard', status: '✅' },
    { name: 'عروض العميل', url: '/client/offers', status: '✅' },
    { name: 'عروض الحرفي', url: '/craftsman/offers', status: '✅' },
    { name: 'الرسائل', url: '/messages', status: '✅' },
    { name: 'اختبار API', url: '/test-api', status: '✅' },
    { name: 'اختبار رفع الملفات', url: '/test-upload', status: '✅' },
    { name: 'اختبار التقييمات', url: '/test-reviews', status: '✅' },
    { name: 'لوحة الاختبار الشاملة', url: '/test-dashboard', status: '✅' }
  ];

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="text-center mb-12">
              <h1 className="text-4xl font-bold text-navy mb-4">
                🎉 ملخص مشروع دوزان
              </h1>
              <p className="text-xl text-gray-600 mb-6">
                منصة شاملة لربط العملاء بالحرفيين المؤهلين
              </p>
              <div className="flex justify-center space-x-4 space-x-reverse">
                <Button 
                  onClick={() => window.open('/test-dashboard', '_blank')}
                  className="bg-gradient-to-r from-navy to-teal"
                >
                  🧪 لوحة الاختبار الشاملة
                </Button>
                <Button 
                  onClick={() => window.open('/', '_blank')}
                  variant="outline"
                >
                  🏠 الصفحة الرئيسية
                </Button>
              </div>
            </div>

            {/* Progress Overview */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
              {features.map((phase, index) => (
                <Card key={index} className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                  <CardContent className="p-6 text-center">
                    <div className="text-3xl mb-3">
                      {phase.status.includes('✅') ? '✅' : '🚧'}
                    </div>
                    <h3 className="font-semibold text-navy mb-2">{phase.phase}</h3>
                    <p className={`text-sm ${
                      phase.status.includes('✅') ? 'text-green-600' : 'text-yellow-600'
                    }`}>
                      {phase.status}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Features Detail */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              {features.map((phase, index) => (
                <Card key={index} className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>{phase.phase}</span>
                      <span className="text-sm">{phase.status}</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {phase.items.map((item, itemIndex) => (
                        <li key={itemIndex} className="flex items-center space-x-2 space-x-reverse">
                          <span className="text-green-500">✓</span>
                          <span className="text-gray-700">{item}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Tech Stack */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-lg mb-12">
              <CardHeader>
                <CardTitle>🛠️ التقنيات المستخدمة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {techStack.map((category, index) => (
                    <div key={index}>
                      <h3 className="font-semibold text-navy mb-3">{category.category}</h3>
                      <ul className="space-y-1">
                        {category.technologies.map((tech, techIndex) => (
                          <li key={techIndex} className="text-sm text-gray-600">
                            • {tech}
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Pages List */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-lg mb-12">
              <CardHeader>
                <CardTitle>📄 الصفحات المتاحة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {pages.map((page, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span>{page.status}</span>
                        <span className="font-medium">{page.name}</span>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => window.open(page.url, '_blank')}
                      >
                        زيارة
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Test Accounts */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-lg mb-12">
              <CardHeader>
                <CardTitle>👥 حسابات الاختبار</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {[
                    { role: 'مدير', email: '<EMAIL>', name: 'مدير النظام' },
                    { role: 'عميل', email: '<EMAIL>', name: 'أحمد محمد' },
                    { role: 'حرفي نجار', email: '<EMAIL>', name: 'محمد النجار' },
                    { role: 'حرفي كهربائي', email: '<EMAIL>', name: 'علي الكهربائي' }
                  ].map((account, index) => (
                    <div key={index} className="p-4 bg-gray-50 rounded-lg">
                      <h4 className="font-semibold text-navy mb-2">{account.role}</h4>
                      <p className="text-sm text-gray-600 mb-1">{account.name}</p>
                      <p className="text-xs text-gray-500 mb-2">{account.email}</p>
                      <p className="text-xs font-mono bg-gray-200 p-1 rounded">Test123!@#</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Next Steps */}
            <Card className="border-0 bg-gradient-to-r from-navy to-teal text-white">
              <CardHeader>
                <CardTitle>🚀 الخطوات التالية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold mb-3">التطوير:</h3>
                    <ul className="space-y-1 text-white/90">
                      <li>• إعداد قاعدة البيانات الحقيقية</li>
                      <li>• تحسينات الأمان والأداء</li>
                      <li>• اختبارات شاملة</li>
                      <li>• تحسين تجربة المستخدم</li>
                    </ul>
                  </div>
                  <div>
                    <h3 className="font-semibold mb-3">النشر:</h3>
                    <ul className="space-y-1 text-white/90">
                      <li>• نشر Frontend على Vercel</li>
                      <li>• نشر Database على Railway</li>
                      <li>• إعداد CDN للملفات</li>
                      <li>• مراقبة الأداء</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default ProjectSummaryPage;
