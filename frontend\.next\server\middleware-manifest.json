{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e40b77._.js", "server/edge/chunks/[root of the server]__d50270._.js", "server/edge/chunks/edge-wrapper_ba7eeb.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ahwIqb2qUVRDMZW8RssLB4jRlbJLBysjVAa6JpbKp3A=", "__NEXT_PREVIEW_MODE_ID": "bfd8c1a2801aabe5bb383d35eec7caed", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6b6960033c2517415100fcc86d84d7b099a7af421952fef67d29140b0a3539b5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d3473f99bfdcb88b46e108505014776cdaea85ac4d4eef1b9c8d71402dc596fc"}}}, "sortedMiddleware": ["/"], "functions": {}}