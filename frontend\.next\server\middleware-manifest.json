{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e40b77._.js", "server/edge/chunks/[root of the server]__d50270._.js", "server/edge/chunks/edge-wrapper_ba7eeb.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ahwIqb2qUVRDMZW8RssLB4jRlbJLBysjVAa6JpbKp3A=", "__NEXT_PREVIEW_MODE_ID": "1450ee56bf4a96ca713efceef15e1a7f", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "aac1421e7e475f4496f7945765e41db059684b742259e5620cfeb4f8e4814a3b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ed3cbb71359f4441666f7a37ba8268a4ae7f7daef0b1cc4fef4e80dd4cae4919"}}}, "sortedMiddleware": ["/"], "functions": {}}