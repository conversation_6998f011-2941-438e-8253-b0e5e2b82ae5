{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e40b77._.js", "server/edge/chunks/[root of the server]__d50270._.js", "server/edge/chunks/edge-wrapper_ba7eeb.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ahwIqb2qUVRDMZW8RssLB4jRlbJLBysjVAa6JpbKp3A=", "__NEXT_PREVIEW_MODE_ID": "55980eee22784966d963fe696b69c955", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "4a0a98af1753716bdc12132ca0f6920f92a83a659165f0d1c29f811d72dc4994", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0455ff07dbab29b6157864c7586351a510fbe9b924aeb3390fc227eb5dcea38b"}}}, "sortedMiddleware": ["/"], "functions": {}}