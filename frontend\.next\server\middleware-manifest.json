{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e40b77._.js", "server/edge/chunks/[root of the server]__d50270._.js", "server/edge/chunks/edge-wrapper_ba7eeb.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ahwIqb2qUVRDMZW8RssLB4jRlbJLBysjVAa6JpbKp3A=", "__NEXT_PREVIEW_MODE_ID": "f2f038265bd5a6bacb4cf863c20002e4", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ba86ddb4d17bc38fb634870e94bc0f6bfacdc031cf09c47c4ab141adced0fb9b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "62c72383a2d79d1cfaa798c25d36d389d0785565a00ccb46bfc0420e37010e5f"}}}, "sortedMiddleware": ["/"], "functions": {}}