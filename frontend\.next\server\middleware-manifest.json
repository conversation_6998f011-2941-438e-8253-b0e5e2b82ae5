{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "S0asyaGKqzWjBcsKgmMv4", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ahwIqb2qUVRDMZW8RssLB4jRlbJLBysjVAa6JpbKp3A=", "__NEXT_PREVIEW_MODE_ID": "63a5c47b0ad1890a46df6dacbcfd31f2", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9389db9ba8fd32a4190167d9bb3f8dd743c405d4160aba5d09837c3644d1f7a6", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b7c63e3ecd5b62df76c4d72dbf5e5a4616e8a34efff6338c5d43db93cdff54be"}}}, "functions": {}, "sortedMiddleware": ["/"]}