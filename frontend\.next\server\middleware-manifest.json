{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_e40b77._.js", "server/edge/chunks/[root of the server]__d50270._.js", "server/edge/chunks/edge-wrapper_ba7eeb.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "ahwIqb2qUVRDMZW8RssLB4jRlbJLBysjVAa6JpbKp3A=", "__NEXT_PREVIEW_MODE_ID": "4afdcd38e93460aa3b8c73f664d19f8a", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e34dfc6fe44af4de951ac758ad6618c646e949ee48b77dd5328d96c695b2f061", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cdca23f96d1e8e17e85344b2b772e4bea2a139086fff4172038a2d5155142176"}}}, "sortedMiddleware": ["/"], "functions": {}}