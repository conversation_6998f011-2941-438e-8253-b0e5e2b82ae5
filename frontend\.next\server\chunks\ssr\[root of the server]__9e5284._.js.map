{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/providers/SessionProvider.tsx"], "sourcesContent": ["'use client';\n\nimport { SessionProvider as NextAuthSessionProvider } from 'next-auth/react';\nimport { ReactNode } from 'react';\n\ninterface SessionProviderProps {\n  children: ReactNode;\n}\n\nexport default function SessionProvider({ children }: SessionProviderProps) {\n  return (\n    <NextAuthSessionProvider>\n      {children}\n    </NextAuthSessionProvider>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IACxE,qBACE,8OAAC,8IAAA,CAAA,kBAAuB;kBACrB;;;;;;AAGP"}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 38, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// دالة تنسيق التاريخ بشكل ثابت لتجنب مشاكل Hydration\nexport function formatDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n\n    // التحقق من صحة التاريخ\n    if (isNaN(date.getTime())) {\n      return 'تاريخ غير صحيح';\n    }\n\n    // تنسيق ثابت للتاريخ\n    const year = date.getFullYear();\n    const month = date.getMonth() + 1;\n    const day = date.getDate();\n\n    // أسماء الشهور بالعربية\n    const monthNames = [\n      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',\n      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'\n    ];\n\n    return `${day} ${monthNames[month - 1]} ${year}`;\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق التاريخ النسبي (منذ كم يوم)\nexport function formatRelativeDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInMs = now.getTime() - date.getTime();\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\n\n    if (diffInDays === 0) {\n      return 'اليوم';\n    } else if (diffInDays === 1) {\n      return 'أمس';\n    } else if (diffInDays < 7) {\n      return `منذ ${diffInDays} أيام`;\n    } else if (diffInDays < 30) {\n      const weeks = Math.floor(diffInDays / 7);\n      return `منذ ${weeks} ${weeks === 1 ? 'أسبوع' : 'أسابيع'}`;\n    } else if (diffInDays < 365) {\n      const months = Math.floor(diffInDays / 30);\n      return `منذ ${months} ${months === 1 ? 'شهر' : 'أشهر'}`;\n    } else {\n      const years = Math.floor(diffInDays / 365);\n      return `منذ ${years} ${years === 1 ? 'سنة' : 'سنوات'}`;\n    }\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق الأرقام بالفواصل\nexport function formatNumber(num: number): string {\n  return num.toLocaleString('ar-SA');\n}\n\n// دالة للتحقق من صحة البريد الإلكتروني\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// دالة للتحقق من صحة رقم الهاتف السوري\nexport function isValidSyrianPhone(phone: string): boolean {\n  const phoneRegex = /^(\\+963|0)?[0-9]{9,10}$/;\n  return phoneRegex.test(phone);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,UAAkB;IAC3C,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QAEtB,wBAAwB;QACxB,IAAI,MAAM,KAAK,OAAO,KAAK;YACzB,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,KAAK,QAAQ,KAAK;QAChC,MAAM,MAAM,KAAK,OAAO;QAExB,wBAAwB;QACxB,MAAM,aAAa;YACjB;YAAS;YAAU;YAAQ;YAAS;YAAQ;YAC5C;YAAS;YAAS;YAAU;YAAU;YAAU;SACjD;QAED,OAAO,GAAG,IAAI,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,MAAM;IAClD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,mBAAmB,UAAkB;IACnD,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;QAC7C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG;YACpB,OAAO;QACT,OAAO,IAAI,eAAe,GAAG;YAC3B,OAAO;QACT,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO,CAAC,IAAI,EAAE,WAAW,KAAK,CAAC;QACjC,OAAO,IAAI,aAAa,IAAI;YAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,UAAU,UAAU;QAC3D,OAAO,IAAI,aAAa,KAAK;YAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,aAAa;YACvC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,WAAW,IAAI,QAAQ,QAAQ;QACzD,OAAO;YACL,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,QAAQ,SAAS;QACxD;IACF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,cAAc,CAAC;AAC5B;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,mBAAmB,KAAa;IAC9C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB"}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',\n  {\n    variants: {\n      variant: {\n        default: 'bg-navy text-white hover:bg-navy/90',\n        destructive: 'bg-red-500 text-white hover:bg-red-500/90',\n        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',\n        secondary: 'bg-teal text-white hover:bg-teal/90',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'underline-offset-4 hover:underline text-primary',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 rounded-md',\n        lg: 'h-11 px-8 rounded-md',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,wQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 186, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/dev/RoleSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useSession, signOut, signIn } from 'next-auth/react';\nimport { Button } from '@/components/ui/Button';\n\nconst RoleSwitcher = () => {\n  const { data: session, update } = useSession();\n\n  const switchRole = async (newRole: 'client' | 'craftsman' | 'admin') => {\n    if (session?.user) {\n      try {\n        // في التطوير: تسجيل خروج ثم دخول بالدور الجديد\n        const email = session.user.email;\n\n        // تسجيل خروج\n        await signOut({ redirect: false });\n\n        // انتظار قصير\n        await new Promise(resolve => setTimeout(resolve, 100));\n\n        // تسجيل دخول بالدور الجديد\n        const result = await signIn('credentials', {\n          email: email,\n          password: '123456',\n          role: newRole, // إرسال الدور الجديد\n          redirect: false,\n        });\n\n        if (result?.ok) {\n          // إعادة تحميل الصفحة\n          window.location.reload();\n        }\n      } catch (error) {\n        console.error('Error switching role:', error);\n      }\n    }\n  };\n\n  if (!session?.user) {\n    return null;\n  }\n\n  return (\n    <div className=\"fixed bottom-4 left-4 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4\">\n      <h3 className=\"text-sm font-semibold text-gray-700 mb-3\">تبديل الدور (للتطوير)</h3>\n      <div className=\"space-y-2\">\n        <Button\n          size=\"sm\"\n          variant={session.user.role === 'client' ? 'default' : 'outline'}\n          onClick={() => switchRole('client')}\n          className=\"w-full text-xs\"\n        >\n          👤 عميل\n        </Button>\n        <Button\n          size=\"sm\"\n          variant={session.user.role === 'craftsman' ? 'default' : 'outline'}\n          onClick={() => switchRole('craftsman')}\n          className=\"w-full text-xs\"\n        >\n          👨‍🔧 حرفي\n        </Button>\n        <Button\n          size=\"sm\"\n          variant={session.user.role === 'admin' ? 'default' : 'outline'}\n          onClick={() => switchRole('admin')}\n          className=\"w-full text-xs\"\n        >\n          👑 مدير\n        </Button>\n      </div>\n      <div className=\"mt-3 pt-3 border-t border-gray-200\">\n        <p className=\"text-xs text-gray-500\">\n          الدور الحالي: <span className=\"font-medium\">{session.user.role}</span>\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default RoleSwitcher;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,eAAe;IACnB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAE3C,MAAM,aAAa,OAAO;QACxB,IAAI,SAAS,MAAM;YACjB,IAAI;gBACF,+CAA+C;gBAC/C,MAAM,QAAQ,QAAQ,IAAI,CAAC,KAAK;gBAEhC,aAAa;gBACb,MAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;oBAAE,UAAU;gBAAM;gBAEhC,cAAc;gBACd,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBAEjD,2BAA2B;gBAC3B,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,eAAe;oBACzC,OAAO;oBACP,UAAU;oBACV,MAAM;oBACN,UAAU;gBACZ;gBAEA,IAAI,QAAQ,IAAI;oBACd,qBAAqB;oBACrB,OAAO,QAAQ,CAAC,MAAM;gBACxB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC;QACF;IACF;IAEA,IAAI,CAAC,SAAS,MAAM;QAClB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA2C;;;;;;0BACzD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,QAAQ,IAAI,CAAC,IAAI,KAAK,WAAW,YAAY;wBACtD,SAAS,IAAM,WAAW;wBAC1B,WAAU;kCACX;;;;;;kCAGD,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,QAAQ,IAAI,CAAC,IAAI,KAAK,cAAc,YAAY;wBACzD,SAAS,IAAM,WAAW;wBAC1B,WAAU;kCACX;;;;;;kCAGD,8OAAC,kIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAS,QAAQ,IAAI,CAAC,IAAI,KAAK,UAAU,YAAY;wBACrD,SAAS,IAAM,WAAW;wBAC1B,WAAU;kCACX;;;;;;;;;;;;0BAIH,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAE,WAAU;;wBAAwB;sCACrB,8OAAC;4BAAK,WAAU;sCAAe,QAAQ,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;AAKxE;uCAEe"}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}