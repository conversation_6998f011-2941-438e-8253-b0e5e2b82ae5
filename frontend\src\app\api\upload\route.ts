import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];

    if (!files || files.length === 0) {
      return NextResponse.json(
        { error: 'لم يتم اختيار أي ملفات' },
        { status: 400 }
      );
    }

    const uploadedFiles: string[] = [];
    const uploadDir = join(process.cwd(), 'public', 'uploads');

    // إنشاء مجلد الرفع إذا لم يكن موجوداً
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    for (const file of files) {
      // التحقق من صيغة الملف
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        return NextResponse.json(
          { error: `صيغة الملف ${file.name} غير مدعومة` },
          { status: 400 }
        );
      }

      // التحقق من حجم الملف (5MB)
      if (file.size > 5 * 1024 * 1024) {
        return NextResponse.json(
          { error: `حجم الملف ${file.name} كبير جداً (أكثر من 5MB)` },
          { status: 400 }
        );
      }

      // إنشاء اسم ملف فريد
      const timestamp = Date.now();
      const randomString = Math.random().toString(36).substring(2, 15);
      const extension = file.name.split('.').pop();
      const fileName = `${timestamp}_${randomString}.${extension}`;

      // تحويل الملف إلى buffer وحفظه
      const bytes = await file.arrayBuffer();
      const buffer = Buffer.from(bytes);
      const filePath = join(uploadDir, fileName);

      await writeFile(filePath, buffer);

      // إضافة رابط الملف للقائمة
      uploadedFiles.push(`/uploads/${fileName}`);
    }

    return NextResponse.json({
      message: 'تم رفع الملفات بنجاح',
      files: uploadedFiles
    });

  } catch (error) {
    console.error('Upload error:', error);
    return NextResponse.json(
      { error: 'حدث خطأ أثناء رفع الملفات' },
      { status: 500 }
    );
  }
}

// GET endpoint لجلب قائمة الملفات المرفوعة
export async function GET() {
  try {
    const uploadDir = join(process.cwd(), 'public', 'uploads');
    
    if (!existsSync(uploadDir)) {
      return NextResponse.json({ files: [] });
    }

    // في التطبيق الحقيقي، ستجلب قائمة الملفات من قاعدة البيانات
    // هنا نعيد قائمة فارغة للتطوير
    return NextResponse.json({ files: [] });

  } catch (error) {
    console.error('Get files error:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في جلب الملفات' },
      { status: 500 }
    );
  }
}
