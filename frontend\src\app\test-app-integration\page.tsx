'use client';

import { useEffect, useState } from 'react';
import supabase from '@/lib/supabase';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export default function TestAppIntegration() {
  const [users, setUsers] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any[]>([]);

  const addResult = (message: string, success: boolean, data?: any) => {
    setResults(prev => [...prev, {
      id: Date.now(),
      message,
      success,
      data,
      timestamp: new Date().toLocaleTimeString('ar-SA')
    }]);
  };

  const loadUsers = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      setUsers(data || []);
      addResult(`تم تحميل ${data?.length || 0} مستخدم بنجاح ✅`, true, data);
    } catch (err: any) {
      addResult(`خطأ في تحميل المستخدمين: ${err.message}`, false, err);
    }
    setLoading(false);
  };

  const loadProjects = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          client:users!projects_client_id_fkey(name, email)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      
      setProjects(data || []);
      addResult(`تم تحميل ${data?.length || 0} مشروع بنجاح ✅`, true, data);
    } catch (err: any) {
      addResult(`خطأ في تحميل المشاريع: ${err.message}`, false, err);
    }
    setLoading(false);
  };

  const testUserRegistration = async () => {
    setLoading(true);
    try {
      const newUser = {
        email: `test${Date.now()}@dozan.com`,
        password_hash: '$2b$10$example.hash.for.testing.purposes.only',
        name: `مستخدم تجريبي ${new Date().getHours()}:${new Date().getMinutes()}`,
        role: 'CLIENT',
        location: 'دمشق'
      };

      const { data, error } = await supabase
        .from('users')
        .insert([newUser])
        .select();

      if (error) throw error;
      
      addResult('تم إنشاء مستخدم جديد بنجاح! ✅', true, data);
      loadUsers(); // Refresh users list
    } catch (err: any) {
      addResult(`خطأ في إنشاء المستخدم: ${err.message}`, false, err);
    }
    setLoading(false);
  };

  const testProjectCreation = async () => {
    setLoading(true);
    try {
      if (users.length === 0) {
        addResult('يجب تحميل المستخدمين أولاً ⚠️', false);
        setLoading(false);
        return;
      }

      const randomUser = users[Math.floor(Math.random() * users.length)];
      const newProject = {
        title: `مشروع تجريبي ${new Date().getHours()}:${new Date().getMinutes()}`,
        description: 'هذا مشروع تجريبي لاختبار التكامل مع التطبيق',
        category: 'اختبار',
        location: 'دمشق',
        budget_min: 50000,
        budget_max: 100000,
        client_id: randomUser.id
      };

      const { data, error } = await supabase
        .from('projects')
        .insert([newProject])
        .select();

      if (error) throw error;
      
      addResult('تم إنشاء مشروع جديد بنجاح! ✅', true, data);
      loadProjects(); // Refresh projects list
    } catch (err: any) {
      addResult(`خطأ في إنشاء المشروع: ${err.message}`, false, err);
    }
    setLoading(false);
  };

  const testSearch = async () => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('projects')
        .select('*')
        .ilike('title', '%تجريبي%');

      if (error) throw error;
      
      addResult(`البحث: وجد ${data?.length || 0} مشروع يحتوي على "تجريبي" ✅`, true, data);
    } catch (err: any) {
      addResult(`خطأ في البحث: ${err.message}`, false, err);
    }
    setLoading(false);
  };

  const clearResults = () => {
    setResults([]);
  };

  useEffect(() => {
    loadUsers();
    loadProjects();
  }, []);

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-navy mb-4">
                🔗 اختبار تكامل التطبيق
              </h1>
              <p className="text-xl text-gray-600">
                اختبار ربط التطبيق مع قاعدة البيانات الحقيقية
              </p>
            </div>

            {/* Test Controls */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">اختبارات التكامل</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Button 
                    onClick={loadUsers}
                    disabled={loading}
                    className="bg-gradient-to-r from-blue-500 to-blue-600"
                  >
                    تحميل المستخدمين
                  </Button>
                  
                  <Button 
                    onClick={loadProjects}
                    disabled={loading}
                    className="bg-gradient-to-r from-green-500 to-green-600"
                  >
                    تحميل المشاريع
                  </Button>
                  
                  <Button 
                    onClick={testUserRegistration}
                    disabled={loading}
                    className="bg-gradient-to-r from-purple-500 to-purple-600"
                  >
                    إنشاء مستخدم
                  </Button>
                  
                  <Button 
                    onClick={testProjectCreation}
                    disabled={loading}
                    className="bg-gradient-to-r from-orange-500 to-orange-600"
                  >
                    إنشاء مشروع
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <Button 
                    onClick={testSearch}
                    disabled={loading}
                    className="bg-gradient-to-r from-teal-500 to-teal-600"
                  >
                    اختبار البحث
                  </Button>
                  
                  <Button 
                    onClick={clearResults}
                    disabled={loading}
                    variant="outline"
                  >
                    مسح النتائج
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Results */}
            {results.length > 0 && (
              <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
                <CardHeader>
                  <CardTitle className="text-2xl text-navy">نتائج الاختبارات</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {results.map((result) => (
                      <div 
                        key={result.id}
                        className={`p-4 rounded-lg border ${
                          result.success 
                            ? 'bg-green-50 border-green-200 text-green-800' 
                            : 'bg-red-50 border-red-200 text-red-800'
                        }`}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <span className="font-medium">{result.message}</span>
                          <span className="text-xs opacity-75">{result.timestamp}</span>
                        </div>
                        {result.data && (
                          <details className="mt-2">
                            <summary className="cursor-pointer text-xs opacity-75">عرض التفاصيل</summary>
                            <pre className="text-xs bg-white/50 p-2 rounded overflow-auto mt-2">
                              {JSON.stringify(result.data, null, 2)}
                            </pre>
                          </details>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Data Summary */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
              {/* Users Summary */}
              <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl">
                <CardHeader>
                  <CardTitle className="text-xl text-navy">👥 المستخدمين ({users.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  {users.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">لا توجد بيانات</p>
                  ) : (
                    <div className="space-y-2">
                      {users.slice(0, 3).map((user) => (
                        <div key={user.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                          <span className="font-medium">{user.name}</span>
                          <span className="text-xs text-gray-500">{user.role}</span>
                        </div>
                      ))}
                      {users.length > 3 && (
                        <p className="text-xs text-gray-500 text-center">و {users.length - 3} مستخدم آخر...</p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Projects Summary */}
              <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl">
                <CardHeader>
                  <CardTitle className="text-xl text-navy">🏗️ المشاريع ({projects.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  {projects.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">لا توجد مشاريع</p>
                  ) : (
                    <div className="space-y-2">
                      {projects.slice(0, 3).map((project) => (
                        <div key={project.id} className="p-2 bg-gray-50 rounded">
                          <div className="font-medium text-sm">{project.title}</div>
                          <div className="text-xs text-gray-500">{project.category} - {project.location}</div>
                        </div>
                      ))}
                      {projects.length > 3 && (
                        <p className="text-xs text-gray-500 text-center">و {projects.length - 3} مشروع آخر...</p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Status */}
            <Card className="border-0 bg-gradient-to-r from-green-500 to-green-600 text-white">
              <CardHeader>
                <CardTitle>🎯 حالة التكامل</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold">✅</div>
                    <div className="text-white/90">قاعدة البيانات</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">✅</div>
                    <div className="text-white/90">Supabase Client</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold">
                      {loading ? '⏳' : '✅'}
                    </div>
                    <div className="text-white/90">التطبيق</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Navigation */}
            <div className="text-center mt-8 space-x-4 space-x-reverse">
              <a 
                href="/test-dashboard"
                className="inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all"
              >
                العودة للوحة الاختبار
              </a>
              <a 
                href="/view-database"
                className="inline-block bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all"
              >
                عرض قاعدة البيانات
              </a>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
