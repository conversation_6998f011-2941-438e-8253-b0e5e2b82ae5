'use client';

import React from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

const TermsPage = () => {
  const sections = [
    {
      title: '1. التعريفات',
      content: `
        في هذه الشروط والأحكام:
        • "المنصة" تعني منصة دوزان الإلكترونية
        • "المستخدم" يشمل أصحاب المشاريع والحرفيين
        • "العميل" يعني صاحب المشروع الذي ينشر مشروعاً على المنصة
        • "الحرفي" يعني مقدم الخدمة الذي يقدم عروضاً على المشاريع
        • "المشروع" يعني العمل أو الخدمة المطلوب تنفيذها
        • "العرض" يعني الاقتراح المقدم من الحرفي لتنفيذ المشروع
      `
    },
    {
      title: '2. قبول الشروط',
      content: `
        باستخدام منصة دوزان، فإنك توافق على الالتزام بهذه الشروط والأحكام. إذا كنت لا توافق على أي من هذه الشروط، يرجى عدم استخدام المنصة.
        
        نحتفظ بالحق في تعديل هذه الشروط في أي وقت، وسيتم إشعارك بأي تغييرات مهمة عبر البريد الإلكتروني أو من خلال إشعار على المنصة.
      `
    },
    {
      title: '3. التسجيل والحساب',
      content: `
        • يجب أن تكون 18 سنة أو أكثر لاستخدام المنصة
        • يجب تقديم معلومات صحيحة ومحدثة عند التسجيل
        • أنت مسؤول عن الحفاظ على سرية كلمة المرور الخاصة بك
        • يجب إشعارنا فوراً بأي استخدام غير مصرح به لحسابك
        • نحتفظ بالحق في تعليق أو إنهاء الحسابات التي تنتهك هذه الشروط
      `
    },
    {
      title: '4. استخدام المنصة',
      content: `
        يُسمح لك باستخدام المنصة للأغراض المشروعة فقط. يُمنع عليك:
        • نشر محتوى مسيء أو غير قانوني أو مضلل
        • انتهاك حقوق الملكية الفكرية للآخرين
        • محاولة الوصول غير المصرح به إلى أنظمة المنصة
        • استخدام المنصة لأنشطة احتيالية أو ضارة
        • التحايل على رسوم المنصة بالتعامل خارجها
      `
    },
    {
      title: '5. المشاريع والعروض',
      content: `
        للعملاء:
        • يجب تقديم وصف دقيق وواضح للمشروع
        • يجب تحديد ميزانية وموعد نهائي واقعيين
        • يحق لك قبول أو رفض أي عرض مقدم
        
        للحرفيين:
        • يجب تقديم عروض صادقة وواقعية
        • يجب الالتزام بالسعر والموعد المحددين في العرض
        • يجب إنجاز العمل وفقاً للمواصفات المتفق عليها
      `
    },
    {
      title: '6. الدفع والرسوم',
      content: `
        • يتم حجز مبلغ المشروع عند قبول العرض
        • تحصل المنصة على عمولة 5% من قيمة المشروع
        • يتم تحرير المبلغ للحرفي بعد إتمام المشروع وموافقة العميل
        • في حالة النزاع، قد يتم حجز المبلغ حتى حل النزاع
        • جميع الأسعار بالليرة السورية ما لم يُذكر خلاف ذلك
      `
    },
    {
      title: '7. ضمان الجودة والنزاعات',
      content: `
        • المنصة تسهل التواصل بين الأطراف ولا تضمن جودة العمل
        • في حالة وجود نزاع، نوفر خدمة وساطة لحل المشكلة
        • يحق للعميل طلب تعديلات معقولة على العمل المنجز
        • في حالة عدم إنجاز العمل، يحق للعميل استرداد المبلغ
        • قرارات فريق الوساطة نهائية وملزمة للطرفين
      `
    },
    {
      title: '8. الملكية الفكرية',
      content: `
        • تحتفظ المنصة بحقوق الملكية الفكرية لجميع محتوياتها
        • المحتوى الذي ينشره المستخدمون يبقى ملكاً لهم
        • بنشر المحتوى، تمنح المنصة ترخيصاً لاستخدامه لأغراض التشغيل
        • يجب احترام حقوق الملكية الفكرية للآخرين
      `
    },
    {
      title: '9. الخصوصية وحماية البيانات',
      content: `
        • نلتزم بحماية خصوصية المستخدمين وفقاً لسياسة الخصوصية
        • لا نشارك المعلومات الشخصية مع أطراف ثالثة دون موافقة
        • نستخدم تقنيات التشفير لحماية البيانات الحساسة
        • يحق للمستخدمين طلب حذف بياناتهم الشخصية
      `
    },
    {
      title: '10. إخلاء المسؤولية',
      content: `
        • المنصة تقدم الخدمة "كما هي" دون ضمانات صريحة أو ضمنية
        • لا نتحمل مسؤولية الأضرار الناتجة عن استخدام المنصة
        • لا نضمن دقة أو اكتمال المعلومات المقدمة من المستخدمين
        • المستخدمون مسؤولون عن قراراتهم وتعاملاتهم على المنصة
      `
    },
    {
      title: '11. إنهاء الخدمة',
      content: `
        • يمكن للمستخدم إنهاء حسابه في أي وقت
        • نحتفظ بالحق في تعليق أو إنهاء الحسابات التي تنتهك الشروط
        • عند إنهاء الحساب، تبقى الالتزامات المالية سارية
        • قد نحتفظ ببعض البيانات لأغراض قانونية أو محاسبية
      `
    },
    {
      title: '12. القانون المطبق',
      content: `
        • تخضع هذه الشروط للقوانين السورية
        • أي نزاع ينشأ عن استخدام المنصة يخضع لاختصاص المحاكم السورية
        • في حالة تعارض أي بند مع القانون المحلي، يسود القانون المحلي
      `
    }
  ];

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-beige via-white to-skyblue/20 py-16">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-2 rounded-full text-sm font-medium mb-4">
              📋 الشروط والأحكام
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-navy mb-6">
              شروط وأحكام 
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-teal to-navy">دوزان</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              يرجى قراءة هذه الشروط والأحكام بعناية قبل استخدام منصة دوزان
            </p>
            <div className="mt-6 text-sm text-gray-500">
              آخر تحديث: 15 يناير 2024
            </div>
          </div>

          <div className="max-w-4xl mx-auto">
            {/* Introduction */}
            <Card className="border-0 bg-white/90 backdrop-blur-sm mb-8">
              <CardContent className="p-8">
                <p className="text-lg text-gray-700 leading-relaxed">
                  مرحباً بك في منصة دوزان. هذه الشروط والأحكام تحكم استخدامك لمنصتنا الإلكترونية التي تربط بين أصحاب المشاريع والحرفيين المحترفين في سوريا. 
                  باستخدام المنصة، فإنك توافق على الالتزام بهذه الشروط والأحكام.
                </p>
              </CardContent>
            </Card>

            {/* Terms Sections */}
            <div className="space-y-6">
              {sections.map((section, index) => (
                <Card key={index} className="border-0 bg-white/90 backdrop-blur-sm">
                  <CardHeader>
                    <CardTitle className="text-xl text-navy">{section.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="prose prose-gray max-w-none">
                      {section.content.split('\n').map((paragraph, pIndex) => (
                        paragraph.trim() && (
                          <p key={pIndex} className="text-gray-700 leading-relaxed mb-4 last:mb-0">
                            {paragraph.trim()}
                          </p>
                        )
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Contact Information */}
            <Card className="border-0 bg-gradient-to-r from-navy to-teal text-white mt-12">
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-bold mb-4">هل لديك أسئلة حول الشروط والأحكام؟</h3>
                <p className="text-white/90 mb-6">
                  إذا كان لديك أي استفسارات حول هذه الشروط والأحكام، لا تتردد في التواصل معنا
                </p>
                <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
                  <a 
                    href="/contact" 
                    className="bg-white text-navy px-6 py-3 rounded-lg hover:bg-white/90 transition-colors font-semibold"
                  >
                    تواصل معنا
                  </a>
                  <a 
                    href="mailto:<EMAIL>" 
                    className="border border-white text-white px-6 py-3 rounded-lg hover:bg-white hover:text-navy transition-colors font-semibold"
                  >
                    <EMAIL>
                  </a>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default TermsPage;
