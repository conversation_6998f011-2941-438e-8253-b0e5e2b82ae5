'use client';

import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/ui/Button';
import Link from 'next/link';
import { useState } from 'react';
import ClientOnly from '@/components/common/ClientOnly';

export default function AuthButton() {
  const { isAuthenticated, user, logout, isLoading } = useAuth();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  if (isLoading) {
    return (
      <div className="animate-pulse">
        <div className="h-10 w-24 bg-gray-200 rounded"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div className="flex items-center space-x-3 space-x-reverse">
        <Link href="/auth/login">
          <Button variant="outline" size="sm" className="border-navy text-navy hover:bg-navy hover:text-white">
            تسجيل الدخول
          </Button>
        </Link>
        <Link href="/auth/register">
          <Button size="sm" className="bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90">
            إنشاء حساب
          </Button>
        </Link>
      </div>
    );
  }

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'client':
        return 'عميل';
      case 'craftsman':
        return 'حرفي';
      case 'admin':
        return 'مدير';
      default:
        return 'مستخدم';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'client':
        return '👤';
      case 'craftsman':
        return '👨‍🔧';
      case 'admin':
        return '👑';
      default:
        return '👤';
    }
  };

  return (
    <ClientOnly fallback={
      <div className="animate-pulse">
        <div className="h-10 w-24 bg-gray-200 rounded"></div>
      </div>
    }>
      <div className="relative">
        <button
          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          className="flex items-center space-x-2 space-x-reverse bg-white border border-gray-200 rounded-lg px-4 py-2 hover:bg-gray-50 transition-colors duration-200"
        >
        <div className="text-lg">{getRoleIcon(user?.role || '')}</div>
        <div className="text-right">
          <div className="text-sm font-medium text-navy">{user?.name || 'مستخدم'}</div>
          <div className="text-xs text-gray-500">{getRoleDisplayName(user?.role || '')}</div>
        </div>
        <svg
          className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${
            isDropdownOpen ? 'rotate-180' : ''
          }`}
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {isDropdownOpen && (
        <>
          {/* Backdrop */}
          <div
            className="fixed inset-0 z-10"
            onClick={() => setIsDropdownOpen(false)}
          />

          {/* Dropdown Menu */}
          <div className="absolute left-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-20">
            <div className="p-4 border-b border-gray-100">
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className="text-2xl">{getRoleIcon(user?.role || '')}</div>
                <div>
                  <div className="font-medium text-navy">{user?.name || 'مستخدم'}</div>
                  <div className="text-sm text-gray-500">{user?.email || 'بريد إلكتروني'}</div>
                  <div className="text-xs text-teal font-medium">
                    {getRoleDisplayName(user?.role || '')}
                  </div>
                </div>
              </div>
            </div>

            <div className="py-2">
              <Link
                href="/dashboard"
                className="block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                onClick={() => setIsDropdownOpen(false)}
              >
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span>📊</span>
                  <span>لوحة التحكم</span>
                </div>
              </Link>

              <Link
                href="/profile"
                className="block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                onClick={() => setIsDropdownOpen(false)}
              >
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span>⚙️</span>
                  <span>الملف الشخصي</span>
                </div>
              </Link>

              {user?.role === 'client' && (
                <Link
                  href="/projects/create"
                  className="block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                  onClick={() => setIsDropdownOpen(false)}
                >
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span>➕</span>
                    <span>إنشاء مشروع</span>
                  </div>
                </Link>
              )}

              {user?.role === 'craftsman' && (
                <Link
                  href="/offers"
                  className="block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                  onClick={() => setIsDropdownOpen(false)}
                >
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span>💼</span>
                    <span>عروضي</span>
                  </div>
                </Link>
              )}

              <Link
                href="/messages"
                className="block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                onClick={() => setIsDropdownOpen(false)}
              >
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span>💬</span>
                  <span>الرسائل</span>
                </div>
              </Link>
            </div>

            <div className="border-t border-gray-100 py-2">
              <button
                onClick={() => {
                  logout();
                  setIsDropdownOpen(false);
                }}
                className="block w-full text-right px-4 py-2 text-red-600 hover:bg-red-50 transition-colors duration-200"
              >
                <div className="flex items-center space-x-2 space-x-reverse">
                  <span>🚪</span>
                  <span>تسجيل الخروج</span>
                </div>
              </button>
            </div>
          </div>
        </>
      )}
      </div>
    </ClientOnly>
  );
}
