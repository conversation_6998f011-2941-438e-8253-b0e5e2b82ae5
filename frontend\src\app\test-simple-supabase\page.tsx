'use client';

import { useEffect, useState } from 'react';
import supabase from '@/lib/supabase';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export default function TestSimpleSupabase() {
  const [connectionStatus, setConnectionStatus] = useState('جاري الاختبار...');
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<any>(null);

  const testConnection = async () => {
    setConnectionStatus('جاري الاختبار...');
    setError(null);
    setData(null);

    try {
      // Test 1: Simple auth check
      console.log('Testing Supabase connection...');
      
      const { data: authData, error: authError } = await supabase.auth.getSession();
      
      if (authError) {
        throw new Error(`Auth Error: ${authError.message}`);
      }

      // Test 2: Try to get user
      const { data: userData, error: userError } = await supabase.auth.getUser();
      
      setConnectionStatus('متصل بنجاح! ✅');
      setData({
        message: 'Supabase connection working!',
        auth: 'OK',
        session: authData.session ? 'Active' : 'No session',
        user: userData.user ? 'Authenticated' : 'Anonymous',
        url: process.env.NEXT_PUBLIC_SUPABASE_URL,
        timestamp: new Date().toISOString()
      });

    } catch (err: any) {
      console.error('Supabase connection error:', err);
      setError(err.message);
      setConnectionStatus('فشل الاتصال ❌');
    }
  };

  useEffect(() => {
    testConnection();
  }, []);

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-navy mb-4">
                🔗 اختبار Supabase البسيط
              </h1>
              <p className="text-xl text-gray-600">
                اختبار الاتصال مع Supabase باستخدام Client-side
              </p>
            </div>

            {/* Connection Status */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">حالة الاتصال</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className={`text-3xl font-bold mb-4 ${
                    connectionStatus.includes('✅') ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {connectionStatus}
                  </div>
                  
                  <Button onClick={testConnection} className="mb-4">
                    إعادة اختبار الاتصال
                  </Button>
                  
                  {error && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                      <strong>خطأ:</strong> {error}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Configuration Info */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">معلومات التكوين</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold text-navy mb-2">Supabase URL:</h3>
                    <p className="text-gray-700 bg-gray-100 p-2 rounded text-sm">
                      {process.env.NEXT_PUBLIC_SUPABASE_URL || 'غير محدد'}
                    </p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-navy mb-2">Anon Key:</h3>
                    <p className="text-gray-700 bg-gray-100 p-2 rounded text-sm">
                      {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 
                        `${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 20)}...` : 
                        'غير محدد'
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Connection Data */}
            {data && (
              <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
                <CardHeader>
                  <CardTitle className="text-2xl text-navy">بيانات الاتصال</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-gray-100 p-4 rounded overflow-auto text-sm">
                    {JSON.stringify(data, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}

            {/* Instructions */}
            <Card className="border-0 bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">التعليمات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-navy mb-2">إذا كان الاتصال يعمل:</h3>
                    <ul className="space-y-1 text-gray-700">
                      <li>• ✅ Supabase client يعمل بشكل صحيح</li>
                      <li>• ✅ يمكن إنشاء الجداول والبيانات</li>
                      <li>• ✅ يمكن ربط Prisma مع Supabase</li>
                      <li>• ✅ جاهز للنشر على Vercel</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h3 className="font-semibold text-navy mb-2">إذا كان الاتصال لا يعمل:</h3>
                    <ul className="space-y-1 text-gray-700">
                      <li>• ❌ تحقق من Supabase Dashboard</li>
                      <li>• ❌ تأكد أن المشروع نشط (Running)</li>
                      <li>• ❌ تحقق من صحة URL و API Key</li>
                      <li>• ❌ جرب إعادة إنشاء المشروع</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Navigation */}
            <div className="text-center mt-8 space-x-4 space-x-reverse">
              <a 
                href="/test-dashboard"
                className="inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all"
              >
                العودة للوحة الاختبار
              </a>
              <a 
                href="/test-supabase"
                className="inline-block bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all"
              >
                اختبار Server-side
              </a>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
