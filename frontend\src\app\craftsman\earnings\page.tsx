'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';

const CraftsmanEarningsPage = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  const earnings = [
    {
      id: 1,
      projectTitle: 'تجديد المطبخ الرئيسي',
      client: 'أحمد محمد',
      amount: '₺7,500',
      commission: '₺375',
      netAmount: '₺7,125',
      status: 'مدفوع',
      paymentDate: '2024-02-15',
      paymentMethod: 'تحويل بنكي',
      projectCompletedDate: '2024-02-10'
    },
    {
      id: 2,
      projectTitle: 'إصلاح نظام السباكة',
      client: 'فاطمة أحمد',
      amount: '₺2,200',
      commission: '₺110',
      netAmount: '₺2,090',
      status: 'مدفوع',
      paymentDate: '2024-02-12',
      paymentMethod: 'محفظة إلكترونية',
      projectCompletedDate: '2024-02-08'
    },
    {
      id: 3,
      projectTitle: 'دهان الشقة الكاملة',
      client: 'محمد علي',
      amount: '₺3,200',
      commission: '₺160',
      netAmount: '₺3,040',
      status: 'في الانتظار',
      paymentDate: null,
      paymentMethod: null,
      projectCompletedDate: '2024-02-14'
    },
    {
      id: 4,
      projectTitle: 'تركيب خزائن مطبخ',
      client: 'سارة خالد',
      amount: '₺5,800',
      commission: '₺290',
      netAmount: '₺5,510',
      status: 'قيد المعالجة',
      paymentDate: null,
      paymentMethod: null,
      projectCompletedDate: '2024-02-16'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'مدفوع':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'في الانتظار':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'قيد المعالجة':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'مرفوض':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPaymentMethodIcon = (method: string | null) => {
    if (!method) return '⏳';
    switch (method) {
      case 'تحويل بنكي':
        return '🏦';
      case 'محفظة إلكترونية':
        return '📱';
      case 'بطاقة ائتمان':
        return '💳';
      default:
        return '💰';
    }
  };

  const filteredEarnings = earnings.filter(earning => {
    if (activeFilter === 'all') return true;
    return earning.status === activeFilter;
  });

  const totalEarnings = earnings.reduce((sum, earning) => {
    if (earning.status === 'مدفوع') {
      return sum + parseFloat(earning.netAmount.replace('₺', '').replace(',', ''));
    }
    return sum;
  }, 0);

  const pendingEarnings = earnings.reduce((sum, earning) => {
    if (earning.status === 'في الانتظار' || earning.status === 'قيد المعالجة') {
      return sum + parseFloat(earning.netAmount.replace('₺', '').replace(',', ''));
    }
    return sum;
  }, 0);

  const stats = [
    {
      title: 'إجمالي الأرباح',
      value: `₺${totalEarnings.toLocaleString()}`,
      color: 'text-green-600',
      icon: '💰'
    },
    {
      title: 'أرباح معلقة',
      value: `₺${pendingEarnings.toLocaleString()}`,
      color: 'text-yellow-600',
      icon: '⏳'
    },
    {
      title: 'عدد المشاريع',
      value: earnings.length.toString(),
      color: 'text-blue-600',
      icon: '📊'
    },
    {
      title: 'متوسط الربح',
      value: `₺${Math.round(totalEarnings / earnings.filter(e => e.status === 'مدفوع').length || 0).toLocaleString()}`,
      color: 'text-purple-600',
      icon: '📈'
    }
  ];

  return (
    <ProtectedRoute requiredRoles="craftsman">
      <DashboardLayout 
        title="الأرباح والمدفوعات"
        subtitle="متابعة أرباحك ومدفوعاتك من المشاريع"
        actions={
          <div className="flex space-x-2 space-x-reverse">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
            >
              <option value="week">هذا الأسبوع</option>
              <option value="month">هذا الشهر</option>
              <option value="quarter">هذا الربع</option>
              <option value="year">هذا العام</option>
            </select>
            <Button className="bg-gradient-to-r from-navy to-teal">
              تصدير التقرير
            </Button>
          </div>
        }
      >
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className={`text-3xl ${stat.color}`}>
                    {stat.icon}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Earnings Chart Placeholder */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">تطور الأرباح</h3>
            <div className="h-64 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <div className="text-4xl mb-4">📈</div>
                <p className="text-gray-600">سيتم إضافة مخطط الأرباح هنا</p>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-wrap gap-2">
              {[
                { id: 'all', label: 'جميع المدفوعات' },
                { id: 'مدفوع', label: 'مدفوعة' },
                { id: 'في الانتظار', label: 'في الانتظار' },
                { id: 'قيد المعالجة', label: 'قيد المعالجة' }
              ].map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => setActiveFilter(filter.id)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    activeFilter === filter.id
                      ? 'bg-navy text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {filter.label}
                </button>
              ))}
            </div>
          </div>

          {/* Earnings Table */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">المشروع</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">العميل</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">المبلغ الإجمالي</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">العمولة</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">صافي الربح</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">الحالة</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">تاريخ الدفع</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredEarnings.map((earning) => (
                    <tr key={earning.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-6">
                        <div>
                          <div className="font-medium text-gray-900">{earning.projectTitle}</div>
                          <div className="text-sm text-gray-500">
                            اكتمل في {earning.projectCompletedDate}
                          </div>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                            {earning.client.charAt(0)}
                          </div>
                          <span className="font-medium text-gray-900">{earning.client}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <span className="font-bold text-gray-900">{earning.amount}</span>
                      </td>
                      <td className="py-4 px-6">
                        <span className="text-red-600 font-medium">-{earning.commission}</span>
                      </td>
                      <td className="py-4 px-6">
                        <span className="font-bold text-green-600">{earning.netAmount}</span>
                      </td>
                      <td className="py-4 px-6">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(earning.status)}`}>
                          {earning.status}
                        </span>
                      </td>
                      <td className="py-4 px-6">
                        {earning.paymentDate ? (
                          <div>
                            <div className="font-medium text-gray-900">{earning.paymentDate}</div>
                            <div className="flex items-center space-x-1 space-x-reverse text-sm text-gray-500">
                              <span>{getPaymentMethodIcon(earning.paymentMethod)}</span>
                              <span>{earning.paymentMethod}</span>
                            </div>
                          </div>
                        ) : (
                          <span className="text-gray-500 text-sm">في الانتظار</span>
                        )}
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex space-x-2 space-x-reverse">
                          <Button size="sm" variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                            عرض التفاصيل
                          </Button>
                          {earning.status === 'مدفوع' && (
                            <Button size="sm" variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-100">
                              تحميل الإيصال
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {filteredEarnings.length === 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-6xl mb-4">💰</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد أرباح</h3>
              <p className="text-gray-600 mb-6">لم يتم العثور على أرباح بالمعايير المحددة</p>
              <Button 
                onClick={() => setActiveFilter('all')}
                className="bg-gradient-to-r from-navy to-teal"
              >
                عرض جميع الأرباح
              </Button>
            </div>
          )}

          {/* Payment Methods */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">طرق استلام الأرباح</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <span className="text-2xl">🏦</span>
                    <div>
                      <p className="font-medium text-gray-900">حساب بنكي</p>
                      <p className="text-sm text-gray-500">البنك التجاري السوري</p>
                    </div>
                  </div>
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">افتراضي</span>
                </div>
                <div className="text-sm text-gray-600">
                  <p>رقم الحساب: ****5678</p>
                  <p>اسم الحساب: أحمد النجار</p>
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <span className="text-2xl">📱</span>
                    <div>
                      <p className="font-medium text-gray-900">محفظة إلكترونية</p>
                      <p className="text-sm text-gray-500">سيريتل كاش</p>
                    </div>
                  </div>
                </div>
                <div className="text-sm text-gray-600">
                  <p>رقم المحفظة: +963 123 456 789</p>
                </div>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <Button variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                إضافة طريقة دفع جديدة
              </Button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default CraftsmanEarningsPage;
