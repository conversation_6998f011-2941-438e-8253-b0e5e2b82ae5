'use client';

import React, { useState } from 'react';
import Link from 'next/link';

const categories = [
  {
    id: 1,
    name: 'النجارة',
    icon: '🪚',
    description: 'أعمال النجارة والأثاث المنزلي',
    link: '/category/carpentry',
    color: 'from-amber-500 to-orange-600',
    bgColor: 'bg-amber-50',
    projects: '250+ مشروع',
    craftsmen: '45 حرفي'
  },
  {
    id: 2,
    name: 'السباكة',
    icon: '🔧',
    description: 'تركيب وإصلاح أنظمة المياه والصرف الصحي',
    link: '/category/plumbing',
    color: 'from-blue-500 to-cyan-600',
    bgColor: 'bg-blue-50',
    projects: '180+ مشروع',
    craftsmen: '32 حرفي'
  },
  {
    id: 3,
    name: 'الكهرباء',
    icon: '⚡',
    description: 'تركيب وصيانة الأنظمة الكهربائية',
    link: '/category/electrical',
    color: 'from-yellow-500 to-orange-500',
    bgColor: 'bg-yellow-50',
    projects: '320+ مشروع',
    craftsmen: '58 حرفي'
  },
  {
    id: 4,
    name: 'الدهان',
    icon: '🖌️',
    description: 'أعمال الدهان والديكور',
    link: '/category/painting',
    color: 'from-purple-500 to-pink-600',
    bgColor: 'bg-purple-50',
    projects: '290+ مشروع',
    craftsmen: '41 حرفي'
  },
  {
    id: 5,
    name: 'البناء',
    icon: '🧱',
    description: 'أعمال البناء والترميم',
    link: '/category/construction',
    color: 'from-gray-600 to-gray-800',
    bgColor: 'bg-gray-50',
    projects: '150+ مشروع',
    craftsmen: '28 حرفي'
  },
  {
    id: 6,
    name: 'التكييف',
    icon: '❄️',
    description: 'تركيب وصيانة أنظمة التكييف',
    link: '/category/hvac',
    color: 'from-cyan-500 to-blue-600',
    bgColor: 'bg-cyan-50',
    projects: '120+ مشروع',
    craftsmen: '22 حرفي'
  },
  {
    id: 7,
    name: 'الحدادة',
    icon: '🔨',
    description: 'أعمال الحدادة والمعادن',
    link: '/category/metalwork',
    color: 'from-slate-600 to-gray-700',
    bgColor: 'bg-slate-50',
    projects: '95+ مشروع',
    craftsmen: '18 حرفي'
  },
  {
    id: 8,
    name: 'تنسيق الحدائق',
    icon: '🌱',
    description: 'تصميم وتنسيق الحدائق',
    link: '/category/landscaping',
    color: 'from-green-500 to-emerald-600',
    bgColor: 'bg-green-50',
    projects: '85+ مشروع',
    craftsmen: '15 حرفي'
  },
];

const ServiceCategories = () => {
  const [hoveredCategory, setHoveredCategory] = useState<number | null>(null);

  return (
    <section className="py-20 bg-gradient-to-b from-white via-skyblue/10 to-white relative overflow-hidden">
      {/* خلفية هندسية متناسقة */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl"></div>
      </div>

      {/* شبكة نقطية ناعمة */}
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full" style={{
          backgroundImage: 'radial-gradient(circle, #567C8D 1px, transparent 1px)',
          backgroundSize: '50px 50px'
        }}></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <div className="inline-block bg-gradient-to-r from-navy to-teal text-white px-8 py-3 rounded-full text-sm font-medium mb-6 shadow-lg">
            <span className="text-lg">🛠️</span>
            <span className="mr-2">خدمات متنوعة</span>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-navy mb-6 leading-tight">
            استكشف فئات
            <span className="block text-transparent bg-clip-text bg-gradient-to-r from-teal via-navy to-teal">
              الخدمات
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            نوفر مجموعة واسعة ومتنوعة من الخدمات المهنية لتلبية جميع احتياجاتك مع أفضل الحرفيين المحترفين
          </p>
          <div className="mt-8 w-24 h-1 bg-gradient-to-r from-teal to-navy mx-auto rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
          {categories.map((category, index) => (
            <Link key={category.id} href={category.link} className="block group">
              <div
                className={`relative overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 ${category.bgColor} border border-white/50 shadow-lg hover:shadow-2xl`}
                onMouseEnter={() => setHoveredCategory(category.id)}
                onMouseLeave={() => setHoveredCategory(null)}
              >
                {/* خلفية متدرجة */}
                <div className={`absolute inset-0 bg-gradient-to-br ${category.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}></div>

                {/* محتوى البطاقة */}
                <div className="relative p-8 h-full flex flex-col">
                  {/* الأيقونة */}
                  <div className="flex justify-center mb-6">
                    <div className={`w-20 h-20 rounded-2xl bg-gradient-to-br ${category.color} flex items-center justify-center text-3xl shadow-lg group-hover:scale-110 transition-transform duration-300`}>
                      <span className="filter drop-shadow-sm">{category.icon}</span>
                    </div>
                  </div>

                  {/* العنوان والوصف */}
                  <div className="text-center flex-grow">
                    <h3 className="text-2xl font-bold text-navy mb-3 group-hover:text-teal transition-colors duration-300">
                      {category.name}
                    </h3>
                    <p className="text-gray-600 leading-relaxed mb-6 text-sm">
                      {category.description}
                    </p>
                  </div>

                  {/* الإحصائيات */}
                  <div className="flex justify-between items-center pt-4 border-t border-gray-200/50">
                    <div className="text-center">
                      <div className="text-sm font-bold text-navy">{category.projects}</div>
                      <div className="text-xs text-gray-500">مشاريع</div>
                    </div>
                    <div className="text-center">
                      <div className="text-sm font-bold text-navy">{category.craftsmen}</div>
                      <div className="text-xs text-gray-500">حرفي</div>
                    </div>
                  </div>

                  {/* مؤشر الانتقال */}
                  <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className={`w-8 h-1 bg-gradient-to-r ${category.color} rounded-full`}></div>
                  </div>
                </div>

                {/* تأثير الحدود المتوهجة */}
                <div className={`absolute inset-0 rounded-2xl border-2 border-transparent bg-gradient-to-r ${category.color} opacity-0 group-hover:opacity-20 transition-opacity duration-500`}></div>
              </div>
            </Link>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-navy to-teal rounded-2xl p-8 text-white shadow-2xl">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">لم تجد الخدمة التي تبحث عنها؟</h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto text-lg">
              تواصل معنا وسنساعدك في العثور على الحرفي المناسب لأي نوع من المشاريع
            </p>
            <Link href="/contact">
              <button className="bg-white text-navy px-8 py-3 rounded-full font-bold hover:bg-gray-100 transition-colors duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                تواصل معنا
              </button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ServiceCategories;
