'use client';

import { useState } from 'react';
import supabase from '@/lib/supabase';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export default function TestSupabaseTables() {
  const [status, setStatus] = useState('جاهز للاختبار');
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const addResult = (message: string, success: boolean, data?: any) => {
    setResults(prev => [...prev, {
      id: Date.now(),
      message,
      success,
      data,
      timestamp: new Date().toLocaleTimeString('ar-SA')
    }]);
  };

  const testCreateTable = async () => {
    setLoading(true);
    setStatus('جاري إنشاء جدول اختبار...');
    
    try {
      // Create a simple test table
      const { data, error } = await supabase.rpc('create_test_table');
      
      if (error) {
        addResult(`خطأ في إنشاء الجدول: ${error.message}`, false, error);
      } else {
        addResult('تم إنشاء جدول الاختبار بنجاح! ✅', true, data);
      }
    } catch (err: any) {
      addResult(`خطأ: ${err.message}`, false, err);
    }
    
    setLoading(false);
    setStatus('انتهى الاختبار');
  };

  const testInsertData = async () => {
    setLoading(true);
    setStatus('جاري إدخال بيانات اختبار...');
    
    try {
      // Try to insert test data
      const { data, error } = await supabase
        .from('test_table')
        .insert([
          { name: 'اختبار 1', description: 'بيانات تجريبية' },
          { name: 'اختبار 2', description: 'بيانات تجريبية أخرى' }
        ])
        .select();
      
      if (error) {
        addResult(`خطأ في إدخال البيانات: ${error.message}`, false, error);
      } else {
        addResult('تم إدخال البيانات بنجاح! ✅', true, data);
      }
    } catch (err: any) {
      addResult(`خطأ: ${err.message}`, false, err);
    }
    
    setLoading(false);
    setStatus('انتهى الاختبار');
  };

  const testSelectData = async () => {
    setLoading(true);
    setStatus('جاري قراءة البيانات...');
    
    try {
      // Try to select data
      const { data, error } = await supabase
        .from('test_table')
        .select('*');
      
      if (error) {
        addResult(`خطأ في قراءة البيانات: ${error.message}`, false, error);
      } else {
        addResult(`تم قراءة البيانات بنجاح! عدد السجلات: ${data?.length || 0} ✅`, true, data);
      }
    } catch (err: any) {
      addResult(`خطأ: ${err.message}`, false, err);
    }
    
    setLoading(false);
    setStatus('انتهى الاختبار');
  };

  const clearResults = () => {
    setResults([]);
    setStatus('جاهز للاختبار');
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-navy mb-4">
                🗄️ اختبار جداول Supabase
              </h1>
              <p className="text-xl text-gray-600">
                اختبار إنشاء الجداول وإدارة البيانات في Supabase
              </p>
            </div>

            {/* Status */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">الحالة الحالية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className={`text-2xl font-bold mb-4 ${
                    loading ? 'text-blue-600' : 'text-gray-600'
                  }`}>
                    {status}
                  </div>
                  {loading && (
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal mx-auto"></div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Test Buttons */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">اختبارات قاعدة البيانات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <Button 
                    onClick={testCreateTable}
                    disabled={loading}
                    className="bg-gradient-to-r from-green-500 to-green-600"
                  >
                    إنشاء جدول
                  </Button>
                  
                  <Button 
                    onClick={testInsertData}
                    disabled={loading}
                    className="bg-gradient-to-r from-blue-500 to-blue-600"
                  >
                    إدخال بيانات
                  </Button>
                  
                  <Button 
                    onClick={testSelectData}
                    disabled={loading}
                    className="bg-gradient-to-r from-purple-500 to-purple-600"
                  >
                    قراءة بيانات
                  </Button>
                  
                  <Button 
                    onClick={clearResults}
                    disabled={loading}
                    variant="outline"
                  >
                    مسح النتائج
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Results */}
            {results.length > 0 && (
              <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
                <CardHeader>
                  <CardTitle className="text-2xl text-navy">نتائج الاختبارات</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {results.map((result) => (
                      <div 
                        key={result.id}
                        className={`p-4 rounded-lg border ${
                          result.success 
                            ? 'bg-green-50 border-green-200 text-green-800' 
                            : 'bg-red-50 border-red-200 text-red-800'
                        }`}
                      >
                        <div className="flex justify-between items-start mb-2">
                          <span className="font-medium">{result.message}</span>
                          <span className="text-xs opacity-75">{result.timestamp}</span>
                        </div>
                        {result.data && (
                          <pre className="text-xs bg-white/50 p-2 rounded overflow-auto">
                            {JSON.stringify(result.data, null, 2)}
                          </pre>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Instructions */}
            <Card className="border-0 bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">ملاحظات مهمة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-yellow-100 border border-yellow-400 text-yellow-800 px-4 py-3 rounded">
                    <strong>تنبيه:</strong> هذه الاختبارات تتطلب أن يكون لديك صلاحيات إنشاء الجداول في Supabase.
                  </div>
                  
                  <div>
                    <h3 className="font-semibold text-navy mb-2">إذا فشلت الاختبارات:</h3>
                    <ul className="space-y-1 text-gray-700">
                      <li>• تحقق من أن المشروع نشط في Supabase Dashboard</li>
                      <li>• تأكد من صحة API Keys</li>
                      <li>• تحقق من صلاحيات قاعدة البيانات</li>
                      <li>• جرب إنشاء الجداول يدوياً في Supabase Dashboard</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Navigation */}
            <div className="text-center mt-8 space-x-4 space-x-reverse">
              <a 
                href="/test-dashboard"
                className="inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all"
              >
                العودة للوحة الاختبار
              </a>
              <a 
                href="/test-simple-supabase"
                className="inline-block bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all"
              >
                اختبار الاتصال البسيط
              </a>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
