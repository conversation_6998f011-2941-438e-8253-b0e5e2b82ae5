'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/hooks/useAuth';

const DashboardProfilePage = () => {
  const { user, isClient, isCraftsman, isAdmin } = useAuth();
  const [activeTab, setActiveTab] = useState('personal');
  const [isEditing, setIsEditing] = useState(false);

  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: '+963 123 456 789',
    location: 'دمشق، سوريا',
    bio: 'مرحباً، أنا متخصص في مجال البناء والتشييد مع خبرة تزيد عن 10 سنوات.',
    skills: ['نجارة', 'كهرباء', 'سباكة'],
    experience: '10+ سنوات',
    rating: 4.8,
    completedProjects: 45,
    languages: ['العربية', 'الإنجليزية'],
    availability: 'متاح',
    hourlyRate: '₺50',
    workingHours: '8:00 ص - 6:00 م'
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setProfileData(prev => ({ ...prev, [name]: value }));
  };

  const handleSave = () => {
    // هنا سيتم حفظ البيانات في الخادم
    console.log('Saving profile data:', profileData);
    setIsEditing(false);
  };

  const tabs = [
    { id: 'personal', label: 'المعلومات الشخصية', icon: '👤' },
    { id: 'professional', label: 'المعلومات المهنية', icon: '💼' },
    { id: 'security', label: 'الأمان والخصوصية', icon: '🔒' },
    { id: 'notifications', label: 'إعدادات الإشعارات', icon: '🔔' }
  ];

  const renderPersonalInfo = () => (
    <div className="space-y-6">
      <div className="flex items-center space-x-6 space-x-reverse">
        <div className="w-24 h-24 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white text-3xl font-bold">
          {profileData.name.charAt(0)}
        </div>
        <div className="flex-1">
          <h3 className="text-xl font-semibold text-gray-900">{profileData.name}</h3>
          <p className="text-gray-600">{user?.role === 'CLIENT' ? 'عميل' : user?.role === 'CRAFTSMAN' ? 'حرفي' : 'مدير'}</p>
          <Button size="sm" variant="outline" className="mt-2 border-navy text-navy hover:bg-navy hover:text-white">
            تغيير الصورة
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-gray-700 font-medium mb-2">الاسم الكامل</label>
          <input
            type="text"
            name="name"
            value={profileData.name}
            onChange={handleInputChange}
            disabled={!isEditing}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy disabled:bg-gray-50"
          />
        </div>

        <div>
          <label className="block text-gray-700 font-medium mb-2">البريد الإلكتروني</label>
          <input
            type="email"
            name="email"
            value={profileData.email}
            onChange={handleInputChange}
            disabled={!isEditing}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy disabled:bg-gray-50"
          />
        </div>

        <div>
          <label className="block text-gray-700 font-medium mb-2">رقم الهاتف</label>
          <input
            type="tel"
            name="phone"
            value={profileData.phone}
            onChange={handleInputChange}
            disabled={!isEditing}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy disabled:bg-gray-50"
          />
        </div>

        <div>
          <label className="block text-gray-700 font-medium mb-2">الموقع</label>
          <input
            type="text"
            name="location"
            value={profileData.location}
            onChange={handleInputChange}
            disabled={!isEditing}
            className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy disabled:bg-gray-50"
          />
        </div>
      </div>

      <div>
        <label className="block text-gray-700 font-medium mb-2">نبذة شخصية</label>
        <textarea
          name="bio"
          value={profileData.bio}
          onChange={handleInputChange}
          disabled={!isEditing}
          rows={4}
          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy disabled:bg-gray-50"
        />
      </div>
    </div>
  );

  const renderProfessionalInfo = () => (
    <div className="space-y-6">
      {isCraftsman && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-gray-700 font-medium mb-2">سنوات الخبرة</label>
              <input
                type="text"
                name="experience"
                value={profileData.experience}
                onChange={handleInputChange}
                disabled={!isEditing}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy disabled:bg-gray-50"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">السعر بالساعة</label>
              <input
                type="text"
                name="hourlyRate"
                value={profileData.hourlyRate}
                onChange={handleInputChange}
                disabled={!isEditing}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy disabled:bg-gray-50"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">ساعات العمل</label>
              <input
                type="text"
                name="workingHours"
                value={profileData.workingHours}
                onChange={handleInputChange}
                disabled={!isEditing}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy disabled:bg-gray-50"
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">الحالة</label>
              <select
                name="availability"
                value={profileData.availability}
                onChange={handleInputChange}
                disabled={!isEditing}
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy disabled:bg-gray-50"
              >
                <option value="متاح">متاح</option>
                <option value="مشغول">مشغول</option>
                <option value="في إجازة">في إجازة</option>
              </select>
            </div>
          </div>

          <div>
            <label className="block text-gray-700 font-medium mb-2">المهارات والتخصصات</label>
            <div className="flex flex-wrap gap-2 mb-3">
              {profileData.skills.map((skill, index) => (
                <span key={index} className="bg-navy text-white px-3 py-1 rounded-full text-sm">
                  {skill}
                  {isEditing && (
                    <button className="mr-2 text-white hover:text-red-200">×</button>
                  )}
                </span>
              ))}
            </div>
            {isEditing && (
              <input
                type="text"
                placeholder="إضافة مهارة جديدة..."
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
              />
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-blue-50 p-6 rounded-xl text-center">
              <div className="text-3xl font-bold text-blue-600">{profileData.rating}</div>
              <div className="text-sm text-gray-600">التقييم</div>
              <div className="flex justify-center mt-2">
                {Array.from({ length: 5 }, (_, i) => (
                  <span key={i} className={`text-lg ${i < Math.floor(profileData.rating) ? 'text-yellow-400' : 'text-gray-300'}`}>
                    ⭐
                  </span>
                ))}
              </div>
            </div>

            <div className="bg-green-50 p-6 rounded-xl text-center">
              <div className="text-3xl font-bold text-green-600">{profileData.completedProjects}</div>
              <div className="text-sm text-gray-600">مشروع مكتمل</div>
            </div>

            <div className="bg-purple-50 p-6 rounded-xl text-center">
              <div className="text-3xl font-bold text-purple-600">98%</div>
              <div className="text-sm text-gray-600">معدل الإكمال</div>
            </div>
          </div>
        </>
      )}

      {isClient && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-blue-50 p-6 rounded-xl text-center">
            <div className="text-3xl font-bold text-blue-600">12</div>
            <div className="text-sm text-gray-600">مشروع منشور</div>
          </div>

          <div className="bg-green-50 p-6 rounded-xl text-center">
            <div className="text-3xl font-bold text-green-600">8</div>
            <div className="text-sm text-gray-600">مشروع مكتمل</div>
          </div>

          <div className="bg-purple-50 p-6 rounded-xl text-center">
            <div className="text-3xl font-bold text-purple-600">4.9</div>
            <div className="text-sm text-gray-600">تقييم الحرفيين</div>
          </div>
        </div>
      )}
    </div>
  );

  const renderSecuritySettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">تغيير كلمة المرور</h3>
        <div className="grid grid-cols-1 gap-4">
          <div>
            <label className="block text-gray-700 font-medium mb-2">كلمة المرور الحالية</label>
            <input
              type="password"
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
            />
          </div>
          <div>
            <label className="block text-gray-700 font-medium mb-2">كلمة المرور الجديدة</label>
            <input
              type="password"
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
            />
          </div>
          <div>
            <label className="block text-gray-700 font-medium mb-2">تأكيد كلمة المرور الجديدة</label>
            <input
              type="password"
              className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
            />
          </div>
          <Button className="w-fit bg-gradient-to-r from-navy to-teal">
            تحديث كلمة المرور
          </Button>
        </div>
      </div>

      <div className="border-t border-gray-200 pt-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">المصادقة الثنائية</h3>
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
          <div>
            <p className="font-medium text-gray-900">تفعيل المصادقة الثنائية</p>
            <p className="text-sm text-gray-600">إضافة طبقة حماية إضافية لحسابك</p>
          </div>
          <label className="relative inline-flex items-center cursor-pointer">
            <input type="checkbox" className="sr-only peer" />
            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-navy/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-navy"></div>
          </label>
        </div>
      </div>

      <div className="border-t border-gray-200 pt-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">الجلسات النشطة</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-xl">
            <div>
              <p className="font-medium text-gray-900">💻 Chrome على Windows</p>
              <p className="text-sm text-gray-600">الجلسة الحالية - دمشق، سوريا</p>
            </div>
            <span className="text-green-600 text-sm font-medium">نشط الآن</span>
          </div>
          <div className="flex items-center justify-between p-4 border border-gray-200 rounded-xl">
            <div>
              <p className="font-medium text-gray-900">📱 Safari على iPhone</p>
              <p className="text-sm text-gray-600">آخر نشاط: منذ ساعتين</p>
            </div>
            <Button size="sm" variant="outline" className="border-red-300 text-red-700 hover:bg-red-100">
              إنهاء الجلسة
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <ProtectedRoute>
      <DashboardLayout 
        title="الملف الشخصي"
        subtitle="إدارة معلوماتك الشخصية والمهنية"
        actions={
          <div className="flex space-x-2 space-x-reverse">
            {isEditing ? (
              <>
                <Button 
                  onClick={() => setIsEditing(false)}
                  variant="outline" 
                  className="border-gray-300 text-gray-700"
                >
                  إلغاء
                </Button>
                <Button 
                  onClick={handleSave}
                  className="bg-gradient-to-r from-navy to-teal"
                >
                  حفظ التغييرات
                </Button>
              </>
            ) : (
              <Button 
                onClick={() => setIsEditing(true)}
                className="bg-gradient-to-r from-navy to-teal"
              >
                تعديل الملف الشخصي
              </Button>
            )}
          </div>
        }
      >
        <div className="space-y-8">
          {/* Tabs */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-wrap gap-2">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 space-x-reverse px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    activeTab === tab.id
                      ? 'bg-navy text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <span>{tab.icon}</span>
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Tab Content */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
            {activeTab === 'personal' && renderPersonalInfo()}
            {activeTab === 'professional' && renderProfessionalInfo()}
            {activeTab === 'security' && renderSecuritySettings()}
            {activeTab === 'notifications' && (
              <div className="text-center py-12">
                <div className="text-6xl mb-4">🔔</div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">إعدادات الإشعارات</h3>
                <p className="text-gray-600 mb-6">يمكنك إدارة إعدادات الإشعارات من صفحة الإشعارات</p>
                <Button className="bg-gradient-to-r from-navy to-teal">
                  الانتقال إلى الإشعارات
                </Button>
              </div>
            )}
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default DashboardProfilePage;
