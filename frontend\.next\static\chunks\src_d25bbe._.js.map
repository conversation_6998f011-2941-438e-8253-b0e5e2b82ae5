{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-white shadow-sm transition-all duration-200 hover:shadow-md',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('font-semibold leading-none tracking-tight text-navy', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-600', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACpE,GAAG,KAAK;kBAER;;;;;;;AAGL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;;AAGL,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG"}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/test-api/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Button } from '@/components/ui/Button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\n\nexport default function TestApiPage() {\n  const [results, setResults] = useState<any>(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const testEndpoint = async (endpoint: string, method: string = 'GET', body?: any) => {\n    setLoading(true);\n    setError('');\n    setResults(null);\n\n    try {\n      const options: RequestInit = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      };\n\n      if (body && method !== 'GET') {\n        options.body = JSON.stringify(body);\n      }\n\n      const response = await fetch(endpoint, options);\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'حدث خطأ');\n      }\n\n      setResults(data);\n    } catch (err: any) {\n      setError(err.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testCases = [\n    {\n      name: 'جلب جميع المشاريع',\n      endpoint: '/api/projects',\n      method: 'GET'\n    },\n    {\n      name: 'جلب مشروع محدد',\n      endpoint: '/api/projects/project1',\n      method: 'GET'\n    },\n    {\n      name: 'جلب جميع العروض',\n      endpoint: '/api/offers',\n      method: 'GET'\n    },\n    {\n      name: 'جلب عروض مشروع محدد',\n      endpoint: '/api/offers?projectId=project1',\n      method: 'GET'\n    },\n    {\n      name: 'جلب جميع المستخدمين',\n      endpoint: '/api/users',\n      method: 'GET'\n    },\n    {\n      name: 'جلب الحرفيين فقط',\n      endpoint: '/api/users?role=CRAFTSMAN',\n      method: 'GET'\n    },\n    {\n      name: 'جلب مستخدم محدد',\n      endpoint: '/api/users/craftsman1',\n      method: 'GET'\n    },\n    {\n      name: 'إنشاء مشروع جديد',\n      endpoint: '/api/projects',\n      method: 'POST',\n      body: {\n        title: 'مشروع اختبار',\n        description: 'وصف المشروع التجريبي',\n        category: 'اختبار',\n        budget: '₺1,000 - ₺2,000',\n        deadline: '2024-04-01',\n        location: 'دمشق',\n        priority: 'MEDIUM',\n        clientId: 'client',\n        materials: 'متوفرة',\n        workType: 'جديد',\n        requirements: 'لا توجد متطلبات خاصة'\n      }\n    },\n    {\n      name: 'تقديم عرض جديد',\n      endpoint: '/api/offers',\n      method: 'POST',\n      body: {\n        projectId: 'project1',\n        craftsmanId: 'craftsman2',\n        amount: 6500,\n        currency: 'TRY',\n        description: 'عرض تجريبي للاختبار',\n        estimatedDuration: 10\n      }\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-12 px-4\">\n      <div className=\"max-w-6xl mx-auto\">\n        <Card className=\"mb-8\">\n          <CardHeader>\n            <CardTitle className=\"text-center text-2xl font-bold text-navy\">\n              اختبار API Routes\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6\">\n              {testCases.map((testCase, index) => (\n                <Button\n                  key={index}\n                  onClick={() => testEndpoint(testCase.endpoint, testCase.method, testCase.body)}\n                  disabled={loading}\n                  variant={testCase.method === 'GET' ? 'default' : 'outline'}\n                  className=\"h-auto p-4 text-right\"\n                >\n                  <div>\n                    <div className=\"font-semibold\">{testCase.name}</div>\n                    <div className=\"text-xs opacity-75\">\n                      {testCase.method} {testCase.endpoint}\n                    </div>\n                  </div>\n                </Button>\n              ))}\n            </div>\n\n            {loading && (\n              <div className=\"text-center py-8\">\n                <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-teal mx-auto mb-4\"></div>\n                <p>جاري التحميل...</p>\n              </div>\n            )}\n\n            {error && (\n              <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4\">\n                <strong>خطأ:</strong> {error}\n              </div>\n            )}\n\n            {results && (\n              <div className=\"bg-gray-100 rounded-lg p-4\">\n                <h3 className=\"font-semibold mb-2\">النتائج:</h3>\n                <pre className=\"text-sm overflow-auto max-h-96 bg-white p-4 rounded border\">\n                  {JSON.stringify(results, null, 2)}\n                </pre>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardHeader>\n            <CardTitle>حالة الخدمات</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center justify-between\">\n                <span>NextAuth</span>\n                <span className=\"bg-green-100 text-green-800 px-2 py-1 rounded text-sm\">\n                  ✅ يعمل\n                </span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span>API Routes</span>\n                <span className=\"bg-green-100 text-green-800 px-2 py-1 rounded text-sm\">\n                  ✅ يعمل\n                </span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span>Mock Data</span>\n                <span className=\"bg-green-100 text-green-800 px-2 py-1 rounded text-sm\">\n                  ✅ يعمل\n                </span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span>Database (PostgreSQL)</span>\n                <span className=\"bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm\">\n                  ⚠️ معطل (استخدام Mock Data)\n                </span>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,eAAe,OAAO,UAAkB,SAAiB,KAAK,EAAE;QACpE,WAAW;QACX,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,UAAuB;gBAC3B;gBACA,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,QAAQ,WAAW,OAAO;gBAC5B,QAAQ,IAAI,GAAG,KAAK,SAAS,CAAC;YAChC;YAEA,MAAM,WAAW,MAAM,MAAM,UAAU;YACvC,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,WAAW;QACb,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;QACtB,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,YAAY;QAChB;YACE,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;QACV;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,MAAM;gBACJ,OAAO;gBACP,aAAa;gBACb,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,UAAU;gBACV,WAAW;gBACX,UAAU;gBACV,cAAc;YAChB;QACF;QACA;YACE,MAAM;YACN,UAAU;YACV,QAAQ;YACR,MAAM;gBACJ,WAAW;gBACX,aAAa;gBACb,QAAQ;gBACR,UAAU;gBACV,aAAa;gBACb,mBAAmB;YACrB;QACF;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,mIAAA,CAAA,OAAI;oBAAC,WAAU;;sCACd,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gCAAC,WAAU;0CAA2C;;;;;;;;;;;sCAIlE,6LAAC,mIAAA,CAAA,cAAW;;8CACV,6LAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,6LAAC,qIAAA,CAAA,SAAM;4CAEL,SAAS,IAAM,aAAa,SAAS,QAAQ,EAAE,SAAS,MAAM,EAAE,SAAS,IAAI;4CAC7E,UAAU;4CACV,SAAS,SAAS,MAAM,KAAK,QAAQ,YAAY;4CACjD,WAAU;sDAEV,cAAA,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAiB,SAAS,IAAI;;;;;;kEAC7C,6LAAC;wDAAI,WAAU;;4DACZ,SAAS,MAAM;4DAAC;4DAAE,SAAS,QAAQ;;;;;;;;;;;;;2CATnC;;;;;;;;;;gCAgBV,yBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;sDAAE;;;;;;;;;;;;gCAIN,uBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAO;;;;;;wCAAa;wCAAE;;;;;;;gCAI1B,yBACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAqB;;;;;;sDACnC,6LAAC;4CAAI,WAAU;sDACZ,KAAK,SAAS,CAAC,SAAS,MAAM;;;;;;;;;;;;;;;;;;;;;;;;8BAOzC,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;0DAAwD;;;;;;;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;0DAAwD;;;;;;;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;0DAAwD;;;;;;;;;;;;kDAI1E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;gDAAK,WAAU;0DAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1F;GAnMwB;KAAA"}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}