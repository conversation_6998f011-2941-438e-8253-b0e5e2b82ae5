{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/lib/mock-data.ts"], "sourcesContent": ["// بيانات تجريبية للتطوير بدون قاعدة بيانات\n\nexport interface User {\n  id: string;\n  email: string;\n  name: string;\n  role: 'ADMIN' | 'CLIENT' | 'CRAFTSMAN';\n  phone?: string;\n  location?: string;\n  bio?: string;\n  avatar?: string;\n  isActive: boolean;\n  isVerified: boolean;\n  createdAt: Date;\n}\n\nexport interface Project {\n  id: string;\n  title: string;\n  description: string;\n  category: string;\n  budget: string;\n  deadline: Date;\n  location: string;\n  priority: 'LOW' | 'MEDIUM' | 'HIGH';\n  status: 'OPEN' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';\n  clientId: string;\n  craftsmanId?: string;\n  images: string[];\n  materials?: string;\n  workType?: string;\n  requirements?: string;\n  views: number;\n  featured: boolean;\n  createdAt: Date;\n}\n\nexport interface Offer {\n  id: string;\n  projectId: string;\n  craftsmanId: string;\n  amount: number;\n  currency: string;\n  description: string;\n  estimatedDuration: number;\n  status: 'PENDING' | 'ACCEPTED' | 'REJECTED';\n  createdAt: Date;\n}\n\nexport interface Notification {\n  id: string;\n  userId: string;\n  title: string;\n  message: string;\n  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR';\n  isRead: boolean;\n  createdAt: Date;\n}\n\n// بيانات المستخدمين التجريبية\nexport const mockUsers: User[] = [\n  {\n    id: 'admin',\n    email: '<EMAIL>',\n    name: 'مدير النظام',\n    role: 'ADMIN',\n    phone: '+963 11 123 4567',\n    location: 'دمشق، سوريا',\n    bio: 'مدير منصة دوزان',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-01'),\n  },\n  {\n    id: 'client',\n    email: '<EMAIL>',\n    name: 'أحمد محمد',\n    role: 'CLIENT',\n    phone: '+963 123 456 789',\n    location: 'دمشق - المزة',\n    bio: 'عميل يبحث عن حرفيين مؤهلين',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-15'),\n  },\n  {\n    id: 'craftsman1',\n    email: '<EMAIL>',\n    name: 'محمد النجار',\n    role: 'CRAFTSMAN',\n    phone: '+963 987 654 321',\n    location: 'دمشق - كفرسوسة',\n    bio: 'نجار محترف مع خبرة 10 سنوات',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-10'),\n  },\n  {\n    id: 'craftsman2',\n    email: '<EMAIL>',\n    name: 'سارة الكهربائية',\n    role: 'CRAFTSMAN',\n    phone: '+963 555 123 456',\n    location: 'حلب - الفرقان',\n    bio: 'كهربائية متخصصة في الأنظمة الذكية',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-20'),\n  },\n];\n\n// بيانات المشاريع التجريبية\nexport const mockProjects: Project[] = [\n  {\n    id: 'project1',\n    title: 'تجديد مطبخ منزلي',\n    description: 'أحتاج إلى تجديد مطبخ منزلي بالكامل مع تغيير الخزائن والأرضية',\n    category: 'نجارة',\n    budget: '₺6,000 - ₺8,000',\n    deadline: new Date('2024-03-15'),\n    location: 'دمشق - المزة',\n    priority: 'HIGH',\n    status: 'OPEN',\n    clientId: 'client',\n    images: [],\n    materials: 'متوفرة جزئياً',\n    workType: 'تجديد',\n    requirements: 'يفضل استخدام خشب عالي الجودة',\n    views: 15,\n    featured: true,\n    createdAt: new Date('2024-02-01'),\n  },\n  {\n    id: 'project2',\n    title: 'تركيب نظام إضاءة ذكي',\n    description: 'تركيب نظام إضاءة ذكي في المنزل مع إمكانية التحكم عبر الهاتف',\n    category: 'كهرباء',\n    budget: '₺4,000 - ₺6,000',\n    deadline: new Date('2024-03-10'),\n    location: 'دمشق - المالكي',\n    priority: 'MEDIUM',\n    status: 'OPEN',\n    clientId: 'client',\n    images: [],\n    materials: 'غير متوفرة',\n    workType: 'تركيب',\n    requirements: 'نظام متوافق مع الهواتف الذكية',\n    views: 8,\n    featured: false,\n    createdAt: new Date('2024-02-05'),\n  },\n];\n\n// بيانات العروض التجريبية\nexport const mockOffers: Offer[] = [\n  {\n    id: 'offer1',\n    projectId: 'project1',\n    craftsmanId: 'craftsman1',\n    amount: 7000,\n    currency: 'TRY',\n    description: 'يمكنني تنفيذ هذا المشروع بجودة عالية وفي الوقت المحدد',\n    estimatedDuration: 14,\n    status: 'PENDING',\n    createdAt: new Date('2024-02-02'),\n  },\n  {\n    id: 'offer2',\n    projectId: 'project2',\n    craftsmanId: 'craftsman2',\n    amount: 5000,\n    currency: 'TRY',\n    description: 'متخصصة في أنظمة الإضاءة الذكية مع ضمان سنة كاملة',\n    estimatedDuration: 7,\n    status: 'PENDING',\n    createdAt: new Date('2024-02-06'),\n  },\n];\n\n// بيانات الإشعارات التجريبية\nexport const mockNotifications: Notification[] = [\n  {\n    id: 'notif1',\n    userId: 'client',\n    title: 'عرض جديد على مشروعك',\n    message: 'تلقيت عرضاً جديداً من محمد النجار على مشروع تجديد المطبخ',\n    type: 'INFO',\n    isRead: false,\n    createdAt: new Date('2024-02-02'),\n  },\n  {\n    id: 'notif2',\n    userId: 'craftsman1',\n    title: 'مشروع جديد متاح',\n    message: 'يوجد مشروع نجارة جديد في منطقتك',\n    type: 'INFO',\n    isRead: true,\n    createdAt: new Date('2024-02-01'),\n  },\n];\n\n// دوال مساعدة للبحث والفلترة\nexport const getUserById = (id: string): User | undefined => {\n  return mockUsers.find(user => user.id === id);\n};\n\nexport const getUserByEmail = (email: string): User | undefined => {\n  return mockUsers.find(user => user.email === email);\n};\n\nexport const getProjectsByUserId = (userId: string): Project[] => {\n  return mockProjects.filter(project => \n    project.clientId === userId || project.craftsmanId === userId\n  );\n};\n\nexport const getOffersByProjectId = (projectId: string): Offer[] => {\n  return mockOffers.filter(offer => offer.projectId === projectId);\n};\n\nexport const getOffersByCraftsmanId = (craftsmanId: string): Offer[] => {\n  return mockOffers.filter(offer => offer.craftsmanId === craftsmanId);\n};\n\nexport const getNotificationsByUserId = (userId: string): Notification[] => {\n  return mockNotifications.filter(notification => notification.userId === userId);\n};\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;;;;;;;;;AA4DpC,MAAM,YAAoB;IAC/B;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,EAAE;QACV,WAAW;QACX,UAAU;QACV,cAAc;QACd,OAAO;QACP,UAAU;QACV,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,EAAE;QACV,WAAW;QACX,UAAU;QACV,cAAc;QACd,OAAO;QACP,UAAU;QACV,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,aAAsB;IACjC;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,UAAU;QACV,aAAa;QACb,mBAAmB;QACnB,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,UAAU;QACV,aAAa;QACb,mBAAmB;QACnB,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,oBAAoC;IAC/C;QACE,IAAI;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;QACT,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;QACT,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AAC5C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAO,aAAa,MAAM,CAAC,CAAA,UACzB,QAAQ,QAAQ,KAAK,UAAU,QAAQ,WAAW,KAAK;AAE3D;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,WAAW,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,KAAK;AACxD;AAEO,MAAM,yBAAyB,CAAC;IACrC,OAAO,WAAW,MAAM,CAAC,CAAA,QAAS,MAAM,WAAW,KAAK;AAC1D;AAEO,MAAM,2BAA2B,CAAC;IACvC,OAAO,kBAAkB,MAAM,CAAC,CAAA,eAAgB,aAAa,MAAM,KAAK;AAC1E"}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/api/projects/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { mockProjects, mockUsers, mockOffers } from '@/lib/mock-data';\n\n// GET /api/projects/[id] - جلب مشروع محدد\nexport async function GET(\n  request: NextRequest,\n  context: { params: { id: string } }\n) {\n  try {\n    const { id } = context.params;\n    \n    const project = mockProjects.find(p => p.id === id);\n    if (!project) {\n      return NextResponse.json(\n        { error: 'المشروع غير موجود' },\n        { status: 404 }\n      );\n    }\n\n    // زيادة عدد المشاهدات\n    project.views += 1;\n\n    // إضافة بيانات العميل والحرفي\n    const client = mockUsers.find(user => user.id === project.clientId);\n    const craftsman = project.craftsmanId ? \n      mockUsers.find(user => user.id === project.craftsmanId) : null;\n\n    // جلب العروض المقدمة على هذا المشروع\n    const offers = mockOffers.filter(offer => offer.projectId === id);\n    const offersWithCraftsmen = offers.map(offer => {\n      const offerCraftsman = mockUsers.find(user => user.id === offer.craftsmanId);\n      return {\n        ...offer,\n        craftsman: offerCraftsman ? {\n          id: offerCraftsman.id,\n          name: offerCraftsman.name,\n          location: offerCraftsman.location,\n          avatar: offerCraftsman.avatar,\n          bio: offerCraftsman.bio\n        } : null\n      };\n    });\n\n    const projectWithDetails = {\n      ...project,\n      client: client ? {\n        id: client.id,\n        name: client.name,\n        location: client.location,\n        avatar: client.avatar,\n        bio: client.bio\n      } : null,\n      craftsman: craftsman ? {\n        id: craftsman.id,\n        name: craftsman.name,\n        location: craftsman.location,\n        avatar: craftsman.avatar,\n        bio: craftsman.bio\n      } : null,\n      offers: offersWithCraftsmen\n    };\n\n    return NextResponse.json({ project: projectWithDetails });\n  } catch (error) {\n    console.error('Error fetching project:', error);\n    return NextResponse.json(\n      { error: 'خطأ في جلب المشروع' },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT /api/projects/[id] - تحديث مشروع\nexport async function PUT(\n  request: NextRequest,\n  context: { params: { id: string } }\n) {\n  try {\n    const { id } = context.params;\n    const body = await request.json();\n\n    const projectIndex = mockProjects.findIndex(p => p.id === id);\n    if (projectIndex === -1) {\n      return NextResponse.json(\n        { error: 'المشروع غير موجود' },\n        { status: 404 }\n      );\n    }\n\n    // تحديث المشروع\n    const updatedProject = {\n      ...mockProjects[projectIndex],\n      ...body,\n      id, // التأكد من عدم تغيير المعرف\n    };\n\n    mockProjects[projectIndex] = updatedProject;\n\n    return NextResponse.json({\n      project: updatedProject,\n      message: 'تم تحديث المشروع بنجاح'\n    });\n  } catch (error) {\n    console.error('Error updating project:', error);\n    return NextResponse.json(\n      { error: 'خطأ في تحديث المشروع' },\n      { status: 500 }\n    );\n  }\n}\n\n// DELETE /api/projects/[id] - حذف مشروع\nexport async function DELETE(\n  request: NextRequest,\n  context: { params: { id: string } }\n) {\n  try {\n    const { id } = context.params;\n\n    const projectIndex = mockProjects.findIndex(p => p.id === id);\n    if (projectIndex === -1) {\n      return NextResponse.json(\n        { error: 'المشروع غير موجود' },\n        { status: 404 }\n      );\n    }\n\n    // حذف المشروع\n    mockProjects.splice(projectIndex, 1);\n\n    // حذف العروض المرتبطة بالمشروع\n    const offerIndices = mockOffers\n      .map((offer, index) => offer.projectId === id ? index : -1)\n      .filter(index => index !== -1)\n      .reverse(); // البدء من النهاية لتجنب تغيير الفهارس\n\n    offerIndices.forEach(index => mockOffers.splice(index, 1));\n\n    return NextResponse.json({ message: 'تم حذف المشروع بنجاح' });\n  } catch (error) {\n    console.error('Error deleting project:', error);\n    return NextResponse.json(\n      { error: 'خطأ في حذف المشروع' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAGO,eAAe,IACpB,OAAoB,EACpB,OAAmC;IAEnC,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,QAAQ,MAAM;QAE7B,MAAM,UAAU,4HAAA,CAAA,eAAY,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAChD,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,sBAAsB;QACtB,QAAQ,KAAK,IAAI;QAEjB,8BAA8B;QAC9B,MAAM,SAAS,4HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,QAAQ,QAAQ;QAClE,MAAM,YAAY,QAAQ,WAAW,GACnC,4HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,QAAQ,WAAW,IAAI;QAE5D,qCAAqC;QACrC,MAAM,SAAS,4HAAA,CAAA,aAAU,CAAC,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,KAAK;QAC9D,MAAM,sBAAsB,OAAO,GAAG,CAAC,CAAA;YACrC,MAAM,iBAAiB,4HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,MAAM,WAAW;YAC3E,OAAO;gBACL,GAAG,KAAK;gBACR,WAAW,iBAAiB;oBAC1B,IAAI,eAAe,EAAE;oBACrB,MAAM,eAAe,IAAI;oBACzB,UAAU,eAAe,QAAQ;oBACjC,QAAQ,eAAe,MAAM;oBAC7B,KAAK,eAAe,GAAG;gBACzB,IAAI;YACN;QACF;QAEA,MAAM,qBAAqB;YACzB,GAAG,OAAO;YACV,QAAQ,SAAS;gBACf,IAAI,OAAO,EAAE;gBACb,MAAM,OAAO,IAAI;gBACjB,UAAU,OAAO,QAAQ;gBACzB,QAAQ,OAAO,MAAM;gBACrB,KAAK,OAAO,GAAG;YACjB,IAAI;YACJ,WAAW,YAAY;gBACrB,IAAI,UAAU,EAAE;gBAChB,MAAM,UAAU,IAAI;gBACpB,UAAU,UAAU,QAAQ;gBAC5B,QAAQ,UAAU,MAAM;gBACxB,KAAK,UAAU,GAAG;YACpB,IAAI;YACJ,QAAQ;QACV;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAmB;IACzD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAqB,GAC9B;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,OAAmC;IAEnC,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,QAAQ,MAAM;QAC7B,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,MAAM,eAAe,4HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1D,IAAI,iBAAiB,CAAC,GAAG;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,gBAAgB;QAChB,MAAM,iBAAiB;YACrB,GAAG,4HAAA,CAAA,eAAY,CAAC,aAAa;YAC7B,GAAG,IAAI;YACP;QACF;QAEA,4HAAA,CAAA,eAAY,CAAC,aAAa,GAAG;QAE7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,OAAmC;IAEnC,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG,QAAQ,MAAM;QAE7B,MAAM,eAAe,4HAAA,CAAA,eAAY,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1D,IAAI,iBAAiB,CAAC,GAAG;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAoB,GAC7B;gBAAE,QAAQ;YAAI;QAElB;QAEA,cAAc;QACd,4HAAA,CAAA,eAAY,CAAC,MAAM,CAAC,cAAc;QAElC,+BAA+B;QAC/B,MAAM,eAAe,4HAAA,CAAA,aAAU,CAC5B,GAAG,CAAC,CAAC,OAAO,QAAU,MAAM,SAAS,KAAK,KAAK,QAAQ,CAAC,GACxD,MAAM,CAAC,CAAA,QAAS,UAAU,CAAC,GAC3B,OAAO,IAAI,uCAAuC;QAErD,aAAa,OAAO,CAAC,CAAA,QAAS,4HAAA,CAAA,aAAU,CAAC,MAAM,CAAC,OAAO;QAEvD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;QAAuB;IAC7D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAqB,GAC9B;YAAE,QAAQ;QAAI;IAElB;AACF"}}, {"offset": {"line": 356, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}