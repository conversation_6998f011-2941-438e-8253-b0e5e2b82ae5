import { <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, OneToMany } from 'typeorm';
import { Job } from './job.entity';
import { Offer } from './offer.entity';
import { Message } from './message.entity';
import { Transaction } from './transaction.entity';
import { Review } from './review.entity';

export enum UserRole {
  CLIENT = 'client',
  CRAFTSMAN = 'craftsman',
  ADMIN = 'admin',
}

@Entity('users')
export class User {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ length: 100 })
  name: string;

  @Column({ unique: true })
  email: string;

  @Column({ select: false })
  password: string;

  @Column({ nullable: true })
  phoneNumber: string;

  @Column({
    type: 'enum',
    enum: UserRole,
    default: UserRole.CLIENT,
  })
  role: UserRole;

  @Column({ nullable: true })
  profilePicture: string;

  @Column({ nullable: true })
  bio: string;

  @Column({ nullable: true })
  location: string;

  @Column({ nullable: true })
  skills: string;

  @Column({ default: 0 })
  rating: number;

  @Column({ default: 0 })
  reviewCount: number;

  @Column({ nullable: true })
  googleId: string;

  @Column({ nullable: true })
  facebookId: string;

  @Column({ default: false })
  isVerified: boolean;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => Job, job => job.client)
  clientJobs: Job[];

  @OneToMany(() => Offer, offer => offer.craftsman)
  offers: Offer[];

  @OneToMany(() => Message, message => message.sender)
  sentMessages: Message[];

  @OneToMany(() => Message, message => message.receiver)
  receivedMessages: Message[];

  @OneToMany(() => Transaction, transaction => transaction.client)
  clientTransactions: Transaction[];

  @OneToMany(() => Transaction, transaction => transaction.craftsman)
  craftsmanTransactions: Transaction[];

  @OneToMany(() => Review, review => review.client)
  givenReviews: Review[];

  @OneToMany(() => Review, review => review.craftsman)
  receivedReviews: Review[];
}
