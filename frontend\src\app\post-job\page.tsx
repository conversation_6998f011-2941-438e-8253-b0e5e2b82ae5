'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { JobFormProvider, useJobForm } from '@/context/JobFormContext';
import FormLayout from '@/components/jobs/FormLayout';
import Step1BasicDetails from '@/components/jobs/Step1BasicDetails';
import Step2Requirements from '@/components/jobs/Step2Requirements';
import Step3BudgetTimeline from '@/components/jobs/Step3BudgetTimeline';
import Step4Review from '@/components/jobs/Step4Review';
import { useCreateProject } from '@/hooks/useApi';

const JobCreationForm = () => {
  const router = useRouter();
  const { data: session } = useSession();
  const { formData, currentStep, setCurrentStep, isSubmitting, setIsSubmitting } = useJobForm();
  const { mutate: createProject, loading: creating, error: createError } = useCreateProject();
  const totalSteps = 4;

  const validateStep = (step: number) => {
    switch (step) {
      case 1:
        return (
          formData.title && formData.title.trim().length >= 5 &&
          formData.description && formData.description.trim().length >= 20 &&
          formData.category && formData.category.trim() !== ''
        );

      case 2:
        return (
          formData.requirements && formData.requirements.trim() !== '' &&
          formData.skills && formData.skills.length > 0
        );

      case 3:
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const selectedDate = formData.deadline ? new Date(formData.deadline) : null;
        return (
          formData.budget && formData.budget > 0 &&
          formData.deadline && formData.deadline.trim() !== '' &&
          selectedDate && selectedDate >= today
        );

      default:
        return true;
    }
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      if (validateStep(currentStep)) {
        setCurrentStep(currentStep + 1);
        window.scrollTo(0, 0);
      } else {
        // Show detailed validation errors based on current step
        let errorMessage = 'يرجى إكمال جميع الحقول المطلوبة:\n';

        switch (currentStep) {
          case 1:
            if (!formData.title || formData.title.trim().length < 5) {
              errorMessage += '- عنوان المشروع (5 أحرف على الأقل)\n';
            }
            if (!formData.description || formData.description.trim().length < 20) {
              errorMessage += '- وصف المشروع (20 حرف على الأقل)\n';
            }
            if (!formData.category) {
              errorMessage += '- فئة المشروع\n';
            }
            break;
          case 2:
            if (!formData.requirements || formData.requirements.trim() === '') {
              errorMessage += '- متطلبات المشروع\n';
            }
            if (!formData.skills || formData.skills.length === 0) {
              errorMessage += '- المهارات المطلوبة (مهارة واحدة على الأقل)\n';
            }
            break;
          case 3:
            if (!formData.budget || formData.budget <= 0) {
              errorMessage += '- ميزانية المشروع\n';
            }
            if (!formData.deadline) {
              errorMessage += '- الموعد النهائي للمشروع\n';
            }
            break;
        }

        alert(errorMessage);
      }
    } else {
      handleSubmit();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
      window.scrollTo(0, 0);
    }
  };

  const handleSubmit = async () => {
    if (!session?.user?.id) {
      alert('يجب تسجيل الدخول أولاً');
      router.push('/auth/signin');
      return;
    }

    setIsSubmitting(true);

    try {
      const projectData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        budget: formData.budget ? `₺${formData.budget.toLocaleString()}` : '',
        deadline: formData.deadline,
        location: formData.location || 'غير محدد',
        priority: 'MEDIUM',
        clientId: session.user.id,
        materials: formData.materials || 'غير محدد',
        workType: formData.workType || 'عام',
        requirements: formData.requirements,
        images: formData.images || []
      };

      const result = await createProject(projectData);

      if (result) {
        // Redirect to success page
        router.push('/job-posted-success');
      } else {
        throw new Error(createError || 'فشل في إنشاء المشروع');
      }
    } catch (error) {
      console.error('Error submitting job:', error);
      alert('حدث خطأ أثناء نشر المشروع. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <FormLayout
      currentStep={currentStep}
      totalSteps={totalSteps}
      onNext={handleNext}
      onPrevious={handlePrevious}
      isLastStep={currentStep === totalSteps}
      isSubmitting={isSubmitting}
    >
      {currentStep === 1 && <Step1BasicDetails />}
      {currentStep === 2 && <Step2Requirements />}
      {currentStep === 3 && <Step3BudgetTimeline />}
      {currentStep === 4 && <Step4Review />}
    </FormLayout>
  );
};

const PostJobPage = () => {
  return (
    <JobFormProvider>
      <JobCreationForm />
    </JobFormProvider>
  );
};

export default PostJobPage;
