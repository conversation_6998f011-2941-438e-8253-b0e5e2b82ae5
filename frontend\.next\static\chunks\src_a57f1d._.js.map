{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// دالة تنسيق التاريخ بشكل ثابت لتجنب مشاكل Hydration\nexport function formatDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n\n    // التحقق من صحة التاريخ\n    if (isNaN(date.getTime())) {\n      return 'تاريخ غير صحيح';\n    }\n\n    // تنسيق ثابت للتاريخ\n    const year = date.getFullYear();\n    const month = date.getMonth() + 1;\n    const day = date.getDate();\n\n    // أسماء الشهور بالعربية\n    const monthNames = [\n      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',\n      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'\n    ];\n\n    return `${day} ${monthNames[month - 1]} ${year}`;\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق التاريخ النسبي (منذ كم يوم)\nexport function formatRelativeDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInMs = now.getTime() - date.getTime();\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\n\n    if (diffInDays === 0) {\n      return 'اليوم';\n    } else if (diffInDays === 1) {\n      return 'أمس';\n    } else if (diffInDays < 7) {\n      return `منذ ${diffInDays} أيام`;\n    } else if (diffInDays < 30) {\n      const weeks = Math.floor(diffInDays / 7);\n      return `منذ ${weeks} ${weeks === 1 ? 'أسبوع' : 'أسابيع'}`;\n    } else if (diffInDays < 365) {\n      const months = Math.floor(diffInDays / 30);\n      return `منذ ${months} ${months === 1 ? 'شهر' : 'أشهر'}`;\n    } else {\n      const years = Math.floor(diffInDays / 365);\n      return `منذ ${years} ${years === 1 ? 'سنة' : 'سنوات'}`;\n    }\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق الأرقام بالفواصل\nexport function formatNumber(num: number): string {\n  return num.toLocaleString('ar-SA');\n}\n\n// دالة للتحقق من صحة البريد الإلكتروني\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// دالة للتحقق من صحة رقم الهاتف السوري\nexport function isValidSyrianPhone(phone: string): boolean {\n  const phoneRegex = /^(\\+963|0)?[0-9]{9,10}$/;\n  return phoneRegex.test(phone);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,UAAkB;IAC3C,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QAEtB,wBAAwB;QACxB,IAAI,MAAM,KAAK,OAAO,KAAK;YACzB,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,KAAK,QAAQ,KAAK;QAChC,MAAM,MAAM,KAAK,OAAO;QAExB,wBAAwB;QACxB,MAAM,aAAa;YACjB;YAAS;YAAU;YAAQ;YAAS;YAAQ;YAC5C;YAAS;YAAS;YAAU;YAAU;YAAU;SACjD;QAED,OAAO,GAAG,IAAI,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,MAAM;IAClD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,mBAAmB,UAAkB;IACnD,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;QAC7C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG;YACpB,OAAO;QACT,OAAO,IAAI,eAAe,GAAG;YAC3B,OAAO;QACT,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO,CAAC,IAAI,EAAE,WAAW,KAAK,CAAC;QACjC,OAAO,IAAI,aAAa,IAAI;YAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,UAAU,UAAU;QAC3D,OAAO,IAAI,aAAa,KAAK;YAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,aAAa;YACvC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,WAAW,IAAI,QAAQ,QAAQ;QACzD,OAAO;YACL,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,QAAQ,SAAS;QACxD;IACF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,cAAc,CAAC;AAC5B;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,mBAAmB,KAAa;IAC9C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB"}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',\n  {\n    variants: {\n      variant: {\n        default: 'bg-navy text-white hover:bg-navy/90',\n        destructive: 'bg-red-500 text-white hover:bg-red-500/90',\n        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',\n        secondary: 'bg-teal text-white hover:bg-teal/90',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'underline-offset-4 hover:underline text-primary',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 rounded-md',\n        lg: 'h-11 px-8 rounded-md',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,wQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/Button';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title: string;\n  subtitle?: string;\n}\n\nexport default function DashboardLayout({ children, title, subtitle }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  \n  // Mock user for development - will be replaced with real auth\n  const mockUser = { name: 'مستخدم تجريبي', role: 'CLIENT' };\n  \n  const isClient = mockUser?.role === 'CLIENT';\n  const isCraftsman = mockUser?.role === 'CRAFTSMAN';\n  const isAdmin = mockUser?.role === 'ADMIN';\n\n  const clientMenuItems = [\n    { href: '/dashboard/client', label: 'لوحة التحكم', icon: '🏠' },\n    { href: '/dashboard/client/projects', label: 'مشاريعي', icon: '📋' },\n    { href: '/projects/create', label: 'إنشاء مشروع', icon: '➕' },\n    { href: '/messages', label: 'الرسائل', icon: '💬' },\n    { href: '/dashboard/client/profile', label: 'الملف الشخصي', icon: '👤' },\n  ];\n\n  const craftsmanMenuItems = [\n    { href: '/dashboard/craftsman', label: 'لوحة التحكم', icon: '🏠' },\n    { href: '/dashboard/craftsman/bids', label: 'عروضي', icon: '📋' },\n    { href: '/dashboard/craftsman/projects', label: 'مشاريعي', icon: '🔨' },\n    { href: '/dashboard/craftsman/portfolio', label: 'معرض الأعمال', icon: '🎨' },\n    { href: '/messages', label: 'الرسائل', icon: '💬' },\n    { href: '/dashboard/craftsman/profile', label: 'الملف الشخصي', icon: '👤' },\n  ];\n\n  const adminMenuItems = [\n    { href: '/dashboard/admin', label: 'لوحة التحكم', icon: '🏠' },\n    { href: '/dashboard/admin/users', label: 'المستخدمين', icon: '👥' },\n    { href: '/dashboard/admin/projects', label: 'المشاريع', icon: '📋' },\n    { href: '/dashboard/admin/reports', label: 'التقارير', icon: '📊' },\n    { href: '/dashboard/admin/settings', label: 'الإعدادات', icon: '⚙️' },\n  ];\n\n  const menuItems = isClient ? clientMenuItems : isCraftsman ? craftsmanMenuItems : adminMenuItems;\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"flex items-center justify-between px-6 py-4\">\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            <button\n              onClick={() => setSidebarOpen(!sidebarOpen)}\n              className=\"lg:hidden p-2 rounded-md text-gray-600 hover:bg-gray-100\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n            \n            <Link href=\"/\" className=\"flex items-center space-x-2 space-x-reverse\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-teal to-navy rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">د</span>\n              </div>\n              <span className=\"text-xl font-bold text-navy\">دوزان</span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-4 space-x-reverse\">\n            <div className=\"hidden md:block text-right\">\n              <p className=\"text-sm font-medium text-gray-900\">{mockUser?.name}</p>\n              <p className=\"text-xs text-gray-500\">\n                {isClient ? 'عميل' : isCraftsman ? 'حرفي' : 'مدير'}\n              </p>\n            </div>\n            \n            <div className=\"w-10 h-10 bg-gradient-to-r from-teal to-navy rounded-full flex items-center justify-center\">\n              <span className=\"text-white font-bold\">\n                {mockUser?.name?.charAt(0) || 'م'}\n              </span>\n            </div>\n\n            <Button\n              onClick={() => window.location.href = '/'}\n              variant=\"outline\"\n              size=\"sm\"\n              className=\"text-red-600 border-red-200 hover:bg-red-50\"\n            >\n              تسجيل الخروج\n            </Button>\n          </div>\n        </div>\n      </header>\n\n      <div className=\"flex\">\n        {/* Sidebar */}\n        <aside className={`\n          fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out\n          lg:translate-x-0 lg:static lg:inset-0\n          ${sidebarOpen ? 'translate-x-0' : 'translate-x-full'}\n        `}>\n          <div className=\"flex flex-col h-full pt-20 lg:pt-0\">\n            <nav className=\"flex-1 px-4 py-6 space-y-2\">\n              {menuItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors\"\n                  onClick={() => setSidebarOpen(false)}\n                >\n                  <span className=\"text-xl\">{item.icon}</span>\n                  <span className=\"font-medium\">{item.label}</span>\n                </Link>\n              ))}\n            </nav>\n\n            <div className=\"p-4 border-t\">\n              <Link\n                href=\"/\"\n                className=\"flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors\"\n              >\n                <span className=\"text-xl\">🏠</span>\n                <span className=\"font-medium\">العودة للموقع</span>\n              </Link>\n            </div>\n          </div>\n        </aside>\n\n        {/* Overlay for mobile */}\n        {sidebarOpen && (\n          <div\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n\n        {/* Main Content */}\n        <main className=\"flex-1 lg:mr-64\">\n          <div className=\"p-6\">\n            {/* Page Header */}\n            <div className=\"mb-8\">\n              <h1 className=\"text-3xl font-bold text-navy\">{title}</h1>\n              {subtitle && (\n                <p className=\"text-gray-600 mt-2\">{subtitle}</p>\n              )}\n            </div>\n\n            {/* Page Content */}\n            {children}\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAYe,SAAS,gBAAgB,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAwB;;IACzF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,8DAA8D;IAC9D,MAAM,WAAW;QAAE,MAAM;QAAiB,MAAM;IAAS;IAEzD,MAAM,WAAW,UAAU,SAAS;IACpC,MAAM,cAAc,UAAU,SAAS;IACvC,MAAM,UAAU,UAAU,SAAS;IAEnC,MAAM,kBAAkB;QACtB;YAAE,MAAM;YAAqB,OAAO;YAAe,MAAM;QAAK;QAC9D;YAAE,MAAM;YAA8B,OAAO;YAAW,MAAM;QAAK;QACnE;YAAE,MAAM;YAAoB,OAAO;YAAe,MAAM;QAAI;QAC5D;YAAE,MAAM;YAAa,OAAO;YAAW,MAAM;QAAK;QAClD;YAAE,MAAM;YAA6B,OAAO;YAAgB,MAAM;QAAK;KACxE;IAED,MAAM,qBAAqB;QACzB;YAAE,MAAM;YAAwB,OAAO;YAAe,MAAM;QAAK;QACjE;YAAE,MAAM;YAA6B,OAAO;YAAS,MAAM;QAAK;QAChE;YAAE,MAAM;YAAiC,OAAO;YAAW,MAAM;QAAK;QACtE;YAAE,MAAM;YAAkC,OAAO;YAAgB,MAAM;QAAK;QAC5E;YAAE,MAAM;YAAa,OAAO;YAAW,MAAM;QAAK;QAClD;YAAE,MAAM;YAAgC,OAAO;YAAgB,MAAM;QAAK;KAC3E;IAED,MAAM,iBAAiB;QACrB;YAAE,MAAM;YAAoB,OAAO;YAAe,MAAM;QAAK;QAC7D;YAAE,MAAM;YAA0B,OAAO;YAAc,MAAM;QAAK;QAClE;YAAE,MAAM;YAA6B,OAAO;YAAY,MAAM;QAAK;QACnE;YAAE,MAAM;YAA4B,OAAO;YAAY,MAAM;QAAK;QAClE;YAAE,MAAM;YAA6B,OAAO;YAAa,MAAM;QAAK;KACrE;IAED,MAAM,YAAY,WAAW,kBAAkB,cAAc,qBAAqB;IAElF,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAIzE,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,6LAAC;4CAAK,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;sCAIlD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAqC,UAAU;;;;;;sDAC5D,6LAAC;4CAAE,WAAU;sDACV,WAAW,SAAS,cAAc,SAAS;;;;;;;;;;;;8CAIhD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDACb,UAAU,MAAM,OAAO,MAAM;;;;;;;;;;;8CAIlC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,SAAQ;oCACR,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAOP,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAM,WAAW,CAAC;;;UAGjB,EAAE,cAAc,kBAAkB,mBAAmB;QACvD,CAAC;kCACC,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,WAAU;4CACV,SAAS,IAAM,eAAe;;8DAE9B,6LAAC;oDAAK,WAAU;8DAAW,KAAK,IAAI;;;;;;8DACpC,6LAAC;oDAAK,WAAU;8DAAe,KAAK,KAAK;;;;;;;2CANpC,KAAK,IAAI;;;;;;;;;;8CAWpB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC;gDAAK,WAAU;0DAAU;;;;;;0DAC1B,6LAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOrC,6BACC,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,eAAe;;;;;;kCAKlC,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAgC;;;;;;wCAC7C,0BACC,6LAAC;4CAAE,WAAU;sDAAsB;;;;;;;;;;;;gCAKtC;;;;;;;;;;;;;;;;;;;;;;;;AAMb;GAlJwB;KAAA"}}, {"offset": {"line": 585, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 591, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-white shadow-sm transition-all duration-200 hover:shadow-md',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('font-semibold leading-none tracking-tight text-navy', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-600', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACpE,GAAG,KAAK;kBAER;;;;;;;AAGL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;;AAGL,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG"}}, {"offset": {"line": 695, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 701, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useState } from 'react';\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  role: 'client' | 'craftsman';\n  phone?: string;\n}\n\nexport const useAuth = () => {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const login = async (credentials: LoginCredentials) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await signIn('credentials', {\n        email: credentials.email,\n        password: credentials.password,\n        redirect: false,\n      });\n\n      if (result?.error) {\n        setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');\n        return false;\n      }\n\n      if (result?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        setError(errorData.message || 'حدث خطأ أثناء إنشاء الحساب');\n        return false;\n      }\n\n      // تسجيل الدخول تلقائياً بعد التسجيل\n      const loginResult = await signIn('credentials', {\n        email: data.email,\n        password: data.password,\n        redirect: false,\n      });\n\n      if (loginResult?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء إنشاء الحساب');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    setIsLoading(true);\n    try {\n      await signOut({ redirect: false });\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithGoogle = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('google', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Google');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithFacebook = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('facebook', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Facebook');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const isAuthenticated = status === 'authenticated';\n  const isLoading_session = status === 'loading';\n  const user = session?.user;\n\n  // التحقق من الأدوار مع fallback\n  const isClient = user?.role === 'client';\n  const isCraftsman = user?.role === 'craftsman';\n  const isAdmin = user?.role === 'admin';\n\n  // التحقق من الصلاحيات\n  const hasRole = (role: string | string[]) => {\n    if (!user) return false;\n    if (Array.isArray(role)) {\n      return role.includes(user.role);\n    }\n    return user.role === role;\n  };\n\n  const canAccess = (requiredRoles: string | string[]) => {\n    return isAuthenticated && hasRole(requiredRoles);\n  };\n\n  return {\n    // Session data\n    user,\n    session,\n    isAuthenticated,\n    isLoading: isLoading || isLoading_session,\n    error,\n\n    // Role checks\n    isClient,\n    isCraftsman,\n    isAdmin,\n    hasRole,\n    canAccess,\n\n    // Auth methods\n    login,\n    register,\n    logout,\n    loginWithGoogle,\n    loginWithFacebook,\n\n    // Utility\n    setError,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;AAJA;;;;AAmBO,MAAM,UAAU;;IACrB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,QAAQ,OAAO;QACnB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;gBAC9B,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;gBACT,OAAO;YACT;YAEA,IAAI,QAAQ,IAAI;gBACd,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,OAAO,IAAI;gBAC9B,OAAO;YACT;YAEA,oCAAoC;YACpC,MAAM,cAAc,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBAC9C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;YACZ;YAEA,IAAI,aAAa,IAAI;gBACnB,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;gBAAE,UAAU;YAAM;YAChC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBAAE,aAAa;YAAa;QACrD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,YAAY;gBAAE,aAAa;YAAa;QACvD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,WAAW;IACnC,MAAM,oBAAoB,WAAW;IACrC,MAAM,OAAO,SAAS;IAEtB,gCAAgC;IAChC,MAAM,WAAW,MAAM,SAAS;IAChC,MAAM,cAAc,MAAM,SAAS;IACnC,MAAM,UAAU,MAAM,SAAS;IAE/B,sBAAsB;IACtB,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI;QAChC;QACA,OAAO,KAAK,IAAI,KAAK;IACvB;IAEA,MAAM,YAAY,CAAC;QACjB,OAAO,mBAAmB,QAAQ;IACpC;IAEA,OAAO;QACL,eAAe;QACf;QACA;QACA;QACA,WAAW,aAAa;QACxB;QAEA,cAAc;QACd;QACA;QACA;QACA;QACA;QAEA,eAAe;QACf;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;IACF;AACF;GA7Ja;;QACuB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS"}}, {"offset": {"line": 864, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = 'https://lyjelanmcbzymgauwamc.supabase.co'\nconst supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\nconst supabase = createClient(supabaseUrl, supabaseKey)\n\nexport default supabase\n"], "names": [], "mappings": ";;;AAGoB;AAHpB;;AAEA,MAAM,cAAc;AACpB,MAAM;AACN,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;uCAE5B"}}, {"offset": {"line": 883, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 889, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/dashboard/client/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\nimport { useAuth } from '@/hooks/useAuth';\nimport supabase from '@/lib/supabase';\nimport Link from 'next/link';\n\nexport default function ClientDashboard() {\n  const { user } = useAuth();\n  const [stats, setStats] = useState({\n    totalProjects: 0,\n    activeProjects: 0,\n    completedProjects: 0,\n    totalSpent: 0\n  });\n  const [recentProjects, setRecentProjects] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    if (user?.id) {\n      loadDashboardData();\n    }\n  }, [user?.id]);\n\n  const loadDashboardData = async () => {\n    try {\n      // تحميل إحصائيات المشاريع\n      const { data: projects, error } = await supabase\n        .from('projects')\n        .select('*')\n        .eq('client_id', user?.id);\n\n      if (error) throw error;\n\n      const totalProjects = projects?.length || 0;\n      const activeProjects = projects?.filter(p => p.status === 'OPEN' || p.status === 'IN_PROGRESS').length || 0;\n      const completedProjects = projects?.filter(p => p.status === 'COMPLETED').length || 0;\n      const totalSpent = projects?.reduce((sum, p) => sum + (p.budget_max || 0), 0) || 0;\n\n      setStats({\n        totalProjects,\n        activeProjects,\n        completedProjects,\n        totalSpent\n      });\n\n      // تحميل المشاريع الحديثة\n      setRecentProjects(projects?.slice(0, 5) || []);\n\n    } catch (error) {\n      console.error('خطأ في تحميل بيانات لوحة التحكم:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'OPEN': return 'bg-green-100 text-green-800';\n      case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800';\n      case 'COMPLETED': return 'bg-purple-100 text-purple-800';\n      case 'CANCELLED': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'OPEN': return 'مفتوح';\n      case 'IN_PROGRESS': return 'قيد التنفيذ';\n      case 'COMPLETED': return 'مكتمل';\n      case 'CANCELLED': return 'ملغي';\n      default: return status;\n    }\n  };\n\n  if (loading) {\n    return (\n      <DashboardLayout title=\"لوحة تحكم العميل\">\n        <div className=\"flex items-center justify-center h-64\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-teal\"></div>\n        </div>\n      </DashboardLayout>\n    );\n  }\n\n  return (\n    <DashboardLayout \n      title=\"لوحة تحكم العميل\"\n      subtitle={`مرحباً ${user?.name}، إليك نظرة عامة على مشاريعك`}\n    >\n      <div className=\"space-y-6\">\n        {/* الإحصائيات */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <Card className=\"border-0 bg-gradient-to-br from-blue-500 to-blue-600 text-white\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-blue-100 text-sm\">إجمالي المشاريع</p>\n                  <p className=\"text-3xl font-bold\">{stats.totalProjects}</p>\n                </div>\n                <div className=\"text-4xl opacity-80\">📊</div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-0 bg-gradient-to-br from-green-500 to-green-600 text-white\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-green-100 text-sm\">المشاريع النشطة</p>\n                  <p className=\"text-3xl font-bold\">{stats.activeProjects}</p>\n                </div>\n                <div className=\"text-4xl opacity-80\">🚀</div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-0 bg-gradient-to-br from-purple-500 to-purple-600 text-white\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-purple-100 text-sm\">المشاريع المكتملة</p>\n                  <p className=\"text-3xl font-bold\">{stats.completedProjects}</p>\n                </div>\n                <div className=\"text-4xl opacity-80\">✅</div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-0 bg-gradient-to-br from-orange-500 to-orange-600 text-white\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-orange-100 text-sm\">إجمالي الإنفاق</p>\n                  <p className=\"text-2xl font-bold\">{stats.totalSpent.toLocaleString()} ل.س</p>\n                </div>\n                <div className=\"text-4xl opacity-80\">💰</div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* الإجراءات السريعة */}\n        <Card>\n          <CardHeader>\n            <CardTitle>الإجراءات السريعة</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <Link href=\"/projects/create\">\n                <Button className=\"w-full h-20 bg-gradient-to-r from-teal to-navy text-lg\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl mb-1\">➕</div>\n                    <div>إنشاء مشروع جديد</div>\n                  </div>\n                </Button>\n              </Link>\n              \n              <Link href=\"/projects\">\n                <Button variant=\"outline\" className=\"w-full h-20 text-lg\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl mb-1\">🔍</div>\n                    <div>تصفح المشاريع</div>\n                  </div>\n                </Button>\n              </Link>\n              \n              <Link href=\"/craftsmen\">\n                <Button variant=\"outline\" className=\"w-full h-20 text-lg\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl mb-1\">👨‍🔧</div>\n                    <div>البحث عن حرفيين</div>\n                  </div>\n                </Button>\n              </Link>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* المشاريع الحديثة */}\n        <Card>\n          <CardHeader>\n            <div className=\"flex items-center justify-between\">\n              <CardTitle>المشاريع الحديثة</CardTitle>\n              <Link href=\"/dashboard/client/projects\">\n                <Button variant=\"outline\" size=\"sm\">عرض الكل</Button>\n              </Link>\n            </div>\n          </CardHeader>\n          <CardContent>\n            {recentProjects.length === 0 ? (\n              <div className=\"text-center py-8\">\n                <div className=\"text-6xl mb-4\">📭</div>\n                <h3 className=\"text-xl font-semibold text-gray-600 mb-2\">لا توجد مشاريع بعد</h3>\n                <p className=\"text-gray-500 mb-4\">ابدأ بإنشاء مشروعك الأول</p>\n                <Link href=\"/projects/create\">\n                  <Button>إنشاء مشروع جديد</Button>\n                </Link>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {recentProjects.map((project) => (\n                  <div key={project.id} className=\"flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50\">\n                    <div className=\"flex-1\">\n                      <h3 className=\"font-semibold text-navy\">{project.title}</h3>\n                      <p className=\"text-sm text-gray-600 mt-1\">{project.description}</p>\n                      <div className=\"flex items-center space-x-4 space-x-reverse mt-2\">\n                        <span className=\"text-sm text-gray-500\">📍 {project.location}</span>\n                        <span className=\"text-sm text-gray-500\">📅 {new Date(project.created_at).toLocaleDateString('ar-SA')}</span>\n                      </div>\n                    </div>\n                    <div className=\"flex items-center space-x-3 space-x-reverse\">\n                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>\n                        {getStatusText(project.status)}\n                      </span>\n                      <Link href={`/projects/${project.id}`}>\n                        <Button size=\"sm\" variant=\"outline\">عرض</Button>\n                      </Link>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,eAAe;QACf,gBAAgB;QAChB,mBAAmB;QACnB,YAAY;IACd;IACA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC9D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,MAAM,IAAI;gBACZ;YACF;QACF;oCAAG;QAAC,MAAM;KAAG;IAEb,MAAM,oBAAoB;QACxB,IAAI;YACF,0BAA0B;YAC1B,MAAM,EAAE,MAAM,QAAQ,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,UAAQ,CAC7C,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAAM;YAEzB,IAAI,OAAO,MAAM;YAEjB,MAAM,gBAAgB,UAAU,UAAU;YAC1C,MAAM,iBAAiB,UAAU,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,EAAE,MAAM,KAAK,eAAe,UAAU;YAC1G,MAAM,oBAAoB,UAAU,OAAO,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,UAAU;YACpF,MAAM,aAAa,UAAU,OAAO,CAAC,KAAK,IAAM,MAAM,CAAC,EAAE,UAAU,IAAI,CAAC,GAAG,MAAM;YAEjF,SAAS;gBACP;gBACA;gBACA;gBACA;YACF;YAEA,yBAAyB;YACzB,kBAAkB,UAAU,MAAM,GAAG,MAAM,EAAE;QAE/C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAa,OAAO;YACzB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,kJAAA,CAAA,UAAe;YAAC,OAAM;sBACrB,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,6LAAC,kJAAA,CAAA,UAAe;QACd,OAAM;QACN,UAAU,CAAC,OAAO,EAAE,MAAM,KAAK,4BAA4B,CAAC;kBAE5D,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;8DACrC,6LAAC;oDAAE,WAAU;8DAAsB,MAAM,aAAa;;;;;;;;;;;;sDAExD,6LAAC;4CAAI,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;sCAK3C,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAAyB;;;;;;8DACtC,6LAAC;oDAAE,WAAU;8DAAsB,MAAM,cAAc;;;;;;;;;;;;sDAEzD,6LAAC;4CAAI,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;sCAK3C,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA0B;;;;;;8DACvC,6LAAC;oDAAE,WAAU;8DAAsB,MAAM,iBAAiB;;;;;;;;;;;;sDAE5D,6LAAC;4CAAI,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;sCAK3C,6LAAC,mIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA0B;;;;;;8DACvC,6LAAC;oDAAE,WAAU;;wDAAsB,MAAM,UAAU,CAAC,cAAc;wDAAG;;;;;;;;;;;;;sDAEvE,6LAAC;4CAAI,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7C,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0CAAC;;;;;;;;;;;sCAEb,6LAAC,mIAAA,CAAA,cAAW;sCACV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;sDAChB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,6LAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;kDAKX,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAClC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,6LAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;kDAKX,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAClC,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,6LAAC;kEAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASjB,6LAAC,mIAAA,CAAA,OAAI;;sCACH,6LAAC,mIAAA,CAAA,aAAU;sCACT,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;sCAI1C,6LAAC,mIAAA,CAAA,cAAW;sCACT,eAAe,MAAM,KAAK,kBACzB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;sDAAC;;;;;;;;;;;;;;;;qDAIZ,6LAAC;gCAAI,WAAU;0CACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,6LAAC;wCAAqB,WAAU;;0DAC9B,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA2B,QAAQ,KAAK;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEAA8B,QAAQ,WAAW;;;;;;kEAC9D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAwB;oEAAI,QAAQ,QAAQ;;;;;;;0EAC5D,6LAAC;gEAAK,WAAU;;oEAAwB;oEAAI,IAAI,KAAK,QAAQ,UAAU,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;0DAGhG,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,QAAQ,MAAM,GAAG;kEAC5F,cAAc,QAAQ,MAAM;;;;;;kEAE/B,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;kEACnC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,SAAQ;sEAAU;;;;;;;;;;;;;;;;;;uCAdhC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BtC;GA9NwB;;QACL,0HAAA,CAAA,UAAO;;;KADF"}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}