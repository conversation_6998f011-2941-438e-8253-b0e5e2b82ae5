'use client';

import React, { useState } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { formatDate } from '@/lib/utils';
import DateDisplay from '@/components/ui/DateDisplay';

const JobDetailsPage = () => {
  const params = useParams();
  const [showOfferForm, setShowOfferForm] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [offerData, setOfferData] = useState({
    price: '',
    deliveryTime: '',
    description: '',
  });

  // Mock job data - في التطبيق الحقيقي، ستحصل على هذه البيانات من API
  const job = {
    id: params.id,
    title: 'تصميم وتنفيذ خزائن مطبخ خشبية عصرية',
    description: 'مطلوب نجار محترف لتصميم وتنفيذ خزائن مطبخ خشبية بتصميم عصري وعملي. المطبخ بمساحة 12 متر مربع ويحتاج إلى خزائن علوية وسفلية مع جزيرة وسطية. العمل يتطلب دقة عالية في القياسات والتنفيذ، واستخدام خشب عالي الجودة مع تشطيبات ممتازة.',
    requirements: 'يجب أن يكون لدى الحرفي خبرة لا تقل عن 5 سنوات في مجال النجارة، وأن يكون قادراً على قراءة المخططات والتصاميم. كما يجب أن يكون لديه الأدوات اللازمة لإنجاز العمل بجودة عالية.',
    category: 'النجارة',
    location: 'دمشق، المزة',
    budget: 350000,
    deadline: '2024-03-15',
    postedAt: '2024-01-15',
    status: 'open',
    skills: ['نجارة', 'تصميم', 'تركيب', 'قياسات دقيقة'],
    images: [
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&sat=2',
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&brightness=1.1',
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&contrast=1.2',
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&hue=30'
    ],
    attachments: ['مخطط_المطبخ.pdf', 'صور_المساحة.jpg', 'التصميم_المطلوب.png'],
    client: {
      name: 'أحمد محمد',
      rating: 4.8,
      reviewsCount: 12,
      joinedDate: '2023-05-15',
      completedJobs: 8,
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg'
    }
  };

  const offers = [
    {
      id: 1,
      craftsmanName: 'محمد النجار',
      rating: 4.9,
      reviewsCount: 25,
      price: 320000,
      deliveryTime: 14,
      description: 'أستطيع تنفيذ هذا المشروع بجودة عالية وفي الوقت المحدد. لدي خبرة 8 سنوات في تصميم وتنفيذ المطابخ الخشبية.',
      avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
      submittedAt: '2024-01-16'
    },
    {
      id: 2,
      craftsmanName: 'علي الحرفي',
      rating: 4.7,
      reviewsCount: 18,
      price: 340000,
      deliveryTime: 12,
      description: 'متخصص في المطابخ العصرية مع استخدام أفضل أنواع الخشب والتشطيبات. يمكنني تقديم ضمان لمدة سنتين على العمل.',
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
      submittedAt: '2024-01-17'
    }
  ];

  const handleOfferSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Offer submitted:', offerData);
    setShowOfferForm(false);
    // في التطبيق الحقيقي، ستقوم بإرسال العرض إلى API
  };

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % job.images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + job.images.length) % job.images.length);
  };



  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white relative overflow-hidden">
        {/* خلفية هندسية متناسقة */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl"></div>
          <div className="absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl"></div>
        </div>

        {/* شبكة نقطية ناعمة */}
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full" style={{
            backgroundImage: 'radial-gradient(circle, #567C8D 1px, transparent 1px)',
            backgroundSize: '50px 50px'
          }}></div>
        </div>

        <div className="container mx-auto px-4 py-8 relative z-10">
          {/* Image Slider في المقدمة */}
          {job.images && job.images.length > 0 && (
            <div className="mb-12">
              <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-lg overflow-hidden border border-white/20">
                {/* Main Image */}
                <div className="relative h-96 bg-gray-200">
                  <img
                    src={job.images[currentImageIndex]}
                    alt={`${job.title} - صورة ${currentImageIndex + 1}`}
                    className="w-full h-full object-cover"
                  />

                  {/* Navigation Arrows */}
                  {job.images.length > 1 && (
                    <>
                      <button
                        onClick={prevImage}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-navy p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
                      >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                        </svg>
                      </button>
                      <button
                        onClick={nextImage}
                        className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-navy p-3 rounded-full shadow-lg transition-all duration-300 hover:scale-110"
                      >
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </button>
                    </>
                  )}

                  {/* Image Counter */}
                  <div className="absolute bottom-4 left-4 bg-black/50 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm">
                    {currentImageIndex + 1} / {job.images.length}
                  </div>

                  {/* Project Category Badge */}
                  <div className="absolute top-4 right-4">
                    <div className="bg-white/90 backdrop-blur-sm text-navy px-4 py-2 rounded-full text-sm font-medium shadow-lg">
                      🪚 {job.category}
                    </div>
                  </div>
                </div>

                {/* Thumbnail Gallery */}
                {job.images.length > 1 && (
                  <div className="p-4">
                    <div className="flex space-x-2 space-x-reverse overflow-x-auto">
                      {job.images.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setCurrentImageIndex(index)}
                          className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                            currentImageIndex === index
                              ? 'border-teal shadow-lg scale-105'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <img
                            src={image}
                            alt={`صورة مصغرة ${index + 1}`}
                            className="w-full h-full object-cover"
                          />
                        </button>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-6">
              {/* Job Header */}
              <Card className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                <CardHeader className="pb-6">
                  <div className="flex justify-between items-start mb-4">
                    <CardTitle className="text-3xl leading-tight text-navy">{job.title}</CardTitle>
                    <Badge variant="success" className="bg-green-100 text-green-700 border-green-200">
                      <span className="text-green-500 ml-1">●</span>
                      مفتوح
                    </Badge>
                  </div>
                  <div className="flex items-center text-sm text-gray-600 space-x-6 space-x-reverse">
                    <span className="flex items-center bg-gray-50 px-3 py-1 rounded-full">
                      <svg className="w-4 h-4 ml-1 text-teal" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      {job.location}
                    </span>
                    <span className="flex items-center bg-gray-50 px-3 py-1 rounded-full">
                      <svg className="w-4 h-4 ml-1 text-navy" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      نُشر في <DateDisplay date={job.postedAt} />
                    </span>
                  </div>
                </CardHeader>
              </Card>

              {/* Job Description */}
              <Card className="border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>وصف المشروع</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 leading-relaxed mb-6">{job.description}</p>

                  <h4 className="font-semibold text-navy mb-3">المتطلبات:</h4>
                  <p className="text-gray-700 leading-relaxed mb-6">{job.requirements}</p>

                  <h4 className="font-semibold text-navy mb-3">المهارات المطلوبة:</h4>
                  <div className="flex flex-wrap gap-2 mb-6">
                    {job.skills.map((skill, index) => (
                      <Badge key={index} variant="outline">{skill}</Badge>
                    ))}
                  </div>

                  <h4 className="font-semibold text-navy mb-3">المرفقات:</h4>
                  <div className="space-y-2">
                    {job.attachments.map((file, index) => (
                      <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg">
                        <svg className="w-5 h-5 text-gray-500 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <span className="text-gray-700">{file}</span>
                        <button className="mr-auto text-teal hover:text-navy">تحميل</button>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Offers Section */}
              <Card className="border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>العروض المقدمة ({offers.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {offers.map((offer) => (
                      <div key={offer.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex items-center">
                            <img
                              src={offer.avatar}
                              alt={offer.craftsmanName}
                              className="w-12 h-12 rounded-full ml-3"
                            />
                            <div>
                              <h5 className="font-semibold text-navy">{offer.craftsmanName}</h5>
                              <div className="flex items-center text-sm text-gray-600">
                                <svg className="w-4 h-4 text-yellow-500 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                  <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                                </svg>
                                {offer.rating} ({offer.reviewsCount} تقييم)
                              </div>
                            </div>
                          </div>
                          <div className="text-left">
                            <div className="text-lg font-bold text-green-600">
                              {offer.price.toLocaleString()} ل.س
                            </div>
                            <div className="text-sm text-gray-600">
                              {offer.deliveryTime} يوم
                            </div>
                          </div>
                        </div>
                        <p className="text-gray-700 mb-3">{offer.description}</p>
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-500">
                            قُدم في <DateDisplay date={offer.submittedAt} />
                          </span>
                          <div className="space-x-2 space-x-reverse">
                            <Button size="sm" variant="outline">عرض الملف الشخصي</Button>
                            <Button size="sm">قبول العرض</Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Project Info */}
              <Card className="border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>معلومات المشروع</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-lg">
                    <div className="text-sm text-green-600 font-medium">الميزانية</div>
                    <div className="text-2xl font-bold text-green-700">
                      {job.budget.toLocaleString()} ل.س
                    </div>
                  </div>
                  <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-lg">
                    <div className="text-sm text-blue-600 font-medium">الموعد النهائي</div>
                    <div className="text-lg font-bold text-blue-700">
                      <DateDisplay date={job.deadline} />
                    </div>
                  </div>
                  <div className="bg-gradient-to-r from-purple-50 to-purple-100 p-4 rounded-lg">
                    <div className="text-sm text-purple-600 font-medium">عدد العروض</div>
                    <div className="text-lg font-bold text-purple-700">
                      {offers.length} عرض
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Client Info */}
              <Card className="border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle>معلومات العميل</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center mb-4">
                    <img
                      src={job.client.avatar}
                      alt={job.client.name}
                      className="w-16 h-16 rounded-full ml-4"
                    />
                    <div>
                      <h4 className="font-semibold text-navy">{job.client.name}</h4>
                      <div className="flex items-center text-sm text-gray-600">
                        <svg className="w-4 h-4 text-yellow-500 ml-1" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        {job.client.rating} ({job.client.reviewsCount} تقييم)
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div>انضم في <DateDisplay date={job.client.joinedDate} /></div>
                    <div>{job.client.completedJobs} مشروع مكتمل</div>
                  </div>
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  className="w-full bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90"
                  onClick={() => setShowOfferForm(true)}
                >
                  تقديم عرض
                </Button>
                <Button variant="outline" className="w-full">
                  حفظ المشروع
                </Button>
                <Button variant="outline" className="w-full">
                  مشاركة المشروع
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Offer Form Modal */}
      {showOfferForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl p-6 w-full max-w-md">
            <h3 className="text-xl font-bold text-navy mb-4">تقديم عرض</h3>
            <form onSubmit={handleOfferSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  السعر المطلوب (ل.س)
                </label>
                <input
                  type="number"
                  value={offerData.price}
                  onChange={(e) => setOfferData({...offerData, price: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal"
                  placeholder="أدخل السعر"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  مدة التنفيذ (بالأيام)
                </label>
                <input
                  type="number"
                  value={offerData.deliveryTime}
                  onChange={(e) => setOfferData({...offerData, deliveryTime: e.target.value})}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal"
                  placeholder="عدد الأيام"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  وصف العرض
                </label>
                <textarea
                  value={offerData.description}
                  onChange={(e) => setOfferData({...offerData, description: e.target.value})}
                  rows={4}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal"
                  placeholder="اشرح كيف ستنفذ هذا المشروع..."
                  required
                />
              </div>
              <div className="flex space-x-3 space-x-reverse">
                <Button type="submit" className="flex-1">
                  إرسال العرض
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setShowOfferForm(false)}
                  className="flex-1"
                >
                  إلغاء
                </Button>
              </div>
            </form>
          </div>
        </div>
      )}
    </MainLayout>
  );
};

export default JobDetailsPage;
