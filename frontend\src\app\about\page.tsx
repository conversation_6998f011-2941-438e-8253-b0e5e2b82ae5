'use client';

import React from 'react';
import MainLayout from '@/components/layout/MainLayout';

const AboutPage = () => {
  const features = [
    {
      icon: '🎯',
      title: 'دقة في الاختيار',
      description: 'نساعدك في العثور على الحرفي المناسب لمشروعك بدقة عالية'
    },
    {
      icon: '⚡',
      title: 'سرعة في التنفيذ',
      description: 'منصة سريعة وفعالة لربط أصحاب المشاريع بالحرفيين'
    },
    {
      icon: '🛡️',
      title: 'ضمان الجودة',
      description: 'نضمن جودة العمل من خلال نظام التقييمات والمراجعات'
    },
    {
      icon: '💰',
      title: 'أسعار منافسة',
      description: 'احصل على أفضل الأسعار من خلال المنافسة بين الحرفيين'
    }
  ];

  const team = [
    {
      name: 'أحم<PERSON> محمد',
      role: 'المؤسس والرئيس التنفيذي',
      image: 'https://randomuser.me/api/portraits/men/1.jpg',
      description: 'خبرة 15 عاماً في مجال التكنولوجيا والأعمال'
    },
    {
      name: 'فاطمة أحمد',
      role: 'مديرة التطوير',
      image: 'https://randomuser.me/api/portraits/women/1.jpg',
      description: 'متخصصة في تطوير المنصات الرقمية'
    },
    {
      name: 'محمد علي',
      role: 'مدير العمليات',
      image: 'https://randomuser.me/api/portraits/men/2.jpg',
      description: 'خبرة واسعة في إدارة المشاريع والعمليات'
    }
  ];

  return (
    <MainLayout>
      <div className="bg-white">
        {/* Hero Section */}
        <section className="bg-gradient-to-b from-skyblue to-white py-20">
          <div className="container mx-auto px-4 text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-navy mb-6">
              من نحن
            </h1>
            <p className="text-xl text-gray-700 max-w-3xl mx-auto">
              منصة دوزان هي الحل الأمثل لربط أصحاب المشاريع بالحرفيين المهرة في سوريا. 
              نسعى لتسهيل عملية العثور على الخدمات المطلوبة وتقديم تجربة موثوقة وآمنة للجميع.
            </p>
          </div>
        </section>

        {/* Mission Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold text-navy mb-6">رسالتنا</h2>
                <p className="text-gray-700 mb-4">
                  نؤمن بأن كل مشروع يستحق أفضل الحرفيين، وكل حرفي يستحق الفرصة المناسبة. 
                  لذلك أنشأنا منصة دوزان لتكون الجسر الذي يربط بين الطرفين بطريقة عادلة وشفافة.
                </p>
                <p className="text-gray-700 mb-4">
                  نسعى لدعم الاقتصاد المحلي السوري من خلال توفير فرص عمل للحرفيين المهرة 
                  ومساعدة أصحاب المشاريع في الحصول على خدمات عالية الجودة بأسعار منافسة.
                </p>
                <p className="text-gray-700">
                  رؤيتنا هي أن نصبح المنصة الرائدة في سوريا لخدمات الحرف والمهن، 
                  ونساهم في بناء مجتمع من الثقة والتعاون المتبادل.
                </p>
              </div>
              <div className="text-center">
                <img 
                  src="https://placehold.co/500x400/C8D9E6/2F4156?text=رسالتنا" 
                  alt="رسالتنا" 
                  className="w-full h-auto rounded-lg shadow-md"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 bg-beige">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-navy mb-4">لماذا دوزان؟</h2>
              <p className="text-lg text-gray-700 max-w-2xl mx-auto">
                نقدم مجموعة من المميزات التي تجعل تجربتك معنا مميزة وموثوقة
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {features.map((feature, index) => (
                <div key={index} className="bg-white rounded-lg p-6 shadow-md text-center">
                  <div className="text-4xl mb-4">{feature.icon}</div>
                  <h3 className="text-xl font-bold text-navy mb-2">{feature.title}</h3>
                  <p className="text-gray-700">{feature.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Team Section */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-navy mb-4">فريق العمل</h2>
              <p className="text-lg text-gray-700 max-w-2xl mx-auto">
                فريق متخصص ومتفاني يعمل على تطوير وتحسين منصة دوزان باستمرار
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {team.map((member, index) => (
                <div key={index} className="bg-white rounded-lg shadow-md p-6 text-center">
                  <img 
                    src={member.image} 
                    alt={member.name}
                    className="w-24 h-24 rounded-full mx-auto mb-4 object-cover"
                  />
                  <h3 className="text-xl font-bold text-navy mb-1">{member.name}</h3>
                  <p className="text-teal font-medium mb-2">{member.role}</p>
                  <p className="text-gray-700 text-sm">{member.description}</p>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Values Section */}
        <section className="py-16 bg-navy text-white">
          <div className="container mx-auto px-4">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4">قيمنا</h2>
              <p className="text-lg text-skyblue max-w-2xl mx-auto">
                القيم التي نؤمن بها وتوجه عملنا اليومي
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="text-4xl mb-4">🤝</div>
                <h3 className="text-xl font-bold mb-2">الثقة</h3>
                <p className="text-skyblue">
                  نبني علاقات قائمة على الثقة المتبادلة بين جميع أطراف المنصة
                </p>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-4">⭐</div>
                <h3 className="text-xl font-bold mb-2">الجودة</h3>
                <p className="text-skyblue">
                  نسعى دائماً لتقديم أعلى مستويات الجودة في الخدمات والمنتجات
                </p>
              </div>
              <div className="text-center">
                <div className="text-4xl mb-4">🚀</div>
                <h3 className="text-xl font-bold mb-2">الابتكار</h3>
                <p className="text-skyblue">
                  نطور حلولاً مبتكرة لتحسين تجربة المستخدمين باستمرار
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Contact CTA */}
        <section className="py-16 bg-beige">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold text-navy mb-4">هل لديك أسئلة؟</h2>
            <p className="text-lg text-gray-700 mb-8 max-w-2xl mx-auto">
              نحن هنا لمساعدتك. تواصل معنا في أي وقت وسنكون سعداء للإجابة على استفساراتك
            </p>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
              <a 
                href="/contact" 
                className="bg-navy text-white px-8 py-3 rounded-md hover:bg-navy/90 transition-colors"
              >
                تواصل معنا
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="border border-navy text-navy px-8 py-3 rounded-md hover:bg-navy hover:text-white transition-colors"
              >
                <EMAIL>
              </a>
            </div>
          </div>
        </section>
      </div>
    </MainLayout>
  );
};

export default AboutPage;
