'use client';

import React from 'react';
import Link from 'next/link';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

const AboutPage = () => {
  const features = [
    {
      icon: '🎯',
      title: 'دقة في الاختيار',
      description: 'نساعدك في العثور على الحرفي المناسب لمشروعك بدقة عالية'
    },
    {
      icon: '⚡',
      title: 'سرعة في التنفيذ',
      description: 'منصة سريعة وفعالة لربط أصحاب المشاريع بالحرفيين'
    },
    {
      icon: '🛡️',
      title: 'ضمان الجودة',
      description: 'نضمن جودة العمل من خلال نظام التقييمات والمراجعات'
    },
    {
      icon: '💰',
      title: 'أسعار منافسة',
      description: 'احصل على أفضل الأسعار من خلال المنافسة بين الحرفيين'
    }
  ];

  const team = [
    {
      name: 'أحمد محمد',
      role: 'المؤسس والرئيس التنفيذي',
      image: 'https://randomuser.me/api/portraits/men/1.jpg',
      description: 'خبرة 15 عاماً في مجال التكنولوجيا والأعمال'
    },
    {
      name: 'فاطمة أحمد',
      role: 'مديرة التطوير',
      image: 'https://randomuser.me/api/portraits/women/1.jpg',
      description: 'متخصصة في تطوير المنصات الرقمية'
    },
    {
      name: 'محمد علي',
      role: 'مدير العمليات',
      image: 'https://randomuser.me/api/portraits/men/2.jpg',
      description: 'خبرة واسعة في إدارة المشاريع والعمليات'
    }
  ];

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white relative overflow-hidden">
        {/* خلفية هندسية متناسقة */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl"></div>
          <div className="absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl"></div>
        </div>

        {/* شبكة نقطية ناعمة */}
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full" style={{
            backgroundImage: 'radial-gradient(circle, #567C8D 1px, transparent 1px)',
            backgroundSize: '50px 50px'
          }}></div>
        </div>

        <div className="container mx-auto px-4 py-8 relative z-10">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <div className="inline-block bg-gradient-to-r from-navy to-teal text-white px-8 py-3 rounded-full text-sm font-medium mb-6 shadow-lg">
              <span className="text-lg">🏢</span>
              <span className="mr-2">من نحن</span>
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-navy mb-6 leading-tight">
              منصة
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-teal via-navy to-teal">
                دوزان
              </span>
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              منصة دوزان هي الحل الأمثل لربط أصحاب المشاريع بالحرفيين المهرة في سوريا.
              نسعى لتسهيل عملية العثور على الخدمات المطلوبة وتقديم تجربة موثوقة وآمنة للجميع.
            </p>
            <div className="w-24 h-1 bg-gradient-to-r from-teal to-navy mx-auto rounded-full"></div>
          </div>

          {/* Mission Section */}
          <div className="mb-16">
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
              <CardContent className="p-8 md:p-12">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                  <div>
                    <h2 className="text-3xl font-bold text-navy mb-6">رسالتنا</h2>
                    <p className="text-gray-700 leading-relaxed mb-6">
                      نؤمن بأن كل مشروع يستحق أفضل الحرفيين، وكل حرفي يستحق الفرصة المناسبة.
                      لذلك أنشأنا منصة دوزان لتكون الجسر الذي يربط بين الطرفين بطريقة عادلة وشفافة.
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-6">
                      نسعى لدعم الاقتصاد المحلي السوري من خلال توفير فرص عمل للحرفيين المهرة
                      ومساعدة أصحاب المشاريع في الحصول على خدمات عالية الجودة بأسعار منافسة.
                    </p>
                    <p className="text-gray-700 leading-relaxed mb-6">
                      رؤيتنا هي أن نصبح المنصة الرائدة في سوريا لخدمات الحرف والمهن،
                      ونساهم في بناء مجتمع من الثقة والتعاون المتبادل.
                    </p>
                    <Link href="/register">
                      <Button size="lg" className="bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90">
                        انضم إلينا الآن
                      </Button>
                    </Link>
                  </div>
                  <div className="relative">
                    <img
                      src="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?w=600&h=400&fit=crop"
                      alt="فريق العمل"
                      className="w-full h-80 object-cover rounded-2xl shadow-lg"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-navy/20 to-transparent rounded-2xl"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Features Section */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-navy mb-4">لماذا دوزان؟</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                نقدم مجموعة من المميزات التي تجعل تجربتك معنا مميزة وموثوقة
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {features.map((feature, index) => (
                <Card key={index} className="border-0 bg-white/90 backdrop-blur-md shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <CardContent className="p-6 text-center">
                    <div className="text-4xl mb-4">{feature.icon}</div>
                    <h3 className="text-lg font-bold text-navy mb-3">{feature.title}</h3>
                    <p className="text-gray-600 text-sm leading-relaxed">{feature.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Team Section */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold text-navy mb-4">فريق العمل</h2>
              <p className="text-gray-600 max-w-2xl mx-auto">
                فريق متخصص ومتفاني يعمل على تطوير وتحسين منصة دوزان باستمرار
              </p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {team.map((member, index) => (
                <Card key={index} className="border-0 bg-white/90 backdrop-blur-md shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <CardContent className="p-6 text-center">
                    <img
                      src={member.image}
                      alt={member.name}
                      className="w-24 h-24 rounded-full mx-auto mb-4 border-4 border-white shadow-lg object-cover"
                    />
                    <h3 className="text-xl font-bold text-navy mb-2">{member.name}</h3>
                    <p className="text-teal font-medium mb-3">{member.role}</p>
                    <p className="text-gray-600 text-sm">{member.description}</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Values Section */}
          <div className="mb-16">
            <Card className="border-0 bg-gradient-to-r from-navy to-teal shadow-lg">
              <CardContent className="p-12 text-white">
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold mb-4">قيمنا</h2>
                  <p className="text-white/90 max-w-2xl mx-auto">
                    القيم التي نؤمن بها وتوجه عملنا اليومي
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  <div className="text-center">
                    <div className="text-4xl mb-4">🤝</div>
                    <h3 className="text-xl font-bold mb-2">الثقة</h3>
                    <p className="text-white/80">
                      نبني علاقات قائمة على الثقة المتبادلة بين جميع أطراف المنصة
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl mb-4">⭐</div>
                    <h3 className="text-xl font-bold mb-2">الجودة</h3>
                    <p className="text-white/80">
                      نسعى دائماً لتقديم أعلى مستويات الجودة في الخدمات والمنتجات
                    </p>
                  </div>
                  <div className="text-center">
                    <div className="text-4xl mb-4">🚀</div>
                    <h3 className="text-xl font-bold mb-2">الابتكار</h3>
                    <p className="text-white/80">
                      نطور حلولاً مبتكرة لتحسين تجربة المستخدمين باستمرار
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Contact CTA */}
          <div className="text-center">
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
              <CardContent className="p-12">
                <h2 className="text-3xl font-bold text-navy mb-4">هل لديك أسئلة؟</h2>
                <p className="text-gray-600 mb-8 max-w-2xl mx-auto text-lg">
                  نحن هنا لمساعدتك. تواصل معنا في أي وقت وسنكون سعداء للإجابة على استفساراتك
                </p>
                <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
                  <Link href="/contact">
                    <Button size="lg" className="bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90">
                      تواصل معنا
                    </Button>
                  </Link>
                  <Button size="lg" variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                    <EMAIL>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default AboutPage;
