{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useState } from 'react';\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  role: 'client' | 'craftsman';\n  phone?: string;\n}\n\nexport const useAuth = () => {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const login = async (credentials: LoginCredentials) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await signIn('credentials', {\n        email: credentials.email,\n        password: credentials.password,\n        redirect: false,\n      });\n\n      if (result?.error) {\n        setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');\n        return false;\n      }\n\n      if (result?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        setError(errorData.message || 'حدث خطأ أثناء إنشاء الحساب');\n        return false;\n      }\n\n      // تسجيل الدخول تلقائياً بعد التسجيل\n      const loginResult = await signIn('credentials', {\n        email: data.email,\n        password: data.password,\n        redirect: false,\n      });\n\n      if (loginResult?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء إنشاء الحساب');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    setIsLoading(true);\n    try {\n      await signOut({ redirect: false });\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithGoogle = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('google', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Google');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithFacebook = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('facebook', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Facebook');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const isAuthenticated = status === 'authenticated';\n  const isLoading_session = status === 'loading';\n  const user = session?.user;\n\n  // التحقق من الأدوار مع fallback\n  const isClient = user?.role === 'client';\n  const isCraftsman = user?.role === 'craftsman';\n  const isAdmin = user?.role === 'admin';\n\n  // التحقق من الصلاحيات\n  const hasRole = (role: string | string[]) => {\n    if (!user) return false;\n    if (Array.isArray(role)) {\n      return role.includes(user.role);\n    }\n    return user.role === role;\n  };\n\n  const canAccess = (requiredRoles: string | string[]) => {\n    return isAuthenticated && hasRole(requiredRoles);\n  };\n\n  return {\n    // Session data\n    user,\n    session,\n    isAuthenticated,\n    isLoading: isLoading || isLoading_session,\n    error,\n\n    // Role checks\n    isClient,\n    isCraftsman,\n    isAdmin,\n    hasRole,\n    canAccess,\n\n    // Auth methods\n    login,\n    register,\n    logout,\n    loginWithGoogle,\n    loginWithFacebook,\n\n    // Utility\n    setError,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;AAmBO,MAAM,UAAU;IACrB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,QAAQ,OAAO;QACnB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;gBAC9B,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;gBACT,OAAO;YACT;YAEA,IAAI,QAAQ,IAAI;gBACd,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,OAAO,IAAI;gBAC9B,OAAO;YACT;YAEA,oCAAoC;YACpC,MAAM,cAAc,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBAC9C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;YACZ;YAEA,IAAI,aAAa,IAAI;gBACnB,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;gBAAE,UAAU;YAAM;YAChC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBAAE,aAAa;YAAa;QACrD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,YAAY;gBAAE,aAAa;YAAa;QACvD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,WAAW;IACnC,MAAM,oBAAoB,WAAW;IACrC,MAAM,OAAO,SAAS;IAEtB,gCAAgC;IAChC,MAAM,WAAW,MAAM,SAAS;IAChC,MAAM,cAAc,MAAM,SAAS;IACnC,MAAM,UAAU,MAAM,SAAS;IAE/B,sBAAsB;IACtB,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI;QAChC;QACA,OAAO,KAAK,IAAI,KAAK;IACvB;IAEA,MAAM,YAAY,CAAC;QACjB,OAAO,mBAAmB,QAAQ;IACpC;IAEA,OAAO;QACL,eAAe;QACf;QACA;QACA;QACA,WAAW,aAAa;QACxB;QAEA,cAAc;QACd;QACA;QACA;QACA;QACA;QAEA,eAAe;QACf;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;IACF;AACF"}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/auth/AuthButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/hooks/useAuth';\nimport { Button } from '@/components/ui/Button';\nimport Link from 'next/link';\nimport { useState } from 'react';\n\nexport default function AuthButton() {\n  const { isAuthenticated, user, logout, isLoading } = useAuth();\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse\">\n        <div className=\"h-10 w-24 bg-gray-200 rounded\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"flex items-center space-x-3 space-x-reverse\">\n        <Link href=\"/login\">\n          <Button variant=\"outline\" size=\"sm\" className=\"border-navy text-navy hover:bg-navy hover:text-white\">\n            تسجيل الدخول\n          </Button>\n        </Link>\n        <Link href=\"/register\">\n          <Button size=\"sm\" className=\"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90\">\n            إنشاء حساب\n          </Button>\n        </Link>\n      </div>\n    );\n  }\n\n  const getRoleDisplayName = (role: string) => {\n    switch (role) {\n      case 'client':\n        return 'عميل';\n      case 'craftsman':\n        return 'حرفي';\n      case 'admin':\n        return 'مدير';\n      default:\n        return 'مستخدم';\n    }\n  };\n\n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'client':\n        return '👤';\n      case 'craftsman':\n        return '👨‍🔧';\n      case 'admin':\n        return '👑';\n      default:\n        return '👤';\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className=\"flex items-center space-x-2 space-x-reverse bg-white border border-gray-200 rounded-lg px-4 py-2 hover:bg-gray-50 transition-colors duration-200\"\n      >\n        <div className=\"text-lg\">{getRoleIcon(user?.role || '')}</div>\n        <div className=\"text-right\">\n          <div className=\"text-sm font-medium text-navy\">{user?.name || 'مستخدم'}</div>\n          <div className=\"text-xs text-gray-500\">{getRoleDisplayName(user?.role || '')}</div>\n        </div>\n        <svg\n          className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${\n            isDropdownOpen ? 'rotate-180' : ''\n          }`}\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n        </svg>\n      </button>\n\n      {isDropdownOpen && (\n        <>\n          {/* Backdrop */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsDropdownOpen(false)}\n          />\n\n          {/* Dropdown Menu */}\n          <div className=\"absolute left-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-20\">\n            <div className=\"p-4 border-b border-gray-100\">\n              <div className=\"flex items-center space-x-3 space-x-reverse\">\n                <div className=\"text-2xl\">{getRoleIcon(user?.role || '')}</div>\n                <div>\n                  <div className=\"font-medium text-navy\">{user?.name || 'مستخدم'}</div>\n                  <div className=\"text-sm text-gray-500\">{user?.email || 'بريد إلكتروني'}</div>\n                  <div className=\"text-xs text-teal font-medium\">\n                    {getRoleDisplayName(user?.role || '')}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"py-2\">\n              <Link\n                href=\"/dashboard\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>📊</span>\n                  <span>لوحة التحكم</span>\n                </div>\n              </Link>\n\n              <Link\n                href=\"/profile\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>⚙️</span>\n                  <span>الملف الشخصي</span>\n                </div>\n              </Link>\n\n              {user?.role === 'client' && (\n                <Link\n                  href=\"/projects/create\"\n                  className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                  onClick={() => setIsDropdownOpen(false)}\n                >\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span>➕</span>\n                    <span>إنشاء مشروع</span>\n                  </div>\n                </Link>\n              )}\n\n              {user?.role === 'craftsman' && (\n                <Link\n                  href=\"/offers\"\n                  className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                  onClick={() => setIsDropdownOpen(false)}\n                >\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span>💼</span>\n                    <span>عروضي</span>\n                  </div>\n                </Link>\n              )}\n\n              <Link\n                href=\"/messages\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>💬</span>\n                  <span>الرسائل</span>\n                </div>\n              </Link>\n            </div>\n\n            <div className=\"border-t border-gray-100 py-2\">\n              <button\n                onClick={() => {\n                  logout();\n                  setIsDropdownOpen(false);\n                }}\n                className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-red-50 transition-colors duration-200\"\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>🚪</span>\n                  <span>تسجيل الخروج</span>\n                </div>\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,WAAU;kCAAuD;;;;;;;;;;;8BAIvG,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,WAAU;kCAAyE;;;;;;;;;;;;;;;;;IAM7G;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCAAW,YAAY,MAAM,QAAQ;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CAAiC,MAAM,QAAQ;;;;;;0CAC9D,8OAAC;gCAAI,WAAU;0CAAyB,mBAAmB,MAAM,QAAQ;;;;;;;;;;;;kCAE3E,8OAAC;wBACC,WAAW,CAAC,wDAAwD,EAClE,iBAAiB,eAAe,IAChC;wBACF,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAIxE,gCACC;;kCAEE,8OAAC;wBACC,WAAU;wBACV,SAAS,IAAM,kBAAkB;;;;;;kCAInC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAY,YAAY,MAAM,QAAQ;;;;;;sDACrD,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAyB,MAAM,QAAQ;;;;;;8DACtD,8OAAC;oDAAI,WAAU;8DAAyB,MAAM,SAAS;;;;;;8DACvD,8OAAC;oDAAI,WAAU;8DACZ,mBAAmB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAM1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAIV,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;oCAIT,MAAM,SAAS,0BACd,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;oCAKX,MAAM,SAAS,6BACd,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAKZ,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAK;;;;;;8DACN,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;0CAKZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;wCACP;wCACA,kBAAkB;oCACpB;oCACA,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB"}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/notifications/NotificationBell.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '@/components/ui/Button';\n\ninterface Notification {\n  id: string;\n  title: string;\n  message: string;\n  type: 'message' | 'offer' | 'project' | 'review' | 'system';\n  read: boolean;\n  timestamp: string;\n  actionUrl?: string;\n}\n\ninterface NotificationBellProps {\n  notifications: Notification[];\n  onMarkAsRead: (notificationId: string) => void;\n  onMarkAllAsRead: () => void;\n  onNotificationClick: (notification: Notification) => void;\n}\n\nconst NotificationBell: React.FC<NotificationBellProps> = ({\n  notifications,\n  onMarkAsRead,\n  onMarkAllAsRead,\n  onNotificationClick\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'message': return '💬';\n      case 'offer': return '💰';\n      case 'project': return '🏗️';\n      case 'review': return '⭐';\n      case 'system': return '🔔';\n      default: return '📢';\n    }\n  };\n\n  const getNotificationColor = (type: string) => {\n    switch (type) {\n      case 'message': return 'text-blue-600';\n      case 'offer': return 'text-green-600';\n      case 'project': return 'text-purple-600';\n      case 'review': return 'text-yellow-600';\n      case 'system': return 'text-gray-600';\n      default: return 'text-gray-600';\n    }\n  };\n\n  const formatTime = (timestamp: string) => {\n    const now = new Date();\n    const notificationTime = new Date(timestamp);\n    const diffInMinutes = (now.getTime() - notificationTime.getTime()) / (1000 * 60);\n\n    if (diffInMinutes < 1) {\n      return 'الآن';\n    } else if (diffInMinutes < 60) {\n      return `منذ ${Math.floor(diffInMinutes)} دقيقة`;\n    } else if (diffInMinutes < 1440) {\n      return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;\n    } else {\n      return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`;\n    }\n  };\n\n  const handleNotificationClick = (notification: Notification) => {\n    if (!notification.read) {\n      onMarkAsRead(notification.id);\n    }\n    onNotificationClick(notification);\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* Bell Button */}\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2\"\n      >\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\" />\n        </svg>\n        \n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </Button>\n\n      {/* Dropdown */}\n      {isOpen && (\n        <div className=\"absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\">\n          {/* Header */}\n          <div className=\"p-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"font-semibold text-gray-900\">الإشعارات</h3>\n              {unreadCount > 0 && (\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={onMarkAllAsRead}\n                  className=\"text-xs\"\n                >\n                  تحديد الكل كمقروء\n                </Button>\n              )}\n            </div>\n          </div>\n\n          {/* Notifications List */}\n          <div className=\"max-h-96 overflow-y-auto\">\n            {notifications.length > 0 ? (\n              notifications.slice(0, 10).map((notification) => (\n                <div\n                  key={notification.id}\n                  onClick={() => handleNotificationClick(notification)}\n                  className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${\n                    !notification.read ? 'bg-blue-50' : ''\n                  }`}\n                >\n                  <div className=\"flex items-start space-x-3 space-x-reverse\">\n                    <div className={`text-lg ${getNotificationColor(notification.type)}`}>\n                      {getNotificationIcon(notification.type)}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center justify-between mb-1\">\n                        <h4 className={`text-sm font-medium ${\n                          !notification.read ? 'text-gray-900' : 'text-gray-700'\n                        }`}>\n                          {notification.title}\n                        </h4>\n                        {!notification.read && (\n                          <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                        )}\n                      </div>\n                      <p className=\"text-sm text-gray-600 line-clamp-2\">\n                        {notification.message}\n                      </p>\n                      <p className=\"text-xs text-gray-500 mt-1\">\n                        {formatTime(notification.timestamp)}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"p-8 text-center\">\n                <div className=\"text-4xl mb-2\">🔔</div>\n                <p className=\"text-gray-500\">لا توجد إشعارات</p>\n              </div>\n            )}\n          </div>\n\n          {/* Footer */}\n          {notifications.length > 10 && (\n            <div className=\"p-3 border-t border-gray-200 text-center\">\n              <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                عرض جميع الإشعارات\n              </Button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default NotificationBell;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAsBA,MAAM,mBAAoD,CAAC,EACzD,aAAa,EACb,YAAY,EACZ,eAAe,EACf,mBAAmB,EACpB;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;IAE7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,mBAAmB,IAAI,KAAK;QAClC,MAAM,gBAAgB,CAAC,IAAI,OAAO,KAAK,iBAAiB,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE/E,IAAI,gBAAgB,GAAG;YACrB,OAAO;QACT,OAAO,IAAI,gBAAgB,IAAI;YAC7B,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,eAAe,MAAM,CAAC;QACjD,OAAO,IAAI,gBAAgB,MAAM;YAC/B,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;QACrD,OAAO;YACL,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,gBAAgB,MAAM,IAAI,CAAC;QACtD;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,aAAa,IAAI,EAAE;YACtB,aAAa,aAAa,EAAE;QAC9B;QACA,oBAAoB;QACpB,UAAU;IACZ;IAEA,qBACE,8OAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGtE,cAAc,mBACb,8OAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;YAMjC,wBACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8B;;;;;;gCAC3C,cAAc,mBACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,GAAG,IACtB,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,6BAC9B,8OAAC;gCAEC,SAAS,IAAM,wBAAwB;gCACvC,WAAW,CAAC,+EAA+E,EACzF,CAAC,aAAa,IAAI,GAAG,eAAe,IACpC;0CAEF,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,qBAAqB,aAAa,IAAI,GAAG;sDACjE,oBAAoB,aAAa,IAAI;;;;;;sDAExC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAW,CAAC,oBAAoB,EAClC,CAAC,aAAa,IAAI,GAAG,kBAAkB,iBACvC;sEACC,aAAa,KAAK;;;;;;wDAEpB,CAAC,aAAa,IAAI,kBACjB,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAGnB,8OAAC;oDAAE,WAAU;8DACV,aAAa,OAAO;;;;;;8DAEvB,8OAAC;oDAAE,WAAU;8DACV,WAAW,aAAa,SAAS;;;;;;;;;;;;;;;;;;+BAzBnC,aAAa,EAAE;;;;sDAgCxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;oBAMlC,cAAc,MAAM,GAAG,oBACtB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAK,WAAU;sCAAS;;;;;;;;;;;;;;;;;;;;;;;AASrE;uCAEe"}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 956, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useSession } from 'next-auth/react';\nimport { Button } from '../ui/Button';\nimport AuthButton from '../auth/AuthButton';\nimport NotificationBell from '../notifications/NotificationBell';\n\nconst Header = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const { data: session } = useSession();\n\n  // بيانات تجريبية للإشعارات\n  const mockNotifications = [\n    {\n      id: '1',\n      title: 'عرض جديد',\n      message: 'تم تقديم عرض جديد على مشروع تجديد المطبخ',\n      type: 'offer' as const,\n      read: false,\n      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n      actionUrl: '/client/offers'\n    },\n    {\n      id: '2',\n      title: 'رسالة جديدة',\n      message: 'رسالة جديدة من محمد النجار',\n      type: 'message' as const,\n      read: false,\n      timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),\n      actionUrl: '/messages'\n    },\n    {\n      id: '3',\n      title: 'تقييم جديد',\n      message: 'تم تقييم عملك بـ 5 نجوم',\n      type: 'review' as const,\n      read: true,\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      actionUrl: '/craftsman/reviews'\n    }\n  ];\n\n  const handleNotificationClick = (notification: any) => {\n    if (notification.actionUrl) {\n      window.location.href = notification.actionUrl;\n    }\n  };\n\n  const handleMarkAsRead = (notificationId: string) => {\n    // TODO: تنفيذ تحديث حالة الإشعار في قاعدة البيانات\n    console.log('Mark as read:', notificationId);\n  };\n\n  const handleMarkAllAsRead = () => {\n    // TODO: تنفيذ تحديد جميع الإشعارات كمقروءة\n    console.log('Mark all as read');\n  };\n\n  return (\n    <header className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex justify-between items-center\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"group flex items-center space-x-3 space-x-reverse\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-navy to-teal\">\n                دوزان\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:block\">\n            <ul className=\"flex space-x-8 space-x-reverse\">\n              <li>\n                <Link href=\"/\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الرئيسية\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  المشاريع\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الحرفيون\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  من نحن\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الأسئلة الشائعة\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  اتصل بنا\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n            </ul>\n          </nav>\n\n          {/* Desktop Auth */}\n          <div className=\"hidden lg:flex items-center space-x-4 space-x-reverse\">\n            {session && (\n              <>\n                <Link href=\"/messages\">\n                  <Button variant=\"outline\" size=\"sm\" className=\"relative\">\n                    💬\n                    <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center\">\n                      2\n                    </span>\n                  </Button>\n                </Link>\n                <NotificationBell\n                  notifications={mockNotifications}\n                  onMarkAsRead={handleMarkAsRead}\n                  onMarkAllAsRead={handleMarkAllAsRead}\n                  onNotificationClick={handleNotificationClick}\n                />\n              </>\n            )}\n            <AuthButton />\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300\"\n            aria-label=\"فتح القائمة\"\n          >\n            <svg className=\"w-6 h-6 text-navy\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              {isMobileMenuOpen ? (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              ) : (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden mt-4 py-4 border-t border-gray-200\">\n            <nav className=\"space-y-4\">\n              <Link href=\"/\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الرئيسية\n              </Link>\n              <Link href=\"/jobs\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                المشاريع\n              </Link>\n              <Link href=\"/craftsmen\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الحرفيون\n              </Link>\n              <Link href=\"/about\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                من نحن\n              </Link>\n              <Link href=\"/faq\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الأسئلة الشائعة\n              </Link>\n              <Link href=\"/contact\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                اتصل بنا\n              </Link>\n            </nav>\n            <div className=\"mt-6\">\n              <AuthButton />\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,SAAS;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAEnC,2BAA2B;IAC3B,MAAM,oBAAoB;QACxB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC5D,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC5D,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YAChE,WAAW;QACb;KACD;IAED,MAAM,0BAA0B,CAAC;QAC/B,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,QAAQ,CAAC,IAAI,GAAG,aAAa,SAAS;QAC/C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,mDAAmD;QACnD,QAAQ,GAAG,CAAC,iBAAiB;IAC/B;IAEA,MAAM,sBAAsB;QAC1B,2CAA2C;QAC3C,QAAQ,GAAG,CAAC;IACd;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDAA2L;;;;;;kDAG1M,8OAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;;;;;;sCAO1G,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAA0F;8DAEjH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;gDAA0F;8DAErH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;;gDAA0F;8DAE1H,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;;gDAA0F;8DAEtH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAO,WAAU;;gDAA0F;8DAEpH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;gDAA0F;8DAExH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,8OAAC;4BAAI,WAAU;;gCACZ,yBACC;;sDACE,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;;oDAAW;kEAEvD,8OAAC;wDAAK,WAAU;kEAA+G;;;;;;;;;;;;;;;;;sDAKnI,8OAAC,uJAAA,CAAA,UAAgB;4CACf,eAAe;4CACf,cAAc;4CACd,iBAAiB;4CACjB,qBAAqB;;;;;;;;8CAI3B,8OAAC,wIAAA,CAAA,UAAU;;;;;;;;;;;sCAIb,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAoB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC1E,iCACC,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;yDAErE,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAO5E,kCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAiF;;;;;;8CAG1G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAiF;;;;;;8CAG9G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAiF;;;;;;8CAGnH,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiF;;;;;;8CAG/G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAO,WAAU;8CAAiF;;;;;;8CAG7G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiF;;;;;;;;;;;;sCAInH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wIAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB;uCAEe"}}, {"offset": {"line": 1417, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1423, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden\">\n      {/* خلفية هندسية متناسقة */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl\"></div>\n      </div>\n\n      {/* شبكة نقطية ناعمة */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',\n          backgroundSize: '30px 30px'\n        }}></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-16 relative z-10\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Logo and Description */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n              <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center text-white text-2xl font-bold shadow-lg\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-white\">\n                دوزان\n              </span>\n            </div>\n            <p className=\"text-white/90 leading-relaxed mb-6\">\n              منصة تربط بين أصحاب المشاريع والحرفيين المهرة لتنفيذ المشاريع بكفاءة وجودة عالية في جميع أنحاء سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              روابط سريعة\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الرئيسية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  المشاريع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الحرفيون\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  من نحن\n                </Link>\n              </li>\n            </ul>\n          </div>\n          {/* Support */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              الدعم\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/contact\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  اتصل بنا\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  شروط الاستخدام\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              تواصل معنا\n            </h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">البريد الإلكتروني</p>\n                  <a href=\"mailto:<EMAIL>\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">الهاتف</p>\n                  <a href=\"tel:+963112345678\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    +963 11 234 5678\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">العنوان</p>\n                  <p className=\"text-white font-medium\">دمشق، سوريا</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"mt-12 pt-8 border-t border-white/20\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-white/80 text-sm\">\n                &copy; {new Date().getFullYear()} دوزان. جميع الحقوق محفوظة.\n              </p>\n              <p className=\"text-white/60 text-xs mt-1\">\n                صُنع بـ ❤️ في سوريا\n              </p>\n            </div>\n\n            <div className=\"flex items-center space-x-6 space-x-reverse\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">2500+</div>\n                <div className=\"text-white/70 text-xs\">حرفي</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">15000+</div>\n                <div className=\"text-white/70 text-xs\">مشروع</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">98%</div>\n                <div className=\"text-white/70 text-xs\">رضا</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6H;;;;;;0DAG5I,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAIlD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAyQ,UAAS;;;;;;;;;;;;;;;;0DAGjT,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA8jD,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO1mD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;;sEACvB,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;;sEAC3B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;;sEAChC,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAO3H,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAO,WAAU;;sEAC1B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3H,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,MAAK;gEAAuB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAM3H,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,MAAK;gEAAoB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAMxH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EACjE,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;8EACrE,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;;gDAAwB;gDAC3B,IAAI,OAAO,WAAW;gDAAG;;;;;;;sDAEnC,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;uCAEe"}}, {"offset": {"line": 2274, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2280, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout = ({ children }: MainLayoutProps) => {\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      <Header />\n      <main className=\"flex-grow\">{children}</main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB;IAC/C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;0BAAa;;;;;;0BAC7B,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe"}}, {"offset": {"line": 2320, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2326, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-white shadow-sm transition-all duration-200 hover:shadow-md',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('font-semibold leading-none tracking-tight text-navy', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-600', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,qBAAO,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACpE,GAAG,KAAK;kBAER;;;;;;AAGL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;AAGL,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;AAIP,WAAW,WAAW,GAAG"}}, {"offset": {"line": 2408, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2414, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Badge.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n  {\n    variants: {\n      variant: {\n        default: 'border-transparent bg-navy text-white hover:bg-navy/80',\n        secondary: 'border-transparent bg-teal text-white hover:bg-teal/80',\n        destructive: 'border-transparent bg-red-500 text-white hover:bg-red-500/80',\n        outline: 'text-navy border-navy',\n        success: 'border-transparent bg-green-500 text-white hover:bg-green-500/80',\n        warning: 'border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80',\n        info: 'border-transparent bg-skyblue text-navy hover:bg-skyblue/80',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  }\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE"}}, {"offset": {"line": 2454, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2460, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Dropdown.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\n\ninterface DropdownOption {\n  value: string;\n  label: string;\n  icon?: string;\n}\n\ninterface DropdownProps {\n  options: DropdownOption[];\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  className?: string;\n  disabled?: boolean;\n  error?: string;\n  label?: string;\n  required?: boolean;\n}\n\nconst Dropdown: React.FC<DropdownProps> = ({\n  options,\n  value,\n  onChange,\n  placeholder = 'اختر خيار',\n  className = '',\n  disabled = false,\n  error,\n  label,\n  required = false\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const dropdownRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  const selectedOption = options.find(option => option.value === value);\n  \n  const filteredOptions = options.filter(option =>\n    option.label.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n        setSearchTerm('');\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  const handleToggle = () => {\n    if (!disabled) {\n      setIsOpen(!isOpen);\n      if (!isOpen) {\n        setTimeout(() => {\n          inputRef.current?.focus();\n        }, 100);\n      }\n    }\n  };\n\n  const handleSelect = (optionValue: string) => {\n    onChange(optionValue);\n    setIsOpen(false);\n    setSearchTerm('');\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter') {\n      e.preventDefault();\n      if (filteredOptions.length > 0) {\n        handleSelect(filteredOptions[0].value);\n      }\n    } else if (e.key === 'Escape') {\n      setIsOpen(false);\n      setSearchTerm('');\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      {label && (\n        <label className=\"block text-gray-700 font-medium mb-2\">\n          {label}\n          {required && <span className=\"text-red-500 mr-1\">*</span>}\n        </label>\n      )}\n      \n      <div ref={dropdownRef} className=\"relative\">\n        <div\n          onClick={handleToggle}\n          className={`\n            relative w-full px-4 py-3 bg-white border rounded-xl cursor-pointer transition-all duration-300\n            ${isOpen \n              ? 'border-teal ring-2 ring-teal/20 shadow-lg' \n              : error \n                ? 'border-red-300 hover:border-red-400' \n                : 'border-gray-300 hover:border-gray-400'\n            }\n            ${disabled ? 'bg-gray-50 cursor-not-allowed opacity-60' : 'hover:shadow-md'}\n            ${className}\n          `}\n        >\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3 space-x-reverse flex-1\">\n              {selectedOption?.icon && (\n                <span className=\"text-lg flex-shrink-0\">{selectedOption.icon}</span>\n              )}\n              <span className={`block truncate ${selectedOption ? 'text-gray-900' : 'text-gray-500'}`}>\n                {selectedOption ? selectedOption.label : placeholder}\n              </span>\n            </div>\n            \n            {/* أيقونة السهم */}\n            <div className=\"flex-shrink-0 mr-3\">\n              <svg\n                className={`w-5 h-5 text-gray-400 transition-transform duration-300 ${\n                  isOpen ? 'rotate-180' : 'rotate-0'\n                }`}\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n              </svg>\n            </div>\n          </div>\n        </div>\n\n        {/* القائمة المنسدلة */}\n        {isOpen && (\n          <div className=\"absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-xl shadow-xl max-h-60 overflow-hidden\">\n            {/* حقل البحث */}\n            <div className=\"p-3 border-b border-gray-100\">\n              <input\n                ref={inputRef}\n                type=\"text\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                onKeyDown={handleKeyDown}\n                placeholder=\"ابحث...\"\n                className=\"w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal\"\n              />\n            </div>\n\n            {/* قائمة الخيارات */}\n            <div className=\"max-h-48 overflow-y-auto\">\n              {filteredOptions.length > 0 ? (\n                filteredOptions.map((option) => (\n                  <div\n                    key={option.value}\n                    onClick={() => handleSelect(option.value)}\n                    className={`\n                      flex items-center space-x-3 space-x-reverse px-4 py-3 cursor-pointer transition-colors duration-200\n                      ${value === option.value \n                        ? 'bg-gradient-to-r from-navy/10 to-teal/10 text-navy border-r-4 border-teal' \n                        : 'hover:bg-gray-50'\n                      }\n                    `}\n                  >\n                    {option.icon && (\n                      <span className=\"text-lg flex-shrink-0\">{option.icon}</span>\n                    )}\n                    <span className=\"block truncate\">{option.label}</span>\n                    {value === option.value && (\n                      <div className=\"flex-shrink-0 mr-auto\">\n                        <svg className=\"w-5 h-5 text-teal\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </div>\n                    )}\n                  </div>\n                ))\n              ) : (\n                <div className=\"px-4 py-3 text-gray-500 text-center\">\n                  لا توجد نتائج\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {error && (\n        <p className=\"mt-2 text-red-500 text-sm flex items-center\">\n          <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n          {error}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport default Dropdown;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,WAAoC,CAAC,EACzC,OAAO,EACP,KAAK,EACL,QAAQ,EACR,cAAc,WAAW,EACzB,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,KAAK,EACL,KAAK,EACL,WAAW,KAAK,EACjB;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;IAE/D,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAA,SACrC,OAAO,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAG5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;gBACV,cAAc;YAChB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI,CAAC,UAAU;YACb,UAAU,CAAC;YACX,IAAI,CAAC,QAAQ;gBACX,WAAW;oBACT,SAAS,OAAO,EAAE;gBACpB,GAAG;YACL;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,SAAS;QACT,UAAU;QACV,cAAc;IAChB;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,SAAS;YACrB,EAAE,cAAc;YAChB,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,aAAa,eAAe,CAAC,EAAE,CAAC,KAAK;YACvC;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,UAAU;YAC7B,UAAU;YACV,cAAc;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAU;;oBACd;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,KAAK;gBAAa,WAAU;;kCAC/B,8OAAC;wBACC,SAAS;wBACT,WAAW,CAAC;;YAEV,EAAE,SACE,8CACA,QACE,wCACA,wCACL;YACD,EAAE,WAAW,6CAA6C,kBAAkB;YAC5E,EAAE,UAAU;UACd,CAAC;kCAED,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,gBAAgB,sBACf,8OAAC;4CAAK,WAAU;sDAAyB,eAAe,IAAI;;;;;;sDAE9D,8OAAC;4CAAK,WAAW,CAAC,eAAe,EAAE,iBAAiB,kBAAkB,iBAAiB;sDACpF,iBAAiB,eAAe,KAAK,GAAG;;;;;;;;;;;;8CAK7C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,WAAW,CAAC,wDAAwD,EAClE,SAAS,eAAe,YACxB;wCACF,MAAK;wCACL,QAAO;wCACP,SAAQ;kDAER,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAO5E,wBACC,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAK;oCACL,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oCAC7C,WAAW;oCACX,aAAY;oCACZ,WAAU;;;;;;;;;;;0CAKd,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,MAAM,GAAG,IACxB,gBAAgB,GAAG,CAAC,CAAC,uBACnB,8OAAC;wCAEC,SAAS,IAAM,aAAa,OAAO,KAAK;wCACxC,WAAW,CAAC;;sBAEV,EAAE,UAAU,OAAO,KAAK,GACpB,8EACA,mBACH;oBACH,CAAC;;4CAEA,OAAO,IAAI,kBACV,8OAAC;gDAAK,WAAU;0DAAyB,OAAO,IAAI;;;;;;0DAEtD,8OAAC;gDAAK,WAAU;0DAAkB,OAAO,KAAK;;;;;;4CAC7C,UAAU,OAAO,KAAK,kBACrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAoB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC3E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;uCAjBtE,OAAO,KAAK;;;;8DAwBrB,8OAAC;oCAAI,WAAU;8CAAsC;;;;;;;;;;;;;;;;;;;;;;;YAS9D,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACtE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE;;;;;;;;;;;;;AAKX;uCAEe"}}, {"offset": {"line": 2755, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2761, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/craftsmen/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport MainLayout from '@/components/layout/MainLayout';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { Badge } from '@/components/ui/Badge';\nimport { Button } from '@/components/ui/Button';\nimport Dropdown from '@/components/ui/Dropdown';\n\ninterface Craftsman {\n  id: number;\n  name: string;\n  profession: string;\n  bio: string;\n  location: string;\n  rating: number;\n  reviewsCount: number;\n  completedJobs: number;\n  hourlyRate: number;\n  skills: string[];\n  avatar: string;\n  isOnline: boolean;\n  responseTime: string;\n  joinedDate: string;\n  verified: boolean;\n}\n\nconst CraftsmenPage = () => {\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [selectedLocation, setSelectedLocation] = useState('all');\n  const [sortBy, setSortBy] = useState('rating');\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const categories = [\n    { value: 'all', label: 'جميع التخصصات', icon: '🔍' },\n    { value: 'carpentry', label: 'النجارة', icon: '🪚' },\n    { value: 'plumbing', label: 'السباكة', icon: '🔧' },\n    { value: 'electrical', label: 'الكهرباء', icon: '⚡' },\n    { value: 'painting', label: 'الدهان', icon: '🖌️' },\n    { value: 'construction', label: 'البناء', icon: '🧱' },\n    { value: 'hvac', label: 'التكييف', icon: '❄️' },\n    { value: 'metalwork', label: 'الحدادة', icon: '🔨' },\n    { value: 'landscaping', label: 'تنسيق الحدائق', icon: '🌱' },\n  ];\n\n  const locations = [\n    { value: 'all', label: 'جميع المحافظات', icon: '🌍' },\n    { value: 'damascus', label: 'دمشق', icon: '🏛️' },\n    { value: 'aleppo', label: 'حلب', icon: '🏰' },\n    { value: 'homs', label: 'حمص', icon: '🏘️' },\n    { value: 'hama', label: 'حماة', icon: '🌾' },\n    { value: 'lattakia', label: 'اللاذقية', icon: '🌊' },\n    { value: 'tartous', label: 'طرطوس', icon: '⛵' },\n    { value: 'daraa', label: 'درعا', icon: '🏜️' },\n  ];\n\n  const sortOptions = [\n    { value: 'rating', label: 'الأعلى تقييماً', icon: '⭐' },\n    { value: 'experience', label: 'الأكثر خبرة', icon: '📊' },\n    { value: 'price_low', label: 'السعر (الأقل أولاً)', icon: '💵' },\n    { value: 'price_high', label: 'السعر (الأعلى أولاً)', icon: '💰' },\n  ];\n\n  const craftsmen: Craftsman[] = [\n    {\n      id: 1,\n      name: 'محمد النجار',\n      profession: 'نجار محترف',\n      bio: 'نجار محترف مع خبرة 10 سنوات في تصميم وتنفيذ الأثاث المنزلي والمكتبي. متخصص في المطابخ والخزائن العصرية.',\n      location: 'دمشق، المزة',\n      rating: 4.9,\n      reviewsCount: 47,\n      completedJobs: 89,\n      hourlyRate: 2500,\n      skills: ['نجارة', 'تصميم أثاث', 'تركيب', 'ترميم'],\n      avatar: 'https://randomuser.me/api/portraits/men/1.jpg',\n      isOnline: true,\n      responseTime: 'خلال ساعة',\n      joinedDate: '2022-03-15',\n      verified: true\n    },\n    {\n      id: 2,\n      name: 'أحمد السباك',\n      profession: 'سباك معتمد',\n      bio: 'سباك معتمد مع خبرة 8 سنوات في جميع أعمال السباكة والصرف الصحي. أعمل بأحدث التقنيات وأضمن الجودة.',\n      location: 'حلب، الفرقان',\n      rating: 4.8,\n      reviewsCount: 32,\n      completedJobs: 67,\n      hourlyRate: 2000,\n      skills: ['سباكة', 'صرف صحي', 'تسليك', 'تركيب'],\n      avatar: 'https://randomuser.me/api/portraits/men/2.jpg',\n      isOnline: false,\n      responseTime: 'خلال 3 ساعات',\n      joinedDate: '2022-07-20',\n      verified: true\n    },\n    {\n      id: 3,\n      name: 'علي الكهربائي',\n      profession: 'كهربائي محترف',\n      bio: 'كهربائي محترف متخصص في التمديدات الكهربائية والإضاءة الذكية. أعمل وفق أعلى معايير السلامة.',\n      location: 'دمشق، جرمانا',\n      rating: 4.7,\n      reviewsCount: 28,\n      completedJobs: 54,\n      hourlyRate: 2200,\n      skills: ['كهرباء', 'تمديدات', 'إضاءة', 'صيانة'],\n      avatar: 'https://randomuser.me/api/portraits/men/3.jpg',\n      isOnline: true,\n      responseTime: 'خلال 30 دقيقة',\n      joinedDate: '2023-01-10',\n      verified: false\n    },\n    {\n      id: 4,\n      name: 'خالد الدهان',\n      profession: 'دهان وديكور',\n      bio: 'دهان محترف متخصص في الدهانات الحديثة والديكورات الداخلية. أستخدم أفضل أنواع الدهانات العالمية.',\n      location: 'حمص، الوعر',\n      rating: 4.6,\n      reviewsCount: 19,\n      completedJobs: 41,\n      hourlyRate: 1800,\n      skills: ['دهان', 'ديكور', 'تصميم', 'تشطيب'],\n      avatar: 'https://randomuser.me/api/portraits/men/4.jpg',\n      isOnline: true,\n      responseTime: 'خلال ساعتين',\n      joinedDate: '2023-05-22',\n      verified: true\n    },\n    {\n      id: 5,\n      name: 'سامر البناء',\n      profession: 'مقاول بناء',\n      bio: 'مقاول بناء مع فريق عمل متكامل. متخصص في أعمال البناء والترميم والتشطيبات بجودة عالية.',\n      location: 'حماة، المدينة',\n      rating: 4.8,\n      reviewsCount: 35,\n      completedJobs: 23,\n      hourlyRate: 3000,\n      skills: ['بناء', 'ترميم', 'تشطيب', 'إدارة مشاريع'],\n      avatar: 'https://randomuser.me/api/portraits/men/5.jpg',\n      isOnline: false,\n      responseTime: 'خلال 4 ساعات',\n      joinedDate: '2022-11-08',\n      verified: true\n    },\n    {\n      id: 6,\n      name: 'يوسف التكييف',\n      profession: 'فني تكييف',\n      bio: 'فني تكييف معتمد متخصص في تركيب وصيانة جميع أنواع أجهزة التكييف والتبريد.',\n      location: 'اللاذقية، الرمل الشمالي',\n      rating: 4.5,\n      reviewsCount: 22,\n      completedJobs: 38,\n      hourlyRate: 2300,\n      skills: ['تكييف', 'تبريد', 'صيانة', 'تركيب'],\n      avatar: 'https://randomuser.me/api/portraits/men/6.jpg',\n      isOnline: true,\n      responseTime: 'خلال ساعة',\n      joinedDate: '2023-02-14',\n      verified: false\n    }\n  ];\n\n  const filteredCraftsmen = craftsmen.filter(craftsman => {\n    const matchesCategory = selectedCategory === 'all' || craftsman.skills.some(skill =>\n      skill.toLowerCase().includes(selectedCategory.toLowerCase())\n    );\n    const matchesLocation = selectedLocation === 'all' ||\n      craftsman.location.toLowerCase().includes(selectedLocation.toLowerCase());\n    const matchesSearch = searchQuery === '' ||\n      craftsman.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      craftsman.profession.toLowerCase().includes(searchQuery.toLowerCase()) ||\n      craftsman.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()));\n\n    return matchesCategory && matchesLocation && matchesSearch;\n  });\n\n  const sortedCraftsmen = [...filteredCraftsmen].sort((a, b) => {\n    switch (sortBy) {\n      case 'rating':\n        return b.rating - a.rating;\n      case 'price_low':\n        return a.hourlyRate - b.hourlyRate;\n      case 'price_high':\n        return b.hourlyRate - a.hourlyRate;\n      case 'experience':\n        return b.completedJobs - a.completedJobs;\n      default:\n        return b.rating - a.rating;\n    }\n  });\n\n  return (\n    <MainLayout>\n      <div className=\"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white relative overflow-hidden\">\n        {/* خلفية هندسية متناسقة */}\n        <div className=\"absolute inset-0 opacity-5\">\n          <div className=\"absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl\"></div>\n          <div className=\"absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl\"></div>\n        </div>\n\n        {/* شبكة نقطية ناعمة */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"w-full h-full\" style={{\n            backgroundImage: 'radial-gradient(circle, #567C8D 1px, transparent 1px)',\n            backgroundSize: '50px 50px'\n          }}></div>\n        </div>\n\n        <div className=\"container mx-auto px-4 py-8 relative z-10\">\n          {/* Header */}\n          <div className=\"text-center mb-12\">\n            <div className=\"inline-block bg-gradient-to-r from-navy to-teal text-white px-8 py-3 rounded-full text-sm font-medium mb-6 shadow-lg\">\n              <span className=\"text-lg\">👨‍🔧</span>\n              <span className=\"mr-2\">حرفيون محترفون</span>\n            </div>\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-navy mb-6 leading-tight\">\n              أفضل\n              <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-teal via-navy to-teal\">\n                الحرفيين\n              </span>\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed\">\n              اعثر على أفضل الحرفيين المحترفين في سوريا لتنفيذ مشاريعك بجودة عالية وأسعار منافسة\n            </p>\n            <div className=\"mt-8 w-24 h-1 bg-gradient-to-r from-teal to-navy mx-auto rounded-full\"></div>\n          </div>\n\n          {/* Search and Filters */}\n          <div className=\"bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20 p-8 mb-12\">\n            <div className=\"text-center mb-6\">\n              <h3 className=\"text-xl font-bold text-navy mb-2\">البحث والفلترة</h3>\n              <p className=\"text-gray-600\">ابحث عن الحرفي المناسب لمشروعك</p>\n            </div>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6\">\n              <div>\n                <label className=\"block text-gray-700 font-medium mb-2\">\n                  البحث\n                </label>\n                <input\n                  type=\"text\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  placeholder=\"ابحث عن حرفي...\"\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal transition-all duration-300\"\n                />\n              </div>\n              <div>\n                <Dropdown\n                  label=\"التخصص\"\n                  options={categories}\n                  value={selectedCategory}\n                  onChange={setSelectedCategory}\n                  placeholder=\"اختر التخصص\"\n                />\n              </div>\n              <div>\n                <Dropdown\n                  label=\"المحافظة\"\n                  options={locations}\n                  value={selectedLocation}\n                  onChange={setSelectedLocation}\n                  placeholder=\"اختر المحافظة\"\n                />\n              </div>\n              <div>\n                <Dropdown\n                  label=\"ترتيب حسب\"\n                  options={sortOptions}\n                  value={sortBy}\n                  onChange={setSortBy}\n                  placeholder=\"اختر طريقة الترتيب\"\n                />\n              </div>\n            </div>\n\n            {/* إحصائيات سريعة */}\n            <div className=\"pt-6 border-t border-gray-200\">\n              <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-center\">\n                <div className=\"bg-gradient-to-r from-navy/5 to-teal/5 rounded-xl p-4\">\n                  <div className=\"text-2xl font-bold text-navy\">{craftsmen.length}</div>\n                  <div className=\"text-sm text-gray-600\">حرفي محترف</div>\n                </div>\n                <div className=\"bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4\">\n                  <div className=\"text-2xl font-bold text-green-600\">{sortedCraftsmen.length}</div>\n                  <div className=\"text-sm text-gray-600\">نتيجة البحث</div>\n                </div>\n                <div className=\"bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-4\">\n                  <div className=\"text-2xl font-bold text-blue-600\">\n                    {Math.round(craftsmen.reduce((sum, c) => sum + c.rating, 0) / craftsmen.length * 10) / 10}\n                  </div>\n                  <div className=\"text-sm text-gray-600\">متوسط التقييم</div>\n                </div>\n                <div className=\"bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-4\">\n                  <div className=\"text-2xl font-bold text-purple-600\">\n                    {craftsmen.reduce((sum, c) => sum + c.completedJobs, 0)}\n                  </div>\n                  <div className=\"text-sm text-gray-600\">مشروع مكتمل</div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Craftsmen Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n            {sortedCraftsmen.map((craftsman) => (\n              <Card key={craftsman.id} className=\"hover:shadow-xl transition-all duration-300 border-0 bg-white/90 backdrop-blur-sm\">\n                <CardHeader className=\"pb-4\">\n                  <div className=\"flex items-start justify-between\">\n                    <div className=\"flex items-center\">\n                      <div className=\"relative\">\n                        <img\n                          src={craftsman.avatar}\n                          alt={craftsman.name}\n                          className=\"w-16 h-16 rounded-full object-cover\"\n                        />\n                        {craftsman.isOnline && (\n                          <div className=\"absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 border-2 border-white rounded-full\"></div>\n                        )}\n                      </div>\n                      <div className=\"mr-4\">\n                        <div className=\"flex items-center\">\n                          <h3 className=\"font-bold text-navy\">{craftsman.name}</h3>\n                          {craftsman.verified && (\n                            <svg className=\"w-5 h-5 text-blue-500 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                              <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                            </svg>\n                          )}\n                        </div>\n                        <p className=\"text-sm text-gray-600\">{craftsman.profession}</p>\n                        <div className=\"flex items-center text-sm text-gray-500\">\n                          <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                          </svg>\n                          {craftsman.location}\n                        </div>\n                      </div>\n                    </div>\n                    <div className=\"text-left\">\n                      <div className=\"flex items-center\">\n                        <svg className=\"w-4 h-4 text-yellow-500 ml-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                        </svg>\n                        <span className=\"font-semibold\">{craftsman.rating}</span>\n                        <span className=\"text-sm text-gray-500 mr-1\">({craftsman.reviewsCount})</span>\n                      </div>\n                    </div>\n                  </div>\n                </CardHeader>\n\n                <CardContent>\n                  <p className=\"text-gray-700 text-sm mb-4 line-clamp-2\">{craftsman.bio}</p>\n\n                  <div className=\"flex flex-wrap gap-1 mb-4\">\n                    {craftsman.skills.slice(0, 3).map((skill, index) => (\n                      <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                        {skill}\n                      </Badge>\n                    ))}\n                    {craftsman.skills.length > 3 && (\n                      <Badge variant=\"outline\" className=\"text-xs\">\n                        +{craftsman.skills.length - 3}\n                      </Badge>\n                    )}\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-4 mb-4 text-sm\">\n                    <div className=\"bg-green-50 p-2 rounded-lg\">\n                      <div className=\"text-green-600 font-medium\">السعر/ساعة</div>\n                      <div className=\"font-bold text-green-700\">{craftsman.hourlyRate.toLocaleString()} ل.س</div>\n                    </div>\n                    <div className=\"bg-blue-50 p-2 rounded-lg\">\n                      <div className=\"text-blue-600 font-medium\">المشاريع</div>\n                      <div className=\"font-bold text-blue-700\">{craftsman.completedJobs}</div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center justify-between text-sm text-gray-600 mb-4\">\n                    <span className=\"flex items-center\">\n                      <div className={`w-2 h-2 rounded-full ml-1 ${craftsman.isOnline ? 'bg-green-500' : 'bg-gray-400'}`}></div>\n                      {craftsman.isOnline ? 'متصل الآن' : 'غير متصل'}\n                    </span>\n                    <span>يرد {craftsman.responseTime}</span>\n                  </div>\n\n                  <div className=\"flex space-x-2 space-x-reverse\">\n                    <Link href={`/craftsmen/${craftsman.id}`} className=\"flex-1\">\n                      <Button size=\"sm\" className=\"w-full bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90\">\n                        عرض الملف الشخصي\n                      </Button>\n                    </Link>\n                    <Button size=\"sm\" variant=\"outline\" className=\"px-3\">\n                      <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n                      </svg>\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {/* Load More */}\n          <div className=\"text-center mt-12\">\n            <Button variant=\"outline\" size=\"lg\" className=\"px-8\">\n              تحميل المزيد من الحرفيين\n            </Button>\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  );\n};\n\nexport default CraftsmenPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AA4BA,MAAM,gBAAgB;IACpB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,aAAa;QACjB;YAAE,OAAO;YAAO,OAAO;YAAiB,MAAM;QAAK;QACnD;YAAE,OAAO;YAAa,OAAO;YAAW,MAAM;QAAK;QACnD;YAAE,OAAO;YAAY,OAAO;YAAW,MAAM;QAAK;QAClD;YAAE,OAAO;YAAc,OAAO;YAAY,MAAM;QAAI;QACpD;YAAE,OAAO;YAAY,OAAO;YAAU,MAAM;QAAM;QAClD;YAAE,OAAO;YAAgB,OAAO;YAAU,MAAM;QAAK;QACrD;YAAE,OAAO;YAAQ,OAAO;YAAW,MAAM;QAAK;QAC9C;YAAE,OAAO;YAAa,OAAO;YAAW,MAAM;QAAK;QACnD;YAAE,OAAO;YAAe,OAAO;YAAiB,MAAM;QAAK;KAC5D;IAED,MAAM,YAAY;QAChB;YAAE,OAAO;YAAO,OAAO;YAAkB,MAAM;QAAK;QACpD;YAAE,OAAO;YAAY,OAAO;YAAQ,MAAM;QAAM;QAChD;YAAE,OAAO;YAAU,OAAO;YAAO,MAAM;QAAK;QAC5C;YAAE,OAAO;YAAQ,OAAO;YAAO,MAAM;QAAM;QAC3C;YAAE,OAAO;YAAQ,OAAO;YAAQ,MAAM;QAAK;QAC3C;YAAE,OAAO;YAAY,OAAO;YAAY,MAAM;QAAK;QACnD;YAAE,OAAO;YAAW,OAAO;YAAS,MAAM;QAAI;QAC9C;YAAE,OAAO;YAAS,OAAO;YAAQ,MAAM;QAAM;KAC9C;IAED,MAAM,cAAc;QAClB;YAAE,OAAO;YAAU,OAAO;YAAkB,MAAM;QAAI;QACtD;YAAE,OAAO;YAAc,OAAO;YAAe,MAAM;QAAK;QACxD;YAAE,OAAO;YAAa,OAAO;YAAuB,MAAM;QAAK;QAC/D;YAAE,OAAO;YAAc,OAAO;YAAwB,MAAM;QAAK;KAClE;IAED,MAAM,YAAyB;QAC7B;YACE,IAAI;YACJ,MAAM;YACN,YAAY;YACZ,KAAK;YACL,UAAU;YACV,QAAQ;YACR,cAAc;YACd,eAAe;YACf,YAAY;YACZ,QAAQ;gBAAC;gBAAS;gBAAc;gBAAS;aAAQ;YACjD,QAAQ;YACR,UAAU;YACV,cAAc;YACd,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,YAAY;YACZ,KAAK;YACL,UAAU;YACV,QAAQ;YACR,cAAc;YACd,eAAe;YACf,YAAY;YACZ,QAAQ;gBAAC;gBAAS;gBAAW;gBAAS;aAAQ;YAC9C,QAAQ;YACR,UAAU;YACV,cAAc;YACd,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,YAAY;YACZ,KAAK;YACL,UAAU;YACV,QAAQ;YACR,cAAc;YACd,eAAe;YACf,YAAY;YACZ,QAAQ;gBAAC;gBAAU;gBAAW;gBAAS;aAAQ;YAC/C,QAAQ;YACR,UAAU;YACV,cAAc;YACd,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,YAAY;YACZ,KAAK;YACL,UAAU;YACV,QAAQ;YACR,cAAc;YACd,eAAe;YACf,YAAY;YACZ,QAAQ;gBAAC;gBAAQ;gBAAS;gBAAS;aAAQ;YAC3C,QAAQ;YACR,UAAU;YACV,cAAc;YACd,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,YAAY;YACZ,KAAK;YACL,UAAU;YACV,QAAQ;YACR,cAAc;YACd,eAAe;YACf,YAAY;YACZ,QAAQ;gBAAC;gBAAQ;gBAAS;gBAAS;aAAe;YAClD,QAAQ;YACR,UAAU;YACV,cAAc;YACd,YAAY;YACZ,UAAU;QACZ;QACA;YACE,IAAI;YACJ,MAAM;YACN,YAAY;YACZ,KAAK;YACL,UAAU;YACV,QAAQ;YACR,cAAc;YACd,eAAe;YACf,YAAY;YACZ,QAAQ;gBAAC;gBAAS;gBAAS;gBAAS;aAAQ;YAC5C,QAAQ;YACR,UAAU;YACV,cAAc;YACd,YAAY;YACZ,UAAU;QACZ;KACD;IAED,MAAM,oBAAoB,UAAU,MAAM,CAAC,CAAA;QACzC,MAAM,kBAAkB,qBAAqB,SAAS,UAAU,MAAM,CAAC,IAAI,CAAC,CAAA,QAC1E,MAAM,WAAW,GAAG,QAAQ,CAAC,iBAAiB,WAAW;QAE3D,MAAM,kBAAkB,qBAAqB,SAC3C,UAAU,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,iBAAiB,WAAW;QACxE,MAAM,gBAAgB,gBAAgB,MACpC,UAAU,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC7D,UAAU,UAAU,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OACnE,UAAU,MAAM,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAErF,OAAO,mBAAmB,mBAAmB;IAC/C;IAEA,MAAM,kBAAkB;WAAI;KAAkB,CAAC,IAAI,CAAC,CAAC,GAAG;QACtD,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;YAC5B,KAAK;gBACH,OAAO,EAAE,UAAU,GAAG,EAAE,UAAU;YACpC,KAAK;gBACH,OAAO,EAAE,UAAU,GAAG,EAAE,UAAU;YACpC,KAAK;gBACH,OAAO,EAAE,aAAa,GAAG,EAAE,aAAa;YAC1C;gBACE,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;QAC9B;IACF;IAEA,qBACE,8OAAC,0IAAA,CAAA,UAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAgB,OAAO;4BACpC,iBAAiB;4BACjB,gBAAgB;wBAClB;;;;;;;;;;;8BAGF,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,8OAAC;4CAAK,WAAU;sDAAO;;;;;;;;;;;;8CAEzB,8OAAC;oCAAG,WAAU;;wCAA0E;sDAEtF,8OAAC;4CAAK,WAAU;sDAAkF;;;;;;;;;;;;8CAIpG,8OAAC;oCAAE,WAAU;8CAA0D;;;;;;8CAGvE,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAE/B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAuC;;;;;;8DAGxD,8OAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC9C,aAAY;oDACZ,WAAU;;;;;;;;;;;;sDAGd,8OAAC;sDACC,cAAA,8OAAC,oIAAA,CAAA,UAAQ;gDACP,OAAM;gDACN,SAAS;gDACT,OAAO;gDACP,UAAU;gDACV,aAAY;;;;;;;;;;;sDAGhB,8OAAC;sDACC,cAAA,8OAAC,oIAAA,CAAA,UAAQ;gDACP,OAAM;gDACN,SAAS;gDACT,OAAO;gDACP,UAAU;gDACV,aAAY;;;;;;;;;;;sDAGhB,8OAAC;sDACC,cAAA,8OAAC,oIAAA,CAAA,UAAQ;gDACP,OAAM;gDACN,SAAS;gDACT,OAAO;gDACP,UAAU;gDACV,aAAY;;;;;;;;;;;;;;;;;8CAMlB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAgC,UAAU,MAAM;;;;;;kEAC/D,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAqC,gBAAgB,MAAM;;;;;;kEAC1E,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,KAAK,KAAK,CAAC,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE,KAAK,UAAU,MAAM,GAAG,MAAM;;;;;;kEAEzF,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACZ,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,EAAE;;;;;;kEAEvD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO/C,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,0BACpB,8OAAC,gIAAA,CAAA,OAAI;oCAAoB,WAAU;;sDACjC,8OAAC,gIAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,KAAK,UAAU,MAAM;wEACrB,KAAK,UAAU,IAAI;wEACnB,WAAU;;;;;;oEAEX,UAAU,QAAQ,kBACjB,8OAAC;wEAAI,WAAU;;;;;;;;;;;;0EAGnB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAG,WAAU;0FAAuB,UAAU,IAAI;;;;;;4EAClD,UAAU,QAAQ,kBACjB,8OAAC;gFAAI,WAAU;gFAA6B,MAAK;gFAAe,SAAQ;0FACtE,cAAA,8OAAC;oFAAK,UAAS;oFAAU,GAAE;oFAAkiB,UAAS;;;;;;;;;;;;;;;;;kFAI5kB,8OAAC;wEAAE,WAAU;kFAAyB,UAAU,UAAU;;;;;;kFAC1D,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;gFAAe,MAAK;gFAAO,QAAO;gFAAe,SAAQ;0FACtE,cAAA,8OAAC;oFAAK,eAAc;oFAAQ,gBAAe;oFAAQ,aAAa;oFAAG,GAAE;;;;;;;;;;;4EAEtE,UAAU,QAAQ;;;;;;;;;;;;;;;;;;;kEAIzB,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;oEAA+B,MAAK;oEAAe,SAAQ;8EACxE,cAAA,8OAAC;wEAAK,GAAE;;;;;;;;;;;8EAEV,8OAAC;oEAAK,WAAU;8EAAiB,UAAU,MAAM;;;;;;8EACjD,8OAAC;oEAAK,WAAU;;wEAA6B;wEAAE,UAAU,YAAY;wEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAM9E,8OAAC,gIAAA,CAAA,cAAW;;8DACV,8OAAC;oDAAE,WAAU;8DAA2C,UAAU,GAAG;;;;;;8DAErE,8OAAC;oDAAI,WAAU;;wDACZ,UAAU,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACxC,8OAAC,iIAAA,CAAA,QAAK;gEAAa,SAAQ;gEAAU,WAAU;0EAC5C;+DADS;;;;;wDAIb,UAAU,MAAM,CAAC,MAAM,GAAG,mBACzB,8OAAC,iIAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;;gEAAU;gEACzC,UAAU,MAAM,CAAC,MAAM,GAAG;;;;;;;;;;;;;8DAKlC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA6B;;;;;;8EAC5C,8OAAC;oEAAI,WAAU;;wEAA4B,UAAU,UAAU,CAAC,cAAc;wEAAG;;;;;;;;;;;;;sEAEnF,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA4B;;;;;;8EAC3C,8OAAC;oEAAI,WAAU;8EAA2B,UAAU,aAAa;;;;;;;;;;;;;;;;;;8DAIrE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;;8EACd,8OAAC;oEAAI,WAAW,CAAC,0BAA0B,EAAE,UAAU,QAAQ,GAAG,iBAAiB,eAAe;;;;;;gEACjG,UAAU,QAAQ,GAAG,cAAc;;;;;;;sEAEtC,8OAAC;;gEAAK;gEAAK,UAAU,YAAY;;;;;;;;;;;;;8DAGnC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE;4DAAE,WAAU;sEAClD,cAAA,8OAAC,kIAAA,CAAA,SAAM;gEAAC,MAAK;gEAAK,WAAU;0EAAgF;;;;;;;;;;;sEAI9G,8OAAC,kIAAA,CAAA,SAAM;4DAAC,MAAK;4DAAK,SAAQ;4DAAU,WAAU;sEAC5C,cAAA,8OAAC;gEAAI,WAAU;gEAAU,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACjE,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAvFpE,UAAU,EAAE;;;;;;;;;;sCAiG3B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;0CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjE;uCAEe"}}, {"offset": {"line": 3847, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}