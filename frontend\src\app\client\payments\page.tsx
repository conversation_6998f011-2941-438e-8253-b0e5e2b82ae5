'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';

const ClientPaymentsPage = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const payments = [
    {
      id: 1,
      projectId: 1,
      projectTitle: 'تجديد المطبخ الرئيسي',
      craftsmanName: 'أحمد النجار',
      amount: '₺7,500',
      status: 'مكتمل',
      paymentMethod: 'بطاقة ائتمان',
      date: '2024-02-15',
      invoiceNumber: 'INV-001',
      description: 'دفعة كاملة لمشروع تجديد المطبخ',
      type: 'دفعة كاملة'
    },
    {
      id: 2,
      projectId: 2,
      projectTitle: 'إصلاح نظام السباكة',
      craftsmanName: 'علي السباك',
      amount: '₺1,100',
      status: 'معلق',
      paymentMethod: 'تحويل بنكي',
      date: '2024-02-20',
      invoiceNumber: 'INV-002',
      description: 'دفعة مقدمة 50% من قيمة المشروع',
      type: 'دفعة مقدمة'
    },
    {
      id: 3,
      projectId: 4,
      projectTitle: 'تركيب نظام كهرباء ذكي',
      craftsmanName: 'سارة الكهربائية',
      amount: '₺2,400',
      status: 'مرفوض',
      paymentMethod: 'محفظة إلكترونية',
      date: '2024-02-18',
      invoiceNumber: 'INV-003',
      description: 'دفعة مقدمة للمشروع',
      type: 'دفعة مقدمة'
    },
    {
      id: 4,
      projectId: 3,
      projectTitle: 'دهان الشقة الكاملة',
      craftsmanName: 'محمد الدهان',
      amount: '₺3,200',
      status: 'مكتمل',
      paymentMethod: 'نقداً',
      date: '2024-01-25',
      invoiceNumber: 'INV-004',
      description: 'دفعة كاملة بعد إتمام العمل',
      type: 'دفعة نهائية'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'مكتمل':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'معلق':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'مرفوض':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'قيد المعالجة':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'بطاقة ائتمان':
        return '💳';
      case 'تحويل بنكي':
        return '🏦';
      case 'محفظة إلكترونية':
        return '📱';
      case 'نقداً':
        return '💵';
      default:
        return '💰';
    }
  };

  const filteredPayments = payments.filter(payment => {
    if (activeFilter === 'all') return true;
    return payment.status === activeFilter;
  });

  const stats = [
    {
      title: 'إجمالي المدفوعات',
      value: '₺14,200',
      color: 'text-blue-600'
    },
    {
      title: 'المدفوعات المكتملة',
      value: payments.filter(p => p.status === 'مكتمل').length.toString(),
      color: 'text-green-600'
    },
    {
      title: 'المدفوعات المعلقة',
      value: payments.filter(p => p.status === 'معلق').length.toString(),
      color: 'text-yellow-600'
    },
    {
      title: 'هذا الشهر',
      value: '₺8,600',
      color: 'text-purple-600'
    }
  ];

  return (
    <ProtectedRoute requiredRoles="client">
      <DashboardLayout 
        title="المدفوعات والفواتير"
        subtitle="إدارة ومتابعة جميع مدفوعاتك"
        actions={
          <Button className="bg-gradient-to-r from-navy to-teal">
            إضافة طريقة دفع
          </Button>
        }
      >
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className={`text-3xl ${stat.color}`}>
                    💳
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-wrap gap-2">
              {[
                { id: 'all', label: 'جميع المدفوعات' },
                { id: 'مكتمل', label: 'مكتملة' },
                { id: 'معلق', label: 'معلقة' },
                { id: 'قيد المعالجة', label: 'قيد المعالجة' },
                { id: 'مرفوض', label: 'مرفوضة' }
              ].map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => setActiveFilter(filter.id)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    activeFilter === filter.id
                      ? 'bg-navy text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {filter.label}
                </button>
              ))}
            </div>
          </div>

          {/* Payments List */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">رقم الفاتورة</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">المشروع</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">الحرفي</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">المبلغ</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">طريقة الدفع</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">الحالة</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">التاريخ</th>
                    <th className="text-right py-4 px-6 font-medium text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredPayments.map((payment) => (
                    <tr key={payment.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-4 px-6">
                        <div className="font-medium text-navy">{payment.invoiceNumber}</div>
                        <div className="text-sm text-gray-500">{payment.type}</div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="font-medium text-gray-900">{payment.projectTitle}</div>
                        <div className="text-sm text-gray-500">{payment.description}</div>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <div className="w-8 h-8 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white text-sm font-bold">
                            {payment.craftsmanName.charAt(0)}
                          </div>
                          <span className="font-medium text-gray-900">{payment.craftsmanName}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <span className="text-lg font-bold text-gray-900">{payment.amount}</span>
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <span className="text-lg">{getPaymentMethodIcon(payment.paymentMethod)}</span>
                          <span className="text-gray-700">{payment.paymentMethod}</span>
                        </div>
                      </td>
                      <td className="py-4 px-6">
                        <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(payment.status)}`}>
                          {payment.status}
                        </span>
                      </td>
                      <td className="py-4 px-6 text-gray-600">
                        {payment.date}
                      </td>
                      <td className="py-4 px-6">
                        <div className="flex space-x-2 space-x-reverse">
                          <Button size="sm" variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                            عرض الفاتورة
                          </Button>
                          {payment.status === 'مكتمل' && (
                            <Button size="sm" variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-100">
                              تحميل PDF
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {filteredPayments.length === 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-6xl mb-4">💳</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد مدفوعات</h3>
              <p className="text-gray-600 mb-6">لم يتم العثور على مدفوعات بالمعايير المحددة</p>
              <Link href="/client/projects">
                <Button className="bg-gradient-to-r from-navy to-teal">
                  عرض مشاريعي
                </Button>
              </Link>
            </div>
          )}

          {/* Payment Methods */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">طرق الدفع المحفوظة</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="border border-gray-200 rounded-lg p-4 hover:border-navy transition-colors duration-200">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <span className="text-2xl">💳</span>
                    <div>
                      <p className="font-medium text-gray-900">بطاقة ائتمان</p>
                      <p className="text-sm text-gray-500">**** **** **** 1234</p>
                    </div>
                  </div>
                  <span className="px-2 py-1 bg-green-100 text-green-800 rounded text-xs">افتراضية</span>
                </div>
                <div className="flex justify-between text-sm text-gray-600">
                  <span>انتهاء الصلاحية: 12/26</span>
                  <button className="text-navy hover:text-navy/80">تعديل</button>
                </div>
              </div>

              <div className="border border-gray-200 rounded-lg p-4 hover:border-navy transition-colors duration-200">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <span className="text-2xl">🏦</span>
                    <div>
                      <p className="font-medium text-gray-900">حساب بنكي</p>
                      <p className="text-sm text-gray-500">البنك التجاري السوري</p>
                    </div>
                  </div>
                </div>
                <div className="flex justify-between text-sm text-gray-600">
                  <span>رقم الحساب: ****5678</span>
                  <button className="text-navy hover:text-navy/80">تعديل</button>
                </div>
              </div>

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center hover:border-navy transition-colors duration-200 cursor-pointer">
                <div className="text-center">
                  <span className="text-3xl text-gray-400 mb-2 block">➕</span>
                  <p className="text-gray-600 font-medium">إضافة طريقة دفع جديدة</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default ClientPaymentsPage;
