# NextAuth.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=dozan-secret-key-2024-very-secure-random-string-for-development

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3001

# Database Configuration
DATABASE_URL="postgresql://postgres:password@localhost:5432/dozan_db?schema=public"

# Google OAuth (optional - can be added later)
# GOOGLE_CLIENT_ID=your-google-client-id
# GOOGLE_CLIENT_SECRET=your-google-client-secret

# Facebook OAuth (optional - can be added later)
# FACEBOOK_CLIENT_ID=your-facebook-client-id
# FACEBOOK_CLIENT_SECRET=your-facebook-client-secret
