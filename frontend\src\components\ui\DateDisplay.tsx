'use client';

import React, { useState, useEffect } from 'react';
import { formatDate } from '@/lib/utils';

interface DateDisplayProps {
  date: string;
  className?: string;
}

const DateDisplay: React.FC<DateDisplayProps> = ({ date, className = '' }) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // عرض placeholder أثناء التحميل لتجنب مشاكل hydration
  if (!mounted) {
    return <span className={className}>تحميل...</span>;
  }

  return <span className={className}>{formatDate(date)}</span>;
};

export default DateDisplay;
