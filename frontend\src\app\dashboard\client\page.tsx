'use client';

import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/hooks/useAuth';
import supabase from '@/lib/supabase';
import Link from 'next/link';

export default function ClientDashboard() {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    totalSpent: 0
  });
  const [recentProjects, setRecentProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user?.id) {
      loadDashboardData();
    }
  }, [user?.id]);

  const loadDashboardData = async () => {
    try {
      // تحميل إحصائيات المشاريع
      const { data: projects, error } = await supabase
        .from('projects')
        .select('*')
        .eq('client_id', user?.id);

      if (error) throw error;

      const totalProjects = projects?.length || 0;
      const activeProjects = projects?.filter(p => p.status === 'OPEN' || p.status === 'IN_PROGRESS').length || 0;
      const completedProjects = projects?.filter(p => p.status === 'COMPLETED').length || 0;
      const totalSpent = projects?.reduce((sum, p) => sum + (p.budget_max || 0), 0) || 0;

      setStats({
        totalProjects,
        activeProjects,
        completedProjects,
        totalSpent
      });

      // تحميل المشاريع الحديثة
      setRecentProjects(projects?.slice(0, 5) || []);

    } catch (error) {
      console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN': return 'bg-green-100 text-green-800';
      case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800';
      case 'COMPLETED': return 'bg-purple-100 text-purple-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'OPEN': return 'مفتوح';
      case 'IN_PROGRESS': return 'قيد التنفيذ';
      case 'COMPLETED': return 'مكتمل';
      case 'CANCELLED': return 'ملغي';
      default: return status;
    }
  };

  if (loading) {
    return (
      <DashboardLayout title="لوحة تحكم العميل">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout 
      title="لوحة تحكم العميل"
      subtitle={`مرحباً ${user?.name}، إليك نظرة عامة على مشاريعك`}
    >
      <div className="space-y-6">
        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="border-0 bg-gradient-to-br from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">إجمالي المشاريع</p>
                  <p className="text-3xl font-bold">{stats.totalProjects}</p>
                </div>
                <div className="text-4xl opacity-80">📊</div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">المشاريع النشطة</p>
                  <p className="text-3xl font-bold">{stats.activeProjects}</p>
                </div>
                <div className="text-4xl opacity-80">🚀</div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-purple-500 to-purple-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm">المشاريع المكتملة</p>
                  <p className="text-3xl font-bold">{stats.completedProjects}</p>
                </div>
                <div className="text-4xl opacity-80">✅</div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm">إجمالي الإنفاق</p>
                  <p className="text-2xl font-bold">{stats.totalSpent.toLocaleString()} ل.س</p>
                </div>
                <div className="text-4xl opacity-80">💰</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* الإجراءات السريعة */}
        <Card>
          <CardHeader>
            <CardTitle>الإجراءات السريعة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Link href="/projects/create">
                <Button className="w-full h-20 bg-gradient-to-r from-teal to-navy text-lg">
                  <div className="text-center">
                    <div className="text-2xl mb-1">➕</div>
                    <div>إنشاء مشروع جديد</div>
                  </div>
                </Button>
              </Link>
              
              <Link href="/projects">
                <Button variant="outline" className="w-full h-20 text-lg">
                  <div className="text-center">
                    <div className="text-2xl mb-1">🔍</div>
                    <div>تصفح المشاريع</div>
                  </div>
                </Button>
              </Link>
              
              <Link href="/craftsmen">
                <Button variant="outline" className="w-full h-20 text-lg">
                  <div className="text-center">
                    <div className="text-2xl mb-1">👨‍🔧</div>
                    <div>البحث عن حرفيين</div>
                  </div>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* المشاريع الحديثة */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>المشاريع الحديثة</CardTitle>
              <Link href="/dashboard/client/projects">
                <Button variant="outline" size="sm">عرض الكل</Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            {recentProjects.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-6xl mb-4">📭</div>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">لا توجد مشاريع بعد</h3>
                <p className="text-gray-500 mb-4">ابدأ بإنشاء مشروعك الأول</p>
                <Link href="/projects/create">
                  <Button>إنشاء مشروع جديد</Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {recentProjects.map((project) => (
                  <div key={project.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div className="flex-1">
                      <h3 className="font-semibold text-navy">{project.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{project.description}</p>
                      <div className="flex items-center space-x-4 space-x-reverse mt-2">
                        <span className="text-sm text-gray-500">📍 {project.location}</span>
                        <span className="text-sm text-gray-500">📅 {new Date(project.created_at).toLocaleDateString('ar-SA')}</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                        {getStatusText(project.status)}
                      </span>
                      <Link href={`/projects/${project.id}`}>
                        <Button size="sm" variant="outline">عرض</Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
