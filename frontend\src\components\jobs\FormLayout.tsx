'use client';

import React, { ReactNode } from 'react';
import MainLayout from '../layout/MainLayout';
import Link from 'next/link';

interface StepIndicatorProps {
  currentStep: number;
  totalSteps: number;
}

const StepIndicator = ({ currentStep, totalSteps }: StepIndicatorProps) => {
  return (
    <div className="mb-8">
      <div className="flex justify-between items-center">
        {Array.from({ length: totalSteps }).map((_, index) => (
          <React.Fragment key={index}>
            <div className="flex flex-col items-center">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  index + 1 <= currentStep ? 'bg-navy text-white' : 'bg-gray-200 text-gray-500'
                }`}
              >
                {index + 1}
              </div>
              <span
                className={`mt-2 text-sm ${
                  index + 1 <= currentStep ? 'text-navy font-medium' : 'text-gray-500'
                }`}
              >
                {index + 1 === 1 && 'التفاصيل الأساسية'}
                {index + 1 === 2 && 'المتطلبات والمواصفات'}
                {index + 1 === 3 && 'الميزانية والمدة'}
                {index + 1 === 4 && 'المراجعة والنشر'}
              </span>
            </div>
            {index < totalSteps - 1 && (
              <div
                className={`h-1 flex-1 mx-2 ${
                  index + 1 < currentStep ? 'bg-navy' : 'bg-gray-200'
                }`}
              ></div>
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );
};

interface FormLayoutProps {
  children: ReactNode;
  currentStep: number;
  totalSteps: number;
  onNext?: () => void;
  onPrevious?: () => void;
  isLastStep?: boolean;
  isSubmitting?: boolean;
}

const FormLayout = ({
  children,
  currentStep,
  totalSteps,
  onNext,
  onPrevious,
  isLastStep = false,
  isSubmitting = false,
}: FormLayoutProps) => {
  return (
    <MainLayout>
      <div className="bg-beige py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <h1 className="text-3xl font-bold text-navy mb-6 text-center">إنشاء مشروع جديد</h1>
            <StepIndicator currentStep={currentStep} totalSteps={totalSteps} />
            <div className="bg-white rounded-lg shadow-md p-8">
              {children}
              <div className="flex justify-between mt-8">
                {currentStep > 1 ? (
                  <button
                    type="button"
                    onClick={onPrevious}
                    className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    السابق
                  </button>
                ) : (
                  <Link href="/">
                    <button
                      type="button"
                      className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                    >
                      إلغاء
                    </button>
                  </Link>
                )}
                <button
                  type="button"
                  onClick={onNext}
                  disabled={isSubmitting}
                  className={`px-6 py-2 bg-navy text-white rounded-md hover:bg-navy/90 ${
                    isSubmitting ? 'opacity-70 cursor-not-allowed' : ''
                  }`}
                >
                  {isSubmitting ? (
                    <span className="flex items-center">
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      جاري المعالجة...
                    </span>
                  ) : isLastStep ? (
                    'نشر المشروع'
                  ) : (
                    'التالي'
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default FormLayout;
