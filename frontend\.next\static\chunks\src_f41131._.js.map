{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useState } from 'react';\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  role: 'client' | 'craftsman';\n  phone?: string;\n}\n\nexport const useAuth = () => {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const login = async (credentials: LoginCredentials) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await signIn('credentials', {\n        email: credentials.email,\n        password: credentials.password,\n        redirect: false,\n      });\n\n      if (result?.error) {\n        setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');\n        return false;\n      }\n\n      if (result?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        setError(errorData.message || 'حدث خطأ أثناء إنشاء الحساب');\n        return false;\n      }\n\n      // تسجيل الدخول تلقائياً بعد التسجيل\n      const loginResult = await signIn('credentials', {\n        email: data.email,\n        password: data.password,\n        redirect: false,\n      });\n\n      if (loginResult?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء إنشاء الحساب');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    setIsLoading(true);\n    try {\n      await signOut({ redirect: false });\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithGoogle = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('google', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Google');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithFacebook = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('facebook', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Facebook');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const isAuthenticated = status === 'authenticated';\n  const isLoading_session = status === 'loading';\n  const user = session?.user;\n\n  // التحقق من الأدوار مع fallback\n  const isClient = user?.role === 'client';\n  const isCraftsman = user?.role === 'craftsman';\n  const isAdmin = user?.role === 'admin';\n\n  // التحقق من الصلاحيات\n  const hasRole = (role: string | string[]) => {\n    if (!user) return false;\n    if (Array.isArray(role)) {\n      return role.includes(user.role);\n    }\n    return user.role === role;\n  };\n\n  const canAccess = (requiredRoles: string | string[]) => {\n    return isAuthenticated && hasRole(requiredRoles);\n  };\n\n  return {\n    // Session data\n    user,\n    session,\n    isAuthenticated,\n    isLoading: isLoading || isLoading_session,\n    error,\n\n    // Role checks\n    isClient,\n    isCraftsman,\n    isAdmin,\n    hasRole,\n    canAccess,\n\n    // Auth methods\n    login,\n    register,\n    logout,\n    loginWithGoogle,\n    loginWithFacebook,\n\n    // Utility\n    setError,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;AAJA;;;;AAmBO,MAAM,UAAU;;IACrB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,QAAQ,OAAO;QACnB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;gBAC9B,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;gBACT,OAAO;YACT;YAEA,IAAI,QAAQ,IAAI;gBACd,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,OAAO,IAAI;gBAC9B,OAAO;YACT;YAEA,oCAAoC;YACpC,MAAM,cAAc,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBAC9C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;YACZ;YAEA,IAAI,aAAa,IAAI;gBACnB,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;gBAAE,UAAU;YAAM;YAChC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBAAE,aAAa;YAAa;QACrD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,YAAY;gBAAE,aAAa;YAAa;QACvD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,WAAW;IACnC,MAAM,oBAAoB,WAAW;IACrC,MAAM,OAAO,SAAS;IAEtB,gCAAgC;IAChC,MAAM,WAAW,MAAM,SAAS;IAChC,MAAM,cAAc,MAAM,SAAS;IACnC,MAAM,UAAU,MAAM,SAAS;IAE/B,sBAAsB;IACtB,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI;QAChC;QACA,OAAO,KAAK,IAAI,KAAK;IACvB;IAEA,MAAM,YAAY,CAAC;QACjB,OAAO,mBAAmB,QAAQ;IACpC;IAEA,OAAO;QACL,eAAe;QACf;QACA;QACA;QACA,WAAW,aAAa;QACxB;QAEA,cAAc;QACd;QACA;QACA;QACA;QACA;QAEA,eAAe;QACf;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;IACF;AACF;GA7Ja;;QACuB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS"}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/auth/AuthButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/hooks/useAuth';\nimport { Button } from '@/components/ui/Button';\nimport Link from 'next/link';\nimport { useState } from 'react';\n\nexport default function AuthButton() {\n  const { isAuthenticated, user, logout, isLoading } = useAuth();\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse\">\n        <div className=\"h-10 w-24 bg-gray-200 rounded\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"flex items-center space-x-3 space-x-reverse\">\n        <Link href=\"/login\">\n          <Button variant=\"outline\" size=\"sm\" className=\"border-navy text-navy hover:bg-navy hover:text-white\">\n            تسجيل الدخول\n          </Button>\n        </Link>\n        <Link href=\"/register\">\n          <Button size=\"sm\" className=\"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90\">\n            إنشاء حساب\n          </Button>\n        </Link>\n      </div>\n    );\n  }\n\n  const getRoleDisplayName = (role: string) => {\n    switch (role) {\n      case 'client':\n        return 'عميل';\n      case 'craftsman':\n        return 'حرفي';\n      case 'admin':\n        return 'مدير';\n      default:\n        return 'مستخدم';\n    }\n  };\n\n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'client':\n        return '👤';\n      case 'craftsman':\n        return '👨‍🔧';\n      case 'admin':\n        return '👑';\n      default:\n        return '👤';\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className=\"flex items-center space-x-2 space-x-reverse bg-white border border-gray-200 rounded-lg px-4 py-2 hover:bg-gray-50 transition-colors duration-200\"\n      >\n        <div className=\"text-lg\">{getRoleIcon(user?.role || '')}</div>\n        <div className=\"text-right\">\n          <div className=\"text-sm font-medium text-navy\">{user?.name || 'مستخدم'}</div>\n          <div className=\"text-xs text-gray-500\">{getRoleDisplayName(user?.role || '')}</div>\n        </div>\n        <svg\n          className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${\n            isDropdownOpen ? 'rotate-180' : ''\n          }`}\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n        </svg>\n      </button>\n\n      {isDropdownOpen && (\n        <>\n          {/* Backdrop */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsDropdownOpen(false)}\n          />\n\n          {/* Dropdown Menu */}\n          <div className=\"absolute left-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-20\">\n            <div className=\"p-4 border-b border-gray-100\">\n              <div className=\"flex items-center space-x-3 space-x-reverse\">\n                <div className=\"text-2xl\">{getRoleIcon(user?.role || '')}</div>\n                <div>\n                  <div className=\"font-medium text-navy\">{user?.name || 'مستخدم'}</div>\n                  <div className=\"text-sm text-gray-500\">{user?.email || 'بريد إلكتروني'}</div>\n                  <div className=\"text-xs text-teal font-medium\">\n                    {getRoleDisplayName(user?.role || '')}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"py-2\">\n              <Link\n                href=\"/dashboard\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>📊</span>\n                  <span>لوحة التحكم</span>\n                </div>\n              </Link>\n\n              <Link\n                href=\"/profile\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>⚙️</span>\n                  <span>الملف الشخصي</span>\n                </div>\n              </Link>\n\n              {user?.role === 'client' && (\n                <Link\n                  href=\"/projects/create\"\n                  className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                  onClick={() => setIsDropdownOpen(false)}\n                >\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span>➕</span>\n                    <span>إنشاء مشروع</span>\n                  </div>\n                </Link>\n              )}\n\n              {user?.role === 'craftsman' && (\n                <Link\n                  href=\"/offers\"\n                  className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                  onClick={() => setIsDropdownOpen(false)}\n                >\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span>💼</span>\n                    <span>عروضي</span>\n                  </div>\n                </Link>\n              )}\n\n              <Link\n                href=\"/messages\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>💬</span>\n                  <span>الرسائل</span>\n                </div>\n              </Link>\n            </div>\n\n            <div className=\"border-t border-gray-100 py-2\">\n              <button\n                onClick={() => {\n                  logout();\n                  setIsDropdownOpen(false);\n                }}\n                className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-red-50 transition-colors duration-200\"\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>🚪</span>\n                  <span>تسجيل الخروج</span>\n                </div>\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,WAAU;kCAAuD;;;;;;;;;;;8BAIvG,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,WAAU;kCAAyE;;;;;;;;;;;;;;;;;IAM7G;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCAAW,YAAY,MAAM,QAAQ;;;;;;kCACpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAiC,MAAM,QAAQ;;;;;;0CAC9D,6LAAC;gCAAI,WAAU;0CAAyB,mBAAmB,MAAM,QAAQ;;;;;;;;;;;;kCAE3E,6LAAC;wBACC,WAAW,CAAC,wDAAwD,EAClE,iBAAiB,eAAe,IAChC;wBACF,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAIxE,gCACC;;kCAEE,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,kBAAkB;;;;;;kCAInC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAY,YAAY,MAAM,QAAQ;;;;;;sDACrD,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAyB,MAAM,QAAQ;;;;;;8DACtD,6LAAC;oDAAI,WAAU;8DAAyB,MAAM,SAAS;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DACZ,mBAAmB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAM1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAIV,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;oCAIT,MAAM,SAAS,0BACd,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;oCAKX,MAAM,SAAS,6BACd,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAKZ,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;0CAKZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;wCACP;wCACA,kBAAkB;oCACpB;oCACA,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GArLwB;;QAC+B,0HAAA,CAAA,UAAO;;;KADtC"}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/notifications/NotificationBell.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '@/components/ui/Button';\n\ninterface Notification {\n  id: string;\n  title: string;\n  message: string;\n  type: 'message' | 'offer' | 'project' | 'review' | 'system';\n  read: boolean;\n  timestamp: string;\n  actionUrl?: string;\n}\n\ninterface NotificationBellProps {\n  notifications: Notification[];\n  onMarkAsRead: (notificationId: string) => void;\n  onMarkAllAsRead: () => void;\n  onNotificationClick: (notification: Notification) => void;\n}\n\nconst NotificationBell: React.FC<NotificationBellProps> = ({\n  notifications,\n  onMarkAsRead,\n  onMarkAllAsRead,\n  onNotificationClick\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'message': return '💬';\n      case 'offer': return '💰';\n      case 'project': return '🏗️';\n      case 'review': return '⭐';\n      case 'system': return '🔔';\n      default: return '📢';\n    }\n  };\n\n  const getNotificationColor = (type: string) => {\n    switch (type) {\n      case 'message': return 'text-blue-600';\n      case 'offer': return 'text-green-600';\n      case 'project': return 'text-purple-600';\n      case 'review': return 'text-yellow-600';\n      case 'system': return 'text-gray-600';\n      default: return 'text-gray-600';\n    }\n  };\n\n  const formatTime = (timestamp: string) => {\n    const now = new Date();\n    const notificationTime = new Date(timestamp);\n    const diffInMinutes = (now.getTime() - notificationTime.getTime()) / (1000 * 60);\n\n    if (diffInMinutes < 1) {\n      return 'الآن';\n    } else if (diffInMinutes < 60) {\n      return `منذ ${Math.floor(diffInMinutes)} دقيقة`;\n    } else if (diffInMinutes < 1440) {\n      return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;\n    } else {\n      return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`;\n    }\n  };\n\n  const handleNotificationClick = (notification: Notification) => {\n    if (!notification.read) {\n      onMarkAsRead(notification.id);\n    }\n    onNotificationClick(notification);\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* Bell Button */}\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2\"\n      >\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\" />\n        </svg>\n        \n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </Button>\n\n      {/* Dropdown */}\n      {isOpen && (\n        <div className=\"absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\">\n          {/* Header */}\n          <div className=\"p-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"font-semibold text-gray-900\">الإشعارات</h3>\n              {unreadCount > 0 && (\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={onMarkAllAsRead}\n                  className=\"text-xs\"\n                >\n                  تحديد الكل كمقروء\n                </Button>\n              )}\n            </div>\n          </div>\n\n          {/* Notifications List */}\n          <div className=\"max-h-96 overflow-y-auto\">\n            {notifications.length > 0 ? (\n              notifications.slice(0, 10).map((notification) => (\n                <div\n                  key={notification.id}\n                  onClick={() => handleNotificationClick(notification)}\n                  className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${\n                    !notification.read ? 'bg-blue-50' : ''\n                  }`}\n                >\n                  <div className=\"flex items-start space-x-3 space-x-reverse\">\n                    <div className={`text-lg ${getNotificationColor(notification.type)}`}>\n                      {getNotificationIcon(notification.type)}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center justify-between mb-1\">\n                        <h4 className={`text-sm font-medium ${\n                          !notification.read ? 'text-gray-900' : 'text-gray-700'\n                        }`}>\n                          {notification.title}\n                        </h4>\n                        {!notification.read && (\n                          <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                        )}\n                      </div>\n                      <p className=\"text-sm text-gray-600 line-clamp-2\">\n                        {notification.message}\n                      </p>\n                      <p className=\"text-xs text-gray-500 mt-1\">\n                        {formatTime(notification.timestamp)}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"p-8 text-center\">\n                <div className=\"text-4xl mb-2\">🔔</div>\n                <p className=\"text-gray-500\">لا توجد إشعارات</p>\n              </div>\n            )}\n          </div>\n\n          {/* Footer */}\n          {notifications.length > 10 && (\n            <div className=\"p-3 border-t border-gray-200 text-center\">\n              <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                عرض جميع الإشعارات\n              </Button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default NotificationBell;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAsBA,MAAM,mBAAoD,CAAC,EACzD,aAAa,EACb,YAAY,EACZ,eAAe,EACf,mBAAmB,EACpB;;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;iEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,UAAU;oBACZ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;8CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;qCAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,mBAAmB,IAAI,KAAK;QAClC,MAAM,gBAAgB,CAAC,IAAI,OAAO,KAAK,iBAAiB,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE/E,IAAI,gBAAgB,GAAG;YACrB,OAAO;QACT,OAAO,IAAI,gBAAgB,IAAI;YAC7B,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,eAAe,MAAM,CAAC;QACjD,OAAO,IAAI,gBAAgB,MAAM;YAC/B,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;QACrD,OAAO;YACL,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,gBAAgB,MAAM,IAAI,CAAC;QACtD;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,aAAa,IAAI,EAAE;YACtB,aAAa,aAAa,EAAE;QAC9B;QACA,oBAAoB;QACpB,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGtE,cAAc,mBACb,6LAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;YAMjC,wBACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8B;;;;;;gCAC3C,cAAc,mBACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,GAAG,IACtB,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,6BAC9B,6LAAC;gCAEC,SAAS,IAAM,wBAAwB;gCACvC,WAAW,CAAC,+EAA+E,EACzF,CAAC,aAAa,IAAI,GAAG,eAAe,IACpC;0CAEF,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,qBAAqB,aAAa,IAAI,GAAG;sDACjE,oBAAoB,aAAa,IAAI;;;;;;sDAExC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAW,CAAC,oBAAoB,EAClC,CAAC,aAAa,IAAI,GAAG,kBAAkB,iBACvC;sEACC,aAAa,KAAK;;;;;;wDAEpB,CAAC,aAAa,IAAI,kBACjB,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAGnB,6LAAC;oDAAE,WAAU;8DACV,aAAa,OAAO;;;;;;8DAEvB,6LAAC;oDAAE,WAAU;8DACV,WAAW,aAAa,SAAS;;;;;;;;;;;;;;;;;;+BAzBnC,aAAa,EAAE;;;;sDAgCxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;oBAMlC,cAAc,MAAM,GAAG,oBACtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAK,WAAU;sCAAS;;;;;;;;;;;;;;;;;;;;;;;AASrE;GApKM;KAAA;uCAsKS"}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useSession } from 'next-auth/react';\nimport { Button } from '../ui/Button';\nimport AuthButton from '../auth/AuthButton';\nimport NotificationBell from '../notifications/NotificationBell';\n\nconst Header = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const { data: session } = useSession();\n\n  // بيانات تجريبية للإشعارات\n  const mockNotifications = [\n    {\n      id: '1',\n      title: 'عرض جديد',\n      message: 'تم تقديم عرض جديد على مشروع تجديد المطبخ',\n      type: 'offer' as const,\n      read: false,\n      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n      actionUrl: '/client/offers'\n    },\n    {\n      id: '2',\n      title: 'رسالة جديدة',\n      message: 'رسالة جديدة من محمد النجار',\n      type: 'message' as const,\n      read: false,\n      timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),\n      actionUrl: '/messages'\n    },\n    {\n      id: '3',\n      title: 'تقييم جديد',\n      message: 'تم تقييم عملك بـ 5 نجوم',\n      type: 'review' as const,\n      read: true,\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      actionUrl: '/craftsman/reviews'\n    }\n  ];\n\n  const handleNotificationClick = (notification: any) => {\n    if (notification.actionUrl) {\n      window.location.href = notification.actionUrl;\n    }\n  };\n\n  const handleMarkAsRead = (notificationId: string) => {\n    // TODO: تنفيذ تحديث حالة الإشعار في قاعدة البيانات\n    console.log('Mark as read:', notificationId);\n  };\n\n  const handleMarkAllAsRead = () => {\n    // TODO: تنفيذ تحديد جميع الإشعارات كمقروءة\n    console.log('Mark all as read');\n  };\n\n  return (\n    <header className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex justify-between items-center\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"group flex items-center space-x-3 space-x-reverse\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-navy to-teal\">\n                دوزان\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:block\">\n            <ul className=\"flex space-x-8 space-x-reverse\">\n              <li>\n                <Link href=\"/\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الرئيسية\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  المشاريع\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الحرفيون\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  من نحن\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الأسئلة الشائعة\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  اتصل بنا\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n            </ul>\n          </nav>\n\n          {/* Desktop Auth */}\n          <div className=\"hidden lg:flex items-center space-x-4 space-x-reverse\">\n            {session && (\n              <>\n                <Link href=\"/messages\">\n                  <Button variant=\"outline\" size=\"sm\" className=\"relative\">\n                    💬\n                    <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center\">\n                      2\n                    </span>\n                  </Button>\n                </Link>\n                <NotificationBell\n                  notifications={mockNotifications}\n                  onMarkAsRead={handleMarkAsRead}\n                  onMarkAllAsRead={handleMarkAllAsRead}\n                  onNotificationClick={handleNotificationClick}\n                />\n              </>\n            )}\n            <AuthButton />\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300\"\n            aria-label=\"فتح القائمة\"\n          >\n            <svg className=\"w-6 h-6 text-navy\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              {isMobileMenuOpen ? (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              ) : (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden mt-4 py-4 border-t border-gray-200\">\n            <nav className=\"space-y-4\">\n              <Link href=\"/\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الرئيسية\n              </Link>\n              <Link href=\"/jobs\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                المشاريع\n              </Link>\n              <Link href=\"/craftsmen\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الحرفيون\n              </Link>\n              <Link href=\"/about\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                من نحن\n              </Link>\n              <Link href=\"/faq\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الأسئلة الشائعة\n              </Link>\n              <Link href=\"/contact\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                اتصل بنا\n              </Link>\n            </nav>\n            <div className=\"mt-6\">\n              <AuthButton />\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,SAAS;;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAEnC,2BAA2B;IAC3B,MAAM,oBAAoB;QACxB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC5D,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC5D,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YAChE,WAAW;QACb;KACD;IAED,MAAM,0BAA0B,CAAC;QAC/B,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,QAAQ,CAAC,IAAI,GAAG,aAAa,SAAS;QAC/C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,mDAAmD;QACnD,QAAQ,GAAG,CAAC,iBAAiB;IAC/B;IAEA,MAAM,sBAAsB;QAC1B,2CAA2C;QAC3C,QAAQ,GAAG,CAAC;IACd;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDAA2L;;;;;;kDAG1M,6LAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;;;;;;sCAO1G,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAA0F;8DAEjH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;gDAA0F;8DAErH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;;gDAA0F;8DAE1H,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;;gDAA0F;8DAEtH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAO,WAAU;;gDAA0F;8DAEpH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;gDAA0F;8DAExH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,6LAAC;4BAAI,WAAU;;gCACZ,yBACC;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;;oDAAW;kEAEvD,6LAAC;wDAAK,WAAU;kEAA+G;;;;;;;;;;;;;;;;;sDAKnI,6LAAC,0JAAA,CAAA,UAAgB;4CACf,eAAe;4CACf,cAAc;4CACd,iBAAiB;4CACjB,qBAAqB;;;;;;;;8CAI3B,6LAAC,2IAAA,CAAA,UAAU;;;;;;;;;;;sCAIb,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC;gCAAI,WAAU;gCAAoB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC1E,iCACC,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;yDAErE,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAO5E,kCACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAiF;;;;;;8CAG1G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAiF;;;;;;8CAG9G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAiF;;;;;;8CAGnH,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiF;;;;;;8CAG/G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAO,WAAU;8CAAiF;;;;;;8CAG7G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiF;;;;;;;;;;;;sCAInH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2IAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB;GAnLM;;QAEsB,iJAAA,CAAA,aAAU;;;KAFhC;uCAqLS"}}, {"offset": {"line": 1445, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1451, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden\">\n      {/* خلفية هندسية متناسقة */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl\"></div>\n      </div>\n\n      {/* شبكة نقطية ناعمة */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',\n          backgroundSize: '30px 30px'\n        }}></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-16 relative z-10\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Logo and Description */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n              <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center text-white text-2xl font-bold shadow-lg\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-white\">\n                دوزان\n              </span>\n            </div>\n            <p className=\"text-white/90 leading-relaxed mb-6\">\n              منصة تربط بين أصحاب المشاريع والحرفيين المهرة لتنفيذ المشاريع بكفاءة وجودة عالية في جميع أنحاء سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              روابط سريعة\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الرئيسية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  المشاريع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الحرفيون\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  من نحن\n                </Link>\n              </li>\n            </ul>\n          </div>\n          {/* Support */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              الدعم\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/contact\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  اتصل بنا\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  شروط الاستخدام\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              تواصل معنا\n            </h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">البريد الإلكتروني</p>\n                  <a href=\"mailto:<EMAIL>\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">الهاتف</p>\n                  <a href=\"tel:+963112345678\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    +963 11 234 5678\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">العنوان</p>\n                  <p className=\"text-white font-medium\">دمشق، سوريا</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"mt-12 pt-8 border-t border-white/20\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-white/80 text-sm\">\n                &copy; {new Date().getFullYear()} دوزان. جميع الحقوق محفوظة.\n              </p>\n              <p className=\"text-white/60 text-xs mt-1\">\n                صُنع بـ ❤️ في سوريا\n              </p>\n            </div>\n\n            <div className=\"flex items-center space-x-6 space-x-reverse\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">2500+</div>\n                <div className=\"text-white/70 text-xs\">حرفي</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">15000+</div>\n                <div className=\"text-white/70 text-xs\">مشروع</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">98%</div>\n                <div className=\"text-white/70 text-xs\">رضا</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA6H;;;;;;0DAG5I,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAIlD,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAyQ,UAAS;;;;;;;;;;;;;;;;0DAGjT,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA8jD,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO1mD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;;sEACvB,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;;sEAC3B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;;sEAChC,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAO3H,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAO,WAAU;;sEAC1B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3H,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,MAAK;gEAAuB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAM3H,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,MAAK;gEAAoB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAMxH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EACjE,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;8EACrE,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;gDAAwB;gDAC3B,IAAI,OAAO,WAAW;gDAAG;;;;;;;sDAEnC,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;KA5MM;uCA8MS"}}, {"offset": {"line": 2308, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2314, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout = ({ children }: MainLayoutProps) => {\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      <Header />\n      <main className=\"flex-grow\">{children}</main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB;IAC/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAK,WAAU;0BAAa;;;;;;0BAC7B,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;KARM;uCAUS"}}, {"offset": {"line": 2360, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';\nimport Link from 'next/link';\nimport { Button } from '../ui/Button';\n\n// Types للتحسين\ninterface MousePosition {\n  x: number;\n  y: number;\n}\n\ninterface DotConfig {\n  top: number;\n  left: number;\n  size: number;\n}\n\ninterface TypewriterProps {\n  texts: string[];\n  speed?: number;\n  pauseDuration?: number;\n}\n\n// Hook مخصص للـ typewriter effect\nconst useTypewriter = ({ texts, speed = 100, pauseDuration = 2000 }: TypewriterProps) => {\n  const [displayText, setDisplayText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isTyping, setIsTyping] = useState(true);\n\n  useEffect(() => {\n    if (texts.length === 0) return;\n\n    const currentText = texts[currentIndex];\n    let timeoutId: NodeJS.Timeout;\n\n    if (isTyping) {\n      if (displayText.length < currentText.length) {\n        timeoutId = setTimeout(() => {\n          setDisplayText(currentText.slice(0, displayText.length + 1));\n        }, speed);\n      } else {\n        timeoutId = setTimeout(() => {\n          setIsTyping(false);\n        }, pauseDuration);\n      }\n    } else {\n      if (displayText.length > 0) {\n        timeoutId = setTimeout(() => {\n          setDisplayText(displayText.slice(0, -1));\n        }, speed / 2);\n      } else {\n        setCurrentIndex((prev) => (prev + 1) % texts.length);\n        setIsTyping(true);\n      }\n    }\n\n    return () => clearTimeout(timeoutId);\n  }, [displayText, currentIndex, isTyping, texts, speed, pauseDuration]);\n\n  return displayText;\n};\n\nconst Hero = () => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 });\n  const heroRef = useRef<HTMLElement>(null);\n\n  const heroTexts = useMemo(() => [\n    'منصة تربط بين أصحاب المشاريع والحرفيين المهرة في سوريا',\n    'حلول احترافية متكاملة لجميع احتياجاتك في البناء والتشطيب والصيانة',\n    'جودة عالية وأسعار منافسة مع ضمان الرضا التام وخدمة عملاء متميزة'\n  ], []);\n\n  const typewriterText = useTypewriter({\n    texts: heroTexts,\n    speed: 80,\n    pauseDuration: 3000\n  });\n\n  useEffect(() => {\n    setIsVisible(true);\n  }, []);\n\n  const handleMouseMove = useCallback((e: MouseEvent) => {\n    if (heroRef.current) {\n      const rect = heroRef.current.getBoundingClientRect();\n      setMousePosition({\n        x: (e.clientX - rect.left) / rect.width,\n        y: (e.clientY - rect.top) / rect.height\n      });\n    }\n  }, []);\n\n  useEffect(() => {\n    const heroElement = heroRef.current;\n    if (heroElement) {\n      heroElement.addEventListener('mousemove', handleMouseMove, { passive: true });\n      return () => heroElement.removeEventListener('mousemove', handleMouseMove);\n    }\n  }, [handleMouseMove]);\n\n  // تكوين النقاط التفاعلية - مُحسَّن للأداء\n  const interactiveDots = useMemo<DotConfig[]>(() => [\n    { top: 15, left: 10, size: 3 },\n    { top: 25, left: 85, size: 4 },\n    { top: 35, left: 20, size: 3 },\n    { top: 45, left: 75, size: 5 },\n    { top: 55, left: 15, size: 4 },\n    { top: 65, left: 90, size: 3 },\n    { top: 75, left: 25, size: 4 },\n    { top: 85, left: 80, size: 3 },\n    { top: 20, left: 50, size: 4 },\n    { top: 40, left: 60, size: 3 },\n    { top: 60, left: 40, size: 5 },\n    { top: 80, left: 55, size: 4 }\n  ], []);\n\n  // مكون النقاط التفاعلية المُحسَّن\n  const InteractiveDots = React.memo(() => (\n    <div className=\"absolute inset-0 overflow-hidden pointer-events-none\" aria-hidden=\"true\">\n      {interactiveDots.map((dot, i) => {\n        const dotX = dot.left / 100;\n        const dotY = dot.top / 100;\n\n        const distance = Math.sqrt(\n          Math.pow(mousePosition.x - dotX, 2) +\n          Math.pow(mousePosition.y - dotY, 2)\n        );\n\n        const maxDistance = 0.15;\n        const proximity = Math.max(0, 1 - distance / maxDistance);\n\n        const pushDistance = proximity * 20;\n        const angle = Math.atan2(dotY - mousePosition.y, dotX - mousePosition.x);\n        const pushX = Math.cos(angle) * pushDistance;\n        const pushY = Math.sin(angle) * pushDistance;\n\n        return (\n          <div\n            key={i}\n            className=\"absolute bg-white rounded-full transition-all duration-300 ease-out\"\n            style={{\n              top: `${dot.top + pushY}%`,\n              left: `${dot.left + pushX}%`,\n              width: `${dot.size + proximity * 4}px`,\n              height: `${dot.size + proximity * 4}px`,\n              opacity: 0.3 + proximity * 0.7,\n              boxShadow: proximity > 0.3 ? `0 0 ${proximity * 20}px rgba(255, 255, 255, ${proximity * 0.8})` : 'none',\n              transform: `translate(-50%, -50%) scale(${1 + proximity * 0.5})`,\n              filter: proximity > 0.2 ? `blur(${proximity * 0.5}px)` : 'none'\n            }}\n          />\n        );\n      })}\n    </div>\n  ));\n\n  InteractiveDots.displayName = 'InteractiveDots';\n\n  return (\n    <section\n      ref={heroRef}\n      className=\"relative min-h-screen flex items-center justify-center overflow-hidden\"\n      role=\"banner\"\n      aria-label=\"الصفحة الرئيسية لمنصة دوزان\"\n    >\n      {/* خلفية احترافية متناسقة مع قسم الإحصائيات */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-navy via-teal to-skyblue\">\n        <div className=\"absolute inset-0 bg-black/10\"></div>\n\n        {/* أنماط هندسية ناعمة */}\n        <div className=\"absolute top-0 left-0 w-full h-full opacity-10\" aria-hidden=\"true\">\n          <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl\"></div>\n          <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl\"></div>\n          <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl\"></div>\n          <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl\"></div>\n        </div>\n\n        {/* شبكة نقطية ثابتة */}\n        <div className=\"absolute inset-0 opacity-20\" aria-hidden=\"true\">\n          <div className=\"w-full h-full\" style={{\n            backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',\n            backgroundSize: '30px 30px'\n          }}></div>\n        </div>\n      </div>\n\n      {/* النقاط التفاعلية المُحسَّنة */}\n      <InteractiveDots />\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          {/* شعار احترافي */}\n\n          {/* العنوان الرئيسي المحسن */}\n          <div className={`mb-12 mt-12 transform transition-all duration-1000 delay-300 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>\n            <h1 className=\"text-5xl md:text-7xl lg:text-8xl font-bold text-white leading-tight\">\n              <span className=\"block mb-4\">مرحباً بك في</span>\n              <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 via-orange-300 to-yellow-400 drop-shadow-2xl\">\n                دوزان\n              </span>\n            </h1>\n            <div className=\"mt-8 w-32 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto rounded-full\"></div>\n          </div>\n\n          {/* النص المتغير مع typewriter effect */}\n          <div className={`mb-12 transform transition-all duration-1000 delay-500 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>\n            <div className=\"min-h-[120px] flex items-center justify-center\">\n              <p\n                className=\"text-lg sm:text-xl md:text-2xl lg:text-3xl text-white/95 max-w-5xl mx-auto leading-relaxed font-medium\"\n                aria-live=\"polite\"\n                aria-label=\"وصف منصة دوزان\"\n              >\n                {typewriterText}\n                <span className=\"animate-pulse text-yellow-300\">|</span>\n              </p>\n            </div>\n          </div>\n\n          {/* الأزرار المحسنة مع accessibility */}\n          <div className={`flex flex-col sm:flex-row gap-6 justify-center items-center mb-20 transform transition-all duration-1000 delay-700 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>\n            <Link href=\"/jobs/create\" className=\"group\">\n              <Button\n                size=\"lg\"\n                className=\"bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 hover:from-yellow-500 hover:via-orange-600 hover:to-red-600 text-white px-8 sm:px-12 py-4 sm:py-6 text-lg sm:text-xl font-bold shadow-2xl hover:shadow-yellow-500/30 transform hover:scale-105 transition-all duration-300 border-2 border-white/20 hover:border-white/40 focus:ring-4 focus:ring-yellow-300/50\"\n                aria-label=\"ابدأ مشروعك الآن - انتقل إلى صفحة إنشاء مشروع جديد\"\n              >\n                <span className=\"flex items-center gap-3\">\n                  <span className=\"text-xl sm:text-2xl group-hover:scale-125 transition-transform duration-300\" aria-hidden=\"true\">🚀</span>\n                  <span className=\"text-base sm:text-xl\">ابدأ مشروعك الآن</span>\n                </span>\n              </Button>\n            </Link>\n            <Link href=\"/jobs\" className=\"group\">\n              <Button\n                size=\"lg\"\n                className=\"bg-white/15 backdrop-blur-md border-2 border-white/30 text-white hover:bg-white/25 hover:border-white/50 px-8 sm:px-12 py-4 sm:py-6 text-lg sm:text-xl font-bold transform hover:scale-105 transition-all duration-300 shadow-xl focus:ring-4 focus:ring-white/30\"\n                aria-label=\"تصفح المشاريع - انتقل إلى صفحة المشاريع المتاحة\"\n              >\n                <span className=\"flex items-center gap-3\">\n                  <span className=\"text-xl sm:text-2xl group-hover:scale-125 transition-transform duration-300\" aria-hidden=\"true\">🔍</span>\n                  <span className=\"text-base sm:text-xl\">تصفح المشاريع</span>\n                </span>\n              </Button>\n            </Link>\n          </div>\n\n          {/* مؤشر التمرير المحسن مع دعم الحركة المقللة */}\n          <div className={`transform transition-all duration-1000 delay-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>\n            <div className=\"flex flex-col items-center\">\n              <p className=\"text-white/80 text-base sm:text-lg mb-6 font-medium\">اكتشف المزيد من المميزات</p>\n              <button\n                className=\"relative group focus:outline-none focus:ring-4 focus:ring-white/30 rounded-full p-2\"\n                onClick={() => {\n                  const nextSection = document.querySelector('#stats-section');\n                  if (nextSection) {\n                    nextSection.scrollIntoView({ behavior: 'smooth' });\n                  }\n                }}\n                aria-label=\"انتقل إلى القسم التالي\"\n              >\n                <div className=\"w-8 h-14 border-2 border-white/40 rounded-full flex justify-center bg-white/10 backdrop-blur-sm group-hover:border-white/60 transition-colors duration-300\">\n                  <div className=\"w-2 h-4 bg-white/70 rounded-full mt-3 motion-safe:animate-bounce group-hover:bg-white transition-colors duration-300\"></div>\n                </div>\n                \n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\n// تحسين الأداء مع React.memo\nexport default React.memo(Hero);\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAwBA,kCAAkC;AAClC,MAAM,gBAAgB,CAAC,EAAE,KAAK,EAAE,QAAQ,GAAG,EAAE,gBAAgB,IAAI,EAAmB;;IAClF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,MAAM,MAAM,KAAK,GAAG;YAExB,MAAM,cAAc,KAAK,CAAC,aAAa;YACvC,IAAI;YAEJ,IAAI,UAAU;gBACZ,IAAI,YAAY,MAAM,GAAG,YAAY,MAAM,EAAE;oBAC3C,YAAY;mDAAW;4BACrB,eAAe,YAAY,KAAK,CAAC,GAAG,YAAY,MAAM,GAAG;wBAC3D;kDAAG;gBACL,OAAO;oBACL,YAAY;mDAAW;4BACrB,YAAY;wBACd;kDAAG;gBACL;YACF,OAAO;gBACL,IAAI,YAAY,MAAM,GAAG,GAAG;oBAC1B,YAAY;mDAAW;4BACrB,eAAe,YAAY,KAAK,CAAC,GAAG,CAAC;wBACvC;kDAAG,QAAQ;gBACb,OAAO;oBACL;mDAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;;oBACnD,YAAY;gBACd;YACF;YAEA;2CAAO,IAAM,aAAa;;QAC5B;kCAAG;QAAC;QAAa;QAAc;QAAU;QAAO;QAAO;KAAc;IAErE,OAAO;AACT;GApCM;AAsCN,MAAM,OAAO;;IACX,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAAE,GAAG;QAAG,GAAG;IAAE;IAC/E,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe;IAEpC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mCAAE,IAAM;gBAC9B;gBACA;gBACA;aACD;kCAAE,EAAE;IAEL,MAAM,iBAAiB,cAAc;QACnC,OAAO;QACP,OAAO;QACP,eAAe;IACjB;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,aAAa;QACf;yBAAG,EAAE;IAEL,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6CAAE,CAAC;YACnC,IAAI,QAAQ,OAAO,EAAE;gBACnB,MAAM,OAAO,QAAQ,OAAO,CAAC,qBAAqB;gBAClD,iBAAiB;oBACf,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,IAAI,IAAI,KAAK,KAAK;oBACvC,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,GAAG,IAAI,KAAK,MAAM;gBACzC;YACF;QACF;4CAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM,cAAc,QAAQ,OAAO;YACnC,IAAI,aAAa;gBACf,YAAY,gBAAgB,CAAC,aAAa,iBAAiB;oBAAE,SAAS;gBAAK;gBAC3E;sCAAO,IAAM,YAAY,mBAAmB,CAAC,aAAa;;YAC5D;QACF;yBAAG;QAAC;KAAgB;IAEpB,0CAA0C;IAC1C,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yCAAe,IAAM;gBACjD;oBAAE,KAAK;oBAAI,MAAM;oBAAI,MAAM;gBAAE;gBAC7B;oBAAE,KAAK;oBAAI,MAAM;oBAAI,MAAM;gBAAE;gBAC7B;oBAAE,KAAK;oBAAI,MAAM;oBAAI,MAAM;gBAAE;gBAC7B;oBAAE,KAAK;oBAAI,MAAM;oBAAI,MAAM;gBAAE;gBAC7B;oBAAE,KAAK;oBAAI,MAAM;oBAAI,MAAM;gBAAE;gBAC7B;oBAAE,KAAK;oBAAI,MAAM;oBAAI,MAAM;gBAAE;gBAC7B;oBAAE,KAAK;oBAAI,MAAM;oBAAI,MAAM;gBAAE;gBAC7B;oBAAE,KAAK;oBAAI,MAAM;oBAAI,MAAM;gBAAE;gBAC7B;oBAAE,KAAK;oBAAI,MAAM;oBAAI,MAAM;gBAAE;gBAC7B;oBAAE,KAAK;oBAAI,MAAM;oBAAI,MAAM;gBAAE;gBAC7B;oBAAE,KAAK;oBAAI,MAAM;oBAAI,MAAM;gBAAE;gBAC7B;oBAAE,KAAK;oBAAI,MAAM;oBAAI,MAAM;gBAAE;aAC9B;wCAAE,EAAE;IAEL,kCAAkC;IAClC,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC,kBACjC,6LAAC;YAAI,WAAU;YAAuD,eAAY;sBAC/E,gBAAgB,GAAG,CAAC,CAAC,KAAK;gBACzB,MAAM,OAAO,IAAI,IAAI,GAAG;gBACxB,MAAM,OAAO,IAAI,GAAG,GAAG;gBAEvB,MAAM,WAAW,KAAK,IAAI,CACxB,KAAK,GAAG,CAAC,cAAc,CAAC,GAAG,MAAM,KACjC,KAAK,GAAG,CAAC,cAAc,CAAC,GAAG,MAAM;gBAGnC,MAAM,cAAc;gBACpB,MAAM,YAAY,KAAK,GAAG,CAAC,GAAG,IAAI,WAAW;gBAE7C,MAAM,eAAe,YAAY;gBACjC,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,cAAc,CAAC,EAAE,OAAO,cAAc,CAAC;gBACvE,MAAM,QAAQ,KAAK,GAAG,CAAC,SAAS;gBAChC,MAAM,QAAQ,KAAK,GAAG,CAAC,SAAS;gBAEhC,qBACE,6LAAC;oBAEC,WAAU;oBACV,OAAO;wBACL,KAAK,GAAG,IAAI,GAAG,GAAG,MAAM,CAAC,CAAC;wBAC1B,MAAM,GAAG,IAAI,IAAI,GAAG,MAAM,CAAC,CAAC;wBAC5B,OAAO,GAAG,IAAI,IAAI,GAAG,YAAY,EAAE,EAAE,CAAC;wBACtC,QAAQ,GAAG,IAAI,IAAI,GAAG,YAAY,EAAE,EAAE,CAAC;wBACvC,SAAS,MAAM,YAAY;wBAC3B,WAAW,YAAY,MAAM,CAAC,IAAI,EAAE,YAAY,GAAG,uBAAuB,EAAE,YAAY,IAAI,CAAC,CAAC,GAAG;wBACjG,WAAW,CAAC,4BAA4B,EAAE,IAAI,YAAY,IAAI,CAAC,CAAC;wBAChE,QAAQ,YAAY,MAAM,CAAC,KAAK,EAAE,YAAY,IAAI,GAAG,CAAC,GAAG;oBAC3D;mBAXK;;;;;YAcX;;;;;;IAIJ,gBAAgB,WAAW,GAAG;IAE9B,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,MAAK;QACL,cAAW;;0BAGX,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;wBAAiD,eAAY;;0CAC1E,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6LAAC;wBAAI,WAAU;wBAA8B,eAAY;kCACvD,cAAA,6LAAC;4BAAI,WAAU;4BAAgB,OAAO;gCACpC,iBAAiB;gCACjB,gBAAgB;4BAClB;;;;;;;;;;;;;;;;;0BAKJ,6LAAC;;;;;0BAED,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAIb,6LAAC;4BAAI,WAAW,CAAC,6DAA6D,EAAE,YAAY,8BAA8B,4BAA4B;;8CACpJ,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAK,WAAU;sDAAa;;;;;;sDAC7B,6LAAC;4CAAK,WAAU;sDAA8G;;;;;;;;;;;;8CAIhI,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAW,CAAC,uDAAuD,EAAE,YAAY,8BAA8B,4BAA4B;sCAC9I,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,aAAU;oCACV,cAAW;;wCAEV;sDACD,6LAAC;4CAAK,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;sCAMtD,6LAAC;4BAAI,WAAW,CAAC,mHAAmH,EAAE,YAAY,8BAA8B,4BAA4B;;8CAC1M,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAe,WAAU;8CAClC,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAK,WAAU;oDAA8E,eAAY;8DAAO;;;;;;8DACjH,6LAAC;oDAAK,WAAU;8DAAuB;;;;;;;;;;;;;;;;;;;;;;8CAI7C,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAC3B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,cAAW;kDAEX,cAAA,6LAAC;4CAAK,WAAU;;8DACd,6LAAC;oDAAK,WAAU;oDAA8E,eAAY;8DAAO;;;;;;8DACjH,6LAAC;oDAAK,WAAU;8DAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAO/C,6LAAC;4BAAI,WAAW,CAAC,kDAAkD,EAAE,YAAY,8BAA8B,4BAA4B;sCACzI,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAsD;;;;;;kDACnE,6LAAC;wCACC,WAAU;wCACV,SAAS;4CACP,MAAM,cAAc,SAAS,aAAa,CAAC;4CAC3C,IAAI,aAAa;gDACf,YAAY,cAAc,CAAC;oDAAE,UAAU;gDAAS;4CAClD;wCACF;wCACA,cAAW;kDAEX,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjC;IAlNM;;QAWmB;;;KAXnB;2DAqNS,6JAAA,CAAA,UAAK,CAAC,IAAI,CAAC"}}, {"offset": {"line": 2931, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2937, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\n\nconst categories = [\n  {\n    id: 1,\n    name: 'النجارة',\n    icon: '🪚',\n    description: 'أعمال النجارة والأثاث المنزلي',\n    link: '/category/carpentry',\n    color: 'from-amber-500 to-orange-600',\n    bgColor: 'bg-amber-50',\n    projects: '250+ مشروع',\n    craftsmen: '45 حرفي'\n  },\n  {\n    id: 2,\n    name: 'السباكة',\n    icon: '🔧',\n    description: 'تركيب وإصلاح أنظمة المياه والصرف الصحي',\n    link: '/category/plumbing',\n    color: 'from-blue-500 to-cyan-600',\n    bgColor: 'bg-blue-50',\n    projects: '180+ مشروع',\n    craftsmen: '32 حرفي'\n  },\n  {\n    id: 3,\n    name: 'الكهرباء',\n    icon: '⚡',\n    description: 'تركيب وصيانة الأنظمة الكهربائية',\n    link: '/category/electrical',\n    color: 'from-yellow-500 to-orange-500',\n    bgColor: 'bg-yellow-50',\n    projects: '320+ مشروع',\n    craftsmen: '58 حرفي'\n  },\n  {\n    id: 4,\n    name: 'الدهان',\n    icon: '🖌️',\n    description: 'أعمال الدهان والديكور',\n    link: '/category/painting',\n    color: 'from-purple-500 to-pink-600',\n    bgColor: 'bg-purple-50',\n    projects: '290+ مشروع',\n    craftsmen: '41 حرفي'\n  },\n  {\n    id: 5,\n    name: 'البناء',\n    icon: '🧱',\n    description: 'أعمال البناء والترميم',\n    link: '/category/construction',\n    color: 'from-gray-600 to-gray-800',\n    bgColor: 'bg-gray-50',\n    projects: '150+ مشروع',\n    craftsmen: '28 حرفي'\n  },\n  {\n    id: 6,\n    name: 'التكييف',\n    icon: '❄️',\n    description: 'تركيب وصيانة أنظمة التكييف',\n    link: '/category/hvac',\n    color: 'from-cyan-500 to-blue-600',\n    bgColor: 'bg-cyan-50',\n    projects: '120+ مشروع',\n    craftsmen: '22 حرفي'\n  },\n  {\n    id: 7,\n    name: 'الحدادة',\n    icon: '🔨',\n    description: 'أعمال الحدادة والمعادن',\n    link: '/category/metalwork',\n    color: 'from-slate-600 to-gray-700',\n    bgColor: 'bg-slate-50',\n    projects: '95+ مشروع',\n    craftsmen: '18 حرفي'\n  },\n  {\n    id: 8,\n    name: 'تنسيق الحدائق',\n    icon: '🌱',\n    description: 'تصميم وتنسيق الحدائق',\n    link: '/category/landscaping',\n    color: 'from-green-500 to-emerald-600',\n    bgColor: 'bg-green-50',\n    projects: '85+ مشروع',\n    craftsmen: '15 حرفي'\n  },\n];\n\nconst ServiceCategories = () => {\n  const [hoveredCategory, setHoveredCategory] = useState<number | null>(null);\n\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-white via-skyblue/10 to-white relative overflow-hidden\">\n      {/* خلفية هندسية متناسقة */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl\"></div>\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl\"></div>\n      </div>\n\n      {/* شبكة نقطية ناعمة */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: 'radial-gradient(circle, #567C8D 1px, transparent 1px)',\n          backgroundSize: '50px 50px'\n        }}></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-block bg-gradient-to-r from-navy to-teal text-white px-8 py-3 rounded-full text-sm font-medium mb-6 shadow-lg\">\n            <span className=\"text-lg\">🛠️</span>\n            <span className=\"mr-2\">خدمات متنوعة</span>\n          </div>\n          <h2 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-navy mb-6 leading-tight\">\n            استكشف فئات\n            <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-teal via-navy to-teal\">\n              الخدمات\n            </span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed\">\n            نوفر مجموعة واسعة ومتنوعة من الخدمات المهنية لتلبية جميع احتياجاتك مع أفضل الحرفيين المحترفين\n          </p>\n          <div className=\"mt-8 w-24 h-1 bg-gradient-to-r from-teal to-navy mx-auto rounded-full\"></div>\n        </div>\n\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8\">\n          {categories.map((category, index) => (\n            <Link key={category.id} href={category.link} className=\"block group\">\n              <div\n                className={`relative overflow-hidden rounded-2xl transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 ${category.bgColor} border border-white/50 shadow-lg hover:shadow-2xl`}\n                onMouseEnter={() => setHoveredCategory(category.id)}\n                onMouseLeave={() => setHoveredCategory(null)}\n              >\n                {/* خلفية متدرجة */}\n                <div className={`absolute inset-0 bg-gradient-to-br ${category.color} opacity-0 group-hover:opacity-10 transition-opacity duration-500`}></div>\n\n                {/* محتوى البطاقة */}\n                <div className=\"relative p-8 h-full flex flex-col\">\n                  {/* الأيقونة */}\n                  <div className=\"flex justify-center mb-6\">\n                    <div className={`w-20 h-20 rounded-2xl bg-gradient-to-br ${category.color} flex items-center justify-center text-3xl shadow-lg group-hover:scale-110 transition-transform duration-300`}>\n                      <span className=\"filter drop-shadow-sm\">{category.icon}</span>\n                    </div>\n                  </div>\n\n                  {/* العنوان والوصف */}\n                  <div className=\"text-center flex-grow\">\n                    <h3 className=\"text-2xl font-bold text-navy mb-3 group-hover:text-teal transition-colors duration-300\">\n                      {category.name}\n                    </h3>\n                    <p className=\"text-gray-600 leading-relaxed mb-6 text-sm\">\n                      {category.description}\n                    </p>\n                  </div>\n\n                  {/* الإحصائيات */}\n                  <div className=\"flex justify-between items-center pt-4 border-t border-gray-200/50\">\n                    <div className=\"text-center\">\n                      <div className=\"text-sm font-bold text-navy\">{category.projects}</div>\n                      <div className=\"text-xs text-gray-500\">مشاريع</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-sm font-bold text-navy\">{category.craftsmen}</div>\n                      <div className=\"text-xs text-gray-500\">حرفي</div>\n                    </div>\n                  </div>\n\n                  {/* مؤشر الانتقال */}\n                  <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                    <div className={`w-8 h-1 bg-gradient-to-r ${category.color} rounded-full`}></div>\n                  </div>\n                </div>\n\n                {/* تأثير الحدود المتوهجة */}\n                <div className={`absolute inset-0 rounded-2xl border-2 border-transparent bg-gradient-to-r ${category.color} opacity-0 group-hover:opacity-20 transition-opacity duration-500`}></div>\n              </div>\n            </Link>\n          ))}\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-gradient-to-r from-navy to-teal rounded-2xl p-8 text-white shadow-2xl\">\n            <h3 className=\"text-2xl md:text-3xl font-bold mb-4\">لم تجد الخدمة التي تبحث عنها؟</h3>\n            <p className=\"text-white/90 mb-6 max-w-2xl mx-auto text-lg\">\n              تواصل معنا وسنساعدك في العثور على الحرفي المناسب لأي نوع من المشاريع\n            </p>\n            <Link href=\"/contact\">\n              <button className=\"bg-white text-navy px-8 py-3 rounded-full font-bold hover:bg-gray-100 transition-colors duration-300 shadow-lg hover:shadow-xl transform hover:scale-105\">\n                تواصل معنا\n              </button>\n            </Link>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default ServiceCategories;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,aAAa;IACjB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,OAAO;QACP,SAAS;QACT,UAAU;QACV,WAAW;IACb;CACD;AAED,MAAM,oBAAoB;;IACxB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAK,WAAU;kDAAO;;;;;;;;;;;;0CAEzB,6LAAC;gCAAG,WAAU;;oCAA0E;kDAEtF,6LAAC;wCAAK,WAAU;kDAAkF;;;;;;;;;;;;0CAIpG,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;0CAGvE,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,UAAU,sBACzB,6LAAC,+JAAA,CAAA,UAAI;gCAAmB,MAAM,SAAS,IAAI;gCAAE,WAAU;0CACrD,cAAA,6LAAC;oCACC,WAAW,CAAC,gHAAgH,EAAE,SAAS,OAAO,CAAC,kDAAkD,CAAC;oCAClM,cAAc,IAAM,mBAAmB,SAAS,EAAE;oCAClD,cAAc,IAAM,mBAAmB;;sDAGvC,6LAAC;4CAAI,WAAW,CAAC,mCAAmC,EAAE,SAAS,KAAK,CAAC,iEAAiE,CAAC;;;;;;sDAGvI,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAW,CAAC,wCAAwC,EAAE,SAAS,KAAK,CAAC,4GAA4G,CAAC;kEACrL,cAAA,6LAAC;4DAAK,WAAU;sEAAyB,SAAS,IAAI;;;;;;;;;;;;;;;;8DAK1D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEACX,SAAS,IAAI;;;;;;sEAEhB,6LAAC;4DAAE,WAAU;sEACV,SAAS,WAAW;;;;;;;;;;;;8DAKzB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA+B,SAAS,QAAQ;;;;;;8EAC/D,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAA+B,SAAS,SAAS;;;;;;8EAChE,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAK3C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAW,CAAC,yBAAyB,EAAE,SAAS,KAAK,CAAC,aAAa,CAAC;;;;;;;;;;;;;;;;;sDAK7E,6LAAC;4CAAI,WAAW,CAAC,0EAA0E,EAAE,SAAS,KAAK,CAAC,iEAAiE,CAAC;;;;;;;;;;;;+BA/CvK,SAAS,EAAE;;;;;;;;;;kCAsD1B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAG5D,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC;wCAAO,WAAU;kDAA2J;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3L;GA9GM;KAAA;uCAgHS"}}, {"offset": {"line": 3399, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3405, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\n\nconst testimonials = [\n  {\n    id: 1,\n    name: 'أحم<PERSON> محمد',\n    role: 'صاحب مشروع',\n    location: 'دمشق، المزة',\n    content: 'وجدت حرفيين ممتازين من خلال منصة دوزان. كانت التجربة سلسة من البداية إلى النهاية، وأنا سعيد جدًا بالنتائج.',\n    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',\n    rating: 5,\n    project: 'تجديد شقة كاملة',\n    date: 'منذ شهرين',\n    verified: true\n  },\n  {\n    id: 2,\n    name: 'سارة أحمد',\n    role: 'مالكة منزل',\n    location: 'حلب، الشهباء',\n    content: 'ساعدتني دوزان في العثور على نجار محترف لتجديد مطبخي. كانت الأسعار معقولة والعمل ممتاز.',\n    avatar: 'https://randomuser.me/api/portraits/women/2.jpg',\n    rating: 4,\n    project: 'تجديد المطبخ',\n    date: 'منذ 3 أسابيع',\n    verified: true\n  },\n  {\n    id: 3,\n    name: 'خالد العلي',\n    role: 'نجار محترف',\n    location: 'حمص، الوعر',\n    content: 'كحرفي، ساعدتني دوزان في الوصول إلى المزيد من العملاء وزيادة دخلي. المنصة سهلة الاستخدام وفريق الدعم ممتاز.',\n    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',\n    rating: 5,\n    project: 'أكثر من 50 مشروع',\n    date: 'عضو منذ 8 أشهر',\n    verified: true\n  },\n  {\n    id: 4,\n    name: 'فاطمة حسن',\n    role: 'مهندسة معمارية',\n    location: 'اللاذقية، الزراعة',\n    content: 'منصة رائعة تسهل التواصل مع الحرفيين المحترفين. استخدمتها في عدة مشاريع وكانت النتائج مذهلة.',\n    avatar: 'https://randomuser.me/api/portraits/women/4.jpg',\n    rating: 5,\n    project: 'مشاريع متعددة',\n    date: 'منذ شهر',\n    verified: true\n  },\n  {\n    id: 5,\n    name: 'محمد الشامي',\n    role: 'كهربائي',\n    location: 'طرطوس، الكورنيش',\n    content: 'بفضل دوزان تمكنت من بناء سمعة ممتازة وزيادة عدد عملائي بشكل كبير. أنصح كل حرفي بالانضمام.',\n    avatar: 'https://randomuser.me/api/portraits/men/5.jpg',\n    rating: 5,\n    project: 'أكثر من 30 مشروع',\n    date: 'عضو منذ 6 أشهر',\n    verified: true\n  },\n  {\n    id: 6,\n    name: 'ليلى قاسم',\n    role: 'صاحبة مطعم',\n    location: 'دمشق، باب توما',\n    content: 'احتجت لتجديد مطعمي بالكامل، ومن خلال دوزان وجدت فريق عمل متكامل أنجز المشروع في الوقت المحدد.',\n    avatar: 'https://randomuser.me/api/portraits/women/6.jpg',\n    rating: 4,\n    project: 'تجديد مطعم',\n    date: 'منذ 5 أسابيع',\n    verified: true\n  }\n];\n\nconst StarRating = ({ rating }: { rating: number }) => {\n  return (\n    <div className=\"flex items-center space-x-1 space-x-reverse\">\n      {[...Array(5)].map((_, i) => (\n        <svg\n          key={i}\n          className={`h-5 w-5 transition-colors duration-300 ${\n            i < rating\n              ? 'text-yellow-400 drop-shadow-sm'\n              : 'text-white/30'\n          }`}\n          fill=\"currentColor\"\n          viewBox=\"0 0 20 20\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n        </svg>\n      ))}\n      <span className=\"text-white/70 text-sm mr-2\">({rating}/5)</span>\n    </div>\n  );\n};\n\nconst Testimonials = () => {\n  const [currentSlide, setCurrentSlide] = useState(0);\n  const [isAutoPlaying, setIsAutoPlaying] = useState(true);\n\n  // Auto-slide functionality\n  useEffect(() => {\n    if (!isAutoPlaying) return;\n\n    const interval = setInterval(() => {\n      setCurrentSlide((prev) => (prev + 1) % Math.ceil(testimonials.length / 3));\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [isAutoPlaying]);\n\n  const getVisibleTestimonials = () => {\n    const start = currentSlide * 3;\n    return testimonials.slice(start, start + 3);\n  };\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden\">\n      {/* خلفية هندسية متناسقة */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl\"></div>\n      </div>\n\n      {/* شبكة نقطية ناعمة */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',\n          backgroundSize: '30px 30px'\n        }}></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-block bg-white/20 backdrop-blur-sm rounded-full px-8 py-3 mb-6\">\n            <span className=\"text-white text-sm font-medium\">💬 آراء عملائنا</span>\n          </div>\n          <h2 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight\">\n            ماذا يقول\n            <span className=\"block pb-2 pt-2 text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 via-orange-300 to-yellow-400\">\n              عملاؤنا\n            </span>\n          </h2>\n          <p className=\"text-xl text-white/90 max-w-4xl mx-auto leading-relaxed\">\n            آراء حقيقية من أصحاب المشاريع والحرفيين الذين استخدموا منصة دوزان وحققوا نجاحات مميزة\n          </p>\n          <div className=\"mt-8 w-24 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto rounded-full\"></div>\n        </div>\n\n        {/* Testimonials Slider */}\n        <div\n          className=\"relative\"\n          onMouseEnter={() => setIsAutoPlaying(false)}\n          onMouseLeave={() => setIsAutoPlaying(true)}\n        >\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n            {getVisibleTestimonials().map((testimonial, index) => (\n              <div\n                key={testimonial.id}\n                className=\"group bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 hover:bg-white/20 hover:border-white/40 transition-all duration-500 hover:scale-105 hover:-translate-y-2 shadow-xl\"\n              >\n                {/* Quote Icon */}\n                <div className=\"text-4xl text-yellow-300 mb-4 opacity-50\">\n                  \"\n                </div>\n\n                {/* Content */}\n                <p className=\"text-white/90 leading-relaxed mb-6 text-lg italic\">\n                  {testimonial.content}\n                </p>\n\n                {/* Rating */}\n                <div className=\"mb-6\">\n                  <StarRating rating={testimonial.rating} />\n                </div>\n\n                {/* User Info */}\n                <div className=\"flex items-center space-x-4 space-x-reverse\">\n                  <div className=\"relative\">\n                    <img\n                      src={testimonial.avatar}\n                      alt={testimonial.name}\n                      className=\"w-16 h-16 rounded-full border-3 border-white/30 group-hover:border-white/50 transition-colors duration-300\"\n                      onError={(e) => {\n                        const target = e.target as HTMLImageElement;\n                        target.src = 'https://placehold.co/100x100/C8D9E6/2F4156?text=صورة';\n                      }}\n                    />\n                    {testimonial.verified && (\n                      <div className=\"absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center\">\n                        <span className=\"text-white text-xs\">✓</span>\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"flex-1 text-right\">\n                    <h3 className=\"font-bold text-white text-lg group-hover:text-yellow-300 transition-colors duration-300\">\n                      {testimonial.name}\n                    </h3>\n                    <p className=\"text-white/70 text-sm\">{testimonial.role}</p>\n                    <p className=\"text-white/60 text-xs\">{testimonial.location}</p>\n                  </div>\n                </div>\n\n                {/* Project Info */}\n                <div className=\"mt-6 pt-6 border-t border-white/20\">\n                  <div className=\"flex justify-between items-center text-sm\">\n                    <span className=\"text-white/70\">{testimonial.project}</span>\n                    <span className=\"text-white/60\">{testimonial.date}</span>\n                  </div>\n                </div>\n\n                {/* Hover Effect */}\n                <div className=\"absolute inset-0 bg-gradient-to-r from-yellow-400/10 to-orange-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n              </div>\n            ))}\n          </div>\n\n          {/* Navigation Dots */}\n          <div className=\"flex justify-center space-x-3 space-x-reverse\">\n            {Array.from({ length: Math.ceil(testimonials.length / 3) }).map((_, index) => (\n              <button\n                key={index}\n                onClick={() => setCurrentSlide(index)}\n                className={`w-3 h-3 rounded-full transition-all duration-300 ${\n                  currentSlide === index\n                    ? 'bg-yellow-400 w-8'\n                    : 'bg-white/40 hover:bg-white/60'\n                }`}\n              />\n            ))}\n          </div>\n        </div>\n\n        {/* CTA Section */}\n        <div className=\"text-center mt-16\">\n          <div className=\"bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20\">\n            <h3 className=\"text-2xl md:text-3xl font-bold text-white mb-4\">\n              انضم إلى آلاف العملاء الراضين\n            </h3>\n            <p className=\"text-white/90 mb-6 max-w-2xl mx-auto text-lg\">\n              ابدأ مشروعك اليوم واكتشف لماذا يثق بنا الآلاف من العملاء والحرفيين\n            </p>\n            <div className=\"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse\">\n              <button className=\"bg-yellow-400 text-navy px-8 py-3 rounded-full font-bold hover:bg-yellow-300 transition-colors duration-300 shadow-lg hover:shadow-xl transform hover:scale-105\">\n                ابدأ مشروعك الآن\n              </button>\n              <button className=\"bg-white/20 backdrop-blur-sm border-2 border-white/30 text-white px-8 py-3 rounded-full font-bold hover:bg-white/30 hover:border-white/50 transition-all duration-300\">\n                انضم كحرفي\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Testimonials;\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,MAAM;QACN,UAAU;IACZ;CACD;AAED,MAAM,aAAa,CAAC,EAAE,MAAM,EAAsB;IAChD,qBACE,6LAAC;QAAI,WAAU;;YACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;oBAEC,WAAW,CAAC,uCAAuC,EACjD,IAAI,SACA,mCACA,iBACJ;oBACF,MAAK;oBACL,SAAQ;oBACR,OAAM;8BAEN,cAAA,6LAAC;wBAAK,GAAE;;;;;;mBAVH;;;;;0BAaT,6LAAC;gBAAK,WAAU;;oBAA6B;oBAAE;oBAAO;;;;;;;;;;;;;AAG5D;KArBM;AAuBN,MAAM,eAAe;;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,eAAe;YAEpB,MAAM,WAAW;mDAAY;oBAC3B;2DAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG;;gBACzE;kDAAG;YAEH;0CAAO,IAAM,cAAc;;QAC7B;iCAAG;QAAC;KAAc;IAElB,MAAM,yBAAyB;QAC7B,MAAM,QAAQ,eAAe;QAC7B,OAAO,aAAa,KAAK,CAAC,OAAO,QAAQ;IAC3C;IAEA,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;0CAEnD,6LAAC;gCAAG,WAAU;;oCAA2E;kDAEvF,6LAAC;wCAAK,WAAU;kDAA8G;;;;;;;;;;;;0CAIhI,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;0CAGvE,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6LAAC;wBACC,WAAU;wBACV,cAAc,IAAM,iBAAiB;wBACrC,cAAc,IAAM,iBAAiB;;0CAErC,6LAAC;gCAAI,WAAU;0CACZ,yBAAyB,GAAG,CAAC,CAAC,aAAa,sBAC1C,6LAAC;wCAEC,WAAU;;0DAGV,6LAAC;gDAAI,WAAU;0DAA2C;;;;;;0DAK1D,6LAAC;gDAAE,WAAU;0DACV,YAAY,OAAO;;;;;;0DAItB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAW,QAAQ,YAAY,MAAM;;;;;;;;;;;0DAIxC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,KAAK,YAAY,MAAM;gEACvB,KAAK,YAAY,IAAI;gEACrB,WAAU;gEACV,SAAS,CAAC;oEACR,MAAM,SAAS,EAAE,MAAM;oEACvB,OAAO,GAAG,GAAG;gEACf;;;;;;4DAED,YAAY,QAAQ,kBACnB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAAqB;;;;;;;;;;;;;;;;;kEAI3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EACX,YAAY,IAAI;;;;;;0EAEnB,6LAAC;gEAAE,WAAU;0EAAyB,YAAY,IAAI;;;;;;0EACtD,6LAAC;gEAAE,WAAU;0EAAyB,YAAY,QAAQ;;;;;;;;;;;;;;;;;;0DAK9D,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAiB,YAAY,OAAO;;;;;;sEACpD,6LAAC;4DAAK,WAAU;sEAAiB,YAAY,IAAI;;;;;;;;;;;;;;;;;0DAKrD,6LAAC;gDAAI,WAAU;;;;;;;uCAtDV,YAAY,EAAE;;;;;;;;;;0CA4DzB,6LAAC;gCAAI,WAAU;0CACZ,MAAM,IAAI,CAAC;oCAAE,QAAQ,KAAK,IAAI,CAAC,aAAa,MAAM,GAAG;gCAAG,GAAG,GAAG,CAAC,CAAC,GAAG,sBAClE,6LAAC;wCAEC,SAAS,IAAM,gBAAgB;wCAC/B,WAAW,CAAC,iDAAiD,EAC3D,iBAAiB,QACb,sBACA,iCACJ;uCANG;;;;;;;;;;;;;;;;kCAab,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiD;;;;;;8CAG/D,6LAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAG5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAO,WAAU;sDAAkK;;;;;;sDAGpL,6LAAC;4CAAO,WAAU;sDAAwK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxM;GAjKM;MAAA;uCAmKS"}}, {"offset": {"line": 3946, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 3952, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\n\nconst Stats = () => {\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      { threshold: 0.1 }\n    );\n\n    const element = document.getElementById('stats-section');\n    if (element) {\n      observer.observe(element);\n    }\n\n    return () => {\n      if (element) {\n        observer.unobserve(element);\n      }\n    };\n  }, []);\n\n  const stats = [\n    {\n      id: 1,\n      number: 2500,\n      suffix: '+',\n      title: 'حرفي محترف',\n      description: 'من جميع أنحاء سوريا',\n      icon: '👷‍♂️',\n      color: 'from-blue-500 to-blue-600',\n      delay: 0\n    },\n    {\n      id: 2,\n      number: 15000,\n      suffix: '+',\n      title: 'مشروع مكتمل',\n      description: 'بنجاح وجودة عالية',\n      icon: '✅',\n      color: 'from-green-500 to-green-600',\n      delay: 200\n    },\n    {\n      id: 3,\n      number: 98,\n      suffix: '%',\n      title: 'رضا العملاء',\n      description: 'تقييم ممتاز',\n      icon: '⭐',\n      color: 'from-yellow-500 to-yellow-600',\n      delay: 400\n    },\n    {\n      id: 4,\n      number: 50,\n      suffix: '+',\n      title: 'مدينة سورية',\n      description: 'نغطي جميع المحافظات',\n      icon: '🏙️',\n      color: 'from-purple-500 to-purple-600',\n      delay: 600\n    }\n  ];\n\n  const CountUpAnimation = ({ end, duration = 2000, suffix = '', delay = 0 }: {\n    end: number;\n    duration?: number;\n    suffix?: string;\n    delay?: number;\n  }) => {\n    const [count, setCount] = useState(0);\n    const [mounted, setMounted] = useState(false);\n\n    useEffect(() => {\n      setMounted(true);\n    }, []);\n\n    useEffect(() => {\n      if (!isVisible || !mounted) return;\n\n      const timer = setTimeout(() => {\n        let startTime: number;\n        const animate = (currentTime: number) => {\n          if (!startTime) startTime = currentTime;\n          const progress = Math.min((currentTime - startTime) / duration, 1);\n\n          const easeOutQuart = 1 - Math.pow(1 - progress, 4);\n          setCount(Math.floor(easeOutQuart * end));\n\n          if (progress < 1) {\n            requestAnimationFrame(animate);\n          }\n        };\n        requestAnimationFrame(animate);\n      }, delay);\n\n      return () => clearTimeout(timer);\n    }, [isVisible, mounted, end, duration, delay]);\n\n    if (!mounted) {\n      return <span>0{suffix}</span>;\n    }\n\n    return <span>{count.toLocaleString()}{suffix}</span>;\n  };\n\n  return (\n    <section id=\"stats-section\" className=\"py-20 relative overflow-hidden\">\n      {/* خلفية متدرجة مع أنماط */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-navy via-teal to-skyblue\">\n        <div className=\"absolute inset-0 bg-black/10\"></div>\n        {/* أنماط هندسية */}\n        <div className=\"absolute top-0 left-0 w-full h-full opacity-10\">\n          <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl\"></div>\n          <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl\"></div>\n          <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl\"></div>\n          <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl\"></div>\n        </div>\n\n        {/* شبكة نقطية */}\n        <div className=\"absolute inset-0 opacity-20\">\n          <div className=\"w-full h-full\" style={{\n            backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',\n            backgroundSize: '30px 30px'\n          }}></div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-block bg-white/20 backdrop-blur-sm rounded-full px-6 py-2 mb-6\">\n            <span className=\"text-white text-sm font-medium\">📊 أرقام تتحدث عن نفسها</span>\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            منصة دوزان في أرقام\n          </h2>\n          <p className=\"text-xl text-white/90 max-w-3xl mx-auto leading-relaxed\">\n            نفخر بالثقة التي وضعها فينا آلاف العملاء والحرفيين في جميع أنحاء سوريا\n          </p>\n        </div>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {stats.map((stat) => (\n            <div\n              key={stat.id}\n              className={`group relative bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-500 hover:scale-105 hover:-translate-y-2`}\n              style={{\n                animationDelay: `${stat.delay}ms`,\n                animation: isVisible ? 'fadeInUp 0.8s ease-out forwards' : 'none'\n              }}\n            >\n              {/* تأثير الضوء */}\n              <div className=\"absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n\n              {/* الأيقونة */}\n              <div className=\"relative z-10 mb-6\">\n                <div className=\"w-16 h-16 mx-auto bg-white/20 rounded-2xl flex items-center justify-center text-3xl group-hover:scale-110 transition-transform duration-300\">\n                  {stat.icon}\n                </div>\n              </div>\n\n              {/* الرقم */}\n              <div className=\"relative z-10 text-center\">\n                <div className=\"text-4xl md:text-5xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300\">\n                  <CountUpAnimation\n                    end={stat.number}\n                    suffix={stat.suffix}\n                    delay={stat.delay}\n                  />\n                </div>\n                <h3 className=\"text-xl font-semibold text-white mb-2\">\n                  {stat.title}\n                </h3>\n                <p className=\"text-white/80 text-sm\">\n                  {stat.description}\n                </p>\n              </div>\n\n              {/* تأثير الحدود المتوهجة */}\n              <div className=\"absolute inset-0 rounded-2xl border-2 border-transparent bg-gradient-to-r from-white/30 via-transparent to-white/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500\"></div>\n            </div>\n          ))}\n        </div>\n\n        {/* شريط الشركاء المحسن - Partners Slider */}\n        <div className=\"mt-20\">\n          <div className=\"bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 overflow-hidden\">\n            <div className=\"text-center mb-8\">\n              <div className=\"inline-block bg-white/20 backdrop-blur-sm rounded-full px-6 py-2 mb-4\">\n                <span className=\"text-white text-sm font-medium\">🤝 شركاؤنا</span>\n              </div>\n              <h3 className=\"text-2xl md:text-3xl font-bold text-white mb-2\">\n                موثوق من قبل أفضل الشركات في سوريا\n              </h3>\n              <p className=\"text-white/80 text-sm md:text-base\">\n                يثق بنا المئات من الشركات والمؤسسات الرائدة\n              </p>\n            </div>\n\n            {/* Partners Slider */}\n            <div className=\"relative\">\n              <div className=\"flex animate-scroll-right space-x-12 space-x-reverse\">\n                {[\n                  { name: 'شركة البناء السورية', icon: '🏗️', specialty: 'البناء والإنشاءات' },\n                  { name: 'مجموعة الإعمار', icon: '🏢', specialty: 'التطوير العقاري' },\n                  { name: 'شركة التطوير العقاري', icon: '🏘️', specialty: 'المشاريع السكنية' },\n                  { name: 'مؤسسة الحرفيين', icon: '👷‍♂️', specialty: 'الحرف التقليدية' },\n                  { name: 'شركة الديكور الحديث', icon: '🎨', specialty: 'التصميم الداخلي' },\n                  { name: 'مجموعة الصيانة', icon: '🔧', specialty: 'الصيانة العامة' }\n                ].concat([\n                  { name: 'شركة البناء السورية', icon: '🏗️', specialty: 'البناء والإنشاءات' },\n                  { name: 'مجموعة الإعمار', icon: '🏢', specialty: 'التطوير العقاري' },\n                  { name: 'شركة التطوير العقاري', icon: '🏘️', specialty: 'المشاريع السكنية' },\n                  { name: 'مؤسسة الحرفيين', icon: '👷‍♂️', specialty: 'الحرف التقليدية' }\n                ]).map((partner, index) => (\n                  <div\n                    key={index}\n                    className=\"flex-shrink-0 group\"\n                  >\n                    <div className=\"bg-white/15 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:bg-white/25 hover:border-white/40 transition-all duration-300 hover:scale-105 min-w-[280px]\">\n                      <div className=\"flex items-center space-x-4 space-x-reverse\">\n                        <div className=\"text-3xl group-hover:scale-110 transition-transform duration-300\">\n                          {partner.icon}\n                        </div>\n                        <div className=\"text-right\">\n                          <h4 className=\"text-white font-bold text-lg group-hover:text-yellow-300 transition-colors duration-300\">\n                            {partner.name}\n                          </h4>\n                          <p className=\"text-white/70 text-sm\">\n                            {partner.specialty}\n                          </p>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <style jsx>{`\n        @keyframes fadeInUp {\n          from {\n            opacity: 0;\n            transform: translateY(30px);\n          }\n          to {\n            opacity: 1;\n            transform: translateY(0);\n          }\n        }\n\n        @keyframes scroll-right {\n          0% {\n            transform: translateX(0);\n          }\n          100% {\n            transform: translateX(-50%);\n          }\n        }\n\n        .animate-scroll-right {\n          animation: scroll-right 30s linear infinite;\n        }\n\n        .animate-scroll-right:hover {\n          animation-play-state: paused;\n        }\n      `}</style>\n    </section>\n  );\n};\n\nexport default Stats;\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;;AAIA,MAAM,QAAQ;;;IACZ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM,WAAW,IAAI;mCACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;kCACA;gBAAE,WAAW;YAAI;YAGnB,MAAM,UAAU,SAAS,cAAc,CAAC;YACxC,IAAI,SAAS;gBACX,SAAS,OAAO,CAAC;YACnB;YAEA;mCAAO;oBACL,IAAI,SAAS;wBACX,SAAS,SAAS,CAAC;oBACrB;gBACF;;QACF;0BAAG,EAAE;IAEL,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,OAAO;QACT;QACA;YACE,IAAI;YACJ,QAAQ;YACR,QAAQ;YACR,OAAO;YACP,aAAa;YACb,MAAM;YACN,OAAO;YACP,OAAO;QACT;KACD;IAED,MAAM,mBAAmB,CAAC,EAAE,GAAG,EAAE,WAAW,IAAI,EAAE,SAAS,EAAE,EAAE,QAAQ,CAAC,EAKvE;;QACC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;gBACR,WAAW;YACb;+CAAG,EAAE;QAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gDAAE;gBACR,IAAI,CAAC,aAAa,CAAC,SAAS;gBAE5B,MAAM,QAAQ;8DAAW;wBACvB,IAAI;wBACJ,MAAM;8EAAU,CAAC;gCACf,IAAI,CAAC,WAAW,YAAY;gCAC5B,MAAM,WAAW,KAAK,GAAG,CAAC,CAAC,cAAc,SAAS,IAAI,UAAU;gCAEhE,MAAM,eAAe,IAAI,KAAK,GAAG,CAAC,IAAI,UAAU;gCAChD,SAAS,KAAK,KAAK,CAAC,eAAe;gCAEnC,IAAI,WAAW,GAAG;oCAChB,sBAAsB;gCACxB;4BACF;;wBACA,sBAAsB;oBACxB;6DAAG;gBAEH;wDAAO,IAAM,aAAa;;YAC5B;+CAAG;YAAC;YAAW;YAAS;YAAK;YAAU;SAAM;QAE7C,IAAI,CAAC,SAAS;YACZ,qBAAO,6LAAC;;oBAAK;oBAAE;;;;;;;QACjB;QAEA,qBAAO,6LAAC;;gBAAM,MAAM,cAAc;gBAAI;;;;;;;IACxC;QAxCM;IA0CN,qBACE,6LAAC;QAAQ,IAAG;kDAA0B;;0BAEpC,6LAAC;0DAAc;;kCACb,6LAAC;kEAAc;;;;;;kCAEf,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;;;;;;0CACf,6LAAC;0EAAc;;;;;;;;;;;;kCAIjB,6LAAC;kEAAc;kCACb,cAAA,6LAAC;4BAA8B,OAAO;gCACpC,iBAAiB;gCACjB,gBAAgB;4BAClB;sEAHe;;;;;;;;;;;;;;;;;0BAOnB,6LAAC;0DAAc;;kCACb,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;0CACb,cAAA,6LAAC;8EAAe;8CAAiC;;;;;;;;;;;0CAEnD,6LAAC;0EAAa;0CAAiD;;;;;;0CAG/D,6LAAC;0EAAY;0CAA0D;;;;;;;;;;;;kCAKzE,6LAAC;kEAAc;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gCAGC,OAAO;oCACL,gBAAgB,GAAG,KAAK,KAAK,CAAC,EAAE,CAAC;oCACjC,WAAW,YAAY,oCAAoC;gCAC7D;0EAJW,CAAC,qKAAqK,CAAC;;kDAOlL,6LAAC;kFAAc;;;;;;kDAGf,6LAAC;kFAAc;kDACb,cAAA,6LAAC;sFAAc;sDACZ,KAAK,IAAI;;;;;;;;;;;kDAKd,6LAAC;kFAAc;;0DACb,6LAAC;0FAAc;0DACb,cAAA,6LAAC;oDACC,KAAK,KAAK,MAAM;oDAChB,QAAQ,KAAK,MAAM;oDACnB,OAAO,KAAK,KAAK;;;;;;;;;;;0DAGrB,6LAAC;0FAAa;0DACX,KAAK,KAAK;;;;;;0DAEb,6LAAC;0FAAY;0DACV,KAAK,WAAW;;;;;;;;;;;;kDAKrB,6LAAC;kFAAc;;;;;;;+BAnCV,KAAK,EAAE;;;;;;;;;;kCAyClB,6LAAC;kEAAc;kCACb,cAAA,6LAAC;sEAAc;;8CACb,6LAAC;8EAAc;;sDACb,6LAAC;sFAAc;sDACb,cAAA,6LAAC;0FAAe;0DAAiC;;;;;;;;;;;sDAEnD,6LAAC;sFAAa;sDAAiD;;;;;;sDAG/D,6LAAC;sFAAY;sDAAqC;;;;;;;;;;;;8CAMpD,6LAAC;8EAAc;8CACb,cAAA,6LAAC;kFAAc;kDACZ;4CACC;gDAAE,MAAM;gDAAuB,MAAM;gDAAO,WAAW;4CAAoB;4CAC3E;gDAAE,MAAM;gDAAkB,MAAM;gDAAM,WAAW;4CAAkB;4CACnE;gDAAE,MAAM;gDAAwB,MAAM;gDAAO,WAAW;4CAAmB;4CAC3E;gDAAE,MAAM;gDAAkB,MAAM;gDAAS,WAAW;4CAAkB;4CACtE;gDAAE,MAAM;gDAAuB,MAAM;gDAAM,WAAW;4CAAkB;4CACxE;gDAAE,MAAM;gDAAkB,MAAM;gDAAM,WAAW;4CAAiB;yCACnE,CAAC,MAAM,CAAC;4CACP;gDAAE,MAAM;gDAAuB,MAAM;gDAAO,WAAW;4CAAoB;4CAC3E;gDAAE,MAAM;gDAAkB,MAAM;gDAAM,WAAW;4CAAkB;4CACnE;gDAAE,MAAM;gDAAwB,MAAM;gDAAO,WAAW;4CAAmB;4CAC3E;gDAAE,MAAM;gDAAkB,MAAM;gDAAS,WAAW;4CAAkB;yCACvE,EAAE,GAAG,CAAC,CAAC,SAAS,sBACf,6LAAC;0FAEW;0DAEV,cAAA,6LAAC;8FAAc;8DACb,cAAA,6LAAC;kGAAc;;0EACb,6LAAC;0GAAc;0EACZ,QAAQ,IAAI;;;;;;0EAEf,6LAAC;0GAAc;;kFACb,6LAAC;kHAAa;kFACX,QAAQ,IAAI;;;;;;kFAEf,6LAAC;kHAAY;kFACV,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;;;;;+CAbrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyDzB;GArRM;KAAA;uCAuRS"}}, {"offset": {"line": 4508, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4514, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Button } from '../ui/Button';\nimport { Badge } from '../ui/Badge';\n\nconst FeaturedProjectsSimple = () => {\n  const projects = [\n    {\n      id: 1,\n      title: 'تجديد مطبخ عصري فاخر',\n      beforeImage: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop',\n      afterImage: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop&sat=2&brightness=1.1',\n      craftsman: 'محمد النجار',\n      location: 'دمشق، المزة',\n      duration: '14 يوم',\n      budget: '350,000 ل.س',\n      rating: 5,\n      description: 'تحويل مطبخ تقليدي إلى مطبخ عصري بخزائن خشبية فاخرة وجزيرة وسطية'\n    },\n    {\n      id: 2,\n      title: 'تصميم حمام مودرن',\n      beforeImage: 'https://images.unsplash.com/photo-1620626011761-996317b8d101?w=600&h=400&fit=crop',\n      afterImage: 'https://images.unsplash.com/photo-1620626011761-996317b8d101?w=600&h=400&fit=crop&sat=2&brightness=1.1',\n      craftsman: 'أحمد السباك',\n      location: 'حلب، الفرقان',\n      duration: '10 أيام',\n      budget: '180,000 ل.س',\n      rating: 5,\n      description: 'تجديد حمام كامل بتصميم عصري وتركيبات حديثة'\n    },\n    {\n      id: 3,\n      title: 'غرفة نوم رئيسية فاخرة',\n      beforeImage: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop',\n      afterImage: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop&sat=2&brightness=1.1',\n      craftsman: 'علي النجار',\n      location: 'دمشق، جرمانا',\n      duration: '12 يوم',\n      budget: '280,000 ل.س',\n      rating: 5,\n      description: 'تصميم غرفة نوم رئيسية مع خزائن مدمجة وإضاءة مخفية'\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-gradient-to-b from-white via-beige/30 to-white relative overflow-hidden\">\n      {/* عناصر تصميمية في الخلفية */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute top-20 left-10 w-64 h-64 bg-navy rounded-full blur-3xl\"></div>\n        <div className=\"absolute bottom-20 right-10 w-48 h-48 bg-teal rounded-full blur-3xl\"></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        {/* العنوان */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-2 rounded-full text-sm font-medium mb-4\">\n            🏆 مشاريع مميزة\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-navy mb-6\">\n            أعمال تحكي قصص \n            <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-teal to-navy\">النجاح</span>\n          </h2>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed\">\n            اكتشف مجموعة من أفضل المشاريع التي تم إنجازها على منصة دوزان بجودة استثنائية\n          </p>\n        </div>\n\n        {/* شبكة المشاريع */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n          {projects.map((project, index) => (\n            <div\n              key={project.id}\n              className=\"group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-teal/30 hover:scale-105\"\n            >\n              {/* مقارنة قبل وبعد */}\n              <div className=\"relative h-48 overflow-hidden\">\n                <div className=\"absolute inset-0 grid grid-cols-2\">\n                  {/* صورة قبل */}\n                  <div className=\"relative overflow-hidden\">\n                    <img\n                      src={project.beforeImage}\n                      alt=\"قبل\"\n                      className=\"w-full h-full object-cover transition-transform duration-700 group-hover:scale-110\"\n                    />\n                    <div className=\"absolute top-4 left-4 bg-red-500/90 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                      قبل\n                    </div>\n                  </div>\n                  \n                  {/* صورة بعد */}\n                  <div className=\"relative overflow-hidden\">\n                    <img\n                      src={project.afterImage}\n                      alt=\"بعد\"\n                      className=\"w-full h-full object-cover transition-transform duration-700 group-hover:scale-110\"\n                    />\n                    <div className=\"absolute top-4 right-4 bg-green-500/90 text-white px-3 py-1 rounded-full text-sm font-medium\">\n                      بعد\n                    </div>\n                  </div>\n                </div>\n\n                {/* خط الفاصل */}\n                <div className=\"absolute inset-y-0 left-1/2 w-1 bg-white shadow-lg transform -translate-x-1/2 z-10\"></div>\n                <div className=\"absolute top-1/2 left-1/2 w-8 h-8 bg-white rounded-full shadow-lg transform -translate-x-1/2 -translate-y-1/2 z-20 flex items-center justify-center\">\n                  <div className=\"w-4 h-4 bg-gradient-to-r from-navy to-teal rounded-full\"></div>\n                </div>\n\n                {/* تأثير التدرج */}\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n              </div>\n\n              {/* محتوى البطاقة */}\n              <div className=\"p-6\">\n                <div className=\"flex items-start justify-between mb-4\">\n                  <div>\n                    <h3 className=\"text-xl font-bold text-navy mb-2 group-hover:text-teal transition-colors\">\n                      {project.title}\n                    </h3>\n                    <p className=\"text-gray-600 text-sm line-clamp-2\">\n                      {project.description}\n                    </p>\n                  </div>\n                  <div className=\"flex items-center\">\n                    {[...Array(5)].map((_, i) => (\n                      <svg\n                        key={i}\n                        className={`w-4 h-4 ${\n                          i < project.rating ? 'text-yellow-500' : 'text-gray-300'\n                        }`}\n                        fill=\"currentColor\"\n                        viewBox=\"0 0 20 20\"\n                      >\n                        <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                      </svg>\n                    ))}\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4 mb-4\">\n                  <div className=\"bg-gray-50 p-3 rounded-lg\">\n                    <div className=\"text-xs text-gray-500 mb-1\">الحرفي</div>\n                    <div className=\"font-semibold text-navy text-sm\">{project.craftsman}</div>\n                  </div>\n                  <div className=\"bg-gray-50 p-3 rounded-lg\">\n                    <div className=\"text-xs text-gray-500 mb-1\">المدة</div>\n                    <div className=\"font-semibold text-navy text-sm\">{project.duration}</div>\n                  </div>\n                </div>\n\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <div className=\"text-xs text-gray-500\">الميزانية</div>\n                    <div className=\"font-bold text-green-600\">{project.budget}</div>\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    📍 {project.location}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* زر عرض المزيد */}\n        <div className=\"text-center\">\n          <Link href=\"/jobs\">\n            <Button size=\"lg\" className=\"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90 px-8 py-4 text-lg\">\n              استكشف جميع المشاريع\n              <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n              </svg>\n            </Button>\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeaturedProjectsSimple;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAOA,MAAM,yBAAyB;IAC7B,MAAM,WAAW;QACf;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,WAAW;YACX,UAAU;YACV,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,WAAW;YACX,UAAU;YACV,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,YAAY;YACZ,WAAW;YACX,UAAU;YACV,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,aAAa;QACf;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAA6G;;;;;;0CAG5H,6LAAC;gCAAG,WAAU;;oCAAgD;kDAE5D,6LAAC;wCAAK,WAAU;kDAAmE;;;;;;;;;;;;0CAErF,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAMzE,6LAAC;wBAAI,WAAU;kCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;gCAEC,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEAEb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,KAAK,QAAQ,WAAW;gEACxB,KAAI;gEACJ,WAAU;;;;;;0EAEZ,6LAAC;gEAAI,WAAU;0EAA4F;;;;;;;;;;;;kEAM7G,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,KAAK,QAAQ,UAAU;gEACvB,KAAI;gEACJ,WAAU;;;;;;0EAEZ,6LAAC;gEAAI,WAAU;0EAA+F;;;;;;;;;;;;;;;;;;0DAOlH,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;;;;;;;;;;0DAIjB,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK;;;;;;0EAEhB,6LAAC;gEAAE,WAAU;0EACV,QAAQ,WAAW;;;;;;;;;;;;kEAGxB,6LAAC;wDAAI,WAAU;kEACZ;+DAAI,MAAM;yDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;gEAEC,WAAW,CAAC,QAAQ,EAClB,IAAI,QAAQ,MAAM,GAAG,oBAAoB,iBACzC;gEACF,MAAK;gEACL,SAAQ;0EAER,cAAA,6LAAC;oEAAK,GAAE;;;;;;+DAPH;;;;;;;;;;;;;;;;0DAab,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAA6B;;;;;;0EAC5C,6LAAC;gEAAI,WAAU;0EAAmC,QAAQ,SAAS;;;;;;;;;;;;kEAErE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAA6B;;;;;;0EAC5C,6LAAC;gEAAI,WAAU;0EAAmC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;0DAItE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAwB;;;;;;0EACvC,6LAAC;gEAAI,WAAU;0EAA4B,QAAQ,MAAM;;;;;;;;;;;;kEAE3D,6LAAC;wDAAI,WAAU;;4DAAwB;4DACjC,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;;;+BArFrB,QAAQ,EAAE;;;;;;;;;;kCA8FrB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,WAAU;;oCAA2F;kDAErH,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF;KA9KM;uCAgLS"}}, {"offset": {"line": 4984, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 4990, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Badge.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n  {\n    variants: {\n      variant: {\n        default: 'border-transparent bg-navy text-white hover:bg-navy/80',\n        secondary: 'border-transparent bg-teal text-white hover:bg-teal/80',\n        destructive: 'border-transparent bg-red-500 text-white hover:bg-red-500/80',\n        outline: 'text-navy border-navy',\n        success: 'border-transparent bg-green-500 text-white hover:bg-green-500/80',\n        warning: 'border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80',\n        info: 'border-transparent bg-skyblue text-navy hover:bg-skyblue/80',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  }\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS"}}, {"offset": {"line": 5036, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 5042, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/home/<USER>"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { Button } from '../ui/Button';\nimport { Badge } from '../ui/Badge';\n\nconst TopCraftsmenSimple = () => {\n  const craftsmen = [\n    {\n      id: 1,\n      name: 'محمد النجار',\n      profession: 'نجار محترف',\n      avatar: 'https://randomuser.me/api/portraits/men/1.jpg',\n      rating: 4.9,\n      reviewsCount: 47,\n      completedJobs: 89,\n      specialties: ['مطابخ', 'خزائن', 'أثاث مكتبي'],\n      location: 'دمشق، المزة',\n      hourlyRate: 2500,\n      verified: true,\n      portfolioImage: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=200&fit=crop'\n    },\n    {\n      id: 2,\n      name: 'أحمد السباك',\n      profession: 'سباك معتمد',\n      avatar: 'https://randomuser.me/api/portraits/men/2.jpg',\n      rating: 4.8,\n      reviewsCount: 32,\n      completedJobs: 67,\n      specialties: ['سباكة', 'صرف صحي', 'تسليك'],\n      location: 'حلب، الفرقان',\n      hourlyRate: 2000,\n      verified: true,\n      portfolioImage: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop'\n    },\n    {\n      id: 3,\n      name: 'علي الكهربائي',\n      profession: 'كهربائي محترف',\n      avatar: 'https://randomuser.me/api/portraits/men/3.jpg',\n      rating: 4.7,\n      reviewsCount: 28,\n      completedJobs: 54,\n      specialties: ['كهرباء', 'تمديدات', 'إضاءة'],\n      location: 'دمشق، جرمانا',\n      hourlyRate: 2200,\n      verified: false,\n      portfolioImage: 'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=200&fit=crop'\n    }\n  ];\n\n  return (\n    <section className=\"py-20 bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden\">\n      {/* خلفية متحركة */}\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-0 left-0 w-full h-full opacity-10\">\n          <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-pulse\"></div>\n          <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl animate-pulse\" style={{ animationDelay: '1s' }}></div>\n          <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl animate-pulse\" style={{ animationDelay: '2s' }}></div>\n          <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl animate-pulse\" style={{ animationDelay: '3s' }}></div>\n        </div>\n      </div>\n\n      <div className=\"container mx-auto px-4 relative z-10\">\n        {/* العنوان */}\n        <div className=\"text-center mb-16\">\n          <div className=\"inline-block bg-white/20 backdrop-blur-sm rounded-full px-6 py-2 mb-6\">\n            <span className=\"text-white text-sm font-medium\">⭐ نخبة الحرفيين</span>\n          </div>\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            أفضل الحرفيين في \n            <span className=\"text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-300\">سوريا</span>\n          </h2>\n          <p className=\"text-xl text-white/90 max-w-3xl mx-auto leading-relaxed\">\n            تعرف على نخبة من أمهر الحرفيين المحترفين الذين حققوا أعلى التقييمات وأنجزوا مئات المشاريع بنجاح\n          </p>\n        </div>\n\n        {/* عرض الحرفيين */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\">\n          {craftsmen.map((craftsman, index) => (\n            <div\n              key={craftsman.id}\n              className=\"bg-white/10 backdrop-blur-md rounded-3xl p-6 border border-white/20 hover:bg-white/20 transition-all duration-500 hover:scale-105 hover:-translate-y-2\"\n            >\n              {/* معلومات الحرفي */}\n              <div className=\"flex items-start gap-4 mb-6\">\n                <div className=\"relative\">\n                  <img\n                    src={craftsman.avatar}\n                    alt={craftsman.name}\n                    className=\"w-16 h-16 rounded-full object-cover border-4 border-white/30\"\n                  />\n                  {craftsman.verified && (\n                    <div className=\"absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center\">\n                      <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"flex-1\">\n                  <h3 className=\"text-xl font-bold text-white mb-1\">\n                    {craftsman.name}\n                  </h3>\n                  <p className=\"text-white/80 mb-2\">{craftsman.profession}</p>\n                  <div className=\"flex items-center text-white/70 text-sm mb-2\">\n                    <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    </svg>\n                    {craftsman.location}\n                  </div>\n                  \n                  <div className=\"flex items-center mb-3\">\n                    <div className=\"flex items-center ml-2\">\n                      {[...Array(5)].map((_, i) => (\n                        <svg\n                          key={i}\n                          className={`w-4 h-4 ${\n                            i < Math.floor(craftsman.rating) ? 'text-yellow-400' : 'text-white/30'\n                          }`}\n                          fill=\"currentColor\"\n                          viewBox=\"0 0 20 20\"\n                        >\n                          <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                        </svg>\n                      ))}\n                    </div>\n                    <span className=\"text-white font-semibold\">{craftsman.rating}</span>\n                    <span className=\"text-white/70 text-sm mr-2\">({craftsman.reviewsCount} تقييم)</span>\n                  </div>\n                </div>\n              </div>\n\n              {/* صورة من أعماله */}\n              <div className=\"mb-4\">\n                <img\n                  src={craftsman.portfolioImage}\n                  alt={`عمل ${craftsman.name}`}\n                  className=\"w-full h-full object-cover rounded-lg border border-white/20 hover:scale-105 transition-transform duration-300\"\n                />\n              </div>\n\n              {/* الإحصائيات */}\n              <div className=\"grid grid-cols-2 gap-4 mb-4\">\n                <div className=\"bg-white/10 p-3 rounded-xl\">\n                  <div className=\"text-white/70 text-sm mb-1\">المشاريع المكتملة</div>\n                  <div className=\"text-xl font-bold text-white\">{craftsman.completedJobs}</div>\n                </div>\n                <div className=\"bg-white/10 p-3 rounded-xl\">\n                  <div className=\"text-white/70 text-sm mb-1\">السعر/ساعة</div>\n                  <div className=\"text-lg font-bold text-white\">{craftsman.hourlyRate.toLocaleString()} ل.س</div>\n                </div>\n              </div>\n\n              {/* التخصصات */}\n              <div className=\"flex flex-wrap gap-2 mb-4\">\n                {craftsman.specialties.map((specialty, index) => (\n                  <Badge key={index} variant=\"outline\" className=\"border-white/30 text-white text-xs\">\n                    {specialty}\n                  </Badge>\n                ))}\n              </div>\n\n              {/* زر عرض الملف الشخصي */}\n              <Link href={`/craftsmen/${craftsman.id}`}>\n                <Button className=\"w-full bg-white text-navy hover:bg-white/90\">\n                  عرض الملف الشخصي\n                </Button>\n              </Link>\n            </div>\n          ))}\n        </div>\n\n        {/* زر عرض جميع الحرفيين */}\n        <div className=\"text-center\">\n          <Link href=\"/craftsmen\">\n            <Button size=\"lg\" className=\"bg-white text-navy hover:bg-white/90 px-8 py-4 text-lg\">\n              استكشف جميع الحرفيين\n              <svg className=\"w-5 h-5 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n              </svg>\n            </Button>\n          </Link>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default TopCraftsmenSimple;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAOA,MAAM,qBAAqB;IACzB,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,MAAM;YACN,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,eAAe;YACf,aAAa;gBAAC;gBAAS;gBAAS;aAAa;YAC7C,UAAU;YACV,YAAY;YACZ,UAAU;YACV,gBAAgB;QAClB;QACA;YACE,IAAI;YACJ,MAAM;YACN,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,eAAe;YACf,aAAa;gBAAC;gBAAS;gBAAW;aAAQ;YAC1C,UAAU;YACV,YAAY;YACZ,UAAU;YACV,gBAAgB;QAClB;QACA;YACE,IAAI;YACJ,MAAM;YACN,YAAY;YACZ,QAAQ;YACR,QAAQ;YACR,cAAc;YACd,eAAe;YACf,aAAa;gBAAC;gBAAU;gBAAW;aAAQ;YAC3C,UAAU;YACV,YAAY;YACZ,UAAU;YACV,gBAAgB;QAClB;KACD;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;4BAAiF,OAAO;gCAAE,gBAAgB;4BAAK;;;;;;sCAC9H,6LAAC;4BAAI,WAAU;4BAAmF,OAAO;gCAAE,gBAAgB;4BAAK;;;;;;sCAChI,6LAAC;4BAAI,WAAU;4BAAoF,OAAO;gCAAE,gBAAgB;4BAAK;;;;;;;;;;;;;;;;;0BAIrI,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAAiC;;;;;;;;;;;0CAEnD,6LAAC;gCAAG,WAAU;;oCAAiD;kDAE7D,6LAAC;wCAAK,WAAU;kDAA+E;;;;;;;;;;;;0CAEjG,6LAAC;gCAAE,WAAU;0CAA0D;;;;;;;;;;;;kCAMzE,6LAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,WAAW,sBACzB,6LAAC;gCAEC,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,KAAK,UAAU,MAAM;wDACrB,KAAK,UAAU,IAAI;wDACnB,WAAU;;;;;;oDAEX,UAAU,QAAQ,kBACjB,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAe,SAAQ;sEAC9D,cAAA,6LAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAkiB,UAAS;;;;;;;;;;;;;;;;;;;;;;0DAM9kB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEACX,UAAU,IAAI;;;;;;kEAEjB,6LAAC;wDAAE,WAAU;kEAAsB,UAAU,UAAU;;;;;;kEACvD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;gEAAe,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACtE,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;4DAEtE,UAAU,QAAQ;;;;;;;kEAGrB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACZ;uEAAI,MAAM;iEAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wEAEC,WAAW,CAAC,QAAQ,EAClB,IAAI,KAAK,KAAK,CAAC,UAAU,MAAM,IAAI,oBAAoB,iBACvD;wEACF,MAAK;wEACL,SAAQ;kFAER,cAAA,6LAAC;4EAAK,GAAE;;;;;;uEAPH;;;;;;;;;;0EAWX,6LAAC;gEAAK,WAAU;0EAA4B,UAAU,MAAM;;;;;;0EAC5D,6LAAC;gEAAK,WAAU;;oEAA6B;oEAAE,UAAU,YAAY;oEAAC;;;;;;;;;;;;;;;;;;;;;;;;;kDAM5E,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,KAAK,UAAU,cAAc;4CAC7B,KAAK,CAAC,IAAI,EAAE,UAAU,IAAI,EAAE;4CAC5B,WAAU;;;;;;;;;;;kDAKd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAA6B;;;;;;kEAC5C,6LAAC;wDAAI,WAAU;kEAAgC,UAAU,aAAa;;;;;;;;;;;;0DAExE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAA6B;;;;;;kEAC5C,6LAAC;wDAAI,WAAU;;4DAAgC,UAAU,UAAU,CAAC,cAAc;4DAAG;;;;;;;;;;;;;;;;;;;kDAKzF,6LAAC;wCAAI,WAAU;kDACZ,UAAU,WAAW,CAAC,GAAG,CAAC,CAAC,WAAW,sBACrC,6LAAC,oIAAA,CAAA,QAAK;gDAAa,SAAQ;gDAAU,WAAU;0DAC5C;+CADS;;;;;;;;;;kDAOhB,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM,CAAC,WAAW,EAAE,UAAU,EAAE,EAAE;kDACtC,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,WAAU;sDAA8C;;;;;;;;;;;;+BArF7D,UAAU,EAAE;;;;;;;;;;kCA8FvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;sCACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,WAAU;;oCAAyD;kDAEnF,6LAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF;KAxLM;uCA0LS"}}, {"offset": {"line": 5571, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}