'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import BeforeAfterUpload from '@/components/ui/BeforeAfterUpload';
import DateDisplay from '@/components/ui/DateDisplay';

interface Project {
  id: number;
  title: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  budget: number;
  deadline: string;
  startDate?: string;
  completedDate?: string;
  client: {
    name: string;
    avatar: string;
    rating: number;
  };
  hasBeforeImages: boolean;
  hasAfterImages: boolean;
}

const ProjectsManagementPage = () => {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [filter, setFilter] = useState<'all' | 'pending' | 'in_progress' | 'completed'>('all');

  const projects: Project[] = [
    {
      id: 1,
      title: 'تصميم وتنفيذ خزائن مطبخ',
      description: 'تصميم وتنفيذ خزائن مطبخ خشبية عصرية مع جزيرة وسطية',
      status: 'in_progress',
      budget: 350000,
      deadline: '2024-03-15',
      startDate: '2024-02-01',
      client: {
        name: 'أحمد محمد',
        avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
        rating: 4.8
      },
      hasBeforeImages: true,
      hasAfterImages: false
    },
    {
      id: 2,
      title: 'ترميم غرفة نوم',
      description: 'ترميم وتجديد غرفة نوم كاملة مع تركيب أرضية جديدة',
      status: 'completed',
      budget: 280000,
      deadline: '2024-01-30',
      startDate: '2024-01-15',
      completedDate: '2024-01-28',
      client: {
        name: 'فاطمة أحمد',
        avatar: 'https://randomuser.me/api/portraits/women/1.jpg',
        rating: 4.9
      },
      hasBeforeImages: true,
      hasAfterImages: true
    },
    {
      id: 3,
      title: 'تركيب خزائن مكتب',
      description: 'تصميم وتركيب خزائن مكتب مخصصة مع وحدات تخزين',
      status: 'pending',
      budget: 150000,
      deadline: '2024-04-10',
      client: {
        name: 'محمد علي',
        avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
        rating: 4.7
      },
      hasBeforeImages: false,
      hasAfterImages: false
    }
  ];

  const filteredProjects = projects.filter(project => 
    filter === 'all' || project.status === filter
  );

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="warning">في الانتظار</Badge>;
      case 'in_progress':
        return <Badge variant="info">قيد التنفيذ</Badge>;
      case 'completed':
        return <Badge variant="success">مكتمل</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">ملغي</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const handleStartProject = (project: Project) => {
    setSelectedProject(project);
    setShowUploadModal(true);
  };

  const handleCompleteProject = (project: Project) => {
    setSelectedProject(project);
    setShowUploadModal(true);
  };

  const handleBeforeImagesUpload = (images: File[]) => {
    console.log('Before images:', images);
  };

  const handleAfterImagesUpload = (images: File[]) => {
    console.log('After images:', images);
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-beige via-white to-skyblue/20 py-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-navy mb-2">إدارة المشاريع</h1>
            <p className="text-gray-600">
              تابع مشاريعك الحالية ووثق أعمالك بصور قبل وبعد
            </p>
          </div>

          {/* Filters */}
          <div className="mb-8">
            <div className="flex flex-wrap gap-2">
              {[
                { id: 'all', label: 'جميع المشاريع', count: projects.length },
                { id: 'pending', label: 'في الانتظار', count: projects.filter(p => p.status === 'pending').length },
                { id: 'in_progress', label: 'قيد التنفيذ', count: projects.filter(p => p.status === 'in_progress').length },
                { id: 'completed', label: 'مكتملة', count: projects.filter(p => p.status === 'completed').length }
              ].map((filterOption) => (
                <button
                  key={filterOption.id}
                  onClick={() => setFilter(filterOption.id as any)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    filter === filterOption.id
                      ? 'bg-navy text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200'
                  }`}
                >
                  {filterOption.label} ({filterOption.count})
                </button>
              ))}
            </div>
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredProjects.map((project) => (
              <Card key={project.id} className="border-0 bg-white/90 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
                <CardHeader>
                  <div className="flex justify-between items-start mb-2">
                    <CardTitle className="text-lg leading-tight">{project.title}</CardTitle>
                    {getStatusBadge(project.status)}
                  </div>
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <img
                      src={project.client.avatar}
                      alt={project.client.name}
                      className="w-8 h-8 rounded-full"
                    />
                    <div>
                      <div className="font-medium text-sm">{project.client.name}</div>
                      <div className="flex items-center text-xs text-gray-500">
                        <svg className="w-3 h-3 text-yellow-500 ml-1" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        {project.client.rating}
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent>
                  <p className="text-gray-700 text-sm mb-4 line-clamp-2">{project.description}</p>

                  <div className="grid grid-cols-2 gap-3 mb-4">
                    <div className="bg-green-50 p-2 rounded-lg">
                      <div className="text-green-600 text-xs font-medium">الميزانية</div>
                      <div className="font-bold text-green-700 text-sm">
                        {project.budget.toLocaleString()} ل.س
                      </div>
                    </div>
                    <div className="bg-blue-50 p-2 rounded-lg">
                      <div className="text-blue-600 text-xs font-medium">الموعد النهائي</div>
                      <div className="font-bold text-blue-700 text-xs">
                        <DateDisplay date={project.deadline} />
                      </div>
                    </div>
                  </div>

                  {/* مؤشر التوثيق */}
                  <div className="mb-4">
                    <div className="text-xs font-medium text-gray-600 mb-2">حالة التوثيق:</div>
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full ml-1 ${
                          project.hasBeforeImages ? 'bg-green-500' : 'bg-gray-300'
                        }`}></div>
                        <span className="text-xs text-gray-600">صور قبل</span>
                      </div>
                      <div className="flex items-center">
                        <div className={`w-3 h-3 rounded-full ml-1 ${
                          project.hasAfterImages ? 'bg-green-500' : 'bg-gray-300'
                        }`}></div>
                        <span className="text-xs text-gray-600">صور بعد</span>
                      </div>
                    </div>
                  </div>

                  {/* الإجراءات */}
                  <div className="space-y-2">
                    {project.status === 'pending' && (
                      <Button
                        size="sm"
                        className="w-full bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90"
                        onClick={() => handleStartProject(project)}
                      >
                        بدء العمل وتوثيق الحالة الأولية
                      </Button>
                    )}

                    {project.status === 'in_progress' && (
                      <div className="space-y-2">
                        {!project.hasBeforeImages && (
                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full"
                            onClick={() => handleStartProject(project)}
                          >
                            إضافة صور قبل العمل
                          </Button>
                        )}
                        <Button
                          size="sm"
                          className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
                          onClick={() => handleCompleteProject(project)}
                        >
                          إنهاء المشروع وتوثيق النتيجة
                        </Button>
                      </div>
                    )}

                    {project.status === 'completed' && (
                      <div className="text-center py-2">
                        <div className="text-green-600 text-sm font-medium">
                          ✓ تم إنجاز المشروع بنجاح
                        </div>
                        {project.completedDate && (
                          <div className="text-xs text-gray-500">
                            اكتمل في <DateDisplay date={project.completedDate} />
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Empty State */}
          {filteredProjects.length === 0 && (
            <div className="text-center py-12">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مشاريع</h3>
              <p className="text-gray-500">لا توجد مشاريع في هذه الفئة حالياً</p>
            </div>
          )}
        </div>
      </div>

      {/* Modal لرفع الصور */}
      {showUploadModal && selectedProject && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-xl font-bold text-navy">توثيق المشروع</h3>
                <button
                  onClick={() => setShowUploadModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <BeforeAfterUpload
                projectTitle={selectedProject.title}
                onBeforeImagesChange={handleBeforeImagesUpload}
                onAfterImagesChange={handleAfterImagesUpload}
                isCompleted={selectedProject.status === 'completed'}
              />
            </div>
          </div>
        </div>
      )}
    </MainLayout>
  );
};

export default ProjectsManagementPage;
