'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import StarRating from '@/components/ui/StarRating';
import ReviewCard from '@/components/ui/ReviewCard';
import AddReview from '@/components/ui/AddReview';

const TestReviewsPage = () => {
  const [reviews, setReviews] = useState([
    {
      id: '1',
      rating: 5,
      comment: 'عمل ممتاز ودقيق. الحرفي محترف جداً والتزم بالمواعيد المحددة. النتيجة فاقت توقعاتي بكثير. أنصح بالتعامل معه.',
      reviewerName: 'أحمد محمد',
      projectTitle: 'تجديد المطبخ',
      createdAt: '2024-02-01',
      helpful: 12
    },
    {
      id: '2',
      rating: 4,
      comment: 'عمل جيد بشكل عام. هناك بعض التفاصيل الصغيرة التي كان يمكن تحسينها، لكن النتيجة النهائية مرضية.',
      reviewerName: 'فاطمة أحمد',
      projectTitle: 'إصلاح السباكة',
      createdAt: '2024-01-28',
      helpful: 8
    },
    {
      id: '3',
      rating: 3,
      comment: 'العمل متوسط. تأخر قليلاً عن الموعد المحدد ولكن النتيجة مقبولة. السعر مناسب.',
      reviewerName: 'محمد علي',
      projectTitle: 'دهان الشقة',
      createdAt: '2024-01-25',
      helpful: 3
    }
  ]);

  const [testRating, setTestRating] = useState(0);

  const handleAddReview = (newReview: { rating: number; comment: string }) => {
    const review = {
      id: Date.now().toString(),
      rating: newReview.rating,
      comment: newReview.comment,
      reviewerName: 'مستخدم تجريبي',
      projectTitle: 'مشروع تجريبي',
      createdAt: new Date().toISOString(),
      helpful: 0
    };

    setReviews(prev => [review, ...prev]);
    alert('تم إضافة التقييم بنجاح!');
  };

  const averageRating = reviews.length > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
    : 0;

  const ratingDistribution = [5, 4, 3, 2, 1].map(rating => ({
    rating,
    count: reviews.filter(r => r.rating === rating).length,
    percentage: reviews.length > 0 
      ? (reviews.filter(r => r.rating === rating).length / reviews.length) * 100 
      : 0
  }));

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-navy mb-4">اختبار نظام التقييمات</h1>
              <p className="text-gray-600">اختبر مكونات التقييمات والتعليقات</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column - Components Test */}
              <div className="lg:col-span-1 space-y-6">
                {/* Star Rating Test */}
                <Card>
                  <CardHeader>
                    <CardTitle>اختبار تقييم النجوم</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <p className="text-sm font-medium text-gray-700 mb-2">تفاعلي:</p>
                      <StarRating
                        rating={testRating}
                        onRatingChange={setTestRating}
                        size="lg"
                        showValue
                      />
                    </div>

                    <div>
                      <p className="text-sm font-medium text-gray-700 mb-2">للقراءة فقط - أحجام مختلفة:</p>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <StarRating rating={4.5} readonly size="sm" />
                          <span className="text-sm">صغير</span>
                        </div>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <StarRating rating={4.5} readonly size="md" />
                          <span className="text-sm">متوسط</span>
                        </div>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <StarRating rating={4.5} readonly size="lg" showValue />
                          <span className="text-sm">كبير</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Rating Summary */}
                <Card>
                  <CardHeader>
                    <CardTitle>ملخص التقييمات</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-center mb-6">
                      <div className="text-4xl font-bold text-navy mb-2">
                        {averageRating.toFixed(1)}
                      </div>
                      <StarRating rating={averageRating} readonly size="lg" />
                      <p className="text-gray-600 mt-2">
                        {reviews.length} تقييم
                      </p>
                    </div>

                    <div className="space-y-2">
                      {ratingDistribution.map(({ rating, count, percentage }) => (
                        <div key={rating} className="flex items-center space-x-2 space-x-reverse">
                          <span className="text-sm w-8">{rating} ⭐</span>
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-yellow-400 h-2 rounded-full"
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                          <span className="text-sm text-gray-600 w-8">{count}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Column - Reviews */}
              <div className="lg:col-span-2 space-y-6">
                {/* Add Review */}
                <AddReview onSubmit={handleAddReview} />

                {/* Reviews List */}
                <div>
                  <h2 className="text-2xl font-bold text-navy mb-6">
                    التقييمات ({reviews.length})
                  </h2>
                  
                  <div className="space-y-4">
                    {reviews.map((review) => (
                      <ReviewCard
                        key={review.id}
                        review={review}
                        showProject={true}
                      />
                    ))}
                  </div>

                  {reviews.length === 0 && (
                    <Card>
                      <CardContent className="p-12 text-center">
                        <div className="text-6xl mb-4">⭐</div>
                        <h3 className="text-xl font-semibold text-navy mb-2">
                          لا توجد تقييمات بعد
                        </h3>
                        <p className="text-gray-600">
                          كن أول من يقيم هذا الحرفي
                        </p>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            </div>

            {/* Usage Examples */}
            <div className="mt-12">
              <Card>
                <CardHeader>
                  <CardTitle>أمثلة الاستخدام</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center p-6 bg-gray-50 rounded-lg">
                      <div className="text-3xl mb-3">👨‍🔧</div>
                      <h3 className="font-semibold text-navy mb-2">تقييم الحرفيين</h3>
                      <p className="text-sm text-gray-600">
                        تقييم الحرفيين بعد إنجاز المشاريع
                      </p>
                    </div>

                    <div className="text-center p-6 bg-gray-50 rounded-lg">
                      <div className="text-3xl mb-3">👤</div>
                      <h3 className="font-semibold text-navy mb-2">تقييم العملاء</h3>
                      <p className="text-sm text-gray-600">
                        تقييم العملاء من قبل الحرفيين
                      </p>
                    </div>

                    <div className="text-center p-6 bg-gray-50 rounded-lg">
                      <div className="text-3xl mb-3">🏗️</div>
                      <h3 className="font-semibold text-navy mb-2">تقييم المشاريع</h3>
                      <p className="text-sm text-gray-600">
                        تقييم جودة المشاريع المنجزة
                      </p>
                    </div>
                  </div>

                  <div className="mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 className="font-semibold text-navy mb-3">📋 ميزات نظام التقييمات:</h3>
                    <ul className="text-sm text-gray-700 space-y-1">
                      <li>• تقييم بالنجوم من 1 إلى 5</li>
                      <li>• تعليقات نصية مفصلة</li>
                      <li>• عرض متوسط التقييمات</li>
                      <li>• توزيع التقييمات بالرسوم البيانية</li>
                      <li>• إمكانية الإعجاب بالتقييمات</li>
                      <li>• الرد على التقييمات</li>
                      <li>• الإبلاغ عن التقييمات غير المناسبة</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default TestReviewsPage;
