import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// PUT /api/notifications/[id] - تحديث إشعار
export async function PUT(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { isRead } = body;

    const { id } = context.params;
    const notification = await prisma.notification.update({
      where: { id },
      data: {
        isRead: isRead !== undefined ? isRead : true,
      },
    });

    return NextResponse.json(notification);
  } catch (error) {
    console.error('Error updating notification:', error);
    return NextResponse.json(
      { error: 'خطأ في تحديث الإشعار' },
      { status: 500 }
    );
  }
}

// DELETE /api/notifications/[id] - حذف إشعار
export async function DELETE(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const { id } = context.params;
    await prisma.notification.delete({
      where: { id },
    });

    return NextResponse.json({ message: 'تم حذف الإشعار بنجاح' });
  } catch (error) {
    console.error('Error deleting notification:', error);
    return NextResponse.json(
      { error: 'خطأ في حذف الإشعار' },
      { status: 500 }
    );
  }
}
