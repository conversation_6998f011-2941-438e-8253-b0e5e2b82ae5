-- إنشاء جدول العروض (Bids)
CREATE TABLE IF NOT EXISTS bids (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  craftsman_id UUID REFERENCES users(id) ON DELETE CASCADE,
  amount INTEGER NOT NULL,
  description TEXT NOT NULL,
  estimated_duration VARCHAR(100),
  materials_included BOOLEAN DEFAULT false,
  warranty_period VARCHAR(100),
  start_date DATE,
  status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'ACCEPTED', 'REJECTED', 'WITHDRAWN')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX IF NOT EXISTS idx_bids_project_id ON bids(project_id);
CREATE INDEX IF NOT EXISTS idx_bids_craftsman_id ON bids(craftsman_id);
CREATE INDEX IF NOT EXISTS idx_bids_status ON bids(status);
CREATE INDEX IF NOT EXISTS idx_bids_created_at ON bids(created_at);

-- إنشاء trigger لتحديث updated_at تلقائياً
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_bids_updated_at 
    BEFORE UPDATE ON bids 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- إضافة بيانات تجريبية للعروض
INSERT INTO bids (project_id, craftsman_id, amount, description, estimated_duration, materials_included, warranty_period, start_date, status) VALUES
(
  (SELECT id FROM projects LIMIT 1),
  (SELECT id FROM users WHERE role = 'CRAFTSMAN' LIMIT 1),
  75000,
  'أستطيع تنفيذ هذا المشروع بجودة عالية وفي الوقت المحدد. لدي خبرة 10 سنوات في هذا المجال وسأستخدم أفضل المواد.',
  '7-10 أيام',
  true,
  '6 أشهر',
  CURRENT_DATE + INTERVAL '3 days',
  'PENDING'
),
(
  (SELECT id FROM projects LIMIT 1),
  (SELECT id FROM users WHERE role = 'CRAFTSMAN' LIMIT 1 OFFSET 1),
  85000,
  'عرض متميز مع ضمان شامل. سأقوم بتنفيذ العمل وفقاً لأعلى المعايير مع متابعة دورية.',
  '5-7 أيام',
  true,
  '12 شهر',
  CURRENT_DATE + INTERVAL '1 day',
  'PENDING'
);

-- إنشاء view لعرض العروض مع تفاصيل المشروع والحرفي
CREATE OR REPLACE VIEW bids_with_details AS
SELECT 
  b.*,
  p.title as project_title,
  p.category as project_category,
  p.location as project_location,
  u.name as craftsman_name,
  u.email as craftsman_email,
  u.phone as craftsman_phone,
  c.name as client_name,
  c.email as client_email
FROM bids b
JOIN projects p ON b.project_id = p.id
JOIN users u ON b.craftsman_id = u.id
JOIN users c ON p.client_id = c.id;

-- إنشاء دالة للحصول على عروض مشروع معين
CREATE OR REPLACE FUNCTION get_project_bids(project_uuid UUID)
RETURNS TABLE (
  bid_id UUID,
  craftsman_id UUID,
  craftsman_name VARCHAR,
  amount INTEGER,
  description TEXT,
  estimated_duration VARCHAR,
  materials_included BOOLEAN,
  warranty_period VARCHAR,
  start_date DATE,
  status VARCHAR,
  created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    b.id,
    b.craftsman_id,
    u.name,
    b.amount,
    b.description,
    b.estimated_duration,
    b.materials_included,
    b.warranty_period,
    b.start_date,
    b.status,
    b.created_at
  FROM bids b
  JOIN users u ON b.craftsman_id = u.id
  WHERE b.project_id = project_uuid
  ORDER BY b.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- إنشاء دالة لقبول عرض (وإلغاء باقي العروض)
CREATE OR REPLACE FUNCTION accept_bid(bid_uuid UUID)
RETURNS BOOLEAN AS $$
DECLARE
  project_uuid UUID;
  craftsman_uuid UUID;
BEGIN
  -- الحصول على معرف المشروع والحرفي
  SELECT project_id, craftsman_id INTO project_uuid, craftsman_uuid
  FROM bids WHERE id = bid_uuid;
  
  -- تحديث حالة العرض المقبول
  UPDATE bids SET status = 'ACCEPTED' WHERE id = bid_uuid;
  
  -- رفض باقي العروض للمشروع نفسه
  UPDATE bids SET status = 'REJECTED' 
  WHERE project_id = project_uuid AND id != bid_uuid;
  
  -- تحديث حالة المشروع وتعيين الحرفي
  UPDATE projects 
  SET status = 'IN_PROGRESS', craftsman_id = craftsman_uuid
  WHERE id = project_uuid;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql;
