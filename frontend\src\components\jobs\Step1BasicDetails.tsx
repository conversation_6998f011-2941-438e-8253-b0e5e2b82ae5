'use client';

import React, { useState, useEffect } from 'react';
import { useJobForm } from '@/context/JobFormContext';
import ImageUpload from '../ui/ImageUpload';
import Dropdown from '../ui/Dropdown';

const categories = [
  { value: 'carpentry', label: 'النجارة', icon: '🪚' },
  { value: 'plumbing', label: 'السباكة', icon: '🔧' },
  { value: 'electrical', label: 'الكهرباء', icon: '⚡' },
  { value: 'painting', label: 'الدهان', icon: '🖌️' },
  { value: 'construction', label: 'البناء', icon: '🧱' },
  { value: 'hvac', label: 'التكييف', icon: '❄️' },
  { value: 'metalwork', label: 'الحدادة', icon: '🔨' },
  { value: 'landscaping', label: 'تنسيق الحدائق', icon: '🌱' },
  { value: 'other', label: 'أخرى', icon: '🔧' },
];

const Step1BasicDetails = () => {
  const { formData, updateFormData } = useJobForm();
  const [errors, setErrors] = useState({
    title: '',
    description: '',
    category: '',
  });

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      title: '',
      description: '',
      category: '',
    };

    if (!formData.title || !formData.title.trim()) {
      newErrors.title = 'عنوان المشروع مطلوب';
      isValid = false;
    } else if (formData.title.length < 5) {
      newErrors.title = 'عنوان المشروع يجب أن يكون 5 أحرف على الأقل';
      isValid = false;
    }

    if (!formData.description || !formData.description.trim()) {
      newErrors.description = 'وصف المشروع مطلوب';
      isValid = false;
    } else if (formData.description.length < 20) {
      newErrors.description = 'وصف المشروع يجب أن يكون 20 حرف على الأقل';
      isValid = false;
    }

    if (!formData.category) {
      newErrors.category = 'فئة المشروع مطلوبة';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  useEffect(() => {
    // Add validation to parent form
    const parentForm = document.getElementById('job-form');
    if (parentForm) {
      const originalSubmit = parentForm.onsubmit;
      parentForm.onsubmit = (e) => {
        if (!validateForm()) {
          e.preventDefault();
          return false;
        }
        if (originalSubmit) {
          return originalSubmit.call(parentForm, e);
        }
      };
    }
  }, [formData]);

  return (
    <div>
      <h2 className="text-xl font-bold text-navy mb-6">التفاصيل الأساسية للمشروع</h2>

      <div className="space-y-6">
        <div>
          <label htmlFor="title" className="block text-gray-700 font-medium mb-2">
            عنوان المشروع <span className="text-red-500">*</span>
          </label>
          <input
            type="text"
            id="title"
            value={formData.title}
            onChange={(e) => updateFormData({ title: e.target.value })}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
            placeholder="مثال: تصميم وتنفيذ خزائن مطبخ خشبية"
          />
          {errors.title && <p className="mt-1 text-red-500 text-sm">{errors.title}</p>}
        </div>

        <div>
          <Dropdown
            label="فئة المشروع"
            options={categories}
            value={formData.category}
            onChange={(value) => updateFormData({ category: value })}
            placeholder="اختر فئة المشروع"
            required={true}
            error={errors.category}
          />
        </div>

        <div>
          <label htmlFor="location" className="block text-gray-700 font-medium mb-2">
            الموقع
          </label>
          <input
            type="text"
            id="location"
            value={formData.location}
            onChange={(e) => updateFormData({ location: e.target.value })}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
            placeholder="مثال: دمشق، المزة"
          />
        </div>

        <div>
          <label htmlFor="description" className="block text-gray-700 font-medium mb-2">
            وصف المشروع <span className="text-red-500">*</span>
          </label>
          <textarea
            id="description"
            value={formData.description}
            onChange={(e) => updateFormData({ description: e.target.value })}
            rows={5}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
            placeholder="اشرح تفاصيل مشروعك بوضوح..."
          ></textarea>
          {errors.description && <p className="mt-1 text-red-500 text-sm">{errors.description}</p>}
        </div>

        {/* رفع صور المشروع */}
        <div>
          <ImageUpload
            onImagesChange={(images) => updateFormData({ images })}
            maxImages={5}
            label="صور المشروع"
            description="أضف صور واضحة تظهر المشكلة أو المنطقة التي تحتاج للعمل عليها. هذا سيساعد الحرفيين على فهم المطلوب بشكل أفضل."
            required={false}
          />
        </div>
      </div>
    </div>
  );
};

export default Step1BasicDetails;
