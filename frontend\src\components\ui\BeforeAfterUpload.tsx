'use client';

import React, { useState } from 'react';
import { Button } from './Button';
import ImageUpload from './ImageUpload';

interface BeforeAfterUploadProps {
  onBeforeImagesChange: (images: File[]) => void;
  onAfterImagesChange: (images: File[]) => void;
  projectTitle: string;
  isCompleted?: boolean;
}

const BeforeAfterUpload: React.FC<BeforeAfterUploadProps> = ({
  onBeforeImagesChange,
  onAfterImagesChange,
  projectTitle,
  isCompleted = false
}) => {
  const [beforeImages, setBeforeImages] = useState<File[]>([]);
  const [afterImages, setAfterImages] = useState<File[]>([]);
  const [currentStep, setCurrentStep] = useState<'before' | 'after'>('before');

  const handleBeforeImagesChange = (images: File[]) => {
    setBeforeImages(images);
    onBeforeImagesChange(images);
  };

  const handleAfterImagesChange = (images: File[]) => {
    setAfterImages(images);
    onAfterImagesChange(images);
  };

  const canProceedToAfter = beforeImages.length > 0;
  const canComplete = beforeImages.length > 0 && afterImages.length > 0;

  return (
    <div className="bg-white rounded-2xl shadow-lg p-6">
      <div className="mb-6">
        <h3 className="text-xl font-bold text-navy mb-2">توثيق المشروع: {projectTitle}</h3>
        <p className="text-gray-600">
          قم برفع صور قبل وبعد العمل لتوثيق جودة عملك وبناء معرض أعمالك
        </p>
      </div>

      {/* مؤشر التقدم */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div className={`flex items-center ${currentStep === 'before' ? 'text-teal' : beforeImages.length > 0 ? 'text-green-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
              currentStep === 'before' ? 'bg-teal text-white' : 
              beforeImages.length > 0 ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-600'
            }`}>
              {beforeImages.length > 0 ? '✓' : '1'}
            </div>
            <span className="mr-2 font-medium">صور قبل العمل</span>
          </div>
          
          <div className={`flex-1 h-1 mx-4 rounded ${beforeImages.length > 0 ? 'bg-green-600' : 'bg-gray-200'}`}></div>
          
          <div className={`flex items-center ${currentStep === 'after' ? 'text-teal' : afterImages.length > 0 ? 'text-green-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
              currentStep === 'after' ? 'bg-teal text-white' : 
              afterImages.length > 0 ? 'bg-green-600 text-white' : 'bg-gray-200 text-gray-600'
            }`}>
              {afterImages.length > 0 ? '✓' : '2'}
            </div>
            <span className="mr-2 font-medium">صور بعد العمل</span>
          </div>
        </div>
      </div>

      {/* رفع صور قبل العمل */}
      {currentStep === 'before' && (
        <div className="space-y-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-blue-600 mt-0.5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h4 className="font-semibold text-blue-800 mb-1">صور قبل البدء بالعمل</h4>
                <p className="text-blue-700 text-sm">
                  التقط صور واضحة تظهر حالة المكان أو الشيء قبل البدء بالعمل. هذا سيساعد في إظهار الفرق والتطور في عملك.
                </p>
              </div>
            </div>
          </div>

          <ImageUpload
            onImagesChange={handleBeforeImagesChange}
            maxImages={5}
            label="صور قبل العمل"
            description="التقط صور من زوايا مختلفة تظهر الحالة الأولية"
            required={true}
          />

          <div className="flex justify-end">
            <Button
              onClick={() => setCurrentStep('after')}
              disabled={!canProceedToAfter}
              className="bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90"
            >
              التالي: صور بعد العمل
            </Button>
          </div>
        </div>
      )}

      {/* رفع صور بعد العمل */}
      {currentStep === 'after' && (
        <div className="space-y-6">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-green-600 mt-0.5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <div>
                <h4 className="font-semibold text-green-800 mb-1">صور بعد إنجاز العمل</h4>
                <p className="text-green-700 text-sm">
                  التقط صور تظهر النتيجة النهائية لعملك. تأكد من أن الصور واضحة وتظهر جودة العمل المنجز.
                </p>
              </div>
            </div>
          </div>

          <ImageUpload
            onImagesChange={handleAfterImagesChange}
            maxImages={5}
            label="صور بعد العمل"
            description="التقط صور من نفس الزوايا لإظهار الفرق بوضوح"
            required={true}
          />

          <div className="flex justify-between">
            <Button
              variant="outline"
              onClick={() => setCurrentStep('before')}
            >
              العودة للخطوة السابقة
            </Button>
            
            <Button
              disabled={!canComplete}
              className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800"
            >
              {isCompleted ? 'حفظ التوثيق' : 'إنهاء المشروع'}
            </Button>
          </div>
        </div>
      )}

      {/* ملخص الصور المرفوعة */}
      {(beforeImages.length > 0 || afterImages.length > 0) && (
        <div className="mt-8 pt-6 border-t border-gray-200">
          <h4 className="font-semibold text-gray-700 mb-4">ملخص الصور المرفوعة:</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="font-medium text-blue-800">صور قبل العمل</div>
              <div className="text-blue-600">{beforeImages.length} صور</div>
            </div>
            <div className="bg-green-50 p-3 rounded-lg">
              <div className="font-medium text-green-800">صور بعد العمل</div>
              <div className="text-green-600">{afterImages.length} صور</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BeforeAfterUpload;
