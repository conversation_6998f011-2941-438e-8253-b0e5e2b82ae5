import supabase from './supabase';

interface CraftsmanScore {
  craftsman: any;
  score: number;
  reasons: string[];
}

interface RecommendationCriteria {
  projectCategory: string;
  projectLocation: string;
  budgetMin: number;
  budgetMax: number;
  urgency?: 'low' | 'medium' | 'high';
}

/**
 * خوارزمية اقتراح الحرفيين المناسبين للمشروع
 * تعتمد على عدة عوامل: الموقع، التخصص، التقييم، الخبرة، التوفر
 */
export async function recommendCraftsmen(criteria: RecommendationCriteria): Promise<CraftsmanScore[]> {
  try {
    // جلب جميع الحرفيين
    const { data: craftsmen, error } = await supabase
      .from('users')
      .select('*')
      .eq('role', 'CRAFTSMAN');

    if (error) throw error;

    if (!craftsmen || craftsmen.length === 0) {
      return [];
    }

    // حساب النقاط لكل حرفي
    const scoredCraftsmen: CraftsmanScore[] = [];

    for (const craftsman of craftsmen) {
      const score = await calculateCraftsmanScore(craftsman, criteria);
      if (score.score > 0) {
        scoredCraftsmen.push(score);
      }
    }

    // ترتيب الحرفيين حسب النقاط (الأعلى أولاً)
    return scoredCraftsmen.sort((a, b) => b.score - a.score);

  } catch (error) {
    console.error('Error in craftsman recommendation:', error);
    return [];
  }
}

/**
 * حساب نقاط الحرفي بناءً على معايير المشروع
 */
async function calculateCraftsmanScore(
  craftsman: any, 
  criteria: RecommendationCriteria
): Promise<CraftsmanScore> {
  let score = 0;
  const reasons: string[] = [];

  // 1. تطابق الموقع (40 نقطة كحد أقصى)
  const locationScore = calculateLocationScore(craftsman.location, criteria.projectLocation);
  score += locationScore;
  if (locationScore > 0) {
    if (locationScore >= 35) {
      reasons.push('نفس المدينة');
    } else if (locationScore >= 20) {
      reasons.push('مدينة قريبة');
    } else {
      reasons.push('في نفس المنطقة');
    }
  }

  // 2. تطابق التخصص (30 نقطة كحد أقصى)
  const categoryScore = calculateCategoryScore(craftsman, criteria.projectCategory);
  score += categoryScore;
  if (categoryScore > 0) {
    reasons.push('متخصص في المجال');
  }

  // 3. التقييم والسمعة (20 نقطة كحد أقصى)
  const ratingScore = await calculateRatingScore(craftsman.id);
  score += ratingScore;
  if (ratingScore >= 15) {
    reasons.push('تقييم ممتاز');
  } else if (ratingScore >= 10) {
    reasons.push('تقييم جيد');
  }

  // 4. الخبرة والمشاريع المكتملة (15 نقطة كحد أقصى)
  const experienceScore = await calculateExperienceScore(craftsman.id);
  score += experienceScore;
  if (experienceScore >= 10) {
    reasons.push('خبرة واسعة');
  } else if (experienceScore >= 5) {
    reasons.push('خبرة جيدة');
  }

  // 5. التوفر وسرعة الاستجابة (10 نقاط كحد أقصى)
  const availabilityScore = await calculateAvailabilityScore(craftsman.id);
  score += availabilityScore;
  if (availabilityScore >= 8) {
    reasons.push('متاح فوراً');
  } else if (availabilityScore >= 5) {
    reasons.push('متاح قريباً');
  }

  // 6. مطابقة الميزانية (10 نقاط كحد أقصى)
  const budgetScore = calculateBudgetScore(craftsman, criteria.budgetMin, criteria.budgetMax);
  score += budgetScore;
  if (budgetScore >= 8) {
    reasons.push('أسعار مناسبة');
  }

  // 7. عوامل إضافية (5 نقاط)
  if (craftsman.verified) {
    score += 3;
    reasons.push('حساب موثق');
  }
  
  if (craftsman.premium) {
    score += 2;
    reasons.push('عضوية مميزة');
  }

  return {
    craftsman,
    score: Math.round(score),
    reasons
  };
}

/**
 * حساب نقاط الموقع
 */
function calculateLocationScore(craftsmanLocation: string, projectLocation: string): number {
  if (!craftsmanLocation || !projectLocation) return 0;

  const craftsmanLoc = craftsmanLocation.toLowerCase().trim();
  const projectLoc = projectLocation.toLowerCase().trim();

  // نفس المدينة بالضبط
  if (craftsmanLoc === projectLoc) return 40;

  // نفس المدينة (مع اختلاف في الكتابة)
  if (craftsmanLoc.includes(projectLoc) || projectLoc.includes(craftsmanLoc)) return 35;

  // مدن سورية رئيسية - حساب المسافة التقريبية
  const syrianCities: { [key: string]: string[] } = {
    'دمشق': ['ريف دمشق', 'جرمانا', 'صحنايا', 'دوما', 'داريا'],
    'حلب': ['ريف حلب', 'عفرين', 'اعزاز', 'منبج'],
    'حمص': ['ريف حمص', 'تدمر', 'القصير'],
    'حماة': ['ريف حماة', 'سلمية', 'مصياف'],
    'اللاذقية': ['ريف اللاذقية', 'جبلة', 'القرداحة'],
    'طرطوس': ['ريف طرطوس', 'بانياس', 'صافيتا'],
    'درعا': ['ريف درعا', 'ازرع', 'الصنمين'],
    'السويداء': ['ريف السويداء', 'شهبا', 'صلخد'],
    'القنيطرة': ['ريف القنيطرة'],
    'دير الزور': ['ريف دير الزور', 'الميادين', 'البوكمال'],
    'الرقة': ['ريف الرقة', 'تل أبيض'],
    'الحسكة': ['ريف الحسكة', 'القامشلي', 'رأس العين'],
    'إدلب': ['ريف إدلب', 'جسر الشغور', 'معرة النعمان']
  };

  // البحث عن المدن في نفس المحافظة
  for (const [mainCity, suburbs] of Object.entries(syrianCities)) {
    const isProjectInProvince = projectLoc.includes(mainCity.toLowerCase()) || 
                               suburbs.some(suburb => projectLoc.includes(suburb.toLowerCase()));
    const isCraftsmanInProvince = craftsmanLoc.includes(mainCity.toLowerCase()) || 
                                 suburbs.some(suburb => craftsmanLoc.includes(suburb.toLowerCase()));
    
    if (isProjectInProvince && isCraftsmanInProvince) return 25;
  }

  // مدن متجاورة (مثل دمشق وحمص)
  const neighboringCities = [
    ['دمشق', 'حمص'],
    ['حلب', 'حماة'],
    ['اللاذقية', 'طرطوس'],
    ['درعا', 'السويداء']
  ];

  for (const [city1, city2] of neighboringCities) {
    if ((craftsmanLoc.includes(city1) && projectLoc.includes(city2)) ||
        (craftsmanLoc.includes(city2) && projectLoc.includes(city1))) {
      return 15;
    }
  }

  return 0;
}

/**
 * حساب نقاط التخصص
 */
function calculateCategoryScore(craftsman: any, projectCategory: string): number {
  if (!projectCategory) return 0;

  // التخصص الرئيسي (إذا كان متوفراً في بيانات الحرفي)
  if (craftsman.specialties && craftsman.specialties.includes(projectCategory)) {
    return 30;
  }

  // تخصصات متقاربة
  const relatedCategories: { [key: string]: string[] } = {
    'نجارة': ['أثاث', 'ديكور', 'خشب'],
    'كهرباء': ['إضاءة', 'تمديدات', 'صيانة كهربائية'],
    'سباكة': ['صرف صحي', 'تسليك', 'تمديدات مياه'],
    'دهان': ['ديكور', 'تشطيبات', 'دهانات'],
    'بناء': ['إنشاءات', 'مقاولات', 'ترميم']
  };

  if (relatedCategories[projectCategory]) {
    for (const related of relatedCategories[projectCategory]) {
      if (craftsman.specialties && craftsman.specialties.includes(related)) {
        return 20;
      }
    }
  }

  return 10; // نقاط أساسية لأي حرفي
}

/**
 * حساب نقاط التقييم
 */
async function calculateRatingScore(craftsmanId: string): Promise<number> {
  try {
    // في المستقبل: جلب التقييمات الحقيقية من قاعدة البيانات
    // const { data: ratings } = await supabase
    //   .from('ratings')
    //   .select('rating')
    //   .eq('craftsman_id', craftsmanId);

    // حالياً: تقييم عشوائي للاختبار
    const mockRating = 4.0 + Math.random() * 1.0; // بين 4.0 و 5.0
    
    if (mockRating >= 4.8) return 20;
    if (mockRating >= 4.5) return 15;
    if (mockRating >= 4.0) return 10;
    if (mockRating >= 3.5) return 5;
    return 0;
  } catch (error) {
    return 0;
  }
}

/**
 * حساب نقاط الخبرة
 */
async function calculateExperienceScore(craftsmanId: string): Promise<number> {
  try {
    // جلب عدد المشاريع المكتملة
    const { data: projects, error } = await supabase
      .from('projects')
      .select('id')
      .eq('craftsman_id', craftsmanId)
      .eq('status', 'COMPLETED');

    if (error) throw error;

    const completedProjects = projects?.length || 0;
    
    if (completedProjects >= 20) return 15;
    if (completedProjects >= 10) return 12;
    if (completedProjects >= 5) return 8;
    if (completedProjects >= 2) return 5;
    return 2;
  } catch (error) {
    return 0;
  }
}

/**
 * حساب نقاط التوفر
 */
async function calculateAvailabilityScore(craftsmanId: string): Promise<number> {
  try {
    // جلب المشاريع النشطة
    const { data: activeProjects, error } = await supabase
      .from('projects')
      .select('id')
      .eq('craftsman_id', craftsmanId)
      .eq('status', 'IN_PROGRESS');

    if (error) throw error;

    const activeProjectsCount = activeProjects?.length || 0;
    
    // كلما قل عدد المشاريع النشطة، زادت النقاط
    if (activeProjectsCount === 0) return 10;
    if (activeProjectsCount === 1) return 7;
    if (activeProjectsCount === 2) return 4;
    return 1;
  } catch (error) {
    return 5; // نقاط افتراضية
  }
}

/**
 * حساب نقاط الميزانية
 */
function calculateBudgetScore(craftsman: any, budgetMin: number, budgetMax: number): number {
  // في المستقبل: مقارنة مع أسعار الحرفي التاريخية
  // حالياً: نقاط ثابتة
  return 8;
}
