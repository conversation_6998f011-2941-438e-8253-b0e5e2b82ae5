'use client';

import React, { useState } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Button } from '../../../components/ui/Button';

// بيانات وهمية للمشاريع (في التطبيق الحقيقي ستأتي من API)
const projectsData = {
  1: {
    id: 1,
    title: 'تجديد مطبخ عصري فاخر',
    category: 'kitchen',
    images: [
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&sat=2',
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&brightness=1.1',
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&contrast=1.2',
      'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&hue=30'
    ],
    beforeImage: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&sat=-1',
    afterImage: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop&sat=2&brightness=1.1',
    craftsman: {
      name: 'محمد النجار',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
      rating: 5,
      experience: '8 سنوات',
      completedProjects: 45
    },
    location: 'دمشق، المزة',
    duration: '14 يوم',
    budget: '350,000 ل.س',
    rating: 5,
    description: 'تحويل مطبخ تقليدي إلى مطبخ عصري بخزائن خشبية فاخرة وجزيرة وسطية مع إضاءة LED مخفية وأجهزة كهربائية حديثة.',
    fullDescription: `
      هذا المشروع يتضمن تجديد كامل للمطبخ بتصميم عصري وأنيق. تم استخدام خشب البلوط الطبيعي للخزائن مع تشطيبات لامعة عالية الجودة.

      المميزات الرئيسية:
      • خزائن خشبية فاخرة بتصميم عصري
      • جزيرة وسطية مع مقاعد بار
      • إضاءة LED مخفية تحت الخزائن
      • أجهزة كهربائية مدمجة حديثة
      • أسطح عمل من الجرانيت الطبيعي
      • نظام تهوية متطور

      تم إنجاز المشروع في الوقت المحدد مع الحفاظ على أعلى معايير الجودة.
    `,
    materials: [
      'خشب البلوط الطبيعي',
      'جرانيت طبيعي للأسطح',
      'إضاءة LED عالية الكفاءة',
      'أجهزة كهربائية من ماركات عالمية',
      'مقابض وإكسسوارات معدنية فاخرة'
    ],
    timeline: [
      { phase: 'التخطيط والتصميم', duration: '2 يوم', status: 'completed' },
      { phase: 'هدم المطبخ القديم', duration: '1 يوم', status: 'completed' },
      { phase: 'أعمال السباكة والكهرباء', duration: '3 أيام', status: 'completed' },
      { phase: 'تركيب الخزائن', duration: '4 أيام', status: 'completed' },
      { phase: 'تركيب الأسطح والإكسسوارات', duration: '2 يوم', status: 'completed' },
      { phase: 'التشطيبات النهائية', duration: '2 يوم', status: 'completed' }
    ]
  },
  2: {
    id: 2,
    title: 'تصميم حمام مودرن',
    category: 'bathroom',
    images: [
      'https://images.unsplash.com/photo-1620626011761-996317b8d101?w=800&h=600&fit=crop',
      'https://images.unsplash.com/photo-1620626011761-996317b8d101?w=800&h=600&fit=crop&sat=2',
      'https://images.unsplash.com/photo-1620626011761-996317b8d101?w=800&h=600&fit=crop&brightness=1.1',
      'https://images.unsplash.com/photo-1620626011761-996317b8d101?w=800&h=600&fit=crop&contrast=1.2'
    ],
    beforeImage: 'https://images.unsplash.com/photo-1620626011761-996317b8d101?w=800&h=600&fit=crop&sat=-1',
    afterImage: 'https://images.unsplash.com/photo-1620626011761-996317b8d101?w=800&h=600&fit=crop&sat=2&brightness=1.1',
    craftsman: {
      name: 'أحمد السباك',
      avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
      rating: 5,
      experience: '6 سنوات',
      completedProjects: 32
    },
    location: 'حلب، الفرقان',
    duration: '10 أيام',
    budget: '180,000 ل.س',
    rating: 5,
    description: 'تجديد حمام كامل بتصميم عصري وتركيبات حديثة مع إضاءة مميزة ومرايا أنيقة.',
    fullDescription: `
      تجديد شامل للحمام بتصميم عصري يجمع بين الأناقة والوظائف العملية.

      المميزات الرئيسية:
      • بلاط بورسلين عالي الجودة
      • تركيبات صحية حديثة
      • إضاءة LED متعددة المستويات
      • مرايا بإطارات أنيقة
      • نظام تهوية متطور
      • خزائن تخزين مدمجة
    `,
    materials: [
      'بلاط بورسلين مقاوم للماء',
      'تركيبات صحية عالية الجودة',
      'إضاءة LED مقاومة للرطوبة',
      'مرايا بإطارات معدنية',
      'إكسسوارات من الستانلس ستيل'
    ],
    timeline: [
      { phase: 'التخطيط والتصميم', duration: '1 يوم', status: 'completed' },
      { phase: 'هدم الحمام القديم', duration: '1 يوم', status: 'completed' },
      { phase: 'أعمال السباكة والكهرباء', duration: '3 أيام', status: 'completed' },
      { phase: 'تركيب البلاط', duration: '3 أيام', status: 'completed' },
      { phase: 'تركيب التركيبات الصحية', duration: '1 يوم', status: 'completed' },
      { phase: 'التشطيبات النهائية', duration: '1 يوم', status: 'completed' }
    ]
  }
};

const ProjectDetailsPage = () => {
  const params = useParams();
  const projectId = params.id as string;
  const project = projectsData[projectId as keyof typeof projectsData];

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showBeforeAfter, setShowBeforeAfter] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');

  if (!project) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-navy mb-4">المشروع غير موجود</h1>
          <Link href="/">
            <Button>العودة للرئيسية</Button>
          </Link>
        </div>
      </div>
    );
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) => (prev + 1) % project.images.length);
  };

  const prevImage = () => {
    setCurrentImageIndex((prev) => (prev - 1 + project.images.length) % project.images.length);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4 space-x-reverse">
            <Link href="/" className="text-teal hover:text-navy transition-colors">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <div>
              <h1 className="text-2xl font-bold text-navy">{project.title}</h1>
              <p className="text-gray-600">{project.location}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Image Gallery */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
              {/* Main Image */}
              <div className="relative h-96 bg-gray-200">
                <img
                  src={project.images[currentImageIndex]}
                  alt={`${project.title} - صورة ${currentImageIndex + 1}`}
                  className="w-full h-full object-cover"
                />

                {/* Navigation Arrows */}
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-navy p-2 rounded-full shadow-lg transition-all duration-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                  </svg>
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-navy p-2 rounded-full shadow-lg transition-all duration-300"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>

                {/* Image Counter */}
                <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                  {currentImageIndex + 1} / {project.images.length}
                </div>
              </div>

              {/* Thumbnail Gallery */}
              <div className="p-4">
                <div className="flex space-x-2 space-x-reverse overflow-x-auto">
                  {project.images.map((image, index) => (
                    <button
                      key={index}
                      onClick={() => setCurrentImageIndex(index)}
                      className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                        currentImageIndex === index
                          ? 'border-teal shadow-lg'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      <img
                        src={image}
                        alt={`صورة مصغرة ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </button>
                  ))}
                </div>
              </div>

              {/* Before/After Toggle */}
              <div className="p-4 border-t">
                <button
                  onClick={() => setShowBeforeAfter(!showBeforeAfter)}
                  className="w-full bg-gradient-to-r from-navy to-teal text-white py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-300"
                >
                  {showBeforeAfter ? 'إخفاء مقارنة قبل/بعد' : 'عرض مقارنة قبل/بعد'}
                </button>

                {showBeforeAfter && (
                  <div className="mt-4 grid grid-cols-2 gap-4">
                    <div className="relative">
                      <img
                        src={project.beforeImage}
                        alt="قبل التجديد"
                        className="w-full h-48 object-cover rounded-lg"
                      />
                      <div className="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 rounded text-sm">
                        قبل
                      </div>
                    </div>
                    <div className="relative">
                      <img
                        src={project.afterImage}
                        alt="بعد التجديد"
                        className="w-full h-48 object-cover rounded-lg"
                      />
                      <div className="absolute top-2 right-2 bg-green-500 text-white px-2 py-1 rounded text-sm">
                        بعد
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Project Info Sidebar */}
          <div className="space-y-6">
            {/* Craftsman Info */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-navy mb-4">الحرفي</h3>
              <div className="flex items-center space-x-4 space-x-reverse mb-4">
                <img
                  src={project.craftsman.avatar}
                  alt={project.craftsman.name}
                  className="w-16 h-16 rounded-full border-2 border-teal"
                />
                <div>
                  <h4 className="font-bold text-navy">{project.craftsman.name}</h4>
                  <div className="flex items-center text-yellow-500 text-sm">
                    {[...Array(5)].map((_, i) => (
                      <svg key={i} className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                    <span className="mr-2 text-gray-600">({project.craftsman.rating})</span>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">الخبرة:</span>
                  <div className="font-semibold text-navy">{project.craftsman.experience}</div>
                </div>
                <div>
                  <span className="text-gray-500">المشاريع:</span>
                  <div className="font-semibold text-navy">{project.craftsman.completedProjects}</div>
                </div>
              </div>
            </div>

            {/* Project Stats */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-navy mb-4">تفاصيل المشروع</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">المدة:</span>
                  <span className="font-semibold text-navy">{project.duration}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">الميزانية:</span>
                  <span className="font-bold text-green-600 text-lg">{project.budget}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">التقييم:</span>
                  <div className="flex items-center">
                    <span className="text-yellow-500 mr-1">⭐</span>
                    <span className="font-semibold text-navy">{project.rating}/5</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">الموقع:</span>
                  <span className="font-semibold text-navy">{project.location}</span>
                </div>
              </div>
            </div>

            {/* Contact Button */}
            <div className="bg-gradient-to-r from-navy to-teal rounded-2xl p-6 text-white">
              <h3 className="text-xl font-bold mb-2">مهتم بمشروع مشابه؟</h3>
              <p className="text-white/90 text-sm mb-4">تواصل مع الحرفي مباشرة</p>
              <Button className="w-full bg-white text-navy hover:bg-gray-100">
                تواصل مع {project.craftsman.name}
              </Button>
            </div>
          </div>
        </div>

        {/* Tabs Section */}
        <div className="mt-12">
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            {/* Tab Headers */}
            <div className="border-b border-gray-200">
              <div className="flex">
                {[
                  { id: 'overview', name: 'نظرة عامة', icon: '📋' },
                  { id: 'materials', name: 'المواد المستخدمة', icon: '🧱' },
                  { id: 'timeline', name: 'الجدول الزمني', icon: '⏰' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-1 px-6 py-4 text-center font-medium transition-all duration-300 ${
                      activeTab === tab.id
                        ? 'bg-gradient-to-r from-navy to-teal text-white'
                        : 'text-gray-600 hover:text-navy hover:bg-gray-50'
                    }`}
                  >
                    <span className="text-lg mr-2">{tab.icon}</span>
                    {tab.name}
                  </button>
                ))}
              </div>
            </div>

            {/* Tab Content */}
            <div className="p-8">
              {activeTab === 'overview' && (
                <div>
                  <h3 className="text-2xl font-bold text-navy mb-6">وصف المشروع</h3>
                  <div className="prose prose-lg max-w-none">
                    <p className="text-gray-600 leading-relaxed mb-4">
                      {project.description}
                    </p>
                    <div className="whitespace-pre-line text-gray-600 leading-relaxed">
                      {project.fullDescription}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === 'materials' && (
                <div>
                  <h3 className="text-2xl font-bold text-navy mb-6">المواد والأدوات المستخدمة</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {project.materials.map((material, index) => (
                      <div
                        key={index}
                        className="flex items-center p-4 bg-gradient-to-r from-navy/5 to-teal/5 rounded-xl border border-navy/10"
                      >
                        <div className="w-3 h-3 bg-gradient-to-r from-navy to-teal rounded-full mr-3"></div>
                        <span className="text-gray-700 font-medium">{material}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {activeTab === 'timeline' && (
                <div>
                  <h3 className="text-2xl font-bold text-navy mb-6">مراحل تنفيذ المشروع</h3>
                  <div className="space-y-4">
                    {project.timeline.map((phase, index) => (
                      <div
                        key={index}
                        className="flex items-center p-4 bg-white border border-gray-200 rounded-xl shadow-sm"
                      >
                        <div className="flex-shrink-0 w-10 h-10 bg-gradient-to-r from-navy to-teal rounded-full flex items-center justify-center text-white font-bold mr-4">
                          {index + 1}
                        </div>
                        <div className="flex-grow">
                          <h4 className="font-semibold text-navy">{phase.phase}</h4>
                          <p className="text-gray-600 text-sm">المدة: {phase.duration}</p>
                        </div>
                        <div className="flex-shrink-0">
                          <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Related Projects */}
        <div className="mt-12">
          <div className="text-center mb-8">
            <h3 className="text-3xl font-bold text-navy mb-4">مشاريع مشابهة</h3>
            <p className="text-gray-600">اكتشف المزيد من المشاريع المميزة</p>
          </div>

          <div className="text-center">
            <Link href="/">
              <Button size="lg" className="bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90">
                عرض جميع المشاريع
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectDetailsPage;
