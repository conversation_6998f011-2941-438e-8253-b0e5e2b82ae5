'use client';

import React, { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import { Button } from '../../../components/ui/Button';
import supabase from '@/lib/supabase';

export default function ProjectDetailsPage() {
  const params = useParams();
  const projectId = params.id as string;
  
  const [project, setProject] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  useEffect(() => {
    loadProject();
  }, [projectId]);

  const loadProject = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          client:users!projects_client_id_fkey(name, email, phone),
          craftsman:users!projects_craftsman_id_fkey(name, email, phone)
        `)
        .eq('id', projectId)
        .single();

      if (error) throw error;
      setProject(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const formatBudget = (min: number, max: number) => {
    if (!min && !max) return 'غير محدد';
    if (!max) return `من ${min.toLocaleString()} ل.س`;
    if (!min) return `حتى ${max.toLocaleString()} ل.س`;
    return `${min.toLocaleString()} - ${max.toLocaleString()} ل.س`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN': return 'bg-green-100 text-green-800';
      case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800';
      case 'COMPLETED': return 'bg-purple-100 text-purple-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'OPEN': return 'مفتوح';
      case 'IN_PROGRESS': return 'قيد التنفيذ';
      case 'COMPLETED': return 'مكتمل';
      case 'CANCELLED': return 'ملغي';
      default: return status;
    }
  };

  // Mock images for now
  const mockImages = [
    'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=800&h=600&fit=crop',
    'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=800&h=600&fit=crop'
  ];

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal mx-auto mb-4"></div>
          <p className="text-gray-600">جاري تحميل المشروع...</p>
        </div>
      </div>
    );
  }

  if (error || !project) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-navy mb-4">
            {error ? 'خطأ في تحميل المشروع' : 'المشروع غير موجود'}
          </h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <Link href="/projects">
            <Button>العودة للمشاريع</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center space-x-4 space-x-reverse">
            <Link href="/projects" className="text-teal hover:text-navy transition-colors">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </Link>
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-navy">{project.title}</h1>
              <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
                <span>📍 {project.location}</span>
                <span>🏷️ {project.category}</span>
                <span className={`px-2 py-1 rounded text-xs ${getStatusColor(project.status)}`}>
                  {getStatusText(project.status)}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            {/* Image Gallery */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden mb-8">
              <div className="relative h-96 bg-gray-200">
                <img
                  src={mockImages[currentImageIndex]}
                  alt={project.title}
                  className="w-full h-full object-cover"
                />
                
                {/* Navigation Arrows */}
                {mockImages.length > 1 && (
                  <>
                    <button
                      onClick={() => setCurrentImageIndex((prev) => (prev - 1 + mockImages.length) % mockImages.length)}
                      className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-navy p-2 rounded-full shadow-lg transition-all duration-300"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                      </svg>
                    </button>
                    <button
                      onClick={() => setCurrentImageIndex((prev) => (prev + 1) % mockImages.length)}
                      className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-navy p-2 rounded-full shadow-lg transition-all duration-300"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </button>
                  </>
                )}

                {/* Image Counter */}
                <div className="absolute bottom-4 left-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                  {currentImageIndex + 1} / {mockImages.length}
                </div>
              </div>

              {/* Thumbnail Gallery */}
              {mockImages.length > 1 && (
                <div className="p-4">
                  <div className="flex space-x-2 space-x-reverse overflow-x-auto">
                    {mockImages.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setCurrentImageIndex(index)}
                        className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-all duration-300 ${
                          currentImageIndex === index
                            ? 'border-teal shadow-lg'
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <img
                          src={image}
                          alt={`صورة مصغرة ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Project Description */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-2xl font-bold text-navy mb-4">وصف المشروع</h3>
              <p className="text-gray-600 leading-relaxed">{project.description}</p>
              
              {project.requirements && (
                <div className="mt-6">
                  <h4 className="text-lg font-semibold text-navy mb-2">المتطلبات:</h4>
                  <p className="text-gray-600">{project.requirements}</p>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Project Info */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-navy mb-4">تفاصيل المشروع</h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">الميزانية:</span>
                  <span className="font-bold text-green-600">
                    {formatBudget(project.budget_min, project.budget_max)}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">الموعد النهائي:</span>
                  <span className="font-semibold text-navy">
                    {new Date(project.deadline).toLocaleDateString('ar-SA')}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">تاريخ النشر:</span>
                  <span className="font-semibold text-navy">
                    {new Date(project.created_at).toLocaleDateString('ar-SA')}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">الأولوية:</span>
                  <span className="font-semibold text-navy">{project.priority}</span>
                </div>
              </div>
            </div>

            {/* Client Info */}
            <div className="bg-white rounded-2xl shadow-lg p-6">
              <h3 className="text-xl font-bold text-navy mb-4">العميل</h3>
              <div className="flex items-center space-x-4 space-x-reverse mb-4">
                <div className="w-12 h-12 bg-gradient-to-r from-teal to-navy rounded-full flex items-center justify-center text-white font-bold">
                  {project.client?.name?.charAt(0) || 'ع'}
                </div>
                <div>
                  <h4 className="font-bold text-navy">{project.client?.name || 'عميل'}</h4>
                  <p className="text-gray-600 text-sm">{project.client?.email}</p>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="space-y-3">
              {project.status === 'OPEN' && (
                <Link href={`/projects/${project.id}/submit-bid`}>
                  <Button className="w-full bg-gradient-to-r from-teal to-navy">
                    تقديم عرض
                  </Button>
                </Link>
              )}
              
              <Link href={`/projects/${project.id}/bids`}>
                <Button variant="outline" className="w-full">
                  عرض العروض المقدمة
                </Button>
              </Link>
              
              <Link href={`/projects/${project.id}/recommended-craftsmen`}>
                <Button variant="outline" className="w-full">
                  الحرفيين المقترحين
                </Button>
              </Link>

              {/* Login prompt for non-authenticated users */}
              <div className="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <p className="text-sm text-blue-800 text-center">
                  <Link href="/auth/login" className="font-medium hover:underline">
                    سجل دخولك
                  </Link>
                  {' '}للتفاعل مع هذا المشروع
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
