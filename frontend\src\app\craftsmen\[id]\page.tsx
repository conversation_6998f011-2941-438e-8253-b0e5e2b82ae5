'use client';

import React, { useState } from 'react';
import { useParams } from 'next/navigation';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import CompletedProject from '@/components/portfolio/CompletedProject';

interface CompletedProject {
  id: number;
  title: string;
  description: string;
  category: string;
  completedAt: string;
  duration: number;
  budget: number;
  beforeImages: string[];
  afterImages: string[];
  skills: string[];
  clientRating: number;
  clientReview: string;
  client: {
    name: string;
    location: string;
  };
}

const CraftsmanProfilePage = () => {
  const params = useParams();
  const [activeTab, setActiveTab] = useState<'about' | 'portfolio' | 'reviews'>('about');

  // بيانات وهمية للحرفي
  const craftsman = {
    id: params.id,
    name: 'محمد النجار',
    profession: 'نجار محترف',
    bio: 'نجار محترف مع خبرة 10 سنوات في تصميم وتنفيذ الأثاث المنزلي والمكتبي. متخصص في المطابخ والخزائن العصرية. أعمل بأحدث التقنيات وأضمن الجودة العالية في جميع أعمالي.',
    location: 'دمشق، المزة',
    rating: 4.9,
    reviewsCount: 47,
    completedJobs: 89,
    hourlyRate: 2500,
    skills: ['نجارة', 'تصميم أثاث', 'تركيب', 'ترميم', 'خزائن مطابخ', 'أثاث مكتبي'],
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    isOnline: true,
    responseTime: 'خلال ساعة',
    joinedDate: '2022-03-15',
    verified: true,
    languages: ['العربية', 'الإنجليزية'],
    workingHours: 'الأحد - الخميس: 8:00 - 18:00'
  };

  // مشاريع مكتملة مع صور قبل وبعد
  const completedProjects: CompletedProject[] = [
    {
      id: 1,
      title: 'تصميم وتنفيذ مطبخ عصري',
      description: 'تصميم وتنفيذ مطبخ عصري بخزائن خشبية عالية الجودة مع جزيرة وسطية وإضاءة LED مدمجة.',
      category: 'مطابخ',
      completedAt: '2024-01-10',
      duration: 14,
      budget: 350000,
      beforeImages: [
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop',
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&crop=entropy&cs=tinysrgb'
      ],
      afterImages: [
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&sat=2',
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&sat=2&crop=entropy'
      ],
      skills: ['نجارة', 'تصميم', 'تركيب'],
      clientRating: 5,
      clientReview: 'عمل ممتاز وجودة عالية. محمد حرفي محترف ومتقن لعمله. أنصح بالتعامل معه.',
      client: {
        name: 'أحمد محمد',
        location: 'دمشق، المزة'
      }
    },
    {
      id: 2,
      title: 'تجديد غرفة نوم كاملة',
      description: 'تجديد وترميم غرفة نوم كاملة مع تصميم خزائن جديدة وتركيب أرضية خشبية.',
      category: 'غرف نوم',
      completedAt: '2023-12-20',
      duration: 10,
      budget: 280000,
      beforeImages: [
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=entropy'
      ],
      afterImages: [
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&sat=2',
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&sat=2&crop=entropy'
      ],
      skills: ['نجارة', 'ترميم', 'تركيب'],
      clientRating: 5,
      clientReview: 'تجاوز توقعاتي بكثير. العمل نظيف ومتقن والنتيجة رائعة.',
      client: {
        name: 'فاطمة أحمد',
        location: 'دمشق، جرمانا'
      }
    },
    {
      id: 3,
      title: 'مكتب منزلي مخصص',
      description: 'تصميم وتنفيذ مكتب منزلي مخصص مع أرفف ووحدات تخزين متعددة.',
      category: 'مكاتب',
      completedAt: '2023-11-15',
      duration: 7,
      budget: 150000,
      beforeImages: [
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=faces',
      ],
      afterImages: [
        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=faces&sat=2',
      ],
      skills: ['نجارة', 'تصميم', 'أثاث مكتبي'],
      clientRating: 4,
      clientReview: 'عمل جيد ولكن كان هناك تأخير بسيط في التسليم.',
      client: {
        name: 'محمد علي',
        location: 'دمشق، المالكي'
      }
    }
  ];

  const reviews = [
    {
      id: 1,
      clientName: 'أحمد محمد',
      rating: 5,
      comment: 'عمل ممتاز وجودة عالية. محمد حرفي محترف ومتقن لعمله.',
      date: '2024-01-12',
      projectTitle: 'تصميم مطبخ عصري'
    },
    {
      id: 2,
      clientName: 'فاطمة أحمد',
      rating: 5,
      comment: 'تجاوز توقعاتي بكثير. العمل نظيف ومتقن والنتيجة رائعة.',
      date: '2023-12-22',
      projectTitle: 'تجديد غرفة نوم'
    },
    {
      id: 3,
      clientName: 'محمد علي',
      rating: 4,
      comment: 'عمل جيد ولكن كان هناك تأخير بسيط في التسليم.',
      date: '2023-11-18',
      projectTitle: 'مكتب منزلي مخصص'
    }
  ];

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-beige via-white to-skyblue/20 py-8">
        <div className="container mx-auto px-4">
          {/* معلومات الحرفي */}
          <Card className="border-0 bg-white/90 backdrop-blur-sm mb-8">
            <CardContent className="p-8">
              <div className="flex flex-col md:flex-row items-start md:items-center gap-6">
                <div className="relative">
                  <img
                    src={craftsman.avatar}
                    alt={craftsman.name}
                    className="w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg"
                  />
                  {craftsman.isOnline && (
                    <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 border-4 border-white rounded-full"></div>
                  )}
                  {craftsman.verified && (
                    <div className="absolute -top-2 -left-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                  )}
                </div>

                <div className="flex-1">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                    <div>
                      <h1 className="text-3xl font-bold text-navy mb-2">{craftsman.name}</h1>
                      <p className="text-lg text-gray-600 mb-2">{craftsman.profession}</p>
                      <div className="flex items-center text-gray-500 mb-2">
                        <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        </svg>
                        {craftsman.location}
                      </div>
                    </div>

                    <div className="text-left">
                      <div className="flex items-center mb-2">
                        <div className="flex items-center">
                          {[...Array(5)].map((_, i) => (
                            <svg
                              key={i}
                              className={`w-5 h-5 ${
                                i < Math.floor(craftsman.rating) ? 'text-yellow-500' : 'text-gray-300'
                              }`}
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                          ))}
                        </div>
                        <span className="mr-2 font-semibold">{craftsman.rating}</span>
                        <span className="text-gray-500">({craftsman.reviewsCount} تقييم)</span>
                      </div>
                      <div className="text-2xl font-bold text-green-600">
                        {craftsman.hourlyRate.toLocaleString()} ل.س/ساعة
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="text-center">
                      <div className="text-xl font-bold text-navy">{craftsman.completedJobs}</div>
                      <div className="text-sm text-gray-600">مشروع مكتمل</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xl font-bold text-navy">{craftsman.reviewsCount}</div>
                      <div className="text-sm text-gray-600">تقييم</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xl font-bold text-navy">10</div>
                      <div className="text-sm text-gray-600">سنوات خبرة</div>
                    </div>
                    <div className="text-center">
                      <div className="text-xl font-bold text-navy">{craftsman.responseTime}</div>
                      <div className="text-sm text-gray-600">وقت الرد</div>
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {craftsman.skills.slice(0, 6).map((skill, index) => (
                      <Badge key={index} variant="outline">
                        {skill}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex space-x-3 space-x-reverse">
                    <Button className="bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90">
                      تواصل معي
                    </Button>
                    <Button variant="outline">
                      احفظ الملف الشخصي
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* التبويبات */}
          <div className="mb-8">
            <div className="flex border-b border-gray-200 bg-white rounded-t-lg">
              {[
                { id: 'about', label: 'نبذة عني', count: null },
                { id: 'portfolio', label: 'معرض الأعمال', count: completedProjects.length },
                { id: 'reviews', label: 'التقييمات', count: reviews.length }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === tab.id
                      ? 'border-teal text-teal'
                      : 'border-transparent text-gray-500 hover:text-gray-700'
                  }`}
                >
                  {tab.label}
                  {tab.count !== null && (
                    <span className="mr-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                      {tab.count}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* محتوى التبويبات */}
          {activeTab === 'about' && (
            <Card className="border-0 bg-white/90 backdrop-blur-sm">
              <CardHeader>
                <CardTitle>نبذة عني</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed mb-6">{craftsman.bio}</p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="font-semibold text-navy mb-3">معلومات إضافية</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">تاريخ الانضمام:</span>
                        <span>مارس 2022</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">اللغات:</span>
                        <span>{craftsman.languages.join(', ')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">ساعات العمل:</span>
                        <span>{craftsman.workingHours}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold text-navy mb-3">المهارات</h4>
                    <div className="flex flex-wrap gap-2">
                      {craftsman.skills.map((skill, index) => (
                        <Badge key={index} variant="secondary">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {activeTab === 'portfolio' && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {completedProjects.map((project) => (
                <CompletedProject
                  key={project.id}
                  project={project}
                  showClientInfo={true}
                />
              ))}
            </div>
          )}

          {activeTab === 'reviews' && (
            <div className="space-y-4">
              {reviews.map((review) => (
                <Card key={review.id} className="border-0 bg-white/90 backdrop-blur-sm">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h4 className="font-semibold text-navy">{review.clientName}</h4>
                        <p className="text-sm text-gray-500">{review.projectTitle}</p>
                      </div>
                      <div className="flex items-center">
                        <div className="flex items-center ml-2">
                          {[...Array(5)].map((_, i) => (
                            <svg
                              key={i}
                              className={`w-4 h-4 ${
                                i < review.rating ? 'text-yellow-500' : 'text-gray-300'
                              }`}
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                            </svg>
                          ))}
                        </div>
                        <span className="text-sm text-gray-500">{review.date}</span>
                      </div>
                    </div>
                    <p className="text-gray-700 italic">"{review.comment}"</p>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
};

export default CraftsmanProfilePage;
