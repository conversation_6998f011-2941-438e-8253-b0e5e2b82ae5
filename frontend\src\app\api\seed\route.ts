import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { hashPassword } from '@/lib/security';
import { UserRole, ProjectStatus, ProjectPriority } from '@prisma/client';

export async function POST() {
  try {
    // التحقق من وجود بيانات مسبقاً
    const existingUsers = await prisma.user.count();
    if (existingUsers > 0) {
      return NextResponse.json(
        { message: 'البيانات موجودة مسبقاً' },
        { status: 400 }
      );
    }

    console.log('🌱 بدء تهيئة قاعدة البيانات...');

    // كلمة مرور افتراضية للحسابات التجريبية
    const defaultPassword = await hashPassword('Test123!@#');

    // إنشاء مستخدمين تجريبيين
    const adminUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'مدير النظام',
        password: defaultPassword,
        role: UserRole.ADMIN,
        location: 'دمشق، سوريا',
        bio: 'مدير منصة دوزان',
        isActive: true,
        isVerified: true,
        emailVerifiedAt: new Date(),
      },
    });

    const clientUser = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'أحمد محمد',
        password: defaultPassword,
        phone: '+963123456789',
        role: UserRole.CLIENT,
        location: 'دمشق - المزة',
        bio: 'عميل يبحث عن حرفيين مؤهلين',
        isActive: true,
        isVerified: true,
        emailVerifiedAt: new Date(),
      },
    });

    const craftsmanUser1 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'محمد النجار',
        password: defaultPassword,
        phone: '+963987654321',
        role: UserRole.CRAFTSMAN,
        location: 'دمشق - كفرسوسة',
        bio: 'نجار محترف مع خبرة 10 سنوات',
        isActive: true,
        isVerified: true,
        emailVerifiedAt: new Date(),
      },
    });

    const craftsmanUser2 = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        name: 'سارة الكهربائية',
        password: defaultPassword,
        phone: '+963555123456',
        role: UserRole.CRAFTSMAN,
        location: 'حلب - الفرقان',
        bio: 'كهربائية متخصصة في الأنظمة الذكية',
        isActive: true,
        isVerified: true,
        emailVerifiedAt: new Date(),
      },
    });

    // إنشاء ملفات الحرفيين
    const craftsmanProfile1 = await prisma.craftsmanProfile.create({
      data: {
        userId: craftsmanUser1.id,
        skills: ['نجارة', 'تركيب خزائن', 'أثاث مخصص'],
        experience: 10,
        hourlyRate: 50,
        availability: 'AVAILABLE',
        workingHours: '8:00 ص - 6:00 م',
        languages: ['العربية', 'الإنجليزية'],
        rating: 4.8,
        totalProjects: 45,
        completedProjects: 42,
      },
    });

    const craftsmanProfile2 = await prisma.craftsmanProfile.create({
      data: {
        userId: craftsmanUser2.id,
        skills: ['كهرباء', 'أنظمة ذكية', 'إضاءة'],
        experience: 7,
        hourlyRate: 45,
        availability: 'AVAILABLE',
        workingHours: '9:00 ص - 5:00 م',
        languages: ['العربية'],
        rating: 4.9,
        totalProjects: 28,
        completedProjects: 26,
      },
    });

    // إنشاء مشاريع تجريبية
    const project1 = await prisma.project.create({
      data: {
        title: 'تجديد المطبخ الرئيسي',
        description: 'تجديد كامل للمطبخ مع تركيب خزائن جديدة وأجهزة حديثة',
        category: 'نجارة',
        budget: '₺6,000 - ₺8,000',
        deadline: new Date('2024-03-15'),
        location: 'دمشق - المزة',
        priority: ProjectPriority.HIGH,
        materials: 'متوفرة جزئياً',
        workType: 'تجديد',
        requirements: 'يفضل استخدام خشب عالي الجودة',
        images: [],
        status: ProjectStatus.OPEN,
        clientId: clientUser.id,
        views: 15,
        featured: true,
      },
    });

    const project2 = await prisma.project.create({
      data: {
        title: 'تركيب نظام كهرباء ذكي',
        description: 'تركيب نظام إضاءة ذكي ومفاتيح تحكم عن بعد',
        category: 'كهرباء',
        budget: '₺4,000 - ₺6,000',
        deadline: new Date('2024-03-10'),
        location: 'دمشق - المالكي',
        priority: ProjectPriority.MEDIUM,
        materials: 'غير متوفرة',
        workType: 'تركيب',
        requirements: 'نظام متوافق مع الهواتف الذكية',
        images: [],
        status: ProjectStatus.OPEN,
        clientId: clientUser.id,
        views: 8,
        featured: false,
      },
    });

    // إنشاء عروض تجريبية
    await prisma.offer.create({
      data: {
        projectId: project1.id,
        craftsmanId: craftsmanUser1.id,
        clientId: clientUser.id,
        price: 7200,
        duration: '14 يوم',
        description: 'أقدم خدمات تجديد المطابخ بأعلى جودة مع ضمان سنة كاملة',
        materials: 'متضمنة',
        warranty: '12 شهر',
        status: 'PENDING',
      },
    });

    await prisma.offer.create({
      data: {
        projectId: project2.id,
        craftsmanId: craftsmanUser2.id,
        clientId: clientUser.id,
        price: 4800,
        duration: '7 أيام',
        description: 'متخصصة في الأنظمة الذكية والتحكم الآلي',
        materials: 'متضمنة',
        warranty: '24 شهر',
        status: 'PENDING',
      },
    });

    // إنشاء إشعارات تجريبية
    await prisma.notification.create({
      data: {
        userId: clientUser.id,
        type: 'OFFER_RECEIVED',
        title: 'عرض جديد على مشروعك',
        message: 'تم استلام عرض جديد من محمد النجار على مشروع تجديد المطبخ',
        actionUrl: '/client/offers',
      },
    });

    console.log('✅ تم إنشاء البيانات التجريبية بنجاح!');

    return NextResponse.json({
      message: 'تم إنشاء البيانات التجريبية بنجاح',
      data: {
        users: 4,
        projects: 2,
        offers: 2,
        notifications: 1,
        accounts: [
          { email: '<EMAIL>', role: 'ADMIN', password: 'Test123!@#' },
          { email: '<EMAIL>', role: 'CLIENT', password: 'Test123!@#' },
          { email: '<EMAIL>', role: 'CRAFTSMAN', password: 'Test123!@#' },
          { email: '<EMAIL>', role: 'CRAFTSMAN', password: 'Test123!@#' },
        ]
      }
    });

  } catch (error) {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', error);
    return NextResponse.json(
      { error: 'خطأ في تهيئة البيانات التجريبية' },
      { status: 500 }
    );
  }
}
