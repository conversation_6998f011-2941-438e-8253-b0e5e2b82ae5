import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password, name, phone, role = 'CLIENT' } = body;

    // التحقق من صحة البيانات
    if (!email || !password || !name) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني وكلمة المرور والاسم مطلوبة' },
        { status: 400 }
      );
    }

    // للتطوير: رفض إنشاء حسابات جديدة مؤقتاً
    return NextResponse.json(
      { error: 'إنشاء الحسابات الجديدة معطل مؤقتاً. استخدم الحسابات التجريبية.' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: 'خطأ في إنشاء الحساب' },
      { status: 500 }
    );
  }
}
