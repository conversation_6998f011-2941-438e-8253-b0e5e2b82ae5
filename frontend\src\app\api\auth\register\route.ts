import { NextRequest, NextResponse } from 'next/server';
import {
  hashPassword,
  validatePasswordStrength,
  validateEmail,
  validateSyrianPhone,
  sanitizeInput,
  generateEmailVerificationToken,
  getClientIP,
  getUserAgent,
  checkRateLimit
} from '@/lib/security';

export async function POST(request: NextRequest) {
  try {
    // التحقق من معدل الطلبات
    const clientIP = getClientIP(request);
    const rateLimit = checkRateLimit(`register_${clientIP}`, 5, 300000); // 5 طلبات كل 5 دقائق

    if (!rateLimit.allowed) {
      return NextResponse.json(
        {
          error: 'تم تجاوز الحد المسموح من المحاولات. يرجى المحاولة بعد 5 دقائق',
          resetTime: rateLimit.resetTime
        },
        { status: 429 }
      );
    }

    const body = await request.json();
    const { name, email, password, phone, role, location, bio } = body;

    // التحقق من البيانات المطلوبة
    if (!name || !email || !password) {
      return NextResponse.json(
        { error: 'الاسم والبريد الإلكتروني وكلمة المرور مطلوبة' },
        { status: 400 }
      );
    }

    // تنظيف المدخلات
    const sanitizedName = sanitizeInput(name);
    const sanitizedEmail = email.toLowerCase().trim();
    const sanitizedPhone = phone ? sanitizeInput(phone) : null;
    const sanitizedLocation = location ? sanitizeInput(location) : null;
    const sanitizedBio = bio ? sanitizeInput(bio) : null;

    // التحقق من صحة البريد الإلكتروني
    if (!validateEmail(sanitizedEmail)) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني غير صحيح' },
        { status: 400 }
      );
    }

    // التحقق من قوة كلمة المرور
    const passwordValidation = validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { error: 'كلمة المرور ضعيفة', details: passwordValidation.errors },
        { status: 400 }
      );
    }

    // التحقق من رقم الهاتف السوري (إذا تم توفيره)
    if (sanitizedPhone && !validateSyrianPhone(sanitizedPhone)) {
      return NextResponse.json(
        { error: 'رقم الهاتف غير صحيح. يجب أن يكون رقم هاتف سوري صحيح' },
        { status: 400 }
      );
    }

    // تشفير كلمة المرور
    const hashedPassword = await hashPassword(password);

    // إنشاء رمز تأكيد البريد الإلكتروني
    const verificationToken = generateEmailVerificationToken();

    // محاكاة إنشاء المستخدم (سيتم استبدالها بقاعدة البيانات الحقيقية)
    const user = {
      id: Date.now().toString(),
      name: sanitizedName,
      email: sanitizedEmail,
      password: hashedPassword,
      phone: sanitizedPhone,
      role: role || 'CLIENT',
      location: sanitizedLocation,
      bio: sanitizedBio,
      isActive: true,
      isVerified: false,
      emailVerificationToken: verificationToken,
      createdAt: new Date().toISOString()
    };

    console.log('تم إنشاء مستخدم جديد:', { ...user, password: '[مخفي]' });
    console.log('رمز تأكيد البريد الإلكتروني:', verificationToken);

    return NextResponse.json({
      message: 'تم إنشاء الحساب بنجاح. يرجى تأكيد بريدك الإلكتروني',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        isVerified: user.isVerified
      },
      verificationToken // في الإنتاج، لن يتم إرسال هذا
    }, { status: 201 });

  } catch (error: any) {
    console.error('خطأ في تسجيل المستخدم:', error);

    return NextResponse.json(
      { error: 'حدث خطأ في الخادم. يرجى المحاولة مرة أخرى' },
      { status: 500 }
    );
  }
}
