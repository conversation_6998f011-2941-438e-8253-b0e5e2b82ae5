import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { hashPassword, createEmailVerificationToken, logAuditAction, isValidEmail, isStrongPassword, sanitizeInput } from '@/lib/security';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, password, name, phone, role = 'CLIENT' } = body;

    // التحقق من صحة البيانات
    if (!email || !password || !name) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني وكلمة المرور والاسم مطلوبة' },
        { status: 400 }
      );
    }

    // التحقق من صحة البريد الإلكتروني
    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني غير صحيح' },
        { status: 400 }
      );
    }

    // التحقق من قوة كلمة المرور
    if (!isStrongPassword(password)) {
      return NextResponse.json(
        {
          error: 'كلمة المرور يجب أن تحتوي على 8 أحرف على الأقل، حرف كبير، حرف صغير، رقم، ورمز خاص'
        },
        { status: 400 }
      );
    }

    // تنظيف البيانات
    const cleanName = sanitizeInput(name);
    const cleanPhone = phone ? sanitizeInput(phone) : null;

    // التحقق من وجود المستخدم
    const existingUser = await prisma.user.findUnique({
      where: { email: email.toLowerCase() },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'المستخدم موجود بالفعل' },
        { status: 400 }
      );
    }

    // تشفير كلمة المرور
    const hashedPassword = await hashPassword(password);

    // إنشاء المستخدم
    const user = await prisma.user.create({
      data: {
        email: email.toLowerCase(),
        password: hashedPassword,
        name: cleanName,
        phone: cleanPhone,
        role: role as 'CLIENT' | 'CRAFTSMAN' | 'ADMIN',
        isActive: true,
        isVerified: false,
      },
    });

    // إنشاء ملف الحرفي إذا كان الدور حرفي
    if (role === 'CRAFTSMAN') {
      await prisma.craftsmanProfile.create({
        data: {
          userId: user.id,
          skills: [],
          experience: 0,
          languages: ['العربية'],
          rating: 0,
          totalProjects: 0,
          completedProjects: 0,
        },
      });
    }

    // إنشاء token تأكيد البريد الإلكتروني
    const verificationToken = await createEmailVerificationToken(user.id);

    // تسجيل العملية في سجل التدقيق
    await logAuditAction(
      user.id,
      'REGISTER',
      'User',
      user.id,
      null,
      { email: user.email, name: user.name, role: user.role },
      request.headers.get('x-forwarded-for') || request.ip,
      request.headers.get('user-agent')
    );

    // إرجاع بيانات المستخدم (بدون كلمة المرور)
    const { password: _, ...userWithoutPassword } = user;

    return NextResponse.json({
      user: userWithoutPassword,
      message: 'تم إنشاء الحساب بنجاح. يرجى تأكيد البريد الإلكتروني.',
      verificationToken, // للتطوير فقط
    }, { status: 201 });

  } catch (error) {
    console.error('Registration error:', error);

    // تسجيل محاولة التسجيل الفاشلة
    await logAuditAction(
      null,
      'FAILED_REGISTER',
      'User',
      null,
      null,
      null,
      request.headers.get('x-forwarded-for') || request.ip,
      request.headers.get('user-agent')
    );

    return NextResponse.json(
      { error: 'خطأ في إنشاء الحساب' },
      { status: 500 }
    );
  }
}
