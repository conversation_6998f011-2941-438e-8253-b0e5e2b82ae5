import { useState, useEffect } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'CLIENT' | 'CRAFTSMAN' | 'ADMIN';
}

export function useSimpleAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (!mounted) return;

    // تحميل المستخدم من localStorage فقط بعد mount
    try {
      const savedUser = localStorage.getItem('user');
      if (savedUser) {
        const userData = JSON.parse(savedUser);
        setUser(userData);
      }
    } catch (error) {
      console.error('Error parsing user data:', error);
      if (typeof window !== 'undefined') {
        localStorage.removeItem('user');
      }
    }
    setLoading(false);
  }, [mounted]);

  const logout = () => {
    setUser(null);
    if (typeof window !== 'undefined') {
      localStorage.removeItem('user');
      window.location.href = '/';
    }
  };

  const login = (userData: User) => {
    setUser(userData);
    if (typeof window !== 'undefined') {
      localStorage.setItem('user', JSON.stringify(userData));
    }
  };

  // Helper functions
  const isClient = user?.role === 'CLIENT';
  const isCraftsman = user?.role === 'CRAFTSMAN';
  const isAdmin = user?.role === 'ADMIN';

  return {
    user,
    loading,
    logout,
    login,
    isAuthenticated: !!user,
    isClient,
    isCraftsman,
    isAdmin
  };
}
