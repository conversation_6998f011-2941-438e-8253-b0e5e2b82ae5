import { useState, useEffect } from 'react';

interface User {
  id: string;
  name: string;
  email: string;
  role: 'CLIENT' | 'CRAFTSMAN' | 'ADMIN';
}

export function useSimpleAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // تحميل المستخدم من localStorage
    const savedUser = localStorage.getItem('user');
    if (savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        setUser(userData);
      } catch (error) {
        console.error('Error parsing user data:', error);
        localStorage.removeItem('user');
      }
    }
    setLoading(false);
  }, []);

  const logout = () => {
    setUser(null);
    localStorage.removeItem('user');
    window.location.href = '/';
  };

  const login = (userData: User) => {
    setUser(userData);
    localStorage.setItem('user', JSON.stringify(userData));
  };

  // Helper functions
  const isClient = user?.role === 'CLIENT';
  const isCraftsman = user?.role === 'CRAFTSMAN';
  const isAdmin = user?.role === 'ADMIN';

  return {
    user,
    loading,
    logout,
    login,
    isAuthenticated: !!user,
    isClient,
    isCraftsman,
    isAdmin
  };
}
