{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/lib/mock-data.ts"], "sourcesContent": ["// بيانات تجريبية للتطوير بدون قاعدة بيانات\n\nexport interface User {\n  id: string;\n  email: string;\n  name: string;\n  role: 'ADMIN' | 'CLIENT' | 'CRAFTSMAN';\n  phone?: string;\n  location?: string;\n  bio?: string;\n  avatar?: string;\n  isActive: boolean;\n  isVerified: boolean;\n  createdAt: Date;\n}\n\nexport interface Project {\n  id: string;\n  title: string;\n  description: string;\n  category: string;\n  budget: string;\n  deadline: Date;\n  location: string;\n  priority: 'LOW' | 'MEDIUM' | 'HIGH';\n  status: 'OPEN' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';\n  clientId: string;\n  craftsmanId?: string;\n  images: string[];\n  materials?: string;\n  workType?: string;\n  requirements?: string;\n  views: number;\n  featured: boolean;\n  createdAt: Date;\n}\n\nexport interface Offer {\n  id: string;\n  projectId: string;\n  craftsmanId: string;\n  amount: number;\n  currency: string;\n  description: string;\n  estimatedDuration: number;\n  status: 'PENDING' | 'ACCEPTED' | 'REJECTED';\n  createdAt: Date;\n}\n\nexport interface Notification {\n  id: string;\n  userId: string;\n  title: string;\n  message: string;\n  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR';\n  isRead: boolean;\n  createdAt: Date;\n}\n\n// بيانات المستخدمين التجريبية\nexport const mockUsers: User[] = [\n  {\n    id: 'admin',\n    email: '<EMAIL>',\n    name: 'مدير النظام',\n    role: 'ADMIN',\n    phone: '+963 11 123 4567',\n    location: 'دمشق، سوريا',\n    bio: 'مدير منصة دوزان',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-01'),\n  },\n  {\n    id: 'client',\n    email: '<EMAIL>',\n    name: 'أحمد محمد',\n    role: 'CLIENT',\n    phone: '+963 123 456 789',\n    location: 'دمشق - المزة',\n    bio: 'عميل يبحث عن حرفيين مؤهلين',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-15'),\n  },\n  {\n    id: 'craftsman1',\n    email: '<EMAIL>',\n    name: 'محمد النجار',\n    role: 'CRAFTSMAN',\n    phone: '+963 987 654 321',\n    location: 'دمشق - كفرسوسة',\n    bio: 'نجار محترف مع خبرة 10 سنوات',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-10'),\n  },\n  {\n    id: 'craftsman2',\n    email: '<EMAIL>',\n    name: 'سارة الكهربائية',\n    role: 'CRAFTSMAN',\n    phone: '+963 555 123 456',\n    location: 'حلب - الفرقان',\n    bio: 'كهربائية متخصصة في الأنظمة الذكية',\n    isActive: true,\n    isVerified: true,\n    createdAt: new Date('2024-01-20'),\n  },\n];\n\n// بيانات المشاريع التجريبية\nexport const mockProjects: Project[] = [\n  {\n    id: 'project1',\n    title: 'تجديد مطبخ منزلي',\n    description: 'أحتاج إلى تجديد مطبخ منزلي بالكامل مع تغيير الخزائن والأرضية',\n    category: 'نجارة',\n    budget: '₺6,000 - ₺8,000',\n    deadline: new Date('2024-03-15'),\n    location: 'دمشق - المزة',\n    priority: 'HIGH',\n    status: 'OPEN',\n    clientId: 'client',\n    images: [],\n    materials: 'متوفرة جزئياً',\n    workType: 'تجديد',\n    requirements: 'يفضل استخدام خشب عالي الجودة',\n    views: 15,\n    featured: true,\n    createdAt: new Date('2024-02-01'),\n  },\n  {\n    id: 'project2',\n    title: 'تركيب نظام إضاءة ذكي',\n    description: 'تركيب نظام إضاءة ذكي في المنزل مع إمكانية التحكم عبر الهاتف',\n    category: 'كهرباء',\n    budget: '₺4,000 - ₺6,000',\n    deadline: new Date('2024-03-10'),\n    location: 'دمشق - المالكي',\n    priority: 'MEDIUM',\n    status: 'OPEN',\n    clientId: 'client',\n    images: [],\n    materials: 'غير متوفرة',\n    workType: 'تركيب',\n    requirements: 'نظام متوافق مع الهواتف الذكية',\n    views: 8,\n    featured: false,\n    createdAt: new Date('2024-02-05'),\n  },\n];\n\n// بيانات العروض التجريبية\nexport const mockOffers: Offer[] = [\n  {\n    id: 'offer1',\n    projectId: 'project1',\n    craftsmanId: 'craftsman1',\n    amount: 7000,\n    currency: 'TRY',\n    description: 'يمكنني تنفيذ هذا المشروع بجودة عالية وفي الوقت المحدد',\n    estimatedDuration: 14,\n    status: 'PENDING',\n    createdAt: new Date('2024-02-02'),\n  },\n  {\n    id: 'offer2',\n    projectId: 'project2',\n    craftsmanId: 'craftsman2',\n    amount: 5000,\n    currency: 'TRY',\n    description: 'متخصصة في أنظمة الإضاءة الذكية مع ضمان سنة كاملة',\n    estimatedDuration: 7,\n    status: 'PENDING',\n    createdAt: new Date('2024-02-06'),\n  },\n];\n\n// بيانات الإشعارات التجريبية\nexport const mockNotifications: Notification[] = [\n  {\n    id: 'notif1',\n    userId: 'client',\n    title: 'عرض جديد على مشروعك',\n    message: 'تلقيت عرضاً جديداً من محمد النجار على مشروع تجديد المطبخ',\n    type: 'INFO',\n    isRead: false,\n    createdAt: new Date('2024-02-02'),\n  },\n  {\n    id: 'notif2',\n    userId: 'craftsman1',\n    title: 'مشروع جديد متاح',\n    message: 'يوجد مشروع نجارة جديد في منطقتك',\n    type: 'INFO',\n    isRead: true,\n    createdAt: new Date('2024-02-01'),\n  },\n];\n\n// دوال مساعدة للبحث والفلترة\nexport const getUserById = (id: string): User | undefined => {\n  return mockUsers.find(user => user.id === id);\n};\n\nexport const getUserByEmail = (email: string): User | undefined => {\n  return mockUsers.find(user => user.email === email);\n};\n\nexport const getProjectsByUserId = (userId: string): Project[] => {\n  return mockProjects.filter(project => \n    project.clientId === userId || project.craftsmanId === userId\n  );\n};\n\nexport const getOffersByProjectId = (projectId: string): Offer[] => {\n  return mockOffers.filter(offer => offer.projectId === projectId);\n};\n\nexport const getOffersByCraftsmanId = (craftsmanId: string): Offer[] => {\n  return mockOffers.filter(offer => offer.craftsmanId === craftsmanId);\n};\n\nexport const getNotificationsByUserId = (userId: string): Notification[] => {\n  return mockNotifications.filter(notification => notification.userId === userId);\n};\n"], "names": [], "mappings": "AAAA,2CAA2C;;;;;;;;;;;;;AA4DpC,MAAM,YAAoB;IAC/B;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;QACP,UAAU;QACV,KAAK;QACL,UAAU;QACV,YAAY;QACZ,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,eAA0B;IACrC;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,EAAE;QACV,WAAW;QACX,UAAU;QACV,cAAc;QACd,OAAO;QACP,UAAU;QACV,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,UAAU;QACV,QAAQ;QACR,UAAU,IAAI,KAAK;QACnB,UAAU;QACV,UAAU;QACV,QAAQ;QACR,UAAU;QACV,QAAQ,EAAE;QACV,WAAW;QACX,UAAU;QACV,cAAc;QACd,OAAO;QACP,UAAU;QACV,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,aAAsB;IACjC;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,UAAU;QACV,aAAa;QACb,mBAAmB;QACnB,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,WAAW;QACX,aAAa;QACb,QAAQ;QACR,UAAU;QACV,aAAa;QACb,mBAAmB;QACnB,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,oBAAoC;IAC/C;QACE,IAAI;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;QACT,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;IACA;QACE,IAAI;QACJ,QAAQ;QACR,OAAO;QACP,SAAS;QACT,MAAM;QACN,QAAQ;QACR,WAAW,IAAI,KAAK;IACtB;CACD;AAGM,MAAM,cAAc,CAAC;IAC1B,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;AAC5C;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;AAC/C;AAEO,MAAM,sBAAsB,CAAC;IAClC,OAAO,aAAa,MAAM,CAAC,CAAA,UACzB,QAAQ,QAAQ,KAAK,UAAU,QAAQ,WAAW,KAAK;AAE3D;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,WAAW,MAAM,CAAC,CAAA,QAAS,MAAM,SAAS,KAAK;AACxD;AAEO,MAAM,yBAAyB,CAAC;IACrC,OAAO,WAAW,MAAM,CAAC,CAAA,QAAS,MAAM,WAAW,KAAK;AAC1D;AAEO,MAAM,2BAA2B,CAAC;IACvC,OAAO,kBAAkB,MAAM,CAAC,CAAA,eAAgB,aAAa,MAAM,KAAK;AAC1E"}}, {"offset": {"line": 220, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/api/projects/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { mockProjects, mockUsers, Project } from '@/lib/mock-data';\n\n// GET /api/projects - جلب جميع المشاريع\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const category = searchParams.get('category');\n    const location = searchParams.get('location');\n    const status = searchParams.get('status');\n    const clientId = searchParams.get('clientId');\n    const craftsmanId = searchParams.get('craftsmanId');\n\n    let filteredProjects = [...mockProjects];\n\n    // تطبيق الفلاتر\n    if (category) {\n      filteredProjects = filteredProjects.filter(p => p.category === category);\n    }\n    if (location) {\n      filteredProjects = filteredProjects.filter(p => p.location.includes(location));\n    }\n    if (status) {\n      filteredProjects = filteredProjects.filter(p => p.status === status);\n    }\n    if (clientId) {\n      filteredProjects = filteredProjects.filter(p => p.clientId === clientId);\n    }\n    if (craftsmanId) {\n      filteredProjects = filteredProjects.filter(p => p.craftsmanId === craftsmanId);\n    }\n\n    // إضافة بيانات العميل لكل مشروع\n    const projectsWithClient = filteredProjects.map(project => {\n      const client = mockUsers.find(user => user.id === project.clientId);\n      const craftsman = project.craftsmanId ? \n        mockUsers.find(user => user.id === project.craftsmanId) : null;\n      \n      return {\n        ...project,\n        client: client ? {\n          id: client.id,\n          name: client.name,\n          location: client.location,\n          avatar: client.avatar\n        } : null,\n        craftsman: craftsman ? {\n          id: craftsman.id,\n          name: craftsman.name,\n          location: craftsman.location,\n          avatar: craftsman.avatar\n        } : null\n      };\n    });\n\n    return NextResponse.json({\n      projects: projectsWithClient,\n      total: projectsWithClient.length\n    });\n  } catch (error) {\n    console.error('Error fetching projects:', error);\n    return NextResponse.json(\n      { error: 'خطأ في جلب المشاريع' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST /api/projects - إنشاء مشروع جديد\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const {\n      title,\n      description,\n      category,\n      budget,\n      deadline,\n      location,\n      priority = 'MEDIUM',\n      clientId,\n      materials,\n      workType,\n      requirements,\n      images = []\n    } = body;\n\n    // التحقق من البيانات المطلوبة\n    if (!title || !description || !category || !budget || !deadline || !location || !clientId) {\n      return NextResponse.json(\n        { error: 'جميع الحقول المطلوبة يجب ملؤها' },\n        { status: 400 }\n      );\n    }\n\n    // التحقق من وجود العميل\n    const client = mockUsers.find(user => user.id === clientId);\n    if (!client || client.role !== 'CLIENT') {\n      return NextResponse.json(\n        { error: 'العميل غير موجود' },\n        { status: 404 }\n      );\n    }\n\n    // إنشاء مشروع جديد\n    const newProject: Project = {\n      id: `project_${Date.now()}`,\n      title,\n      description,\n      category,\n      budget,\n      deadline: new Date(deadline),\n      location,\n      priority,\n      status: 'OPEN',\n      clientId,\n      images,\n      materials,\n      workType,\n      requirements,\n      views: 0,\n      featured: false,\n      createdAt: new Date()\n    };\n\n    // إضافة المشروع للبيانات التجريبية (في التطبيق الحقيقي سيتم حفظه في قاعدة البيانات)\n    mockProjects.push(newProject);\n\n    return NextResponse.json({\n      project: newProject,\n      message: 'تم إنشاء المشروع بنجاح'\n    }, { status: 201 });\n\n  } catch (error) {\n    console.error('Error creating project:', error);\n    return NextResponse.json(\n      { error: 'خطأ في إنشاء المشروع' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,WAAW,aAAa,GAAG,CAAC;QAClC,MAAM,cAAc,aAAa,GAAG,CAAC;QAErC,IAAI,mBAAmB;eAAI,4HAAA,CAAA,eAAY;SAAC;QAExC,gBAAgB;QAChB,IAAI,UAAU;YACZ,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QACjE;QACA,IAAI,UAAU;YACZ,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,CAAC,QAAQ,CAAC;QACtE;QACA,IAAI,QAAQ;YACV,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QAC/D;QACA,IAAI,UAAU;YACZ,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;QACjE;QACA,IAAI,aAAa;YACf,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,IAAK,EAAE,WAAW,KAAK;QACpE;QAEA,gCAAgC;QAChC,MAAM,qBAAqB,iBAAiB,GAAG,CAAC,CAAA;YAC9C,MAAM,SAAS,4HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,QAAQ,QAAQ;YAClE,MAAM,YAAY,QAAQ,WAAW,GACnC,4HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,QAAQ,WAAW,IAAI;YAE5D,OAAO;gBACL,GAAG,OAAO;gBACV,QAAQ,SAAS;oBACf,IAAI,OAAO,EAAE;oBACb,MAAM,OAAO,IAAI;oBACjB,UAAU,OAAO,QAAQ;oBACzB,QAAQ,OAAO,MAAM;gBACvB,IAAI;gBACJ,WAAW,YAAY;oBACrB,IAAI,UAAU,EAAE;oBAChB,MAAM,UAAU,IAAI;oBACpB,UAAU,UAAU,QAAQ;oBAC5B,QAAQ,UAAU,MAAM;gBAC1B,IAAI;YACN;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,UAAU;YACV,OAAO,mBAAmB,MAAM;QAClC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAsB,GAC/B;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,KAAK,EACL,WAAW,EACX,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,WAAW,QAAQ,EACnB,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,YAAY,EACZ,SAAS,EAAE,EACZ,GAAG;QAEJ,8BAA8B;QAC9B,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC,YAAY,CAAC,UAAU;YACzF,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAiC,GAC1C;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,SAAS,4HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,CAAC,UAAU,OAAO,IAAI,KAAK,UAAU;YACvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAmB,GAC5B;gBAAE,QAAQ;YAAI;QAElB;QAEA,mBAAmB;QACnB,MAAM,aAAsB;YAC1B,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,IAAI;YAC3B;YACA;YACA;YACA;YACA,UAAU,IAAI,KAAK;YACnB;YACA;YACA,QAAQ;YACR;YACA;YACA;YACA;YACA;YACA,OAAO;YACP,UAAU;YACV,WAAW,IAAI;QACjB;QAEA,oFAAoF;QACpF,4HAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAElB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX,GAAG;YAAE,QAAQ;QAAI;IAEnB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuB,GAChC;YAAE,QAAQ;QAAI;IAElB;AACF"}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}