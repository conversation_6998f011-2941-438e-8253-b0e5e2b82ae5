import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: 'client' | 'craftsman' | 'admin';
    };
    accessToken?: string;
  }

  interface User {
    id: string;
    email: string;
    name: string;
    role: 'client' | 'craftsman' | 'admin';
    accessToken?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: 'client' | 'craftsman' | 'admin';
    accessToken?: string;
  }
}
