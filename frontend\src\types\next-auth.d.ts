import NextAuth from 'next-auth';

declare module 'next-auth' {
  interface Session {
    user: {
      id: string;
      email: string;
      name: string;
      role: 'CLIENT' | 'CRAFTSMAN' | 'ADMIN';
      isVerified?: boolean;
      avatar?: string;
    };
    accessToken?: string;
  }

  interface User {
    id: string;
    email: string;
    name: string;
    role: 'CLIENT' | 'CRAFTSMAN' | 'ADMIN';
    isVerified?: boolean;
    avatar?: string;
    accessToken?: string;
  }
}

declare module 'next-auth/jwt' {
  interface JWT {
    role: 'CLIENT' | 'CRAFTSMAN' | 'ADMIN';
    isVerified?: boolean;
    accessToken?: string;
  }
}
