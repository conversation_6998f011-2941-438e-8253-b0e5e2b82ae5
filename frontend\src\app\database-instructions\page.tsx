import MainLayout from '@/components/layout/MainLayout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export default function DatabaseInstructions() {
  const sqlCode = `-- Dozan Database Schema for Supabase
-- Copy and paste this code in Supabase Dashboard > SQL Editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON> <PERSON><PERSON><PERSON> types
CREATE TYPE user_role AS ENUM ('CLIENT', 'CRAFTSMAN', 'ADMIN');
CREATE TYPE project_status AS ENUM ('OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'DISPUTED');
CREATE TYPE project_priority AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'URGENT');

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    avatar VARCHAR(500),
    role user_role NOT NULL DEFAULT 'CLIENT',
    location VARCHAR(255),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified_at TIMESTAMP,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Projects table
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    budget_min DECIMAL(10,2),
    budget_max DECIMAL(10,2),
    location VARCHAR(255) NOT NULL,
    priority project_priority DEFAULT 'MEDIUM',
    materials VARCHAR(50) DEFAULT 'NOT_SPECIFIED',
    work_type VARCHAR(255),
    requirements TEXT,
    images TEXT[], -- Array of image URLs
    status project_status DEFAULT 'OPEN',
    client_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    assigned_craftsman_id UUID REFERENCES users(id),
    deadline TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Insert sample data
INSERT INTO users (email, password_hash, name, phone, role, location) VALUES
('<EMAIL>', '$2b$10$example', 'أحمد محمد', '+************', 'CLIENT', 'دمشق'),
('<EMAIL>', '$2b$10$example', 'محمد أحمد', '+************', 'CRAFTSMAN', 'حلب'),
('<EMAIL>', '$2b$10$example', 'مدير النظام', '+************', 'ADMIN', 'دمشق');

-- Insert sample project
INSERT INTO projects (title, description, category, budget_min, budget_max, location, client_id) 
SELECT 'إصلاح باب خشبي', 'يحتاج إصلاح باب خشبي في المنزل', 'نجارة', 50000, 100000, 'دمشق', id 
FROM users WHERE email = '<EMAIL>';

COMMIT;`;

  const copyToClipboard = () => {
    navigator.clipboard.writeText(sqlCode);
    alert('تم نسخ الكود! الصقه في Supabase SQL Editor');
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-navy mb-4">
                📋 تعليمات إعداد قاعدة البيانات
              </h1>
              <p className="text-xl text-gray-600">
                خطوات بسيطة لإعداد قاعدة البيانات في Supabase
              </p>
            </div>

            {/* Step 1 */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">الخطوة 1: افتح Supabase Dashboard</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-700">
                    اذهب إلى Supabase Dashboard وافتح مشروعك:
                  </p>
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="font-mono text-sm">
                      🔗 https://supabase.com/dashboard/projects
                    </p>
                    <p className="text-sm text-gray-600 mt-2">
                      اختر مشروع: <strong>lyjelanmcbzymgauwamc</strong>
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Step 2 */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">الخطوة 2: افتح SQL Editor</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-700">
                    في الشريط الجانبي، انقر على:
                  </p>
                  <div className="bg-green-50 p-4 rounded-lg">
                    <p className="font-semibold">📝 SQL Editor</p>
                    <p className="text-sm text-gray-600 mt-1">
                      ثم انقر "New Query" لإنشاء استعلام جديد
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Step 3 */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">الخطوة 3: انسخ والصق الكود</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <p className="text-gray-700">
                      انسخ الكود التالي والصقه في SQL Editor:
                    </p>
                    <Button onClick={copyToClipboard} className="bg-gradient-to-r from-teal to-navy">
                      📋 نسخ الكود
                    </Button>
                  </div>
                  
                  <div className="bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto max-h-96">
                    <pre className="text-xs font-mono whitespace-pre-wrap">
                      {sqlCode}
                    </pre>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Step 4 */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">الخطوة 4: تشغيل الكود</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-700">
                    بعد لصق الكود، انقر على:
                  </p>
                  <div className="bg-purple-50 p-4 rounded-lg">
                    <p className="font-semibold text-purple-800">▶️ Run</p>
                    <p className="text-sm text-gray-600 mt-1">
                      سيتم إنشاء جميع الجداول والبيانات التجريبية
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Step 5 */}
            <Card className="border-0 bg-gradient-to-r from-green-500 to-green-600 text-white mb-8">
              <CardHeader>
                <CardTitle className="text-2xl">الخطوة 5: اختبار النتيجة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p>
                    بعد تشغيل الكود بنجاح، اختبر قاعدة البيانات:
                  </p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <a 
                      href="/setup-database"
                      className="block bg-white/20 p-4 rounded-lg hover:bg-white/30 transition-all"
                    >
                      <h3 className="font-semibold">🧪 اختبار قاعدة البيانات</h3>
                      <p className="text-sm opacity-90">تحقق من الجداول والبيانات</p>
                    </a>
                    <a 
                      href="/test-dashboard"
                      className="block bg-white/20 p-4 rounded-lg hover:bg-white/30 transition-all"
                    >
                      <h3 className="font-semibold">🎛️ لوحة الاختبار</h3>
                      <p className="text-sm opacity-90">اختبار جميع الميزات</p>
                    </a>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Troubleshooting */}
            <Card className="border-0 bg-yellow-50 border-yellow-200">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">🔧 حل المشاكل</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-navy mb-2">إذا ظهر خطأ:</h3>
                    <ul className="space-y-2 text-gray-700">
                      <li>• تأكد أن المشروع نشط في Supabase</li>
                      <li>• تحقق من صحة الكود المنسوخ</li>
                      <li>• جرب تشغيل الكود على أجزاء صغيرة</li>
                      <li>• تأكد من وجود صلاحيات الكتابة</li>
                    </ul>
                  </div>
                  
                  <div className="bg-blue-100 border border-blue-400 text-blue-800 px-4 py-3 rounded">
                    <strong>💡 نصيحة:</strong> إذا كانت الجداول موجودة مسبقاً، سيتم تجاهل أوامر CREATE TABLE.
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Navigation */}
            <div className="text-center mt-8 space-x-4 space-x-reverse">
              <a 
                href="/test-dashboard"
                className="inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all"
              >
                العودة للوحة الاختبار
              </a>
              <a 
                href="/setup-database"
                className="inline-block bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all"
              >
                اختبار قاعدة البيانات
              </a>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
