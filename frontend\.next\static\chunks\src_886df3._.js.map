{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useState } from 'react';\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  role: 'client' | 'craftsman';\n  phone?: string;\n}\n\nexport const useAuth = () => {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const login = async (credentials: LoginCredentials) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await signIn('credentials', {\n        email: credentials.email,\n        password: credentials.password,\n        redirect: false,\n      });\n\n      if (result?.error) {\n        setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');\n        return false;\n      }\n\n      if (result?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        setError(errorData.message || 'حدث خطأ أثناء إنشاء الحساب');\n        return false;\n      }\n\n      // تسجيل الدخول تلقائياً بعد التسجيل\n      const loginResult = await signIn('credentials', {\n        email: data.email,\n        password: data.password,\n        redirect: false,\n      });\n\n      if (loginResult?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء إنشاء الحساب');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    setIsLoading(true);\n    try {\n      await signOut({ redirect: false });\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithGoogle = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('google', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Google');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithFacebook = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('facebook', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Facebook');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const isAuthenticated = status === 'authenticated';\n  const isLoading_session = status === 'loading';\n  const user = session?.user;\n\n  // التحقق من الأدوار مع fallback\n  const isClient = user?.role === 'client';\n  const isCraftsman = user?.role === 'craftsman';\n  const isAdmin = user?.role === 'admin';\n\n  // التحقق من الصلاحيات\n  const hasRole = (role: string | string[]) => {\n    if (!user) return false;\n    if (Array.isArray(role)) {\n      return role.includes(user.role);\n    }\n    return user.role === role;\n  };\n\n  const canAccess = (requiredRoles: string | string[]) => {\n    return isAuthenticated && hasRole(requiredRoles);\n  };\n\n  return {\n    // Session data\n    user,\n    session,\n    isAuthenticated,\n    isLoading: isLoading || isLoading_session,\n    error,\n\n    // Role checks\n    isClient,\n    isCraftsman,\n    isAdmin,\n    hasRole,\n    canAccess,\n\n    // Auth methods\n    login,\n    register,\n    logout,\n    loginWithGoogle,\n    loginWithFacebook,\n\n    // Utility\n    setError,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;AAJA;;;;AAmBO,MAAM,UAAU;;IACrB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,QAAQ,OAAO;QACnB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;gBAC9B,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;gBACT,OAAO;YACT;YAEA,IAAI,QAAQ,IAAI;gBACd,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,OAAO,IAAI;gBAC9B,OAAO;YACT;YAEA,oCAAoC;YACpC,MAAM,cAAc,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBAC9C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;YACZ;YAEA,IAAI,aAAa,IAAI;gBACnB,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;gBAAE,UAAU;YAAM;YAChC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBAAE,aAAa;YAAa;QACrD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,YAAY;gBAAE,aAAa;YAAa;QACvD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,WAAW;IACnC,MAAM,oBAAoB,WAAW;IACrC,MAAM,OAAO,SAAS;IAEtB,gCAAgC;IAChC,MAAM,WAAW,MAAM,SAAS;IAChC,MAAM,cAAc,MAAM,SAAS;IACnC,MAAM,UAAU,MAAM,SAAS;IAE/B,sBAAsB;IACtB,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI;QAChC;QACA,OAAO,KAAK,IAAI,KAAK;IACvB;IAEA,MAAM,YAAY,CAAC;QACjB,OAAO,mBAAmB,QAAQ;IACpC;IAEA,OAAO;QACL,eAAe;QACf;QACA;QACA;QACA,WAAW,aAAa;QACxB;QAEA,cAAc;QACd;QACA;QACA;QACA;QACA;QAEA,eAAe;QACf;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;IACF;AACF;GA7Ja;;QACuB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS"}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/auth/AuthButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/hooks/useAuth';\nimport { Button } from '@/components/ui/Button';\nimport Link from 'next/link';\nimport { useState } from 'react';\n\nexport default function AuthButton() {\n  const { isAuthenticated, user, logout, isLoading } = useAuth();\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse\">\n        <div className=\"h-10 w-24 bg-gray-200 rounded\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"flex items-center space-x-3 space-x-reverse\">\n        <Link href=\"/login\">\n          <Button variant=\"outline\" size=\"sm\" className=\"border-navy text-navy hover:bg-navy hover:text-white\">\n            تسجيل الدخول\n          </Button>\n        </Link>\n        <Link href=\"/register\">\n          <Button size=\"sm\" className=\"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90\">\n            إنشاء حساب\n          </Button>\n        </Link>\n      </div>\n    );\n  }\n\n  const getRoleDisplayName = (role: string) => {\n    switch (role) {\n      case 'client':\n        return 'عميل';\n      case 'craftsman':\n        return 'حرفي';\n      case 'admin':\n        return 'مدير';\n      default:\n        return 'مستخدم';\n    }\n  };\n\n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'client':\n        return '👤';\n      case 'craftsman':\n        return '👨‍🔧';\n      case 'admin':\n        return '👑';\n      default:\n        return '👤';\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className=\"flex items-center space-x-2 space-x-reverse bg-white border border-gray-200 rounded-lg px-4 py-2 hover:bg-gray-50 transition-colors duration-200\"\n      >\n        <div className=\"text-lg\">{getRoleIcon(user?.role || '')}</div>\n        <div className=\"text-right\">\n          <div className=\"text-sm font-medium text-navy\">{user?.name || 'مستخدم'}</div>\n          <div className=\"text-xs text-gray-500\">{getRoleDisplayName(user?.role || '')}</div>\n        </div>\n        <svg\n          className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${\n            isDropdownOpen ? 'rotate-180' : ''\n          }`}\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n        </svg>\n      </button>\n\n      {isDropdownOpen && (\n        <>\n          {/* Backdrop */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsDropdownOpen(false)}\n          />\n\n          {/* Dropdown Menu */}\n          <div className=\"absolute left-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-20\">\n            <div className=\"p-4 border-b border-gray-100\">\n              <div className=\"flex items-center space-x-3 space-x-reverse\">\n                <div className=\"text-2xl\">{getRoleIcon(user?.role || '')}</div>\n                <div>\n                  <div className=\"font-medium text-navy\">{user?.name || 'مستخدم'}</div>\n                  <div className=\"text-sm text-gray-500\">{user?.email || 'بريد إلكتروني'}</div>\n                  <div className=\"text-xs text-teal font-medium\">\n                    {getRoleDisplayName(user?.role || '')}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"py-2\">\n              <Link\n                href=\"/dashboard\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>📊</span>\n                  <span>لوحة التحكم</span>\n                </div>\n              </Link>\n\n              <Link\n                href=\"/profile\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>⚙️</span>\n                  <span>الملف الشخصي</span>\n                </div>\n              </Link>\n\n              {user?.role === 'client' && (\n                <Link\n                  href=\"/projects/create\"\n                  className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                  onClick={() => setIsDropdownOpen(false)}\n                >\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span>➕</span>\n                    <span>إنشاء مشروع</span>\n                  </div>\n                </Link>\n              )}\n\n              {user?.role === 'craftsman' && (\n                <Link\n                  href=\"/offers\"\n                  className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                  onClick={() => setIsDropdownOpen(false)}\n                >\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span>💼</span>\n                    <span>عروضي</span>\n                  </div>\n                </Link>\n              )}\n\n              <Link\n                href=\"/messages\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>💬</span>\n                  <span>الرسائل</span>\n                </div>\n              </Link>\n            </div>\n\n            <div className=\"border-t border-gray-100 py-2\">\n              <button\n                onClick={() => {\n                  logout();\n                  setIsDropdownOpen(false);\n                }}\n                className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-red-50 transition-colors duration-200\"\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>🚪</span>\n                  <span>تسجيل الخروج</span>\n                </div>\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,WAAU;kCAAuD;;;;;;;;;;;8BAIvG,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,WAAU;kCAAyE;;;;;;;;;;;;;;;;;IAM7G;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCAAW,YAAY,MAAM,QAAQ;;;;;;kCACpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAiC,MAAM,QAAQ;;;;;;0CAC9D,6LAAC;gCAAI,WAAU;0CAAyB,mBAAmB,MAAM,QAAQ;;;;;;;;;;;;kCAE3E,6LAAC;wBACC,WAAW,CAAC,wDAAwD,EAClE,iBAAiB,eAAe,IAChC;wBACF,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAIxE,gCACC;;kCAEE,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,kBAAkB;;;;;;kCAInC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAY,YAAY,MAAM,QAAQ;;;;;;sDACrD,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAyB,MAAM,QAAQ;;;;;;8DACtD,6LAAC;oDAAI,WAAU;8DAAyB,MAAM,SAAS;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DACZ,mBAAmB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAM1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAIV,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;oCAIT,MAAM,SAAS,0BACd,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;oCAKX,MAAM,SAAS,6BACd,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAKZ,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;0CAKZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;wCACP;wCACA,kBAAkB;oCACpB;oCACA,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GArLwB;;QAC+B,0HAAA,CAAA,UAAO;;;KADtC"}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { Button } from '../ui/Button';\nimport AuthButton from '../auth/AuthButton';\n\nconst Header = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex justify-between items-center\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"group flex items-center space-x-3 space-x-reverse\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-navy to-teal\">\n                دوزان\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:block\">\n            <ul className=\"flex space-x-8 space-x-reverse\">\n              <li>\n                <Link href=\"/\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الرئيسية\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  المشاريع\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الحرفيون\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  من نحن\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الأسئلة الشائعة\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  اتصل بنا\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n            </ul>\n          </nav>\n\n          {/* Desktop Auth */}\n          <div className=\"hidden lg:flex items-center\">\n            <AuthButton />\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300\"\n            aria-label=\"فتح القائمة\"\n          >\n            <svg className=\"w-6 h-6 text-navy\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              {isMobileMenuOpen ? (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              ) : (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden mt-4 py-4 border-t border-gray-200\">\n            <nav className=\"space-y-4\">\n              <Link href=\"/\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الرئيسية\n              </Link>\n              <Link href=\"/jobs\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                المشاريع\n              </Link>\n              <Link href=\"/craftsmen\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الحرفيون\n              </Link>\n              <Link href=\"/about\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                من نحن\n              </Link>\n              <Link href=\"/faq\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الأسئلة الشائعة\n              </Link>\n              <Link href=\"/contact\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                اتصل بنا\n              </Link>\n            </nav>\n            <div className=\"mt-6\">\n              <AuthButton />\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;;;AALA;;;;AAOA,MAAM,SAAS;;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDAA2L;;;;;;kDAG1M,6LAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;;;;;;sCAO1G,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAA0F;8DAEjH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;gDAA0F;8DAErH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;;gDAA0F;8DAE1H,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;;gDAA0F;8DAEtH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAO,WAAU;;gDAA0F;8DAEpH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;gDAA0F;8DAExH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2IAAA,CAAA,UAAU;;;;;;;;;;sCAIb,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC;gCAAI,WAAU;gCAAoB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC1E,iCACC,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;yDAErE,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAO5E,kCACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAiF;;;;;;8CAG1G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAiF;;;;;;8CAG9G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAiF;;;;;;8CAGnH,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiF;;;;;;8CAG/G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAO,WAAU;8CAAiF;;;;;;8CAG7G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiF;;;;;;;;;;;;sCAInH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2IAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB;GAjHM;KAAA;uCAmHS"}}, {"offset": {"line": 1033, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1039, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden\">\n      {/* خلفية هندسية متناسقة */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl\"></div>\n      </div>\n\n      {/* شبكة نقطية ناعمة */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',\n          backgroundSize: '30px 30px'\n        }}></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-16 relative z-10\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Logo and Description */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n              <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center text-white text-2xl font-bold shadow-lg\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-white\">\n                دوزان\n              </span>\n            </div>\n            <p className=\"text-white/90 leading-relaxed mb-6\">\n              منصة تربط بين أصحاب المشاريع والحرفيين المهرة لتنفيذ المشاريع بكفاءة وجودة عالية في جميع أنحاء سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              روابط سريعة\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الرئيسية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  المشاريع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الحرفيون\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  من نحن\n                </Link>\n              </li>\n            </ul>\n          </div>\n          {/* Support */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              الدعم\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/contact\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  اتصل بنا\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  شروط الاستخدام\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              تواصل معنا\n            </h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">البريد الإلكتروني</p>\n                  <a href=\"mailto:<EMAIL>\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">الهاتف</p>\n                  <a href=\"tel:+963112345678\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    +963 11 234 5678\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">العنوان</p>\n                  <p className=\"text-white font-medium\">دمشق، سوريا</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"mt-12 pt-8 border-t border-white/20\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-white/80 text-sm\">\n                &copy; {new Date().getFullYear()} دوزان. جميع الحقوق محفوظة.\n              </p>\n              <p className=\"text-white/60 text-xs mt-1\">\n                صُنع بـ ❤️ في سوريا\n              </p>\n            </div>\n\n            <div className=\"flex items-center space-x-6 space-x-reverse\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">2500+</div>\n                <div className=\"text-white/70 text-xs\">حرفي</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">15000+</div>\n                <div className=\"text-white/70 text-xs\">مشروع</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">98%</div>\n                <div className=\"text-white/70 text-xs\">رضا</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA6H;;;;;;0DAG5I,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAIlD,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAyQ,UAAS;;;;;;;;;;;;;;;;0DAGjT,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA8jD,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO1mD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;;sEACvB,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;;sEAC3B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;;sEAChC,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAO3H,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAO,WAAU;;sEAC1B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3H,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,MAAK;gEAAuB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAM3H,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,MAAK;gEAAoB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAMxH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EACjE,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;8EACrE,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;gDAAwB;gDAC3B,IAAI,OAAO,WAAW;gDAAG;;;;;;;sDAEnC,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;KA5MM;uCA8MS"}}, {"offset": {"line": 1896, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1902, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout = ({ children }: MainLayoutProps) => {\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      <Header />\n      <main className=\"flex-grow\">{children}</main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB;IAC/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAK,WAAU;0BAAa;;;;;;0BAC7B,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;KARM;uCAUS"}}, {"offset": {"line": 1948, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1954, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-white shadow-sm transition-all duration-200 hover:shadow-md',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('font-semibold leading-none tracking-tight text-navy', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-600', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACpE,GAAG,KAAK;kBAER;;;;;;;AAGL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;;AAGL,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG"}}, {"offset": {"line": 2058, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2064, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/FileUpload.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef } from 'react';\nimport { Button } from './Button';\n\ninterface FileUploadProps {\n  onFileSelect: (files: File[]) => void;\n  accept?: string;\n  multiple?: boolean;\n  maxFiles?: number;\n  maxSize?: number; // in MB\n  className?: string;\n  children?: React.ReactNode;\n}\n\nconst FileUpload: React.FC<FileUploadProps> = ({\n  onFileSelect,\n  accept = \"image/*\",\n  multiple = true,\n  maxFiles = 5,\n  maxSize = 5, // 5MB\n  className = \"\",\n  children\n}) => {\n  const [isDragging, setIsDragging] = useState(false);\n  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n\n  const handleFileSelect = (files: FileList | null) => {\n    if (!files) return;\n\n    const fileArray = Array.from(files);\n    const validFiles: File[] = [];\n    const errors: string[] = [];\n\n    // التحقق من عدد الملفات\n    if (uploadedFiles.length + fileArray.length > maxFiles) {\n      errors.push(`يمكن رفع ${maxFiles} ملفات كحد أقصى`);\n      return;\n    }\n\n    // التحقق من حجم وصيغة كل ملف\n    fileArray.forEach(file => {\n      // التحقق من الحجم\n      if (file.size > maxSize * 1024 * 1024) {\n        errors.push(`${file.name}: الحجم يجب أن يكون أقل من ${maxSize}MB`);\n        return;\n      }\n\n      // التحقق من الصيغة\n      if (accept !== \"*\" && !file.type.match(accept.replace(\"*\", \".*\"))) {\n        errors.push(`${file.name}: صيغة غير مدعومة`);\n        return;\n      }\n\n      validFiles.push(file);\n    });\n\n    if (errors.length > 0) {\n      alert(errors.join('\\n'));\n      return;\n    }\n\n    const newFiles = [...uploadedFiles, ...validFiles];\n    setUploadedFiles(newFiles);\n    onFileSelect(newFiles);\n  };\n\n  const handleDragOver = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(true);\n  };\n\n  const handleDragLeave = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n  };\n\n  const handleDrop = (e: React.DragEvent) => {\n    e.preventDefault();\n    setIsDragging(false);\n    handleFileSelect(e.dataTransfer.files);\n  };\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    handleFileSelect(e.target.files);\n  };\n\n  const removeFile = (index: number) => {\n    const newFiles = uploadedFiles.filter((_, i) => i !== index);\n    setUploadedFiles(newFiles);\n    onFileSelect(newFiles);\n  };\n\n  const openFileDialog = () => {\n    fileInputRef.current?.click();\n  };\n\n  const formatFileSize = (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  };\n\n  return (\n    <div className={`w-full ${className}`}>\n      {/* Upload Area */}\n      <div\n        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors cursor-pointer ${\n          isDragging\n            ? 'border-teal bg-teal/10'\n            : 'border-gray-300 hover:border-gray-400'\n        }`}\n        onDragOver={handleDragOver}\n        onDragLeave={handleDragLeave}\n        onDrop={handleDrop}\n        onClick={openFileDialog}\n      >\n        <input\n          ref={fileInputRef}\n          type=\"file\"\n          accept={accept}\n          multiple={multiple}\n          onChange={handleInputChange}\n          className=\"hidden\"\n        />\n\n        {children || (\n          <div>\n            <div className=\"text-4xl mb-4\">📁</div>\n            <p className=\"text-lg font-medium text-gray-700 mb-2\">\n              اسحب الملفات هنا أو انقر للاختيار\n            </p>\n            <p className=\"text-sm text-gray-500 mb-4\">\n              {accept === \"image/*\" ? \"الصور فقط\" : \"جميع أنواع الملفات\"} • \n              حد أقصى {maxFiles} ملفات • \n              حجم أقصى {maxSize}MB لكل ملف\n            </p>\n            <Button type=\"button\" variant=\"outline\">\n              اختيار الملفات\n            </Button>\n          </div>\n        )}\n      </div>\n\n      {/* Uploaded Files List */}\n      {uploadedFiles.length > 0 && (\n        <div className=\"mt-4\">\n          <h4 className=\"font-medium text-gray-700 mb-3\">\n            الملفات المرفوعة ({uploadedFiles.length}/{maxFiles})\n          </h4>\n          <div className=\"space-y-2\">\n            {uploadedFiles.map((file, index) => (\n              <div\n                key={index}\n                className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg border\"\n              >\n                <div className=\"flex items-center space-x-3 space-x-reverse\">\n                  {file.type.startsWith('image/') ? (\n                    <img\n                      src={URL.createObjectURL(file)}\n                      alt={file.name}\n                      className=\"w-10 h-10 object-cover rounded\"\n                    />\n                  ) : (\n                    <div className=\"w-10 h-10 bg-gray-200 rounded flex items-center justify-center\">\n                      📄\n                    </div>\n                  )}\n                  <div>\n                    <p className=\"font-medium text-gray-700 text-sm\">{file.name}</p>\n                    <p className=\"text-xs text-gray-500\">{formatFileSize(file.size)}</p>\n                  </div>\n                </div>\n                <button\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    removeFile(index);\n                  }}\n                  className=\"text-red-500 hover:text-red-700 p-1\"\n                >\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FileUpload;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAeA,MAAM,aAAwC,CAAC,EAC7C,YAAY,EACZ,SAAS,SAAS,EAClB,WAAW,IAAI,EACf,WAAW,CAAC,EACZ,UAAU,CAAC,EACX,YAAY,EAAE,EACd,QAAQ,EACT;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,OAAO;QAEZ,MAAM,YAAY,MAAM,IAAI,CAAC;QAC7B,MAAM,aAAqB,EAAE;QAC7B,MAAM,SAAmB,EAAE;QAE3B,wBAAwB;QACxB,IAAI,cAAc,MAAM,GAAG,UAAU,MAAM,GAAG,UAAU;YACtD,OAAO,IAAI,CAAC,CAAC,SAAS,EAAE,SAAS,eAAe,CAAC;YACjD;QACF;QAEA,6BAA6B;QAC7B,UAAU,OAAO,CAAC,CAAA;YAChB,kBAAkB;YAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;gBACrC,OAAO,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,2BAA2B,EAAE,QAAQ,EAAE,CAAC;gBACjE;YACF;YAEA,mBAAmB;YACnB,IAAI,WAAW,OAAO,CAAC,KAAK,IAAI,CAAC,KAAK,CAAC,OAAO,OAAO,CAAC,KAAK,QAAQ;gBACjE,OAAO,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,iBAAiB,CAAC;gBAC3C;YACF;YAEA,WAAW,IAAI,CAAC;QAClB;QAEA,IAAI,OAAO,MAAM,GAAG,GAAG;YACrB,MAAM,OAAO,IAAI,CAAC;YAClB;QACF;QAEA,MAAM,WAAW;eAAI;eAAkB;SAAW;QAClD,iBAAiB;QACjB,aAAa;IACf;IAEA,MAAM,iBAAiB,CAAC;QACtB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,EAAE,cAAc;QAChB,cAAc;IAChB;IAEA,MAAM,aAAa,CAAC;QAClB,EAAE,cAAc;QAChB,cAAc;QACd,iBAAiB,EAAE,YAAY,CAAC,KAAK;IACvC;IAEA,MAAM,oBAAoB,CAAC;QACzB,iBAAiB,EAAE,MAAM,CAAC,KAAK;IACjC;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,WAAW,cAAc,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACtD,iBAAiB;QACjB,aAAa;IACf;IAEA,MAAM,iBAAiB;QACrB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,OAAO,EAAE,WAAW;;0BAEnC,6LAAC;gBACC,WAAW,CAAC,mFAAmF,EAC7F,aACI,2BACA,yCACJ;gBACF,YAAY;gBACZ,aAAa;gBACb,QAAQ;gBACR,SAAS;;kCAET,6LAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,UAAU;wBACV,UAAU;wBACV,WAAU;;;;;;oBAGX,0BACC,6LAAC;;0CACC,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;0CAGtD,6LAAC;gCAAE,WAAU;;oCACV,WAAW,YAAY,cAAc;oCAAqB;oCAClD;oCAAS;oCACR;oCAAQ;;;;;;;0CAEpB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,SAAQ;0CAAU;;;;;;;;;;;;;;;;;;YAQ7C,cAAc,MAAM,GAAG,mBACtB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAiC;4BAC1B,cAAc,MAAM;4BAAC;4BAAE;4BAAS;;;;;;;kCAErD,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI,CAAC,UAAU,CAAC,0BACpB,6LAAC;gDACC,KAAK,IAAI,eAAe,CAAC;gDACzB,KAAK,KAAK,IAAI;gDACd,WAAU;;;;;qEAGZ,6LAAC;gDAAI,WAAU;0DAAiE;;;;;;0DAIlF,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAqC,KAAK,IAAI;;;;;;kEAC3D,6LAAC;wDAAE,WAAU;kEAAyB,eAAe,KAAK,IAAI;;;;;;;;;;;;;;;;;;kDAGlE,6LAAC;wCACC,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,WAAW;wCACb;wCACA,WAAU;kDAEV,cAAA,6LAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACjE,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;;+BA5BpE;;;;;;;;;;;;;;;;;;;;;;AAsCrB;GAnLM;KAAA;uCAqLS"}}, {"offset": {"line": 2361, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2367, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/hooks/useApi.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\nimport { useSession } from 'next-auth/react';\n\ninterface ApiResponse<T> {\n  data: T | null;\n  loading: boolean;\n  error: string | null;\n  refetch: () => void;\n}\n\ninterface ApiMutationResponse<T> {\n  mutate: (data?: any) => Promise<T | null>;\n  loading: boolean;\n  error: string | null;\n}\n\n// Hook لجلب البيانات\nexport function useApiGet<T>(endpoint: string, dependencies: any[] = []): ApiResponse<T> {\n  const [data, setData] = useState<T | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const response = await fetch(endpoint);\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'حدث خطأ في جلب البيانات');\n      }\n\n      setData(result);\n    } catch (err: any) {\n      setError(err.message);\n      setData(null);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchData();\n  }, dependencies);\n\n  return { data, loading, error, refetch: fetchData };\n}\n\n// Hook للعمليات (POST, PUT, DELETE)\nexport function useApiMutation<T>(\n  endpoint: string, \n  method: 'POST' | 'PUT' | 'DELETE' = 'POST'\n): ApiMutationResponse<T> {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const mutate = async (data?: any): Promise<T | null> => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const options: RequestInit = {\n        method,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n      };\n\n      if (data && method !== 'DELETE') {\n        options.body = JSON.stringify(data);\n      }\n\n      const response = await fetch(endpoint, options);\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'حدث خطأ في العملية');\n      }\n\n      return result;\n    } catch (err: any) {\n      setError(err.message);\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return { mutate, loading, error };\n}\n\n// Hooks مخصصة للكيانات المختلفة\n\n// المشاريع\nexport function useProjects(filters?: Record<string, string>) {\n  const queryString = filters ? \n    '?' + new URLSearchParams(filters).toString() : '';\n  \n  return useApiGet<{projects: any[], total: number}>(`/api/projects${queryString}`, [filters]);\n}\n\nexport function useProject(id: string) {\n  return useApiGet<{project: any}>(`/api/projects/${id}`, [id]);\n}\n\nexport function useCreateProject() {\n  return useApiMutation<{project: any, message: string}>('/api/projects', 'POST');\n}\n\nexport function useUpdateProject(id: string) {\n  return useApiMutation<{project: any, message: string}>(`/api/projects/${id}`, 'PUT');\n}\n\nexport function useDeleteProject(id: string) {\n  return useApiMutation<{message: string}>(`/api/projects/${id}`, 'DELETE');\n}\n\n// العروض\nexport function useOffers(filters?: Record<string, string>) {\n  const queryString = filters ? \n    '?' + new URLSearchParams(filters).toString() : '';\n  \n  return useApiGet<{offers: any[], total: number}>(`/api/offers${queryString}`, [filters]);\n}\n\nexport function useCreateOffer() {\n  return useApiMutation<{offer: any, message: string}>('/api/offers', 'POST');\n}\n\n// المستخدمين\nexport function useUsers(filters?: Record<string, string>) {\n  const queryString = filters ? \n    '?' + new URLSearchParams(filters).toString() : '';\n  \n  return useApiGet<{users: any[], total: number}>(`/api/users${queryString}`, [filters]);\n}\n\nexport function useUser(id: string) {\n  return useApiGet<{user: any}>(`/api/users/${id}`, [id]);\n}\n\nexport function useUpdateUser(id: string) {\n  return useApiMutation<{user: any, message: string}>(`/api/users/${id}`, 'PUT');\n}\n\n// Hook للحصول على بيانات المستخدم الحالي\nexport function useCurrentUser() {\n  const { data: session } = useSession();\n  const userId = session?.user?.id;\n  \n  const { data, loading, error, refetch } = useUser(userId || '');\n  \n  return {\n    user: data?.user || null,\n    loading: loading && !!userId,\n    error: userId ? error : null,\n    refetch\n  };\n}\n\n// Hook للحصول على مشاريع المستخدم الحالي\nexport function useMyProjects() {\n  const { data: session } = useSession();\n  const userId = session?.user?.id;\n  \n  const filters = userId ? { clientId: userId } : undefined;\n  return useProjects(filters);\n}\n\n// Hook للحصول على عروض المستخدم الحالي\nexport function useMyOffers() {\n  const { data: session } = useSession();\n  const userId = session?.user?.id;\n\n  const filters = userId ? { craftsmanId: userId } : undefined;\n  return useOffers(filters);\n}\n\n// Hook لرفع الملفات\nexport function useFileUpload() {\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const uploadFiles = async (files: File[]): Promise<string[] | null> => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const formData = new FormData();\n      files.forEach(file => {\n        formData.append('files', file);\n      });\n\n      const response = await fetch('/api/upload', {\n        method: 'POST',\n        body: formData,\n      });\n\n      const result = await response.json();\n\n      if (!response.ok) {\n        throw new Error(result.error || 'حدث خطأ في رفع الملفات');\n      }\n\n      return result.files;\n    } catch (err: any) {\n      setError(err.message);\n      return null;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return { uploadFiles, loading, error };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AACA;;;;AAgBO,SAAS,UAAa,QAAgB,EAAE,eAAsB,EAAE;;IACrE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,QAAQ;QACV,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;YACpB,QAAQ;QACV,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG;IAEH,OAAO;QAAE;QAAM;QAAS;QAAO,SAAS;IAAU;AACpD;GA/BgB;AAkCT,SAAS,eACd,QAAgB,EAChB,SAAoC,MAAM;;IAE1C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,SAAS,OAAO;QACpB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,UAAuB;gBAC3B;gBACA,SAAS;oBACP,gBAAgB;gBAClB;YACF;YAEA,IAAI,QAAQ,WAAW,UAAU;gBAC/B,QAAQ,IAAI,GAAG,KAAK,SAAS,CAAC;YAChC;YAEA,MAAM,WAAW,MAAM,MAAM,UAAU;YACvC,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,OAAO;QACT,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;YACpB,OAAO;QACT,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QAAE;QAAQ;QAAS;IAAM;AAClC;IAxCgB;AA6CT,SAAS,YAAY,OAAgC;;IAC1D,MAAM,cAAc,UAClB,MAAM,IAAI,gBAAgB,SAAS,QAAQ,KAAK;IAElD,OAAO,UAA4C,CAAC,aAAa,EAAE,aAAa,EAAE;QAAC;KAAQ;AAC7F;IALgB;;QAIP;;;AAGF,SAAS,WAAW,EAAU;;IACnC,OAAO,UAA0B,CAAC,cAAc,EAAE,IAAI,EAAE;QAAC;KAAG;AAC9D;IAFgB;;QACP;;;AAGF,SAAS;;IACd,OAAO,eAAgD,iBAAiB;AAC1E;IAFgB;;QACP;;;AAGF,SAAS,iBAAiB,EAAU;;IACzC,OAAO,eAAgD,CAAC,cAAc,EAAE,IAAI,EAAE;AAChF;IAFgB;;QACP;;;AAGF,SAAS,iBAAiB,EAAU;;IACzC,OAAO,eAAkC,CAAC,cAAc,EAAE,IAAI,EAAE;AAClE;IAFgB;;QACP;;;AAIF,SAAS,UAAU,OAAgC;;IACxD,MAAM,cAAc,UAClB,MAAM,IAAI,gBAAgB,SAAS,QAAQ,KAAK;IAElD,OAAO,UAA0C,CAAC,WAAW,EAAE,aAAa,EAAE;QAAC;KAAQ;AACzF;IALgB;;QAIP;;;AAGF,SAAS;;IACd,OAAO,eAA8C,eAAe;AACtE;IAFgB;;QACP;;;AAIF,SAAS,SAAS,OAAgC;;IACvD,MAAM,cAAc,UAClB,MAAM,IAAI,gBAAgB,SAAS,QAAQ,KAAK;IAElD,OAAO,UAAyC,CAAC,UAAU,EAAE,aAAa,EAAE;QAAC;KAAQ;AACvF;IALgB;;QAIP;;;AAGF,SAAS,QAAQ,EAAU;;IAChC,OAAO,UAAuB,CAAC,WAAW,EAAE,IAAI,EAAE;QAAC;KAAG;AACxD;KAFgB;;QACP;;;AAGF,SAAS,cAAc,EAAU;;IACtC,OAAO,eAA6C,CAAC,WAAW,EAAE,IAAI,EAAE;AAC1E;KAFgB;;QACP;;;AAIF,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,SAAS,MAAM;IAE9B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,QAAQ,UAAU;IAE5D,OAAO;QACL,MAAM,MAAM,QAAQ;QACpB,SAAS,WAAW,CAAC,CAAC;QACtB,OAAO,SAAS,QAAQ;QACxB;IACF;AACF;KAZgB;;QACY,iJAAA,CAAA,aAAU;QAGM;;;AAWrC,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,SAAS,MAAM;IAE9B,MAAM,UAAU,SAAS;QAAE,UAAU;IAAO,IAAI;IAChD,OAAO,YAAY;AACrB;KANgB;;QACY,iJAAA,CAAA,aAAU;QAI7B;;;AAIF,SAAS;;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IACnC,MAAM,SAAS,SAAS,MAAM;IAE9B,MAAM,UAAU,SAAS;QAAE,aAAa;IAAO,IAAI;IACnD,OAAO,UAAU;AACnB;KANgB;;QACY,iJAAA,CAAA,aAAU;QAI7B;;;AAIF,SAAS;;IACd,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,cAAc,OAAO;QACzB,IAAI;YACF,WAAW;YACX,SAAS;YAET,MAAM,WAAW,IAAI;YACrB,MAAM,OAAO,CAAC,CAAA;gBACZ,SAAS,MAAM,CAAC,SAAS;YAC3B;YAEA,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;YAEA,OAAO,OAAO,KAAK;QACrB,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO;YACpB,OAAO;QACT,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QAAE;QAAa;QAAS;IAAM;AACvC;KAnCgB"}}, {"offset": {"line": 2651, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2657, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/test-upload/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\nimport FileUpload from '@/components/ui/FileUpload';\nimport { useFileUpload } from '@/hooks/useApi';\n\nconst TestUploadPage = () => {\n  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);\n  const [uploadedUrls, setUploadedUrls] = useState<string[]>([]);\n  const { uploadFiles, loading, error } = useFileUpload();\n\n  const handleFileSelect = (files: File[]) => {\n    setSelectedFiles(files);\n  };\n\n  const handleUpload = async () => {\n    if (selectedFiles.length === 0) {\n      alert('يرجى اختيار ملفات أولاً');\n      return;\n    }\n\n    const urls = await uploadFiles(selectedFiles);\n    if (urls) {\n      setUploadedUrls(urls);\n      setSelectedFiles([]);\n      alert('تم رفع الملفات بنجاح!');\n    }\n  };\n\n  const clearUploaded = () => {\n    setUploadedUrls([]);\n  };\n\n  return (\n    <MainLayout>\n      <div className=\"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            {/* Header */}\n            <div className=\"mb-8\">\n              <h1 className=\"text-3xl font-bold text-navy mb-4\">اختبار رفع الملفات</h1>\n              <p className=\"text-gray-600\">اختبر نظام رفع الصور والملفات</p>\n            </div>\n\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n              {/* Upload Section */}\n              <Card>\n                <CardHeader>\n                  <CardTitle>رفع الملفات</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <FileUpload\n                    onFileSelect={handleFileSelect}\n                    accept=\"image/*\"\n                    multiple={true}\n                    maxFiles={5}\n                    maxSize={5}\n                  />\n\n                  {error && (\n                    <div className=\"mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n                      {error}\n                    </div>\n                  )}\n\n                  {selectedFiles.length > 0 && (\n                    <div className=\"mt-6\">\n                      <Button\n                        onClick={handleUpload}\n                        disabled={loading}\n                        className=\"w-full bg-gradient-to-r from-navy to-teal\"\n                      >\n                        {loading ? 'جاري الرفع...' : `رفع ${selectedFiles.length} ملف`}\n                      </Button>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n\n              {/* Uploaded Files */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"flex items-center justify-between\">\n                    <span>الملفات المرفوعة ({uploadedUrls.length})</span>\n                    {uploadedUrls.length > 0 && (\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={clearUploaded}\n                      >\n                        مسح الكل\n                      </Button>\n                    )}\n                  </CardTitle>\n                </CardHeader>\n                <CardContent>\n                  {uploadedUrls.length > 0 ? (\n                    <div className=\"space-y-4\">\n                      {uploadedUrls.map((url, index) => (\n                        <div key={index} className=\"border border-gray-200 rounded-lg p-4\">\n                          <div className=\"flex items-center space-x-4 space-x-reverse\">\n                            <img\n                              src={url}\n                              alt={`رفع ${index + 1}`}\n                              className=\"w-16 h-16 object-cover rounded-lg\"\n                            />\n                            <div className=\"flex-1\">\n                              <p className=\"font-medium text-gray-700\">صورة {index + 1}</p>\n                              <p className=\"text-sm text-gray-500 break-all\">{url}</p>\n                            </div>\n                            <div className=\"flex space-x-2 space-x-reverse\">\n                              <Button\n                                variant=\"outline\"\n                                size=\"sm\"\n                                onClick={() => window.open(url, '_blank')}\n                              >\n                                عرض\n                              </Button>\n                              <Button\n                                variant=\"outline\"\n                                size=\"sm\"\n                                onClick={() => navigator.clipboard.writeText(url)}\n                              >\n                                نسخ الرابط\n                              </Button>\n                            </div>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  ) : (\n                    <div className=\"text-center py-8\">\n                      <div className=\"text-4xl mb-4\">📷</div>\n                      <p className=\"text-gray-500\">لم يتم رفع أي ملفات بعد</p>\n                    </div>\n                  )}\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* Usage Examples */}\n            <div className=\"mt-12\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>أمثلة الاستخدام</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <div className=\"text-center p-6 bg-gray-50 rounded-lg\">\n                      <div className=\"text-3xl mb-3\">🏗️</div>\n                      <h3 className=\"font-semibold text-navy mb-2\">صور المشاريع</h3>\n                      <p className=\"text-sm text-gray-600\">\n                        رفع صور المشاريع عند إنشاء مشروع جديد\n                      </p>\n                    </div>\n\n                    <div className=\"text-center p-6 bg-gray-50 rounded-lg\">\n                      <div className=\"text-3xl mb-3\">🎨</div>\n                      <h3 className=\"font-semibold text-navy mb-2\">معرض الأعمال</h3>\n                      <p className=\"text-sm text-gray-600\">\n                        رفع صور الأعمال السابقة للحرفيين\n                      </p>\n                    </div>\n\n                    <div className=\"text-center p-6 bg-gray-50 rounded-lg\">\n                      <div className=\"text-3xl mb-3\">👤</div>\n                      <h3 className=\"font-semibold text-navy mb-2\">الصور الشخصية</h3>\n                      <p className=\"text-sm text-gray-600\">\n                        رفع الصور الشخصية للملفات الشخصية\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"mt-8 p-6 bg-blue-50 border border-blue-200 rounded-lg\">\n                    <h3 className=\"font-semibold text-navy mb-3\">📋 معلومات تقنية:</h3>\n                    <ul className=\"text-sm text-gray-700 space-y-1\">\n                      <li>• الصيغ المدعومة: JPEG, PNG, GIF, WebP</li>\n                      <li>• الحد الأقصى للحجم: 5MB لكل ملف</li>\n                      <li>• الحد الأقصى للعدد: 5 ملفات في المرة الواحدة</li>\n                      <li>• يتم حفظ الملفات في مجلد /public/uploads</li>\n                      <li>• يتم إنشاء أسماء فريدة لتجنب التضارب</li>\n                    </ul>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n\n            {/* API Test */}\n            <div className=\"mt-8\">\n              <Card>\n                <CardHeader>\n                  <CardTitle>اختبار API</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"space-y-4\">\n                    <div>\n                      <h4 className=\"font-medium text-navy mb-2\">POST /api/upload</h4>\n                      <p className=\"text-sm text-gray-600 mb-2\">\n                        رفع ملفات متعددة باستخدام FormData\n                      </p>\n                      <div className=\"bg-gray-100 p-3 rounded text-sm font-mono\">\n                        const formData = new FormData();<br/>\n                        files.forEach(file => formData.append('files', file));<br/>\n                        fetch('/api/upload', {'{'} method: 'POST', body: formData {'}'});\n                      </div>\n                    </div>\n\n                    <div>\n                      <h4 className=\"font-medium text-navy mb-2\">Response</h4>\n                      <div className=\"bg-gray-100 p-3 rounded text-sm font-mono\">\n                        {'{'}<br/>\n                        &nbsp;&nbsp;\"message\": \"تم رفع الملفات بنجاح\",<br/>\n                        &nbsp;&nbsp;\"files\": [\"/uploads/filename1.jpg\", \"/uploads/filename2.png\"]<br/>\n                        {'}'}\n                      </div>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  );\n};\n\nexport default TestUploadPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,iBAAiB;;IACrB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC7D,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD;IAEpD,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;IACnB;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,MAAM;YACN;QACF;QAEA,MAAM,OAAO,MAAM,YAAY;QAC/B,IAAI,MAAM;YACR,gBAAgB;YAChB,iBAAiB,EAAE;YACnB,MAAM;QACR;IACF;IAEA,MAAM,gBAAgB;QACpB,gBAAgB,EAAE;IACpB;IAEA,qBACE,6LAAC,6IAAA,CAAA,UAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;sCAG/B,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,6LAAC,mIAAA,CAAA,cAAW;;8DACV,6LAAC,yIAAA,CAAA,UAAU;oDACT,cAAc;oDACd,QAAO;oDACP,UAAU;oDACV,UAAU;oDACV,SAAS;;;;;;gDAGV,uBACC,6LAAC;oDAAI,WAAU;8DACZ;;;;;;gDAIJ,cAAc,MAAM,GAAG,mBACtB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAS;wDACT,UAAU;wDACV,WAAU;kEAET,UAAU,kBAAkB,CAAC,IAAI,EAAE,cAAc,MAAM,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;8CAQxE,6LAAC,mIAAA,CAAA,OAAI;;sDACH,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC;;4DAAK;4DAAmB,aAAa,MAAM;4DAAC;;;;;;;oDAC5C,aAAa,MAAM,GAAG,mBACrB,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;kEACV;;;;;;;;;;;;;;;;;sDAMP,6LAAC,mIAAA,CAAA,cAAW;sDACT,aAAa,MAAM,GAAG,kBACrB,6LAAC;gDAAI,WAAU;0DACZ,aAAa,GAAG,CAAC,CAAC,KAAK,sBACtB,6LAAC;wDAAgB,WAAU;kEACzB,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEACC,KAAK;oEACL,KAAK,CAAC,IAAI,EAAE,QAAQ,GAAG;oEACvB,WAAU;;;;;;8EAEZ,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAE,WAAU;;gFAA4B;gFAAM,QAAQ;;;;;;;sFACvD,6LAAC;4EAAE,WAAU;sFAAmC;;;;;;;;;;;;8EAElD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK;sFACjC;;;;;;sFAGD,6LAAC,qIAAA,CAAA,SAAM;4EACL,SAAQ;4EACR,MAAK;4EACL,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC;sFAC9C;;;;;;;;;;;;;;;;;;uDAvBG;;;;;;;;;qEAgCd,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,6LAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQvC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAgB;;;;;;0EAC/B,6LAAC;gEAAG,WAAU;0EAA+B;;;;;;0EAC7C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAKvC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAgB;;;;;;0EAC/B,6LAAC;gEAAG,WAAU;0EAA+B;;;;;;0EAC7C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAKvC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAgB;;;;;;0EAC/B,6LAAC;gEAAG,WAAU;0EAA+B;;;;;;0EAC7C,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAMzC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;0EACJ,6LAAC;0EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAE,WAAU;sEAA6B;;;;;;sEAG1C,6LAAC;4DAAI,WAAU;;gEAA4C;8EACzB,6LAAC;;;;;gEAAI;8EACiB,6LAAC;;;;;gEAAI;gEACrC;gEAAI;gEAAiC;gEAAI;;;;;;;;;;;;;8DAInE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA6B;;;;;;sEAC3C,6LAAC;4DAAI,WAAU;;gEACZ;8EAAI,6LAAC;;;;;gEAAI;8EACoC,6LAAC;;;;;gEAAI;8EACsB,6LAAC;;;;;gEACzE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYzB;GA3NM;;QAGoC,yHAAA,CAAA,gBAAa;;;KAHjD;uCA6NS"}}, {"offset": {"line": 3345, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}