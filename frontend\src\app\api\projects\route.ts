import { NextRequest, NextResponse } from 'next/server';
import { mockProjects, mockUsers, Project } from '@/lib/mock-data';

// GET /api/projects - جلب جميع المشاريع
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const location = searchParams.get('location');
    const status = searchParams.get('status');
    const clientId = searchParams.get('clientId');
    const craftsmanId = searchParams.get('craftsmanId');

    let filteredProjects = [...mockProjects];

    // تطبيق الفلاتر
    if (category) {
      filteredProjects = filteredProjects.filter(p => p.category === category);
    }
    if (location) {
      filteredProjects = filteredProjects.filter(p => p.location.includes(location));
    }
    if (status) {
      filteredProjects = filteredProjects.filter(p => p.status === status);
    }
    if (clientId) {
      filteredProjects = filteredProjects.filter(p => p.clientId === clientId);
    }
    if (craftsmanId) {
      filteredProjects = filteredProjects.filter(p => p.craftsmanId === craftsmanId);
    }

    // إضافة بيانات العميل لكل مشروع
    const projectsWithClient = filteredProjects.map(project => {
      const client = mockUsers.find(user => user.id === project.clientId);
      const craftsman = project.craftsmanId ? 
        mockUsers.find(user => user.id === project.craftsmanId) : null;
      
      return {
        ...project,
        client: client ? {
          id: client.id,
          name: client.name,
          location: client.location,
          avatar: client.avatar
        } : null,
        craftsman: craftsman ? {
          id: craftsman.id,
          name: craftsman.name,
          location: craftsman.location,
          avatar: craftsman.avatar
        } : null
      };
    });

    return NextResponse.json({
      projects: projectsWithClient,
      total: projectsWithClient.length
    });
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      { error: 'خطأ في جلب المشاريع' },
      { status: 500 }
    );
  }
}

// POST /api/projects - إنشاء مشروع جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      title,
      description,
      category,
      budget,
      deadline,
      location,
      priority = 'MEDIUM',
      clientId,
      materials,
      workType,
      requirements,
      images = []
    } = body;

    // التحقق من البيانات المطلوبة
    if (!title || !description || !category || !budget || !deadline || !location || !clientId) {
      return NextResponse.json(
        { error: 'جميع الحقول المطلوبة يجب ملؤها' },
        { status: 400 }
      );
    }

    // التحقق من وجود العميل
    const client = mockUsers.find(user => user.id === clientId);
    if (!client || client.role !== 'CLIENT') {
      return NextResponse.json(
        { error: 'العميل غير موجود' },
        { status: 404 }
      );
    }

    // إنشاء مشروع جديد
    const newProject: Project = {
      id: `project_${Date.now()}`,
      title,
      description,
      category,
      budget,
      deadline: new Date(deadline),
      location,
      priority,
      status: 'OPEN',
      clientId,
      images,
      materials,
      workType,
      requirements,
      views: 0,
      featured: false,
      createdAt: new Date()
    };

    // إضافة المشروع للبيانات التجريبية (في التطبيق الحقيقي سيتم حفظه في قاعدة البيانات)
    mockProjects.push(newProject);

    return NextResponse.json({
      project: newProject,
      message: 'تم إنشاء المشروع بنجاح'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating project:', error);
    return NextResponse.json(
      { error: 'خطأ في إنشاء المشروع' },
      { status: 500 }
    );
  }
}
