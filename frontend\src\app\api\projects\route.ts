import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { ProjectStatus, ProjectPriority } from '@prisma/client';

// GET /api/projects - جلب المشاريع
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') as ProjectStatus | null;
    const category = searchParams.get('category');
    const clientId = searchParams.get('clientId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';

    const skip = (page - 1) * limit;

    const where = {
      ...(status && { status }),
      ...(category && { category }),
      ...(clientId && { clientId }),
      ...(search && {
        OR: [
          { title: { contains: search, mode: 'insensitive' as const } },
          { description: { contains: search, mode: 'insensitive' as const } },
          { location: { contains: search, mode: 'insensitive' as const } },
        ],
      }),
    };

    const [projects, total] = await Promise.all([
      prisma.project.findMany({
        where,
        skip,
        take: limit,
        include: {
          client: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
              location: true,
            },
          },
          _count: {
            select: {
              offers: true,
              reviews: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.project.count({ where }),
    ]);

    return NextResponse.json({
      projects,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching projects:', error);
    return NextResponse.json(
      { error: 'خطأ في جلب المشاريع' },
      { status: 500 }
    );
  }
}

// POST /api/projects - إنشاء مشروع جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      title,
      description,
      category,
      budget,
      deadline,
      location,
      priority,
      materials,
      workType,
      requirements,
      images,
      clientId,
    } = body;

    const project = await prisma.project.create({
      data: {
        title,
        description,
        category,
        budget,
        deadline: new Date(deadline),
        location,
        priority: priority || ProjectPriority.MEDIUM,
        materials: materials || 'NOT_SPECIFIED',
        workType,
        requirements,
        images: images || [],
        clientId,
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
      },
    });

    return NextResponse.json(project, { status: 201 });
  } catch (error) {
    console.error('Error creating project:', error);
    return NextResponse.json(
      { error: 'خطأ في إنشاء المشروع' },
      { status: 500 }
    );
  }
}
