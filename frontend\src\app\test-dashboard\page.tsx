'use client';

import { useState, useEffect } from 'react';
import { useSession, signIn, signOut } from 'next-auth/react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';

export default function TestDashboardPage() {
  const { data: session, status } = useSession();
  const [projects, setProjects] = useState<any[]>([]);
  const [offers, setOffers] = useState<any[]>([]);
  const [users, setUsers] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [projectsRes, offersRes, usersRes] = await Promise.all([
        fetch('/api/projects'),
        fetch('/api/offers'),
        fetch('/api/users')
      ]);

      const projectsData = await projectsRes.json();
      const offersData = await offersRes.json();
      const usersData = await usersRes.json();

      setProjects(projectsData.projects || []);
      setOffers(offersData.offers || []);
      setUsers(usersData.users || []);
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const createTestProject = async () => {
    try {
      const response = await fetch('/api/projects', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          title: `مشروع تجريبي ${Date.now()}`,
          description: 'وصف المشروع التجريبي',
          category: 'اختبار',
          budget: '₺1,000 - ₺2,000',
          deadline: '2024-04-01',
          location: 'دمشق',
          priority: 'MEDIUM',
          clientId: 'client',
          materials: 'متوفرة',
          workType: 'جديد',
          requirements: 'لا توجد متطلبات خاصة'
        })
      });

      if (response.ok) {
        fetchData(); // إعادة تحميل البيانات
      }
    } catch (error) {
      console.error('Error creating project:', error);
    }
  };

  const createTestOffer = async () => {
    if (projects.length === 0) return;

    try {
      const response = await fetch('/api/offers', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          projectId: projects[0].id,
          craftsmanId: 'craftsman1',
          amount: Math.floor(Math.random() * 5000) + 1000,
          currency: 'TRY',
          description: `عرض تجريبي ${Date.now()}`,
          estimatedDuration: Math.floor(Math.random() * 20) + 5
        })
      });

      if (response.ok) {
        fetchData(); // إعادة تحميل البيانات
      }
    } catch (error) {
      console.error('Error creating offer:', error);
    }
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal mx-auto mb-4"></div>
          <p>جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-navy mb-4">لوحة اختبار دوزان</h1>
          
          {session ? (
            <div className="flex items-center justify-between bg-white p-4 rounded-lg shadow">
              <div>
                <p className="font-semibold">{session.user?.name}</p>
                <p className="text-gray-600">{session.user?.email}</p>
                <Badge variant="secondary">{session.user?.role}</Badge>
              </div>
              <Button onClick={() => signOut()} variant="outline">
                تسجيل الخروج
              </Button>
            </div>
          ) : (
            <div className="bg-white p-4 rounded-lg shadow text-center">
              <p className="mb-4">يجب تسجيل الدخول أولاً</p>
              <Button onClick={() => signIn()}>
                تسجيل الدخول
              </Button>
            </div>
          )}
        </div>

        {session && (
          <>
            {/* Actions */}
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-8">
              <Button onClick={fetchData} disabled={loading}>
                {loading ? 'جاري التحديث...' : 'تحديث البيانات'}
              </Button>
              <Button onClick={createTestProject} variant="outline">
                إنشاء مشروع تجريبي
              </Button>
              <Button onClick={createTestOffer} variant="outline" disabled={projects.length === 0}>
                إنشاء عرض تجريبي
              </Button>
              <Button
                onClick={() => window.open('/test-api', '_blank')}
                variant="outline"
              >
                اختبار API
              </Button>
              <Button
                onClick={() => window.open('/jobs', '_blank')}
                variant="outline"
              >
                صفحة المشاريع
              </Button>
            </div>

            {/* Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
              <Card>
                <CardHeader>
                  <CardTitle>المشاريع</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-teal">{projects.length}</div>
                  <p className="text-gray-600">إجمالي المشاريع</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>العروض</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-navy">{offers.length}</div>
                  <p className="text-gray-600">إجمالي العروض</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>المستخدمين</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-purple-600">{users.length}</div>
                  <p className="text-gray-600">إجمالي المستخدمين</p>
                </CardContent>
              </Card>
            </div>

            {/* Data Tables */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Projects */}
              <Card>
                <CardHeader>
                  <CardTitle>المشاريع الحديثة</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {projects.slice(0, 5).map((project) => (
                      <div key={project.id} className="border-b pb-4">
                        <h4 className="font-semibold">{project.title}</h4>
                        <p className="text-sm text-gray-600">{project.category} • {project.location}</p>
                        <div className="flex items-center justify-between mt-2">
                          <Badge variant={project.status === 'OPEN' ? 'default' : 'secondary'}>
                            {project.status}
                          </Badge>
                          <span className="text-sm text-teal">{project.budget}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Offers */}
              <Card>
                <CardHeader>
                  <CardTitle>العروض الحديثة</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {offers.slice(0, 5).map((offer) => (
                      <div key={offer.id} className="border-b pb-4">
                        <div className="flex items-center justify-between">
                          <span className="font-semibold">₺{offer.amount.toLocaleString()}</span>
                          <Badge variant={offer.status === 'PENDING' ? 'default' : 'secondary'}>
                            {offer.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{offer.description}</p>
                        <p className="text-xs text-gray-500">
                          {offer.estimatedDuration} يوم • {offer.craftsman?.name}
                        </p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
