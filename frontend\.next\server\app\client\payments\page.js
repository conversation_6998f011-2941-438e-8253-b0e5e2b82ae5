(()=>{var e={};e.id=4842,e.ids=[4842],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},40853:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=s(70260),a=s(28203),n=s(25155),i=s.n(n),l=s(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let o=["",{children:["client",{children:["payments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,73109)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\client\\payments\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\client\\payments\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/client/payments/page",pathname:"/client/payments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},16879:(e,t,s)=>{Promise.resolve().then(s.bind(s,73109))},25127:(e,t,s)=>{Promise.resolve().then(s.bind(s,1969))},1969:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c});var r=s(45512),a=s(58009),n=s(28531),i=s.n(n),l=s(60297),d=s(42847),o=s(87272);let c=()=>{let[e,t]=(0,a.useState)("all"),s=[{id:1,projectId:1,projectTitle:"تجديد المطبخ الرئيسي",craftsmanName:"أحمد النجار",amount:"₺7,500",status:"مكتمل",paymentMethod:"بطاقة ائتمان",date:"2024-02-15",invoiceNumber:"INV-001",description:"دفعة كاملة لمشروع تجديد المطبخ",type:"دفعة كاملة"},{id:2,projectId:2,projectTitle:"إصلاح نظام السباكة",craftsmanName:"علي السباك",amount:"₺1,100",status:"معلق",paymentMethod:"تحويل بنكي",date:"2024-02-20",invoiceNumber:"INV-002",description:"دفعة مقدمة 50% من قيمة المشروع",type:"دفعة مقدمة"},{id:3,projectId:4,projectTitle:"تركيب نظام كهرباء ذكي",craftsmanName:"سارة الكهربائية",amount:"₺2,400",status:"مرفوض",paymentMethod:"محفظة إلكترونية",date:"2024-02-18",invoiceNumber:"INV-003",description:"دفعة مقدمة للمشروع",type:"دفعة مقدمة"},{id:4,projectId:3,projectTitle:"دهان الشقة الكاملة",craftsmanName:"محمد الدهان",amount:"₺3,200",status:"مكتمل",paymentMethod:"نقداً",date:"2024-01-25",invoiceNumber:"INV-004",description:"دفعة كاملة بعد إتمام العمل",type:"دفعة نهائية"}],n=e=>{switch(e){case"مكتمل":return"bg-green-100 text-green-800 border-green-200";case"معلق":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"مرفوض":return"bg-red-100 text-red-800 border-red-200";case"قيد المعالجة":return"bg-blue-100 text-blue-800 border-blue-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},c=e=>{switch(e){case"بطاقة ائتمان":return"\uD83D\uDCB3";case"تحويل بنكي":return"\uD83C\uDFE6";case"محفظة إلكترونية":return"\uD83D\uDCF1";case"نقداً":return"\uD83D\uDCB5";default:return"\uD83D\uDCB0"}},x=s.filter(t=>"all"===e||t.status===e),m=[{title:"إجمالي المدفوعات",value:"₺14,200",color:"text-blue-600"},{title:"المدفوعات المكتملة",value:s.filter(e=>"مكتمل"===e.status).length.toString(),color:"text-green-600"},{title:"المدفوعات المعلقة",value:s.filter(e=>"معلق"===e.status).length.toString(),color:"text-yellow-600"},{title:"هذا الشهر",value:"₺8,600",color:"text-purple-600"}];return(0,r.jsx)(l.A,{requiredRoles:"client",children:(0,r.jsx)(d.A,{title:"المدفوعات والفواتير",subtitle:"إدارة ومتابعة جميع مدفوعاتك",actions:(0,r.jsx)(o.Button,{className:"bg-gradient-to-r from-navy to-teal",children:"إضافة طريقة دفع"}),children:(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:m.map((e,t)=>(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-1",children:e.title}),(0,r.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:e.value})]}),(0,r.jsx)("div",{className:`text-3xl ${e.color}`,children:"\uD83D\uDCB3"})]})},t))}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:[{id:"all",label:"جميع المدفوعات"},{id:"مكتمل",label:"مكتملة"},{id:"معلق",label:"معلقة"},{id:"قيد المعالجة",label:"قيد المعالجة"},{id:"مرفوض",label:"مرفوضة"}].map(s=>(0,r.jsx)("button",{onClick:()=>t(s.id),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${e===s.id?"bg-navy text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:s.label},s.id))})}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"رقم الفاتورة"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"المشروع"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"الحرفي"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"المبلغ"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"طريقة الدفع"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"الحالة"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"التاريخ"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"الإجراءات"})]})}),(0,r.jsx)("tbody",{children:x.map(e=>(0,r.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,r.jsxs)("td",{className:"py-4 px-6",children:[(0,r.jsx)("div",{className:"font-medium text-navy",children:e.invoiceNumber}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.type})]}),(0,r.jsxs)("td",{className:"py-4 px-6",children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.projectTitle}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white text-sm font-bold",children:e.craftsmanName.charAt(0)}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:e.craftsmanName})]})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsx)("span",{className:"text-lg font-bold text-gray-900",children:e.amount})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,r.jsx)("span",{className:"text-lg",children:c(e.paymentMethod)}),(0,r.jsx)("span",{className:"text-gray-700",children:e.paymentMethod})]})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsx)("span",{className:`px-3 py-1 rounded-full text-xs font-medium border ${n(e.status)}`,children:e.status})}),(0,r.jsx)("td",{className:"py-4 px-6 text-gray-600",children:e.date}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("div",{className:"flex space-x-2 space-x-reverse",children:[(0,r.jsx)(o.Button,{size:"sm",variant:"outline",className:"border-navy text-navy hover:bg-navy hover:text-white",children:"عرض الفاتورة"}),"مكتمل"===e.status&&(0,r.jsx)(o.Button,{size:"sm",variant:"outline",className:"border-gray-300 text-gray-700 hover:bg-gray-100",children:"تحميل PDF"})]})})]},e.id))})]})})}),0===x.length&&(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCB3"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"لا توجد مدفوعات"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"لم يتم العثور على مدفوعات بالمعايير المحددة"}),(0,r.jsx)(i(),{href:"/client/projects",children:(0,r.jsx)(o.Button,{className:"bg-gradient-to-r from-navy to-teal",children:"عرض مشاريعي"})})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"طرق الدفع المحفوظة"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:border-navy transition-colors duration-200",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCB3"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"بطاقة ائتمان"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"**** **** **** 1234"})]})]}),(0,r.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded text-xs",children:"افتراضية"})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,r.jsx)("span",{children:"انتهاء الصلاحية: 12/26"}),(0,r.jsx)("button",{className:"text-navy hover:text-navy/80",children:"تعديل"})]})]}),(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:border-navy transition-colors duration-200",children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFE6"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"حساب بنكي"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"البنك التجاري السوري"})]})]})}),(0,r.jsxs)("div",{className:"flex justify-between text-sm text-gray-600",children:[(0,r.jsx)("span",{children:"رقم الحساب: ****5678"}),(0,r.jsx)("button",{className:"text-navy hover:text-navy/80",children:"تعديل"})]})]}),(0,r.jsx)("div",{className:"border-2 border-dashed border-gray-300 rounded-lg p-4 flex items-center justify-center hover:border-navy transition-colors duration-200 cursor-pointer",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("span",{className:"text-3xl text-gray-400 mb-2 block",children:"➕"}),(0,r.jsx)("p",{className:"text-gray-600 font-medium",children:"إضافة طريقة دفع جديدة"})]})})]})]})]})})})}},73109:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\g\\\\Duzan Website\\\\frontend\\\\src\\\\app\\\\client\\\\payments\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\client\\payments\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,3351,7180,1057,1799],()=>s(40853));module.exports=r})();