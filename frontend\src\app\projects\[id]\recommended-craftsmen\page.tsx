'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import supabase from '@/lib/supabase';
import { recommendCraftsmen } from '@/lib/craftsman-recommendation';

export default function RecommendedCraftsmenPage() {
  const params = useParams();
  const projectId = params.id as string;

  const [project, setProject] = useState<any>(null);
  const [recommendations, setRecommendations] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProjectAndRecommendations();
  }, [projectId]);

  const loadProjectAndRecommendations = async () => {
    try {
      // تحميل المشروع
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;
      setProject(projectData);

      // الحصول على اقتراحات الحرفيين
      const recommendations = await recommendCraftsmen({
        projectCategory: projectData.category,
        projectLocation: projectData.location,
        budgetMin: projectData.budget_min || 0,
        budgetMax: projectData.budget_max || 1000000,
        urgency: 'medium'
      });

      setRecommendations(recommendations);

    } catch (error) {
      console.error('Error loading recommendations:', error);
      alert('خطأ في تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  const inviteCraftsman = async (craftsmanId: string) => {
    try {
      // في المستقبل: إرسال دعوة للحرفي
      // حالياً: مجرد رسالة تأكيد
      alert('تم إرسال دعوة للحرفي بنجاح!');
    } catch (error) {
      console.error('Error inviting craftsman:', error);
      alert('حدث خطأ أثناء إرسال الدعوة');
    }
  };

  if (loading) {
    return (
      <DashboardLayout title="الحرفيين المقترحين">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout 
      title="الحرفيين المقترحين"
      subtitle={`أفضل الحرفيين المناسبين للمشروع: ${project?.title}`}
    >
      <div className="space-y-6">
        {/* معلومات المشروع */}
        <Card>
          <CardHeader>
            <CardTitle>تفاصيل المشروع</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <span className="text-gray-500">التخصص:</span>
                <p className="font-medium">{project?.category}</p>
              </div>
              <div>
                <span className="text-gray-500">الموقع:</span>
                <p className="font-medium">{project?.location}</p>
              </div>
              <div>
                <span className="text-gray-500">الميزانية:</span>
                <p className="font-medium">
                  {project?.budget_min?.toLocaleString()} - {project?.budget_max?.toLocaleString()} ل.س
                </p>
              </div>
              <div>
                <span className="text-gray-500">الموعد النهائي:</span>
                <p className="font-medium">
                  {new Date(project?.deadline).toLocaleDateString('ar-SA')}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* الحرفيين المقترحين */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-bold text-navy">الحرفيين المقترحين ({recommendations.length})</h2>
            <div className="text-sm text-gray-600">
              مرتبين حسب الملاءمة للمشروع
            </div>
          </div>
          
          {recommendations.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <div className="text-6xl mb-4">🔍</div>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">لا توجد اقتراحات</h3>
                <p className="text-gray-500">لم نجد حرفيين مناسبين لهذا المشروع حالياً</p>
              </CardContent>
            </Card>
          ) : (
            recommendations.map((recommendation, index) => {
              const { craftsman, score, reasons } = recommendation;
              
              return (
                <Card key={craftsman.id} className={`border-l-4 ${
                  index === 0 ? 'border-l-green-500 bg-green-50' : 
                  index === 1 ? 'border-l-blue-500 bg-blue-50' : 
                  index === 2 ? 'border-l-orange-500 bg-orange-50' : 
                  'border-l-gray-400'
                }`}>
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4 space-x-reverse mb-4">
                          <div className="w-16 h-16 bg-gradient-to-r from-teal to-navy rounded-full flex items-center justify-center text-white font-bold text-xl">
                            {craftsman.name?.charAt(0) || 'ح'}
                          </div>
                          <div>
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <h3 className="text-xl font-semibold text-navy">{craftsman.name}</h3>
                              {index < 3 && (
                                <span className={`px-2 py-1 rounded-full text-xs font-bold ${
                                  index === 0 ? 'bg-green-100 text-green-800' :
                                  index === 1 ? 'bg-blue-100 text-blue-800' :
                                  'bg-orange-100 text-orange-800'
                                }`}>
                                  {index === 0 ? 'الأفضل' : index === 1 ? 'ممتاز' : 'جيد جداً'}
                                </span>
                              )}
                            </div>
                            <p className="text-gray-600">{craftsman.email}</p>
                            <div className="flex items-center space-x-2 space-x-reverse mt-1">
                              <span className="text-yellow-500">⭐</span>
                              <span className="text-sm font-medium">4.8</span>
                              <span className="text-sm text-gray-500">(25 تقييم)</span>
                            </div>
                          </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div>
                            <span className="text-gray-500 text-sm">الموقع:</span>
                            <p className="font-medium">{craftsman.location || 'غير محدد'}</p>
                          </div>
                          <div>
                            <span className="text-gray-500 text-sm">المشاريع المكتملة:</span>
                            <p className="font-medium">15 مشروع</p>
                          </div>
                          <div>
                            <span className="text-gray-500 text-sm">نقاط الملاءمة:</span>
                            <p className="font-bold text-lg text-teal">{score}/100</p>
                          </div>
                        </div>

                        <div className="mb-4">
                          <span className="text-gray-500 text-sm">أسباب الاقتراح:</span>
                          <div className="flex flex-wrap gap-2 mt-2">
                            {reasons.map((reason, idx) => (
                              <span 
                                key={idx}
                                className="px-3 py-1 bg-teal-100 text-teal-800 rounded-full text-sm"
                              >
                                {reason}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
                          {craftsman.verified && (
                            <div className="flex items-center">
                              <span className="text-blue-500 ml-1">✓</span>
                              <span>موثق</span>
                            </div>
                          )}
                          <div className="flex items-center">
                            <span className="text-green-500 ml-1">🟢</span>
                            <span>متاح</span>
                          </div>
                          <div>
                            📞 {craftsman.phone || 'غير متوفر'}
                          </div>
                        </div>
                      </div>

                      <div className="mr-4 space-y-2">
                        <Button
                          onClick={() => inviteCraftsman(craftsman.id)}
                          className="bg-gradient-to-r from-teal to-navy w-full"
                        >
                          دعوة للمشروع
                        </Button>
                        <Button
                          variant="outline"
                          className="w-full"
                          onClick={() => window.open(`/craftsmen/${craftsman.id}`, '_blank')}
                        >
                          عرض الملف الشخصي
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })
          )}
        </div>

        {/* نصائح */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-blue-900 mb-3">💡 نصائح لاختيار الحرفي المناسب</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
              <div>• راجع أعمال الحرفي السابقة</div>
              <div>• تأكد من التقييمات والمراجعات</div>
              <div>• اطلب عرض سعر مفصل</div>
              <div>• تأكد من توفر الحرفي في الوقت المطلوب</div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
