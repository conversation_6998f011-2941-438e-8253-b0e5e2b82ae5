'use client';

import React, { useState } from 'react';
import { useJobForm } from '@/context/JobFormContext';

const Step2Requirements = () => {
  const { formData, updateFormData } = useJobForm();
  const [newSkill, setNewSkill] = useState('');
  const [errors, setErrors] = useState({
    requirements: '',
    skills: '',
  });

  const handleAddSkill = () => {
    if (newSkill.trim()) {
      const updatedSkills = [...formData.skills, newSkill.trim()];
      updateFormData({ skills: updatedSkills });
      setNewSkill('');
    }
  };

  const handleRemoveSkill = (index: number) => {
    const updatedSkills = formData.skills.filter((_, i) => i !== index);
    updateFormData({ skills: updatedSkills });
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddSkill();
    }
  };

  const validateForm = () => {
    let isValid = true;
    const newErrors = {
      requirements: '',
      skills: '',
    };

    if (!formData.requirements || !formData.requirements.trim()) {
      newErrors.requirements = 'متطلبات المشروع مطلوبة';
      isValid = false;
    }

    if (!formData.skills || formData.skills.length === 0) {
      newErrors.skills = 'يجب إضافة مهارة واحدة على الأقل';
      isValid = false;
    }

    setErrors(newErrors);
    return isValid;
  };

  // Mock file upload function
  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      // In a real app, you would upload these files to a server
      // For now, we'll just store the file names
      const fileNames = Array.from(files).map(file => file.name);
      updateFormData({ attachments: [...formData.attachments, ...fileNames] });
    }
  };

  const handleRemoveAttachment = (index: number) => {
    const updatedAttachments = formData.attachments.filter((_, i) => i !== index);
    updateFormData({ attachments: updatedAttachments });
  };

  return (
    <div>
      <h2 className="text-xl font-bold text-navy mb-6">متطلبات ومواصفات المشروع</h2>

      <div className="space-y-6">
        <div>
          <label htmlFor="requirements" className="block text-gray-700 font-medium mb-2">
            متطلبات المشروع <span className="text-red-500">*</span>
          </label>
          <textarea
            id="requirements"
            value={formData.requirements}
            onChange={(e) => updateFormData({ requirements: e.target.value })}
            rows={5}
            className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
            placeholder="اذكر المتطلبات والمواصفات التفصيلية للمشروع..."
          ></textarea>
          {errors.requirements && <p className="mt-1 text-red-500 text-sm">{errors.requirements}</p>}
        </div>

        <div>
          <label className="block text-gray-700 font-medium mb-2">
            المهارات المطلوبة <span className="text-red-500">*</span>
          </label>
          <div className="flex">
            <input
              type="text"
              value={newSkill}
              onChange={(e) => setNewSkill(e.target.value)}
              onKeyDown={handleKeyDown}
              className="flex-grow px-4 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
              placeholder="أضف مهارة..."
            />
            <button
              type="button"
              onClick={handleAddSkill}
              className="px-4 py-2 bg-teal text-white rounded-l-md hover:bg-teal/90"
            >
              إضافة
            </button>
          </div>
          {errors.skills && <p className="mt-1 text-red-500 text-sm">{errors.skills}</p>}

          {formData.skills.length > 0 && (
            <div className="mt-3 flex flex-wrap gap-2">
              {formData.skills.map((skill, index) => (
                <div
                  key={index}
                  className="bg-skyblue px-3 py-1 rounded-full flex items-center"
                >
                  <span className="text-navy">{skill}</span>
                  <button
                    type="button"
                    onClick={() => handleRemoveSkill(index)}
                    className="ml-2 text-navy hover:text-red-500"
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>

        <div>
          <label className="block text-gray-700 font-medium mb-2">
            المرفقات (اختياري)
          </label>
          <div className="border-2 border-dashed border-gray-300 rounded-md p-6 text-center">
            <input
              type="file"
              id="file-upload"
              multiple
              onChange={handleFileUpload}
              className="hidden"
            />
            <label
              htmlFor="file-upload"
              className="cursor-pointer inline-flex items-center justify-center px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              <svg
                className="ml-2 -mr-1 h-5 w-5 text-gray-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
                aria-hidden="true"
              >
                <path
                  fillRule="evenodd"
                  d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z"
                  clipRule="evenodd"
                />
              </svg>
              اختر ملفات
            </label>
            <p className="mt-2 text-sm text-gray-500">
              يمكنك رفع صور، مستندات PDF، أو أي ملفات أخرى ذات صلة بالمشروع
            </p>
          </div>

          {formData.attachments.length > 0 && (
            <div className="mt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">الملفات المرفقة:</h4>
              <ul className="space-y-2">
                {formData.attachments.map((file, index) => (
                  <li key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                    <span className="text-sm text-gray-700">{file}</span>
                    <button
                      type="button"
                      onClick={() => handleRemoveAttachment(index)}
                      className="text-red-500 hover:text-red-700"
                    >
                      حذف
                    </button>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Step2Requirements;
