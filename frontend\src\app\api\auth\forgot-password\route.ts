import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { 
  validateEmail, 
  generatePasswordResetToken,
  getClientIP,
  getUserAgent,
  checkRateLimit
} from '@/lib/security';
import { sendPasswordResetEmail } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    // التحقق من معدل الطلبات
    const clientIP = getClientIP(request);
    const rateLimit = checkRateLimit(`forgot_password_${clientIP}`, 3, 900000); // 3 طلبات كل 15 دقيقة
    
    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          error: 'تم تجاوز الحد المسموح من المحاولات. يرجى المحاولة بعد 15 دقيقة',
          resetTime: rateLimit.resetTime
        },
        { status: 429 }
      );
    }

    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من صحة البريد الإلكتروني
    if (!validateEmail(email)) {
      return NextResponse.json(
        { error: 'البريد الإلكتروني غير صحيح' },
        { status: 400 }
      );
    }

    // البحث عن المستخدم
    const user = await prisma.user.findUnique({
      where: { email: email.toLowerCase().trim() }
    });

    // لأسباب أمنية، نعيد نفس الرسالة حتى لو لم يكن المستخدم موجوداً
    const successMessage = 'إذا كان البريد الإلكتروني موجود في نظامنا، ستتلقى رابط إعادة تعيين كلمة المرور';

    if (!user) {
      // تسجيل محاولة إعادة تعيين لبريد غير موجود
      console.log(`محاولة إعادة تعيين كلمة المرور لبريد غير موجود: ${email}`);
      
      return NextResponse.json({
        message: successMessage
      });
    }

    // التحقق من حالة الحساب
    if (!user.isActive) {
      return NextResponse.json({
        message: successMessage
      });
    }

    // إنشاء رمز إعادة تعيين كلمة المرور
    const resetToken = generatePasswordResetToken();
    const resetExpiry = new Date(Date.now() + 60 * 60 * 1000); // ساعة واحدة

    // حفظ الرمز في قاعدة البيانات
    await prisma.user.update({
      where: { id: user.id },
      data: {
        passwordResetToken: resetToken,
        passwordResetExpiry: resetExpiry
      }
    });

    // إرسال بريد إعادة تعيين كلمة المرور
    const emailSent = await sendPasswordResetEmail(user.email, user.name, resetToken);

    // تسجيل النشاط
    await prisma.auditLog.create({
      data: {
        userId: user.id,
        action: 'PASSWORD_RESET_REQUESTED',
        entity: 'User',
        entityId: user.id,
        ipAddress: clientIP,
        userAgent: getUserAgent(request)
      }
    });

    if (!emailSent) {
      console.error('فشل في إرسال بريد إعادة تعيين كلمة المرور للمستخدم:', user.email);
    }

    return NextResponse.json({
      message: successMessage
    });

  } catch (error) {
    console.error('خطأ في طلب إعادة تعيين كلمة المرور:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
