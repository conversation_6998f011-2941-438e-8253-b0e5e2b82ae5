import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { ProjectStatus } from '@prisma/client';

// GET /api/projects/[id] - جلب مشروع واحد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const project = await prisma.project.findUnique({
      where: { id: params.id },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            avatar: true,
            location: true,
          },
        },
        offers: {
          include: {
            craftsman: {
              select: {
                id: true,
                name: true,
                email: true,
                avatar: true,
                craftsmanProfile: {
                  select: {
                    rating: true,
                    completedProjects: true,
                    skills: true,
                  },
                },
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        payments: {
          orderBy: { createdAt: 'desc' },
        },
        reviews: {
          include: {
            client: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
            craftsman: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
          orderBy: { createdAt: 'desc' },
        },
        messages: {
          include: {
            sender: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
            receiver: {
              select: {
                id: true,
                name: true,
                avatar: true,
              },
            },
          },
          orderBy: { createdAt: 'asc' },
        },
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: 'المشروع غير موجود' },
        { status: 404 }
      );
    }

    return NextResponse.json(project);
  } catch (error) {
    console.error('Error fetching project:', error);
    return NextResponse.json(
      { error: 'خطأ في جلب المشروع' },
      { status: 500 }
    );
  }
}

// PUT /api/projects/[id] - تحديث مشروع
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const {
      title,
      description,
      category,
      budget,
      deadline,
      location,
      priority,
      materials,
      workType,
      requirements,
      images,
      status,
    } = body;

    const project = await prisma.project.update({
      where: { id: params.id },
      data: {
        ...(title && { title }),
        ...(description && { description }),
        ...(category && { category }),
        ...(budget && { budget }),
        ...(deadline && { deadline: new Date(deadline) }),
        ...(location && { location }),
        ...(priority && { priority }),
        ...(materials && { materials }),
        ...(workType && { workType }),
        ...(requirements && { requirements }),
        ...(images && { images }),
        ...(status && { status }),
      },
      include: {
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        _count: {
          select: {
            offers: true,
            reviews: true,
          },
        },
      },
    });

    return NextResponse.json(project);
  } catch (error) {
    console.error('Error updating project:', error);
    return NextResponse.json(
      { error: 'خطأ في تحديث المشروع' },
      { status: 500 }
    );
  }
}

// DELETE /api/projects/[id] - حذف مشروع
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.project.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: 'تم حذف المشروع بنجاح' });
  } catch (error) {
    console.error('Error deleting project:', error);
    return NextResponse.json(
      { error: 'خطأ في حذف المشروع' },
      { status: 500 }
    );
  }
}
