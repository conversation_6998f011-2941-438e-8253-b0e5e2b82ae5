import { NextRequest, NextResponse } from 'next/server';
import { mockProjects, mockUsers, mockOffers } from '@/lib/mock-data';

// GET /api/projects/[id] - جلب مشروع محدد
export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;
    
    const project = mockProjects.find(p => p.id === id);
    if (!project) {
      return NextResponse.json(
        { error: 'المشروع غير موجود' },
        { status: 404 }
      );
    }

    // زيادة عدد المشاهدات
    project.views += 1;

    // إضافة بيانات العميل والحرفي
    const client = mockUsers.find(user => user.id === project.clientId);
    const craftsman = project.craftsmanId ? 
      mockUsers.find(user => user.id === project.craftsmanId) : null;

    // جلب العروض المقدمة على هذا المشروع
    const offers = mockOffers.filter(offer => offer.projectId === id);
    const offersWithCraftsmen = offers.map(offer => {
      const offerCraftsman = mockUsers.find(user => user.id === offer.craftsmanId);
      return {
        ...offer,
        craftsman: offerCraftsman ? {
          id: offerCraftsman.id,
          name: offerCraftsman.name,
          location: offerCraftsman.location,
          avatar: offerCraftsman.avatar,
          bio: offerCraftsman.bio
        } : null
      };
    });

    const projectWithDetails = {
      ...project,
      client: client ? {
        id: client.id,
        name: client.name,
        location: client.location,
        avatar: client.avatar,
        bio: client.bio
      } : null,
      craftsman: craftsman ? {
        id: craftsman.id,
        name: craftsman.name,
        location: craftsman.location,
        avatar: craftsman.avatar,
        bio: craftsman.bio
      } : null,
      offers: offersWithCraftsmen
    };

    return NextResponse.json({ project: projectWithDetails });
  } catch (error) {
    console.error('Error fetching project:', error);
    return NextResponse.json(
      { error: 'خطأ في جلب المشروع' },
      { status: 500 }
    );
  }
}

// PUT /api/projects/[id] - تحديث مشروع
export async function PUT(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const { id } = context.params;
    const body = await request.json();

    const projectIndex = mockProjects.findIndex(p => p.id === id);
    if (projectIndex === -1) {
      return NextResponse.json(
        { error: 'المشروع غير موجود' },
        { status: 404 }
      );
    }

    // تحديث المشروع
    const updatedProject = {
      ...mockProjects[projectIndex],
      ...body,
      id, // التأكد من عدم تغيير المعرف
    };

    mockProjects[projectIndex] = updatedProject;

    return NextResponse.json({
      project: updatedProject,
      message: 'تم تحديث المشروع بنجاح'
    });
  } catch (error) {
    console.error('Error updating project:', error);
    return NextResponse.json(
      { error: 'خطأ في تحديث المشروع' },
      { status: 500 }
    );
  }
}

// DELETE /api/projects/[id] - حذف مشروع
export async function DELETE(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const { id } = context.params;

    const projectIndex = mockProjects.findIndex(p => p.id === id);
    if (projectIndex === -1) {
      return NextResponse.json(
        { error: 'المشروع غير موجود' },
        { status: 404 }
      );
    }

    // حذف المشروع
    mockProjects.splice(projectIndex, 1);

    // حذف العروض المرتبطة بالمشروع
    const offerIndices = mockOffers
      .map((offer, index) => offer.projectId === id ? index : -1)
      .filter(index => index !== -1)
      .reverse(); // البدء من النهاية لتجنب تغيير الفهارس

    offerIndices.forEach(index => mockOffers.splice(index, 1));

    return NextResponse.json({ message: 'تم حذف المشروع بنجاح' });
  } catch (error) {
    console.error('Error deleting project:', error);
    return NextResponse.json(
      { error: 'خطأ في حذف المشروع' },
      { status: 500 }
    );
  }
}
