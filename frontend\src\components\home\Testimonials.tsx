'use client';

import React, { useState, useEffect } from 'react';

const testimonials = [
  {
    id: 1,
    name: 'أحم<PERSON> محمد',
    role: 'صاحب مشروع',
    location: 'دمشق، المزة',
    content: 'وجدت حرفيين ممتازين من خلال منصة دوزان. كانت التجربة سلسة من البداية إلى النهاية، وأنا سعيد جدًا بالنتائج.',
    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
    rating: 5,
    project: 'تجديد شقة كاملة',
    date: 'منذ شهرين',
    verified: true
  },
  {
    id: 2,
    name: 'سارة أحمد',
    role: 'مالكة منزل',
    location: 'حلب، الشهباء',
    content: 'ساعدتني دوزان في العثور على نجار محترف لتجديد مطبخي. كانت الأسعار معقولة والعمل ممتاز.',
    avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
    rating: 4,
    project: 'تجديد المطبخ',
    date: 'منذ 3 أسابيع',
    verified: true
  },
  {
    id: 3,
    name: 'خالد العلي',
    role: 'نجار محترف',
    location: 'حمص، الوعر',
    content: 'كحرفي، ساعدتني دوزان في الوصول إلى المزيد من العملاء وزيادة دخلي. المنصة سهلة الاستخدام وفريق الدعم ممتاز.',
    avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
    rating: 5,
    project: 'أكثر من 50 مشروع',
    date: 'عضو منذ 8 أشهر',
    verified: true
  },
  {
    id: 4,
    name: 'فاطمة حسن',
    role: 'مهندسة معمارية',
    location: 'اللاذقية، الزراعة',
    content: 'منصة رائعة تسهل التواصل مع الحرفيين المحترفين. استخدمتها في عدة مشاريع وكانت النتائج مذهلة.',
    avatar: 'https://randomuser.me/api/portraits/women/4.jpg',
    rating: 5,
    project: 'مشاريع متعددة',
    date: 'منذ شهر',
    verified: true
  },
  {
    id: 5,
    name: 'محمد الشامي',
    role: 'كهربائي',
    location: 'طرطوس، الكورنيش',
    content: 'بفضل دوزان تمكنت من بناء سمعة ممتازة وزيادة عدد عملائي بشكل كبير. أنصح كل حرفي بالانضمام.',
    avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
    rating: 5,
    project: 'أكثر من 30 مشروع',
    date: 'عضو منذ 6 أشهر',
    verified: true
  },
  {
    id: 6,
    name: 'ليلى قاسم',
    role: 'صاحبة مطعم',
    location: 'دمشق، باب توما',
    content: 'احتجت لتجديد مطعمي بالكامل، ومن خلال دوزان وجدت فريق عمل متكامل أنجز المشروع في الوقت المحدد.',
    avatar: 'https://randomuser.me/api/portraits/women/6.jpg',
    rating: 4,
    project: 'تجديد مطعم',
    date: 'منذ 5 أسابيع',
    verified: true
  }
];

const StarRating = ({ rating }: { rating: number }) => {
  return (
    <div className="flex items-center space-x-1 space-x-reverse">
      {[...Array(5)].map((_, i) => (
        <svg
          key={i}
          className={`h-5 w-5 transition-colors duration-300 ${
            i < rating
              ? 'text-yellow-400 drop-shadow-sm'
              : 'text-white/30'
          }`}
          fill="currentColor"
          viewBox="0 0 20 20"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
        </svg>
      ))}
      <span className="text-white/70 text-sm mr-2">({rating}/5)</span>
    </div>
  );
};

const Testimonials = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);

  // Auto-slide functionality
  useEffect(() => {
    if (!isAutoPlaying) return;

    const interval = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % Math.ceil(testimonials.length / 3));
    }, 5000);

    return () => clearInterval(interval);
  }, [isAutoPlaying]);

  const getVisibleTestimonials = () => {
    const start = currentSlide * 3;
    return testimonials.slice(start, start + 3);
  };

  return (
    <section className="py-20 bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden">
      {/* خلفية هندسية متناسقة */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl"></div>
        <div className="absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl"></div>
        <div className="absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl"></div>
        <div className="absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl"></div>
      </div>

      {/* شبكة نقطية ناعمة */}
      <div className="absolute inset-0 opacity-20">
        <div className="w-full h-full" style={{
          backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',
          backgroundSize: '30px 30px'
        }}></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <div className="inline-block bg-white/20 backdrop-blur-sm rounded-full px-8 py-3 mb-6">
            <span className="text-white text-sm font-medium">💬 آراء عملائنا</span>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            ماذا يقول
            <span className="block pb-2 pt-2 text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 via-orange-300 to-yellow-400">
              عملاؤنا
            </span>
          </h2>
          <p className="text-xl text-white/90 max-w-4xl mx-auto leading-relaxed">
            آراء حقيقية من أصحاب المشاريع والحرفيين الذين استخدموا منصة دوزان وحققوا نجاحات مميزة
          </p>
          <div className="mt-8 w-24 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto rounded-full"></div>
        </div>

        {/* Testimonials Slider */}
        <div
          className="relative"
          onMouseEnter={() => setIsAutoPlaying(false)}
          onMouseLeave={() => setIsAutoPlaying(true)}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {getVisibleTestimonials().map((testimonial, index) => (
              <div
                key={testimonial.id}
                className="group bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 hover:bg-white/20 hover:border-white/40 transition-all duration-500 hover:scale-105 hover:-translate-y-2 shadow-xl"
              >
                {/* Quote Icon */}
                <div className="text-4xl text-yellow-300 mb-4 opacity-50">
                  "
                </div>

                {/* Content */}
                <p className="text-white/90 leading-relaxed mb-6 text-lg italic">
                  {testimonial.content}
                </p>

                {/* Rating */}
                <div className="mb-6">
                  <StarRating rating={testimonial.rating} />
                </div>

                {/* User Info */}
                <div className="flex items-center space-x-4 space-x-reverse">
                  <div className="relative">
                    <img
                      src={testimonial.avatar}
                      alt={testimonial.name}
                      className="w-16 h-16 rounded-full border-3 border-white/30 group-hover:border-white/50 transition-colors duration-300"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = 'https://placehold.co/100x100/C8D9E6/2F4156?text=صورة';
                      }}
                    />
                    {testimonial.verified && (
                      <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                  </div>
                  <div className="flex-1 text-right">
                    <h3 className="font-bold text-white text-lg group-hover:text-yellow-300 transition-colors duration-300">
                      {testimonial.name}
                    </h3>
                    <p className="text-white/70 text-sm">{testimonial.role}</p>
                    <p className="text-white/60 text-xs">{testimonial.location}</p>
                  </div>
                </div>

                {/* Project Info */}
                <div className="mt-6 pt-6 border-t border-white/20">
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-white/70">{testimonial.project}</span>
                    <span className="text-white/60">{testimonial.date}</span>
                  </div>
                </div>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/10 to-orange-500/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            ))}
          </div>

          {/* Navigation Dots */}
          <div className="flex justify-center space-x-3 space-x-reverse">
            {Array.from({ length: Math.ceil(testimonials.length / 3) }).map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  currentSlide === index
                    ? 'bg-yellow-400 w-8'
                    : 'bg-white/40 hover:bg-white/60'
                }`}
              />
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20">
            <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
              انضم إلى آلاف العملاء الراضين
            </h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto text-lg">
              ابدأ مشروعك اليوم واكتشف لماذا يثق بنا الآلاف من العملاء والحرفيين
            </p>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
              <button className="bg-yellow-400 text-navy px-8 py-3 rounded-full font-bold hover:bg-yellow-300 transition-colors duration-300 shadow-lg hover:shadow-xl transform hover:scale-105">
                ابدأ مشروعك الآن
              </button>
              <button className="bg-white/20 backdrop-blur-sm border-2 border-white/30 text-white px-8 py-3 rounded-full font-bold hover:bg-white/30 hover:border-white/50 transition-all duration-300">
                انضم كحرفي
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
