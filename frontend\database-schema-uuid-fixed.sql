-- إن<PERSON><PERSON><PERSON> قاعدة البيانات مع UUID صحيحة

-- حذف الجداول الموجودة إذا كانت موجودة
DROP TABLE IF EXISTS bids CASCADE;
DROP TABLE IF EXISTS projects CASCADE;
DROP TABLE IF EXISTS users CASCADE;

-- <PERSON><PERSON><PERSON><PERSON><PERSON> جدول المستخدمين
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) DEFAULT 'temp_hash',
    role VARCHAR(20) NOT NULL CHECK (role IN ('CLIENT', 'CRAFTSMAN', 'ADMIN')),
    location VARCHAR(100),
    avatar_url TEXT,
    bio TEXT,
    skills TEXT[],
    rating DECIMAL(3,2) DEFAULT 0.00,
    total_reviews INTEGER DEFAULT 0,
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المشاريع
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    location VARCHAR(100) NOT NULL,
    budget_min INTEGER,
    budget_max INTEGER,
    deadline DATE,
    priority VARCHAR(20) DEFAULT 'MEDIUM' CHECK (priority IN ('LOW', 'MEDIUM', 'HIGH', 'URGENT')),
    requirements TEXT,
    images TEXT[],
    status VARCHAR(20) DEFAULT 'OPEN' CHECK (status IN ('OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED')),
    client_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    craftsman_id UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول العروض
CREATE TABLE bids (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    craftsman_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL,
    description TEXT NOT NULL,
    estimated_duration VARCHAR(100),
    materials_included BOOLEAN DEFAULT FALSE,
    warranty_period VARCHAR(100),
    start_date DATE,
    status VARCHAR(20) DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'ACCEPTED', 'REJECTED', 'WITHDRAWN')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(project_id, craftsman_id)
);

-- إنشاء فهارس
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_location ON users(location);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_category ON projects(category);
CREATE INDEX idx_projects_location ON projects(location);
CREATE INDEX idx_projects_client_id ON projects(client_id);
CREATE INDEX idx_projects_craftsman_id ON projects(craftsman_id);
CREATE INDEX idx_bids_project_id ON bids(project_id);
CREATE INDEX idx_bids_craftsman_id ON bids(craftsman_id);
CREATE INDEX idx_bids_status ON bids(status);

-- إنشاء triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON projects
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bids_updated_at BEFORE UPDATE ON bids
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- إدراج بيانات تجريبية مع UUID صحيحة
DO $$
DECLARE
    client1_id UUID := '550e8400-e29b-41d4-a716-************';
    client2_id UUID := '550e8400-e29b-41d4-a716-************';
    client3_id UUID := '550e8400-e29b-41d4-a716-************';
    craftsman1_id UUID := '550e8400-e29b-41d4-a716-************';
    craftsman2_id UUID := '550e8400-e29b-41d4-a716-************';
    craftsman3_id UUID := '550e8400-e29b-41d4-a716-************';
    admin_id UUID := '550e8400-e29b-41d4-a716-************';
    project1_id UUID := '550e8400-e29b-41d4-a716-************';
    project2_id UUID := '550e8400-e29b-41d4-a716-************';
    project3_id UUID := '550e8400-e29b-41d4-a716-************';
    project4_id UUID := '550e8400-e29b-41d4-a716-************';
    project5_id UUID := '550e8400-e29b-41d4-a716-************';
BEGIN
    -- إدراج المستخدمين
    INSERT INTO users (id, name, email, phone, password_hash, role, location) VALUES
    -- العملاء
    (client1_id, 'أحمد محمد', '<EMAIL>', '+************', 'hashed_password_1', 'CLIENT', 'دمشق'),
    (client2_id, 'فاطمة علي', '<EMAIL>', '+************', 'hashed_password_2', 'CLIENT', 'حلب'),
    (client3_id, 'محمد حسن', '<EMAIL>', '+************', 'hashed_password_3', 'CLIENT', 'حمص'),
    
    -- الحرفيين
    (craftsman1_id, 'محمد النجار', '<EMAIL>', '+************', 'hashed_password_4', 'CRAFTSMAN', 'دمشق'),
    (craftsman2_id, 'أحمد السباك', '<EMAIL>', '+************', 'hashed_password_5', 'CRAFTSMAN', 'حلب'),
    (craftsman3_id, 'علي الكهربائي', '<EMAIL>', '+************', 'hashed_password_6', 'CRAFTSMAN', 'دمشق'),
    
    -- المدير
    (admin_id, 'مدير النظام', '<EMAIL>', '+963999999999', 'hashed_password_admin', 'ADMIN', 'دمشق');

    -- إدراج المشاريع
    INSERT INTO projects (id, title, description, category, location, budget_min, budget_max, deadline, priority, client_id, status) VALUES
    -- مشاريع مفتوحة
    (project1_id, 'تجديد مطبخ عصري', 'تجديد مطبخ كامل بتصميم عصري مع خزائن جديدة وأجهزة حديثة', 'نجارة', 'دمشق', 200000, 350000, CURRENT_DATE + INTERVAL '30 days', 'HIGH', client1_id, 'OPEN'),
    
    (project2_id, 'إصلاح نظام السباكة', 'إصلاح وتجديد نظام السباكة في الشقة مع تغيير الأنابيب القديمة', 'سباكة', 'حلب', 80000, 150000, CURRENT_DATE + INTERVAL '15 days', 'MEDIUM', client2_id, 'OPEN'),
    
    (project3_id, 'تركيب نظام إضاءة LED', 'تركيب نظام إضاءة LED متطور في المنزل مع أنظمة تحكم ذكية', 'كهرباء', 'دمشق', 120000, 200000, CURRENT_DATE + INTERVAL '20 days', 'MEDIUM', client3_id, 'OPEN'),
    
    -- مشاريع قيد التنفيذ
    (project4_id, 'تركيب خزائن غرفة نوم', 'تصميم وتركيب خزائن مدمجة لغرفة النوم الرئيسية', 'نجارة', 'دمشق', 180000, 280000, CURRENT_DATE + INTERVAL '10 days', 'HIGH', client1_id, 'IN_PROGRESS'),
    
    -- مشاريع مكتملة
    (project5_id, 'إصلاح نظام التكييف', 'صيانة وإصلاح نظام التكييف المركزي', 'تكييف', 'دمشق', 80000, 120000, CURRENT_DATE - INTERVAL '5 days', 'MEDIUM', client3_id, 'COMPLETED');

    -- تحديث المشاريع بالحرفيين
    UPDATE projects SET craftsman_id = craftsman1_id WHERE id = project4_id;
    UPDATE projects SET craftsman_id = craftsman3_id WHERE id = project5_id;

    -- إدراج العروض
    INSERT INTO bids (project_id, craftsman_id, amount, description, estimated_duration, materials_included, warranty_period, start_date, status) VALUES
    -- عروض للمشاريع المفتوحة
    (project1_id, craftsman1_id, 280000, 'سأقوم بتجديد المطبخ بالكامل باستخدام أفضل المواد. خبرة 15 سنة في تصميم المطابخ العصرية.', '12-15 يوم', true, '24 شهر', CURRENT_DATE + INTERVAL '7 days', 'PENDING'),
    
    (project2_id, craftsman2_id, 120000, 'إصلاح شامل لنظام السباكة مع تغيير جميع الأنابيب القديمة. استخدام أنابيب PPR عالية الجودة.', '5-7 أيام', true, '10 سنوات', CURRENT_DATE + INTERVAL '3 days', 'PENDING'),
    
    (project3_id, craftsman3_id, 160000, 'تركيب نظام إضاءة LED ذكي مع أنظمة تحكم عن بعد. جميع المواد من ماركات عالمية معتمدة.', '6-8 أيام', true, '5 سنوات', CURRENT_DATE + INTERVAL '4 days', 'PENDING'),
    
    -- عروض مقبولة للمشاريع المنجزة
    (project4_id, craftsman1_id, 230000, 'تصميم وتركيب خزائن مدمجة حسب المقاسات مع مرايا وإضاءة داخلية.', '8-10 أيام', true, '24 شهر', CURRENT_DATE - INTERVAL '5 days', 'ACCEPTED'),
    
    (project5_id, craftsman3_id, 100000, 'صيانة شاملة لنظام التكييف المركزي مع تنظيف الوحدات وتغيير الفلاتر.', '3-4 أيام', true, '12 شهر', CURRENT_DATE - INTERVAL '25 days', 'ACCEPTED');

END $$;

-- عرض إحصائيات
SELECT 
    'إجمالي المستخدمين' as النوع,
    COUNT(*) as العدد
FROM users
UNION ALL
SELECT 
    'العملاء' as النوع,
    COUNT(*) as العدد
FROM users WHERE role = 'CLIENT'
UNION ALL
SELECT 
    'الحرفيين' as النوع,
    COUNT(*) as العدد
FROM users WHERE role = 'CRAFTSMAN'
UNION ALL
SELECT 
    'إجمالي المشاريع' as النوع,
    COUNT(*) as العدد
FROM projects
UNION ALL
SELECT 
    'المشاريع المفتوحة' as النوع,
    COUNT(*) as العدد
FROM projects WHERE status = 'OPEN'
UNION ALL
SELECT 
    'إجمالي العروض' as النوع,
    COUNT(*) as العدد
FROM bids;

-- عرض المعرفات للاستخدام في التطبيق
SELECT 'معرفات المستخدمين:' as info;
SELECT name, id, role FROM users ORDER BY role, name;

SELECT 'معرفات المشاريع:' as info;
SELECT title, id, status FROM projects ORDER BY status, title;
