import { createClient } from '@/utils/supabase/server';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

export default async function TestSupabase() {
  const supabase = await createClient();
  
  // Test connection
  let connectionStatus = 'جاري الاختبار...';
  let tables = null;
  let error = null;

  try {
    // Test simple connection with a basic query
    const { data, error: dbError } = await supabase
      .rpc('version'); // This is a simple function that should work

    if (dbError) {
      // If RPC doesn't work, try a simpler test
      const { data: authData, error: authError } = await supabase.auth.getSession();

      if (authError) {
        error = `Auth Error: ${authError.message}`;
        connectionStatus = 'فشل الاتصال';
      } else {
        connectionStatus = 'متصل بنجاح! ✅ (Auth working)';
        tables = { message: 'Supabase connection working', auth: 'OK' };
      }
    } else {
      connectionStatus = 'متصل بنجاح! ✅';
      tables = { version: data, message: 'Database connection working' };
    }
  } catch (err: any) {
    error = err.message;
    connectionStatus = 'خطأ في الاتصال';
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-navy mb-4">
                🔗 اختبار اتصال Supabase
              </h1>
              <p className="text-xl text-gray-600">
                اختبار الاتصال مع قاعدة البيانات Supabase
              </p>
            </div>

            {/* Connection Status */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">حالة الاتصال</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-center">
                  <div className={`text-3xl font-bold mb-4 ${
                    connectionStatus.includes('✅') ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {connectionStatus}
                  </div>
                  
                  {error && (
                    <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                      <strong>خطأ:</strong> {error}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Configuration Info */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">معلومات التكوين</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-semibold text-navy mb-2">Supabase URL:</h3>
                    <p className="text-gray-700 bg-gray-100 p-2 rounded">
                      {process.env.NEXT_PUBLIC_SUPABASE_URL || 'غير محدد'}
                    </p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-navy mb-2">Anon Key:</h3>
                    <p className="text-gray-700 bg-gray-100 p-2 rounded">
                      {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 
                        `${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY.substring(0, 20)}...` : 
                        'غير محدد'
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Tables Info */}
            {tables && (
              <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
                <CardHeader>
                  <CardTitle className="text-2xl text-navy">الجداول المتاحة</CardTitle>
                </CardHeader>
                <CardContent>
                  <pre className="bg-gray-100 p-4 rounded overflow-auto">
                    {JSON.stringify(tables, null, 2)}
                  </pre>
                </CardContent>
              </Card>
            )}

            {/* Next Steps */}
            <Card className="border-0 bg-gradient-to-r from-green-500 to-green-600 text-white">
              <CardHeader>
                <CardTitle>الخطوات التالية</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {connectionStatus.includes('✅') ? (
                    <>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className="text-2xl">🎉</span>
                        <span>ممتاز! Supabase يعمل بنجاح</span>
                      </div>
                      <ul className="space-y-2 text-white/90">
                        <li>• يمكن الآن إنشاء الجداول في Supabase</li>
                        <li>• تطبيق Prisma migrations</li>
                        <li>• ربط التطبيق بقاعدة البيانات</li>
                        <li>• النشر على Vercel</li>
                      </ul>
                    </>
                  ) : (
                    <>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <span className="text-2xl">⚠️</span>
                        <span>يحتاج إصلاح الاتصال</span>
                      </div>
                      <ul className="space-y-2 text-white/90">
                        <li>• تحقق من Supabase Dashboard</li>
                        <li>• تأكد أن المشروع نشط</li>
                        <li>• تحقق من صحة URL و API Key</li>
                        <li>• جرب إعادة إنشاء المشروع</li>
                      </ul>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Back to Dashboard */}
            <div className="text-center mt-8">
              <a 
                href="/test-dashboard"
                className="inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all"
              >
                العودة للوحة الاختبار
              </a>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
