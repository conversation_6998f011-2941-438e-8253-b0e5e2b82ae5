{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useState } from 'react';\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  role: 'client' | 'craftsman';\n  phone?: string;\n}\n\nexport const useAuth = () => {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const login = async (credentials: LoginCredentials) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await signIn('credentials', {\n        email: credentials.email,\n        password: credentials.password,\n        redirect: false,\n      });\n\n      if (result?.error) {\n        setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');\n        return false;\n      }\n\n      if (result?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        setError(errorData.message || 'حدث خطأ أثناء إنشاء الحساب');\n        return false;\n      }\n\n      // تسجيل الدخول تلقائياً بعد التسجيل\n      const loginResult = await signIn('credentials', {\n        email: data.email,\n        password: data.password,\n        redirect: false,\n      });\n\n      if (loginResult?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء إنشاء الحساب');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    setIsLoading(true);\n    try {\n      await signOut({ redirect: false });\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithGoogle = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('google', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Google');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithFacebook = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('facebook', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Facebook');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const isAuthenticated = status === 'authenticated';\n  const isLoading_session = status === 'loading';\n  const user = session?.user;\n\n  // التحقق من الأدوار مع fallback\n  const isClient = user?.role === 'client';\n  const isCraftsman = user?.role === 'craftsman';\n  const isAdmin = user?.role === 'admin';\n\n  // التحقق من الصلاحيات\n  const hasRole = (role: string | string[]) => {\n    if (!user) return false;\n    if (Array.isArray(role)) {\n      return role.includes(user.role);\n    }\n    return user.role === role;\n  };\n\n  const canAccess = (requiredRoles: string | string[]) => {\n    return isAuthenticated && hasRole(requiredRoles);\n  };\n\n  return {\n    // Session data\n    user,\n    session,\n    isAuthenticated,\n    isLoading: isLoading || isLoading_session,\n    error,\n\n    // Role checks\n    isClient,\n    isCraftsman,\n    isAdmin,\n    hasRole,\n    canAccess,\n\n    // Auth methods\n    login,\n    register,\n    logout,\n    loginWithGoogle,\n    loginWithFacebook,\n\n    // Utility\n    setError,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;AAJA;;;;AAmBO,MAAM,UAAU;IACrB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,QAAQ,OAAO;QACnB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;gBAC9B,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;gBACT,OAAO;YACT;YAEA,IAAI,QAAQ,IAAI;gBACd,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,OAAO,IAAI;gBAC9B,OAAO;YACT;YAEA,oCAAoC;YACpC,MAAM,cAAc,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBAC9C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;YACZ;YAEA,IAAI,aAAa,IAAI;gBACnB,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;gBAAE,UAAU;YAAM;YAChC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBAAE,aAAa;YAAa;QACrD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,YAAY;gBAAE,aAAa;YAAa;QACvD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,WAAW;IACnC,MAAM,oBAAoB,WAAW;IACrC,MAAM,OAAO,SAAS;IAEtB,gCAAgC;IAChC,MAAM,WAAW,MAAM,SAAS;IAChC,MAAM,cAAc,MAAM,SAAS;IACnC,MAAM,UAAU,MAAM,SAAS;IAE/B,sBAAsB;IACtB,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI;QAChC;QACA,OAAO,KAAK,IAAI,KAAK;IACvB;IAEA,MAAM,YAAY,CAAC;QACjB,OAAO,mBAAmB,QAAQ;IACpC;IAEA,OAAO;QACL,eAAe;QACf;QACA;QACA;QACA,WAAW,aAAa;QACxB;QAEA,cAAc;QACd;QACA;QACA;QACA;QACA;QAEA,eAAe;QACf;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;IACF;AACF"}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-white shadow-sm transition-all duration-200 hover:shadow-md',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('font-semibold leading-none tracking-tight text-navy', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-600', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,qBAAO,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACpE,GAAG,KAAK;kBAER;;;;;;AAGL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;AAGL,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;AAIP,WAAW,WAAW,GAAG"}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/hooks/useAuth';\nimport { ReactNode } from 'react';\nimport { Card, CardContent } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\nimport Link from 'next/link';\n\ninterface ProtectedRouteProps {\n  children: ReactNode;\n  requiredRoles?: string | string[];\n  fallback?: ReactNode;\n}\n\nexport default function ProtectedRoute({\n  children,\n  requiredRoles,\n  fallback\n}: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading, canAccess, user } = useAuth();\n\n  // عرض شاشة التحميل\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white flex items-center justify-center\">\n        <Card className=\"border-0 bg-white/90 backdrop-blur-md shadow-lg\">\n          <CardContent className=\"p-12 text-center\">\n            <div className=\"animate-spin rounded-full h-16 w-16 border-b-2 border-navy mx-auto mb-4\"></div>\n            <p className=\"text-gray-600 text-lg\">جاري التحميل...</p>\n          </CardContent>\n        </Card>\n      </div>\n    );\n  }\n\n  // إذا لم يكن المستخدم مسجلاً\n  if (!isAuthenticated) {\n    return fallback || (\n      <div className=\"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white flex items-center justify-center\">\n        <div className=\"container mx-auto px-4\">\n          <Card className=\"max-w-md mx-auto border-0 bg-white/90 backdrop-blur-md shadow-lg\">\n            <CardContent className=\"p-8 text-center\">\n              <div className=\"text-6xl mb-6\">🔒</div>\n              <h2 className=\"text-2xl font-bold text-navy mb-4\">تسجيل الدخول مطلوب</h2>\n              <p className=\"text-gray-600 mb-6\">\n                يجب تسجيل الدخول للوصول إلى هذه الصفحة\n              </p>\n              <div className=\"space-y-3\">\n                <Link href=\"/login\">\n                  <Button className=\"w-full bg-gradient-to-r from-navy to-teal\">\n                    تسجيل الدخول\n                  </Button>\n                </Link>\n                <Link href=\"/register\">\n                  <Button variant=\"outline\" className=\"w-full border-navy text-navy\">\n                    إنشاء حساب جديد\n                  </Button>\n                </Link>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    );\n  }\n\n  // إذا كان هناك أدوار مطلوبة ولا يملك المستخدم الصلاحية\n  if (requiredRoles && !canAccess(requiredRoles)) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white flex items-center justify-center\">\n        <div className=\"container mx-auto px-4\">\n          <Card className=\"max-w-md mx-auto border-0 bg-white/90 backdrop-blur-md shadow-lg\">\n            <CardContent className=\"p-8 text-center\">\n              <div className=\"text-6xl mb-6\">⛔</div>\n              <h2 className=\"text-2xl font-bold text-navy mb-4\">غير مصرح لك</h2>\n              <p className=\"text-gray-600 mb-6\">\n                ليس لديك الصلاحية للوصول إلى هذه الصفحة\n              </p>\n              <div className=\"space-y-3\">\n                <Link href=\"/dashboard\">\n                  <Button className=\"w-full bg-gradient-to-r from-navy to-teal\">\n                    العودة للوحة التحكم\n                  </Button>\n                </Link>\n                <Link href=\"/\">\n                  <Button variant=\"outline\" className=\"w-full border-navy text-navy\">\n                    العودة للصفحة الرئيسية\n                  </Button>\n                </Link>\n              </div>\n              <div className=\"mt-6 text-sm text-gray-500\">\n                مسجل كـ: {user?.role === 'client' ? 'عميل' : user?.role === 'craftsman' ? 'حرفي' : 'مدير'}\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    );\n  }\n\n  // عرض المحتوى إذا كان المستخدم مصرح له\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AANA;;;;;;AAce,SAAS,eAAe,EACrC,QAAQ,EACR,aAAa,EACb,QAAQ,EACY;IACpB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAE9D,mBAAmB;IACnB,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;IAK/C;IAEA,6BAA6B;IAC7B,IAAI,CAAC,iBAAiB;QACpB,OAAO,0BACL,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;sDAA4C;;;;;;;;;;;kDAIhE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAUnF;IAEA,uDAAuD;IACvD,IAAI,iBAAiB,CAAC,UAAU,gBAAgB;QAC9C,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAGlC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;sDAA4C;;;;;;;;;;;kDAIhE,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,WAAU;sDAA+B;;;;;;;;;;;;;;;;;0CAKvE,8OAAC;gCAAI,WAAU;;oCAA6B;oCAChC,MAAM,SAAS,WAAW,SAAS,MAAM,SAAS,cAAc,SAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOjG;IAEA,uCAAuC;IACvC,qBAAO;kBAAG;;AACZ"}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 550, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { useAuth } from '@/hooks/useAuth';\n\ninterface SidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst Sidebar = ({ isOpen, onClose }: SidebarProps) => {\n  const pathname = usePathname();\n  const { user, isClient, isCraftsman, isAdmin, logout } = useAuth();\n\n  const getNavigationItems = () => {\n    const commonItems = [\n      {\n        name: 'لوحة التحكم',\n        href: '/dashboard',\n        icon: (\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\" />\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z\" />\n          </svg>\n        )\n      },\n      {\n        name: 'الملف الشخصي',\n        href: '/profile',\n        icon: (\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n          </svg>\n        )\n      },\n      {\n        name: 'الرسائل',\n        href: '/messages',\n        icon: (\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n          </svg>\n        ),\n        badge: '3'\n      },\n      {\n        name: 'الإشعارات',\n        href: '/notifications',\n        icon: (\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84l3.12 3.12M4.03 8.86l3.12 3.12\" />\n          </svg>\n        ),\n        badge: '5'\n      }\n    ];\n\n    if (isClient) {\n      return [\n        ...commonItems,\n        {\n          name: 'مشاريعي',\n          href: '/client/projects',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n            </svg>\n          )\n        },\n        {\n          name: 'العروض المستلمة',\n          href: '/client/offers',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4\" />\n            </svg>\n          ),\n          badge: '12'\n        },\n        {\n          name: 'المدفوعات',\n          href: '/client/payments',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n            </svg>\n          )\n        }\n      ];\n    }\n\n    if (isCraftsman) {\n      return [\n        ...commonItems,\n        {\n          name: 'المشاريع المتاحة',\n          href: '/craftsman/projects',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          )\n        },\n        {\n          name: 'عروضي',\n          href: '/craftsman/offers',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n            </svg>\n          )\n        },\n        {\n          name: 'مشاريعي الحالية',\n          href: '/craftsman/current-projects',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n            </svg>\n          )\n        },\n        {\n          name: 'معرض أعمالي',\n          href: '/craftsman/portfolio',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2-2v16a2 2 0 002 2z\" />\n            </svg>\n          )\n        },\n        {\n          name: 'الأرباح',\n          href: '/craftsman/earnings',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n            </svg>\n          )\n        }\n      ];\n    }\n\n    if (isAdmin) {\n      return [\n        ...commonItems,\n        {\n          name: 'إدارة المستخدمين',\n          href: '/admin/users',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n            </svg>\n          )\n        },\n        {\n          name: 'إدارة المشاريع',\n          href: '/admin/projects',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n            </svg>\n          )\n        },\n        {\n          name: 'التقارير',\n          href: '/admin/reports',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n            </svg>\n          )\n        },\n        {\n          name: 'الإعدادات',\n          href: '/admin/settings',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n            </svg>\n          )\n        }\n      ];\n    }\n\n    return commonItems;\n  };\n\n  const navigationItems = getNavigationItems();\n\n  const isActive = (href: string) => {\n    if (href === '/dashboard') {\n      return pathname === '/dashboard';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <>\n      {/* Mobile Overlay */}\n      {isOpen && (\n        <div \n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={`\n        fixed top-0 right-0 h-full w-64 bg-white border-l border-gray-200 shadow-xl z-50 transform transition-transform duration-300 ease-in-out\n        ${isOpen ? 'translate-x-0' : 'translate-x-full'}\n        lg:translate-x-0 lg:static lg:z-auto\n      `}>\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <Link href=\"/\" className=\"flex items-center space-x-3 space-x-reverse\">\n            <div className=\"w-8 h-8 bg-gradient-to-br from-navy to-teal rounded-lg flex items-center justify-center text-white text-lg font-bold\">\n              🔨\n            </div>\n            <span className=\"text-xl font-bold text-navy\">دوزان</span>\n          </Link>\n          <button \n            onClick={onClose}\n            className=\"lg:hidden p-2 rounded-lg hover:bg-gray-100\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* User Info */}\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex items-center space-x-3 space-x-reverse\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white font-bold\">\n              {user?.name?.charAt(0) || 'U'}\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium text-gray-900 truncate\">\n                {user?.name || 'مستخدم'}\n              </p>\n              <p className=\"text-xs text-gray-500 truncate\">\n                {user?.email}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"flex-1 p-4 space-y-1\">\n          {navigationItems.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={`\n                flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200\n                ${isActive(item.href)\n                  ? 'bg-navy text-white'\n                  : 'text-gray-700 hover:bg-gray-100'\n                }\n              `}\n              onClick={() => {\n                if (window.innerWidth < 1024) {\n                  onClose();\n                }\n              }}\n            >\n              <div className=\"flex items-center space-x-3 space-x-reverse\">\n                {item.icon}\n                <span>{item.name}</span>\n              </div>\n              {item.badge && (\n                <span className={`\n                  px-2 py-1 text-xs rounded-full\n                  ${isActive(item.href)\n                    ? 'bg-white text-navy'\n                    : 'bg-red-100 text-red-600'\n                  }\n                `}>\n                  {item.badge}\n                </span>\n              )}\n            </Link>\n          ))}\n        </nav>\n\n        {/* Footer */}\n        <div className=\"p-4 border-t border-gray-200\">\n          <button\n            onClick={logout}\n            className=\"flex items-center space-x-3 space-x-reverse w-full px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n            </svg>\n            <span>تسجيل الخروج</span>\n          </button>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAYA,MAAM,UAAU,CAAC,EAAE,MAAM,EAAE,OAAO,EAAgB;IAChD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAE/D,MAAM,qBAAqB;QACzB,MAAM,cAAc;YAClB;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;;sCACjE,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;sCACrE,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;YAG3E;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;gBAGzE,OAAO;YACT;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,8OAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;gBAGzE,OAAO;YACT;SACD;QAED,IAAI,UAAU;YACZ,OAAO;mBACF;gBACH;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;aACD;QACH;QAEA,IAAI,aAAa;YACf,OAAO;mBACF;gBACH;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;aACD;QACH;QAEA,IAAI,SAAS;YACX,OAAO;mBACF;gBACH;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;;0CACjE,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;0CACrE,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;gBAG3E;aACD;QACH;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB;IAExB,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE;;YAEG,wBACC,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,8OAAC;gBAAI,WAAW,CAAC;;QAEf,EAAE,SAAS,kBAAkB,mBAAmB;;MAElD,CAAC;;kCAEC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDAAuH;;;;;;kDAGtI,8OAAC;wCAAK,WAAU;kDAA8B;;;;;;;;;;;;0CAEhD,8OAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACZ,MAAM,MAAM,OAAO,MAAM;;;;;;8CAE5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;sDACV,MAAM,QAAQ;;;;;;sDAEjB,8OAAC;4CAAE,WAAU;sDACV,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAOf,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC;;gBAEV,EAAE,SAAS,KAAK,IAAI,IAChB,uBACA,kCACH;cACH,CAAC;gCACD,SAAS;oCACP,IAAI,OAAO,UAAU,GAAG,MAAM;wCAC5B;oCACF;gCACF;;kDAEA,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI;0DACV,8OAAC;0DAAM,KAAK,IAAI;;;;;;;;;;;;oCAEjB,KAAK,KAAK,kBACT,8OAAC;wCAAK,WAAW,CAAC;;kBAEhB,EAAE,SAAS,KAAK,IAAI,IAChB,uBACA,0BACH;gBACH,CAAC;kDACE,KAAK,KAAK;;;;;;;+BA3BV,KAAK,IAAI;;;;;;;;;;kCAmCpB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;uCAEe"}}, {"offset": {"line": 1239, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1245, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Sidebar from './Sidebar';\nimport { useAuth } from '@/hooks/useAuth';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n  subtitle?: string;\n  actions?: React.ReactNode;\n}\n\nconst DashboardLayout = ({ children, title, subtitle, actions }: DashboardLayoutProps) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { user } = useAuth();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Sidebar */}\n      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />\n\n      {/* Main Content */}\n      <div className=\"lg:mr-64\">\n        {/* Top Bar */}\n        <div className=\"bg-white border-b border-gray-200 px-4 py-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              {/* Mobile menu button */}\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden p-2 rounded-lg text-gray-600 hover:bg-gray-100\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </button>\n\n              {/* Page Title */}\n              <div>\n                {title && (\n                  <h1 className=\"text-2xl font-bold text-gray-900\">{title}</h1>\n                )}\n                {subtitle && (\n                  <p className=\"text-sm text-gray-600 mt-1\">{subtitle}</p>\n                )}\n              </div>\n            </div>\n\n            {/* Top Bar Actions */}\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              {actions}\n              \n              {/* Search */}\n              <div className=\"hidden md:block\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"البحث...\"\n                    className=\"w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy\"\n                  />\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                    </svg>\n                  </div>\n                </div>\n              </div>\n\n              {/* Notifications */}\n              <button className=\"relative p-2 text-gray-600 hover:bg-gray-100 rounded-lg\">\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84l3.12 3.12M4.03 8.86l3.12 3.12\" />\n                </svg>\n                <span className=\"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"></span>\n              </button>\n\n              {/* User Menu */}\n              <div className=\"relative\">\n                <button className=\"flex items-center space-x-2 space-x-reverse p-2 rounded-lg hover:bg-gray-100\">\n                  <div className=\"w-8 h-8 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white text-sm font-bold\">\n                    {user?.name?.charAt(0) || 'U'}\n                  </div>\n                  <div className=\"hidden md:block text-right\">\n                    <p className=\"text-sm font-medium text-gray-900\">{user?.name}</p>\n                    <p className=\"text-xs text-gray-500\">{user?.role}</p>\n                  </div>\n                  <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page Content */}\n        <main className=\"p-4 sm:p-6 lg:p-8\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default DashboardLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAaA,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAwB;IACnF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,uIAAA,CAAA,UAAO;gBAAC,QAAQ;gBAAa,SAAS,IAAM,eAAe;;;;;;0BAG5D,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAKzE,8OAAC;;gDACE,uBACC,8OAAC;oDAAG,WAAU;8DAAoC;;;;;;gDAEnD,0BACC,8OAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;8CAMjD,8OAAC;oCAAI,WAAU;;wCACZ;sDAGD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,WAAU;;;;;;kEAEZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAwB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC/E,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO7E,8OAAC;4CAAO,WAAU;;8DAChB,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC;wDAAI,WAAU;kEACZ,MAAM,MAAM,OAAO,MAAM;;;;;;kEAE5B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAqC,MAAM;;;;;;0EACxD,8OAAC;gEAAE,WAAU;0EAAyB,MAAM;;;;;;;;;;;;kEAE9C,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASjF,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;uCAEe"}}, {"offset": {"line": 1540, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1546, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/dashboard/StatsCard.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface StatsCardProps {\n  title: string;\n  value: string | number;\n  change?: string;\n  changeType?: 'increase' | 'decrease' | 'neutral';\n  icon: React.ReactNode;\n  color?: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'teal';\n  loading?: boolean;\n}\n\nconst StatsCard = ({ \n  title, \n  value, \n  change, \n  changeType = 'neutral', \n  icon, \n  color = 'blue',\n  loading = false \n}: StatsCardProps) => {\n  const colorClasses = {\n    blue: 'from-blue-500 to-blue-600',\n    green: 'from-green-500 to-green-600',\n    purple: 'from-purple-500 to-purple-600',\n    orange: 'from-orange-500 to-orange-600',\n    red: 'from-red-500 to-red-600',\n    teal: 'from-teal-500 to-teal-600'\n  };\n\n  const changeColors = {\n    increase: 'text-green-600 bg-green-50',\n    decrease: 'text-red-600 bg-red-50',\n    neutral: 'text-gray-600 bg-gray-50'\n  };\n\n  const changeIcons = {\n    increase: (\n      <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 17l9.2-9.2M17 17V7H7\" />\n      </svg>\n    ),\n    decrease: (\n      <svg className=\"w-3 h-3\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 7l-9.2 9.2M7 7v10h10\" />\n      </svg>\n    ),\n    neutral: null\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 animate-pulse\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"space-y-3\">\n            <div className=\"h-4 bg-gray-200 rounded w-24\"></div>\n            <div className=\"h-8 bg-gray-200 rounded w-16\"></div>\n            <div className=\"h-3 bg-gray-200 rounded w-20\"></div>\n          </div>\n          <div className=\"w-12 h-12 bg-gray-200 rounded-lg\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex-1\">\n          <p className=\"text-sm font-medium text-gray-600 mb-1\">{title}</p>\n          <p className=\"text-3xl font-bold text-gray-900 mb-2\">{value}</p>\n          {change && (\n            <div className={`inline-flex items-center space-x-1 space-x-reverse px-2 py-1 rounded-full text-xs font-medium ${changeColors[changeType]}`}>\n              {changeIcons[changeType]}\n              <span>{change}</span>\n            </div>\n          )}\n        </div>\n        <div className={`w-12 h-12 bg-gradient-to-r ${colorClasses[color]} rounded-lg flex items-center justify-center text-white shadow-lg`}>\n          {icon}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StatsCard;\n"], "names": [], "mappings": ";;;;AAAA;;AAcA,MAAM,YAAY,CAAC,EACjB,KAAK,EACL,KAAK,EACL,MAAM,EACN,aAAa,SAAS,EACtB,IAAI,EACJ,QAAQ,MAAM,EACd,UAAU,KAAK,EACA;IACf,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,KAAK;QACL,MAAM;IACR;IAEA,MAAM,eAAe;QACnB,UAAU;QACV,UAAU;QACV,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,wBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;QAGzE,wBACE,8OAAC;YAAI,WAAU;YAAU,MAAK;YAAO,QAAO;YAAe,SAAQ;sBACjE,cAAA,8OAAC;gBAAK,eAAc;gBAAQ,gBAAe;gBAAQ,aAAa;gBAAG,GAAE;;;;;;;;;;;QAGzE,SAAS;IACX;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;wBACrD,wBACC,8OAAC;4BAAI,WAAW,CAAC,8FAA8F,EAAE,YAAY,CAAC,WAAW,EAAE;;gCACxI,WAAW,CAAC,WAAW;8CACxB,8OAAC;8CAAM;;;;;;;;;;;;;;;;;;8BAIb,8OAAC;oBAAI,WAAW,CAAC,2BAA2B,EAAE,YAAY,CAAC,MAAM,CAAC,iEAAiE,CAAC;8BACjI;;;;;;;;;;;;;;;;;AAKX;uCAEe"}}, {"offset": {"line": 1732, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1738, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/dashboard/ActivityFeed.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\ninterface Activity {\n  id: string;\n  type: 'project_created' | 'offer_received' | 'payment_completed' | 'message_received' | 'project_completed' | 'user_registered';\n  title: string;\n  description: string;\n  time: string;\n  user?: string;\n  href?: string;\n  avatar?: string;\n}\n\ninterface ActivityFeedProps {\n  activities: Activity[];\n  title?: string;\n  showAll?: boolean;\n  maxItems?: number;\n}\n\nconst ActivityFeed = ({ \n  activities, \n  title = \"النشاط الأخير\", \n  showAll = false,\n  maxItems = 5 \n}: ActivityFeedProps) => {\n  const getActivityIcon = (type: Activity['type']) => {\n    switch (type) {\n      case 'project_created':\n        return (\n          <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n            <svg className=\"w-4 h-4 text-blue-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n            </svg>\n          </div>\n        );\n      case 'offer_received':\n        return (\n          <div className=\"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\">\n            <svg className=\"w-4 h-4 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4\" />\n            </svg>\n          </div>\n        );\n      case 'payment_completed':\n        return (\n          <div className=\"w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center\">\n            <svg className=\"w-4 h-4 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n            </svg>\n          </div>\n        );\n      case 'message_received':\n        return (\n          <div className=\"w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center\">\n            <svg className=\"w-4 h-4 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n            </svg>\n          </div>\n        );\n      case 'project_completed':\n        return (\n          <div className=\"w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center\">\n            <svg className=\"w-4 h-4 text-teal-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n        );\n      case 'user_registered':\n        return (\n          <div className=\"w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center\">\n            <svg className=\"w-4 h-4 text-indigo-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n            </svg>\n          </div>\n        );\n      default:\n        return (\n          <div className=\"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center\">\n            <svg className=\"w-4 h-4 text-gray-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n        );\n    }\n  };\n\n  const displayedActivities = showAll ? activities : activities.slice(0, maxItems);\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n        {!showAll && activities.length > maxItems && (\n          <Link href=\"/dashboard/activity\" className=\"text-sm text-navy hover:text-navy/80 font-medium\">\n            عرض الكل\n          </Link>\n        )}\n      </div>\n\n      <div className=\"space-y-4\">\n        {displayedActivities.map((activity, index) => (\n          <div key={activity.id} className=\"flex items-start space-x-3 space-x-reverse\">\n            {getActivityIcon(activity.type)}\n            <div className=\"flex-1 min-w-0\">\n              <div className=\"flex items-center justify-between\">\n                <p className=\"text-sm font-medium text-gray-900\">\n                  {activity.href ? (\n                    <Link href={activity.href} className=\"hover:text-navy\">\n                      {activity.title}\n                    </Link>\n                  ) : (\n                    activity.title\n                  )}\n                </p>\n                <p className=\"text-xs text-gray-500\">{activity.time}</p>\n              </div>\n              <p className=\"text-sm text-gray-600 mt-1\">{activity.description}</p>\n              {activity.user && (\n                <p className=\"text-xs text-gray-500 mt-1\">بواسطة {activity.user}</p>\n              )}\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {displayedActivities.length === 0 && (\n        <div className=\"text-center py-8\">\n          <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n            </svg>\n          </div>\n          <h3 className=\"text-sm font-medium text-gray-900 mb-1\">لا توجد أنشطة</h3>\n          <p className=\"text-sm text-gray-500\">لم يتم تسجيل أي نشاط حتى الآن</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ActivityFeed;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAuBA,MAAM,eAAe,CAAC,EACpB,UAAU,EACV,QAAQ,eAAe,EACvB,UAAU,KAAK,EACf,WAAW,CAAC,EACM;IAClB,MAAM,kBAAkB,CAAC;QACvB,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAwB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC/E,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;YAI7E,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAyB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAChF,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;YAI7E,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAA0B,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjF,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;YAI7E,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAA0B,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjF,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;YAI7E,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAwB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC/E,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;YAI7E,KAAK;gBACH,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAA0B,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjF,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;YAI7E;gBACE,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAwB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC/E,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;QAI/E;IACF;IAEA,MAAM,sBAAsB,UAAU,aAAa,WAAW,KAAK,CAAC,GAAG;IAEvE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuC;;;;;;oBACpD,CAAC,WAAW,WAAW,MAAM,GAAG,0BAC/B,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;wBAAsB,WAAU;kCAAmD;;;;;;;;;;;;0BAMlG,8OAAC;gBAAI,WAAU;0BACZ,oBAAoB,GAAG,CAAC,CAAC,UAAU,sBAClC,8OAAC;wBAAsB,WAAU;;4BAC9B,gBAAgB,SAAS,IAAI;0CAC9B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,SAAS,IAAI,iBACZ,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAM,SAAS,IAAI;oDAAE,WAAU;8DAClC,SAAS,KAAK;;;;;2DAGjB,SAAS,KAAK;;;;;;0DAGlB,8OAAC;gDAAE,WAAU;0DAAyB,SAAS,IAAI;;;;;;;;;;;;kDAErD,8OAAC;wCAAE,WAAU;kDAA8B,SAAS,WAAW;;;;;;oCAC9D,SAAS,IAAI,kBACZ,8OAAC;wCAAE,WAAU;;4CAA6B;4CAAQ,SAAS,IAAI;;;;;;;;;;;;;;uBAjB3D,SAAS,EAAE;;;;;;;;;;YAwBxB,oBAAoB,MAAM,KAAK,mBAC9B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAK/C;uCAEe"}}, {"offset": {"line": 2115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2121, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/dashboard/QuickActions.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\ninterface QuickAction {\n  id: string;\n  title: string;\n  description: string;\n  href: string;\n  icon: React.ReactNode;\n  color: 'blue' | 'green' | 'purple' | 'orange' | 'red' | 'teal';\n  badge?: string;\n  external?: boolean;\n}\n\ninterface QuickActionsProps {\n  actions: QuickAction[];\n  title?: string;\n  columns?: 2 | 3 | 4;\n}\n\nconst QuickActions = ({ \n  actions, \n  title = \"الإجراءات السريعة\",\n  columns = 4 \n}: QuickActionsProps) => {\n  const colorClasses = {\n    blue: 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',\n    green: 'from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',\n    purple: 'from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',\n    orange: 'from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700',\n    red: 'from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',\n    teal: 'from-teal-500 to-teal-600 hover:from-teal-600 hover:to-teal-700'\n  };\n\n  const gridClasses = {\n    2: 'grid-cols-1 md:grid-cols-2',\n    3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',\n    4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'\n  };\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-6\">{title}</h3>\n      \n      <div className={`grid ${gridClasses[columns]} gap-4`}>\n        {actions.map((action) => (\n          <Link\n            key={action.id}\n            href={action.href}\n            target={action.external ? '_blank' : undefined}\n            rel={action.external ? 'noopener noreferrer' : undefined}\n            className=\"group relative bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-all duration-200 hover:border-gray-300\"\n          >\n            <div className=\"flex items-start space-x-3 space-x-reverse\">\n              <div className={`w-10 h-10 bg-gradient-to-r ${colorClasses[action.color]} rounded-lg flex items-center justify-center text-white shadow-sm group-hover:shadow-md transition-shadow duration-200`}>\n                {action.icon}\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center justify-between\">\n                  <h4 className=\"text-sm font-medium text-gray-900 group-hover:text-navy transition-colors duration-200\">\n                    {action.title}\n                  </h4>\n                  {action.badge && (\n                    <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800\">\n                      {action.badge}\n                    </span>\n                  )}\n                </div>\n                <p className=\"text-xs text-gray-600 mt-1 line-clamp-2\">\n                  {action.description}\n                </p>\n              </div>\n            </div>\n            \n            {/* Hover Arrow */}\n            <div className=\"absolute top-4 left-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\n              <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n              </svg>\n            </div>\n          </Link>\n        ))}\n      </div>\n\n      {actions.length === 0 && (\n        <div className=\"text-center py-8\">\n          <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n            </svg>\n          </div>\n          <h3 className=\"text-sm font-medium text-gray-900 mb-1\">لا توجد إجراءات متاحة</h3>\n          <p className=\"text-sm text-gray-500\">لم يتم تكوين أي إجراءات سريعة</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default QuickActions;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAsBA,MAAM,eAAe,CAAC,EACpB,OAAO,EACP,QAAQ,mBAAmB,EAC3B,UAAU,CAAC,EACO;IAClB,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,KAAK;QACL,MAAM;IACR;IAEA,MAAM,cAAc;QAClB,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAE1D,8OAAC;gBAAI,WAAW,CAAC,KAAK,EAAE,WAAW,CAAC,QAAQ,CAAC,MAAM,CAAC;0BACjD,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,4JAAA,CAAA,UAAI;wBAEH,MAAM,OAAO,IAAI;wBACjB,QAAQ,OAAO,QAAQ,GAAG,WAAW;wBACrC,KAAK,OAAO,QAAQ,GAAG,wBAAwB;wBAC/C,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAW,CAAC,2BAA2B,EAAE,YAAY,CAAC,OAAO,KAAK,CAAC,CAAC,sHAAsH,CAAC;kDAC7L,OAAO,IAAI;;;;;;kDAEd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,OAAO,KAAK;;;;;;oDAEd,OAAO,KAAK,kBACX,8OAAC;wDAAK,WAAU;kEACb,OAAO,KAAK;;;;;;;;;;;;0DAInB,8OAAC;gDAAE,WAAU;0DACV,OAAO,WAAW;;;;;;;;;;;;;;;;;;0CAMzB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC/E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;uBA9BpE,OAAO,EAAE;;;;;;;;;;YAqCnB,QAAQ,MAAM,KAAK,mBAClB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAK/C;uCAEe"}}, {"offset": {"line": 2319, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2325, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport ProtectedRoute from '@/components/auth/ProtectedRoute';\nimport DashboardLayout from '@/components/layout/DashboardLayout';\nimport StatsCard from '@/components/dashboard/StatsCard';\nimport ActivityFeed from '@/components/dashboard/ActivityFeed';\nimport QuickActions from '@/components/dashboard/QuickActions';\nimport { useAuth } from '@/hooks/useAuth';\n\nconst DashboardPage = () => {\n  const { user, isClient, isCraftsman, isAdmin } = useAuth();\n\n  // إحصائيات ديناميكية حسب نوع المستخدم\n  const getStatsData = () => {\n    if (isClient) {\n      return [\n        {\n          title: 'مشاريعي النشطة',\n          value: '5',\n          change: '+2 هذا الأسبوع',\n          changeType: 'increase' as const,\n          icon: (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n            </svg>\n          ),\n          color: 'blue' as const\n        },\n        {\n          title: 'العروض المستلمة',\n          value: '23',\n          change: '+8 جديدة',\n          changeType: 'increase' as const,\n          icon: (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4\" />\n            </svg>\n          ),\n          color: 'green' as const\n        },\n        {\n          title: 'المشاريع المكتملة',\n          value: '12',\n          change: '+3 هذا الشهر',\n          changeType: 'increase' as const,\n          icon: (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          ),\n          color: 'purple' as const\n        },\n        {\n          title: 'إجمالي الإنفاق',\n          value: '₺25,000',\n          change: 'هذا العام',\n          changeType: 'neutral' as const,\n          icon: (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n            </svg>\n          ),\n          color: 'orange' as const\n        }\n      ];\n    } else if (isCraftsman) {\n      return [\n        {\n          title: 'المشاريع الجارية',\n          value: '4',\n          change: '+1 هذا الأسبوع',\n          changeType: 'increase' as const,\n          icon: (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z\" />\n            </svg>\n          ),\n          color: 'blue' as const\n        },\n        {\n          title: 'العروض المرسلة',\n          value: '18',\n          change: '+5 جديدة',\n          changeType: 'increase' as const,\n          icon: (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n            </svg>\n          ),\n          color: 'orange' as const\n        },\n        {\n          title: 'المشاريع المكتملة',\n          value: '32',\n          change: '+3 هذا الشهر',\n          changeType: 'increase' as const,\n          icon: (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          ),\n          color: 'green' as const\n        },\n        {\n          title: 'التقييم',\n          value: '4.9',\n          change: 'من 5 نجوم',\n          changeType: 'neutral' as const,\n          icon: (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\" />\n            </svg>\n          ),\n          color: 'teal' as const\n        }\n      ];\n    } else if (isAdmin) {\n      return [\n        {\n          title: 'إجمالي المستخدمين',\n          value: '2,847',\n          change: '+12% هذا الشهر',\n          changeType: 'increase' as const,\n          icon: (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n            </svg>\n          ),\n          color: 'blue' as const\n        },\n        {\n          title: 'المشاريع النشطة',\n          value: '156',\n          change: '+8% هذا الأسبوع',\n          changeType: 'increase' as const,\n          icon: (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n            </svg>\n          ),\n          color: 'green' as const\n        },\n        {\n          title: 'الإيرادات الشهرية',\n          value: '₺125,000',\n          change: '+15% من الشهر الماضي',\n          changeType: 'increase' as const,\n          icon: (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n            </svg>\n          ),\n          color: 'purple' as const\n        },\n        {\n          title: 'معدل الرضا',\n          value: '4.8',\n          change: '+0.2 من الشهر الماضي',\n          changeType: 'increase' as const,\n          icon: (\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z\" />\n            </svg>\n          ),\n          color: 'teal' as const\n        }\n      ];\n    }\n    return [];\n  };\n\n  // الإجراءات السريعة حسب نوع المستخدم\n  const getQuickActions = () => {\n    if (isClient) {\n      return [\n        {\n          id: 'create-project',\n          title: 'إنشاء مشروع جديد',\n          description: 'ابدأ مشروعك الجديد واحصل على عروض من الحرفيين',\n          href: '/projects/create',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n            </svg>\n          ),\n          color: 'blue' as const\n        },\n        {\n          id: 'browse-craftsmen',\n          title: 'تصفح الحرفيين',\n          description: 'ابحث عن حرفيين مؤهلين في منطقتك',\n          href: '/craftsmen',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n            </svg>\n          ),\n          color: 'green' as const\n        },\n        {\n          id: 'view-offers',\n          title: 'العروض المستلمة',\n          description: 'راجع العروض الجديدة على مشاريعك',\n          href: '/client/offers',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4\" />\n            </svg>\n          ),\n          color: 'purple' as const,\n          badge: '12'\n        },\n        {\n          id: 'messages',\n          title: 'الرسائل',\n          description: 'تواصل مع الحرفيين ومتابعة المحادثات',\n          href: '/messages',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n            </svg>\n          ),\n          color: 'orange' as const,\n          badge: '3'\n        }\n      ];\n    } else if (isCraftsman) {\n      return [\n        {\n          id: 'browse-projects',\n          title: 'تصفح المشاريع المتاحة',\n          description: 'ابحث عن مشاريع جديدة تناسب مهاراتك',\n          href: '/craftsman/projects',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          ),\n          color: 'blue' as const\n        },\n        {\n          id: 'my-offers',\n          title: 'إدارة عروضي',\n          description: 'تابع حالة العروض التي قدمتها',\n          href: '/craftsman/offers',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n            </svg>\n          ),\n          color: 'green' as const\n        },\n        {\n          id: 'portfolio',\n          title: 'معرض أعمالي',\n          description: 'أضف وأدر معرض أعمالك المكتملة',\n          href: '/craftsman/portfolio',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2-2v16a2 2 0 002 2z\" />\n            </svg>\n          ),\n          color: 'purple' as const\n        },\n        {\n          id: 'earnings',\n          title: 'الأرباح',\n          description: 'تتبع أرباحك ومدفوعاتك',\n          href: '/craftsman/earnings',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n            </svg>\n          ),\n          color: 'orange' as const\n        }\n      ];\n    } else if (isAdmin) {\n      return [\n        {\n          id: 'manage-users',\n          title: 'إدارة المستخدمين',\n          description: 'عرض وإدارة جميع المستخدمين',\n          href: '/admin/users',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n            </svg>\n          ),\n          color: 'blue' as const\n        },\n        {\n          id: 'monitor-projects',\n          title: 'مراقبة المشاريع',\n          description: 'متابعة حالة جميع المشاريع',\n          href: '/admin/projects',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n            </svg>\n          ),\n          color: 'green' as const\n        },\n        {\n          id: 'reports',\n          title: 'التقارير المالية',\n          description: 'عرض التقارير والإحصائيات',\n          href: '/admin/reports',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n            </svg>\n          ),\n          color: 'purple' as const\n        },\n        {\n          id: 'settings',\n          title: 'إعدادات النظام',\n          description: 'تكوين إعدادات المنصة',\n          href: '/admin/settings',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n            </svg>\n          ),\n          color: 'orange' as const\n        }\n      ];\n    }\n    return [];\n  };\n\n  // بيانات الأنشطة الحديثة\n  const getRecentActivities = () => {\n    if (isClient) {\n      return [\n        {\n          id: '1',\n          type: 'offer_received' as const,\n          title: 'عرض جديد على مشروعك',\n          description: 'تم استلام عرض جديد على مشروع تجديد المطبخ',\n          time: 'منذ 5 دقائق',\n          user: 'أحمد النجار',\n          href: '/client/offers'\n        },\n        {\n          id: '2',\n          type: 'project_completed' as const,\n          title: 'تم إكمال مشروع',\n          description: 'تم إكمال مشروع دهان الشقة بنجاح',\n          time: 'منذ ساعتين',\n          user: 'محمد الدهان'\n        },\n        {\n          id: '3',\n          type: 'message_received' as const,\n          title: 'رسالة جديدة',\n          description: 'رسالة من سارة الكهربائية حول مشروع الإضاءة',\n          time: 'منذ 4 ساعات',\n          user: 'سارة الكهربائية',\n          href: '/messages'\n        }\n      ];\n    } else if (isCraftsman) {\n      return [\n        {\n          id: '1',\n          type: 'project_created' as const,\n          title: 'مشروع جديد متاح',\n          description: 'مشروع تركيب خزائن مطبخ في دمشق',\n          time: 'منذ 10 دقائق',\n          href: '/craftsman/projects'\n        },\n        {\n          id: '2',\n          type: 'offer_received' as const,\n          title: 'تم قبول عرضك',\n          description: 'تم قبول عرضك على مشروع تجديد الحمام',\n          time: 'منذ ساعة',\n          user: 'فاطمة أحمد'\n        },\n        {\n          id: '3',\n          type: 'payment_completed' as const,\n          title: 'تم استلام دفعة',\n          description: 'تم استلام دفعة مشروع إصلاح السباكة',\n          time: 'منذ 3 ساعات'\n        }\n      ];\n    } else if (isAdmin) {\n      return [\n        {\n          id: '1',\n          type: 'user_registered' as const,\n          title: 'مستخدم جديد',\n          description: 'انضم مستخدم جديد إلى المنصة',\n          time: 'منذ 2 دقائق',\n          user: 'علي محمد'\n        },\n        {\n          id: '2',\n          type: 'project_completed' as const,\n          title: 'مشروع مكتمل',\n          description: 'تم إكمال مشروع تجديد المطبخ بنجاح',\n          time: 'منذ 15 دقيقة'\n        },\n        {\n          id: '3',\n          type: 'payment_completed' as const,\n          title: 'دفعة جديدة',\n          description: 'تم استلام دفعة من عمولة المشروع',\n          time: 'منذ 30 دقيقة'\n        }\n      ];\n    }\n    return [];\n  };\n\n  const statsData = getStatsData();\n  const quickActions = getQuickActions();\n  const recentActivities = getRecentActivities();\n\n  return (\n    <ProtectedRoute>\n      <DashboardLayout\n        title={`مرحباً، ${user?.name || 'مستخدم'}`}\n        subtitle={`لوحة تحكم ${isClient ? 'العميل' : isCraftsman ? 'الحرفي' : 'المدير'}`}\n      >\n        <div className=\"space-y-8\">\n          {/* Stats Cards */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            {statsData.map((stat, index) => (\n              <StatsCard\n                key={index}\n                title={stat.title}\n                value={stat.value}\n                change={stat.change}\n                changeType={stat.changeType}\n                icon={stat.icon}\n                color={stat.color}\n              />\n            ))}\n          </div>\n\n          {/* Quick Actions */}\n          <QuickActions actions={quickActions} />\n\n          {/* Main Content Grid */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n            {/* Recent Activity */}\n            <ActivityFeed activities={recentActivities} />\n\n            {/* Additional Content based on role */}\n            <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 mb-6\">\n                {isClient ? 'مشاريعي الأخيرة' : isCraftsman ? 'المشاريع المتاحة' : 'إحصائيات سريعة'}\n              </h3>\n\n              {isClient && (\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                    <div>\n                      <h4 className=\"font-medium text-gray-900\">تجديد المطبخ</h4>\n                      <p className=\"text-sm text-gray-600\">12 عرض مستلم</p>\n                    </div>\n                    <span className=\"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm\">نشط</span>\n                  </div>\n                  <div className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                    <div>\n                      <h4 className=\"font-medium text-gray-900\">إصلاح السباكة</h4>\n                      <p className=\"text-sm text-gray-600\">8 عروض مستلمة</p>\n                    </div>\n                    <span className=\"px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm\">في الانتظار</span>\n                  </div>\n                </div>\n              )}\n\n              {isCraftsman && (\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                    <div>\n                      <h4 className=\"font-medium text-gray-900\">تركيب خزائن مطبخ</h4>\n                      <p className=\"text-sm text-gray-600\">دمشق - المزة</p>\n                    </div>\n                    <span className=\"px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm\">جديد</span>\n                  </div>\n                  <div className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                    <div>\n                      <h4 className=\"font-medium text-gray-900\">دهان شقة كاملة</h4>\n                      <p className=\"text-sm text-gray-600\">حلب - الفرقان</p>\n                    </div>\n                    <span className=\"px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm\">متاح</span>\n                  </div>\n                </div>\n              )}\n\n              {isAdmin && (\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div className=\"text-center p-4 bg-blue-50 rounded-lg\">\n                    <div className=\"text-2xl font-bold text-blue-600\">89%</div>\n                    <div className=\"text-sm text-gray-600\">معدل إكمال المشاريع</div>\n                  </div>\n                  <div className=\"text-center p-4 bg-green-50 rounded-lg\">\n                    <div className=\"text-2xl font-bold text-green-600\">4.8</div>\n                    <div className=\"text-sm text-gray-600\">متوسط التقييمات</div>\n                  </div>\n                  <div className=\"text-center p-4 bg-purple-50 rounded-lg\">\n                    <div className=\"text-2xl font-bold text-purple-600\">156</div>\n                    <div className=\"text-sm text-gray-600\">مشاريع نشطة</div>\n                  </div>\n                  <div className=\"text-center p-4 bg-orange-50 rounded-lg\">\n                    <div className=\"text-2xl font-bold text-orange-600\">+12%</div>\n                    <div className=\"text-sm text-gray-600\">نمو شهري</div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </DashboardLayout>\n    </ProtectedRoute>\n  );\n};\n\nexport default DashboardPage;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;AAUA,MAAM,gBAAgB;IACpB,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAEvD,sCAAsC;IACtC,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ,OAAO;gBACL;oBACE,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;aACD;QACH,OAAO,IAAI,aAAa;YACtB,OAAO;gBACL;oBACE,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;aACD;QACH,OAAO,IAAI,SAAS;YAClB,OAAO;gBACL;oBACE,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,OAAO;oBACP,OAAO;oBACP,QAAQ;oBACR,YAAY;oBACZ,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;aACD;QACH;QACA,OAAO,EAAE;IACX;IAEA,qCAAqC;IACrC,MAAM,kBAAkB;QACtB,IAAI,UAAU;YACZ,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;oBACP,OAAO;gBACT;aACD;QACH,OAAO,IAAI,aAAa;YACtB,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;aACD;QACH,OAAO,IAAI,SAAS;YAClB,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,oBACE,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;;0CACjE,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;0CACrE,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;oBAGzE,OAAO;gBACT;aACD;QACH;QACA,OAAO,EAAE;IACX;IAEA,yBAAyB;IACzB,MAAM,sBAAsB;QAC1B,IAAI,UAAU;YACZ,OAAO;gBACL;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;aACD;QACH,OAAO,IAAI,aAAa;YACtB,OAAO;gBACL;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,MAAM;gBACR;aACD;QACH,OAAO,IAAI,SAAS;YAClB,OAAO;gBACL;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,MAAM;gBACR;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,MAAM;gBACR;aACD;QACH;QACA,OAAO,EAAE;IACX;IAEA,MAAM,YAAY;IAClB,MAAM,eAAe;IACrB,MAAM,mBAAmB;IAEzB,qBACE,8OAAC,4IAAA,CAAA,UAAc;kBACb,cAAA,8OAAC,+IAAA,CAAA,UAAe;YACd,OAAO,CAAC,QAAQ,EAAE,MAAM,QAAQ,UAAU;YAC1C,UAAU,CAAC,UAAU,EAAE,WAAW,WAAW,cAAc,WAAW,UAAU;sBAEhF,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACZ,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC,4IAAA,CAAA,UAAS;gCAER,OAAO,KAAK,KAAK;gCACjB,OAAO,KAAK,KAAK;gCACjB,QAAQ,KAAK,MAAM;gCACnB,YAAY,KAAK,UAAU;gCAC3B,MAAM,KAAK,IAAI;gCACf,OAAO,KAAK,KAAK;+BANZ;;;;;;;;;;kCAYX,8OAAC,+IAAA,CAAA,UAAY;wBAAC,SAAS;;;;;;kCAGvB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,+IAAA,CAAA,UAAY;gCAAC,YAAY;;;;;;0CAG1B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDACX,WAAW,oBAAoB,cAAc,qBAAqB;;;;;;oCAGpE,0BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA4B;;;;;;0EAC1C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAK,WAAU;kEAA6D;;;;;;;;;;;;0DAE/E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA4B;;;;;;0EAC1C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAK,WAAU;kEAA+D;;;;;;;;;;;;;;;;;;oCAKpF,6BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA4B;;;;;;0EAC1C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAK,WAAU;kEAA2D;;;;;;;;;;;;0DAE7E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAA4B;;;;;;0EAC1C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;kEAEvC,8OAAC;wDAAK,WAAU;kEAA6D;;;;;;;;;;;;;;;;;;oCAKlF,yBACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAmC;;;;;;kEAClD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAoC;;;;;;kEACnD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAqC;;;;;;kEACpD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;0DAEzC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEAAqC;;;;;;kEACpD,8OAAC;wDAAI,WAAU;kEAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3D;uCAEe"}}, {"offset": {"line": 3498, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}