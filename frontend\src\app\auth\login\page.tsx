'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';

export default function LoginPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    role: 'CLIENT'
  });
  const [loading, setLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      // محاكاة تسجيل الدخول - في التطبيق الحقيقي سيتم التحقق من قاعدة البيانات
      localStorage.setItem('user', JSON.stringify({
        id: '1',
        name: formData.role === 'CLIENT' ? 'أحمد محمد' : 'محمد النجار',
        email: formData.email,
        role: formData.role
      }));

      // توجيه حسب نوع المستخدم
      if (formData.role === 'CLIENT') {
        router.push('/client/dashboard');
      } else if (formData.role === 'CRAFTSMAN') {
        router.push('/craftsman/dashboard');
      } else {
        router.push('/admin/dashboard');
      }
    } catch (error) {
      alert('خطأ في تسجيل الدخول');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-navy via-teal to-skyblue flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Logo */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2 space-x-reverse">
            <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-2xl font-bold text-navy">د</span>
            </div>
            <span className="text-3xl font-bold text-white">دوزان</span>
          </Link>
          <p className="text-white/80 mt-2">منصة ربط العملاء بالحرفيين</p>
        </div>

        {/* Login Form */}
        <div className="bg-white rounded-2xl shadow-2xl p-8">
          <h1 className="text-2xl font-bold text-navy text-center mb-6">تسجيل الدخول</h1>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-gray-700 font-medium mb-2">البريد الإلكتروني</label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">كلمة المرور</label>
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                placeholder="••••••••"
                required
              />
            </div>

            <div>
              <label className="block text-gray-700 font-medium mb-2">نوع الحساب</label>
              <select
                name="role"
                value={formData.role}
                onChange={handleInputChange}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
              >
                <option value="CLIENT">عميل</option>
                <option value="CRAFTSMAN">حرفي</option>
                <option value="ADMIN">مدير</option>
              </select>
            </div>

            <Button
              type="submit"
              className="w-full bg-gradient-to-r from-teal to-navy py-3"
              disabled={loading}
            >
              {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
            </Button>
          </form>

          <div className="mt-6 text-center">
            <p className="text-gray-600">
              ليس لديك حساب؟{' '}
              <Link href="/auth/register" className="text-teal font-medium hover:underline">
                إنشاء حساب جديد
              </Link>
            </p>
          </div>

          {/* Demo Accounts */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-700 mb-3">حسابات تجريبية:</h3>
            <div className="space-y-2 text-xs text-gray-600">
              <div>
                <strong>عميل:</strong> <EMAIL> / password
              </div>
              <div>
                <strong>حرفي:</strong> <EMAIL> / password
              </div>
              <div>
                <strong>مدير:</strong> <EMAIL> / password
              </div>
            </div>
          </div>
        </div>

        <div className="text-center mt-6">
          <Link href="/" className="text-white/80 hover:text-white transition-colors">
            ← العودة للصفحة الرئيسية
          </Link>
        </div>
      </div>
    </div>
  );
}
