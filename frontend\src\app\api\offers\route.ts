import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { OfferStatus } from '@prisma/client';

// GET /api/offers - جلب العروض
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status') as OfferStatus | null;
    const projectId = searchParams.get('projectId');
    const craftsmanId = searchParams.get('craftsmanId');
    const clientId = searchParams.get('clientId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    const skip = (page - 1) * limit;

    const where = {
      ...(status && { status }),
      ...(projectId && { projectId }),
      ...(craftsmanId && { craftsmanId }),
      ...(clientId && { clientId }),
    };

    const [offers, total] = await Promise.all([
      prisma.offer.findMany({
        where,
        skip,
        take: limit,
        include: {
          project: {
            select: {
              id: true,
              title: true,
              category: true,
              budget: true,
              location: true,
              deadline: true,
            },
          },
          craftsman: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
              craftsmanProfile: {
                select: {
                  rating: true,
                  completedProjects: true,
                  skills: true,
                },
              },
            },
          },
          client: {
            select: {
              id: true,
              name: true,
              email: true,
              avatar: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.offer.count({ where }),
    ]);

    return NextResponse.json({
      offers,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching offers:', error);
    return NextResponse.json(
      { error: 'خطأ في جلب العروض' },
      { status: 500 }
    );
  }
}

// POST /api/offers - إنشاء عرض جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      projectId,
      craftsmanId,
      clientId,
      price,
      duration,
      description,
      materials,
      warranty,
    } = body;

    // التحقق من عدم وجود عرض سابق من نفس الحرفي لنفس المشروع
    const existingOffer = await prisma.offer.findFirst({
      where: {
        projectId,
        craftsmanId,
      },
    });

    if (existingOffer) {
      return NextResponse.json(
        { error: 'لقد قدمت عرضاً على هذا المشروع من قبل' },
        { status: 400 }
      );
    }

    const offer = await prisma.offer.create({
      data: {
        projectId,
        craftsmanId,
        clientId,
        price: parseFloat(price),
        duration,
        description,
        materials,
        warranty,
      },
      include: {
        project: {
          select: {
            id: true,
            title: true,
            category: true,
          },
        },
        craftsman: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
        client: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    // إنشاء إشعار للعميل
    await prisma.notification.create({
      data: {
        userId: clientId,
        type: 'OFFER_RECEIVED',
        title: 'عرض جديد على مشروعك',
        message: `تم استلام عرض جديد من ${offer.craftsman.name} على مشروع ${offer.project.title}`,
        actionUrl: `/client/offers`,
      },
    });

    return NextResponse.json(offer, { status: 201 });
  } catch (error) {
    console.error('Error creating offer:', error);
    return NextResponse.json(
      { error: 'خطأ في إنشاء العرض' },
      { status: 500 }
    );
  }
}
