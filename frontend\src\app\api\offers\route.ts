import { NextRequest, NextResponse } from 'next/server';
import { mockOffers, mockUsers, mockProjects, Offer } from '@/lib/mock-data';

// GET /api/offers - جلب العروض
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('projectId');
    const craftsmanId = searchParams.get('craftsmanId');
    const status = searchParams.get('status');

    let filteredOffers = [...mockOffers];

    // تطبيق الفلاتر
    if (projectId) {
      filteredOffers = filteredOffers.filter(offer => offer.projectId === projectId);
    }
    if (craftsmanId) {
      filteredOffers = filteredOffers.filter(offer => offer.craftsmanId === craftsmanId);
    }
    if (status) {
      filteredOffers = filteredOffers.filter(offer => offer.status === status);
    }

    // إضافة بيانات الحرفي والمشروع لكل عرض
    const offersWithDetails = filteredOffers.map(offer => {
      const craftsman = mockUsers.find(user => user.id === offer.craftsmanId);
      const project = mockProjects.find(project => project.id === offer.projectId);
      
      return {
        ...offer,
        craftsman: craftsman ? {
          id: craftsman.id,
          name: craftsman.name,
          location: craftsman.location,
          avatar: craftsman.avatar,
          bio: craftsman.bio
        } : null,
        project: project ? {
          id: project.id,
          title: project.title,
          category: project.category,
          location: project.location,
          budget: project.budget
        } : null
      };
    });

    return NextResponse.json({
      offers: offersWithDetails,
      total: offersWithDetails.length
    });
  } catch (error) {
    console.error('Error fetching offers:', error);
    return NextResponse.json(
      { error: 'خطأ في جلب العروض' },
      { status: 500 }
    );
  }
}

// POST /api/offers - إنشاء عرض جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      projectId,
      craftsmanId,
      amount,
      currency = 'TRY',
      description,
      estimatedDuration
    } = body;

    // التحقق من البيانات المطلوبة
    if (!projectId || !craftsmanId || !amount || !description || !estimatedDuration) {
      return NextResponse.json(
        { error: 'جميع الحقول المطلوبة يجب ملؤها' },
        { status: 400 }
      );
    }

    // التحقق من وجود المشروع
    const project = mockProjects.find(p => p.id === projectId);
    if (!project) {
      return NextResponse.json(
        { error: 'المشروع غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من وجود الحرفي
    const craftsman = mockUsers.find(user => user.id === craftsmanId);
    if (!craftsman || craftsman.role !== 'CRAFTSMAN') {
      return NextResponse.json(
        { error: 'الحرفي غير موجود' },
        { status: 404 }
      );
    }

    // التحقق من عدم وجود عرض سابق من نفس الحرفي على نفس المشروع
    const existingOffer = mockOffers.find(
      offer => offer.projectId === projectId && offer.craftsmanId === craftsmanId
    );
    if (existingOffer) {
      return NextResponse.json(
        { error: 'لقد قدمت عرضاً على هذا المشروع من قبل' },
        { status: 400 }
      );
    }

    // إنشاء عرض جديد
    const newOffer: Offer = {
      id: `offer_${Date.now()}`,
      projectId,
      craftsmanId,
      amount: Number(amount),
      currency,
      description,
      estimatedDuration: Number(estimatedDuration),
      status: 'PENDING',
      createdAt: new Date()
    };

    // إضافة العرض للبيانات التجريبية
    mockOffers.push(newOffer);

    return NextResponse.json({
      offer: newOffer,
      message: 'تم تقديم العرض بنجاح'
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating offer:', error);
    return NextResponse.json(
      { error: 'خطأ في تقديم العرض' },
      { status: 500 }
    );
  }
}
