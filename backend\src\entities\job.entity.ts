import { <PERSON><PERSON>ty, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, ManyToOne, OneToMany, JoinColumn } from 'typeorm';
import { User } from './user.entity';
import { Offer } from './offer.entity';
import { Transaction } from './transaction.entity';
import { Review } from './review.entity';

export enum JobStatus {
  DRAFT = 'draft',
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled',
}

@Entity('jobs')
export class Job {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  title: string;

  @Column('text')
  description: string;

  @Column({ nullable: true, type: 'text' })
  requirements: string;

  @Column('decimal', { precision: 10, scale: 2, default: 0 })
  budget: number;

  @Column({ nullable: true })
  location: string;

  @Column({ nullable: true })
  deadline: Date;

  @Column({
    type: 'enum',
    enum: JobStatus,
    default: JobStatus.DRAFT,
  })
  status: JobStatus;

  @Column('simple-array', { nullable: true })
  attachments: string[];

  @Column('simple-array', { nullable: true })
  tags: string[];

  @Column({ nullable: true })
  category: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @ManyToOne(() => User, user => user.clientJobs)
  @JoinColumn({ name: 'clientId' })
  client: User;

  @Column()
  clientId: string;

  @OneToMany(() => Offer, offer => offer.job)
  offers: Offer[];

  @OneToMany(() => Transaction, transaction => transaction.job)
  transactions: Transaction[];

  @OneToMany(() => Review, review => review.job)
  reviews: Review[];
}
