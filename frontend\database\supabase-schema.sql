-- Dozan Database Schema for Supabase
-- Run this SQL in Supabase Dashboard > SQL Editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create <PERSON><PERSON><PERSON> types
CREATE TYPE user_role AS ENUM ('CLIENT', 'CRAFTSMAN', 'ADMIN');
CREATE TYPE project_status AS ENUM ('OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'DISPUTED');
CREATE TYPE project_priority AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'URGENT');
CREATE TYPE bid_status AS ENUM ('PENDING', 'ACCEPTED', 'REJECTED', 'WITHDRAWN');
CREATE TYPE payment_status AS ENUM ('PENDING', 'PAID', 'FAILED', 'REFUNDED');
CREATE TYPE notification_type AS ENUM ('PROJECT_UPDATE', 'NEW_BID', 'PAYMENT', 'MESSAGE', 'SYSTEM');

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    avatar VARCHAR(500),
    role user_role NOT NULL DEFAULT 'CLIENT',
    location VARCHAR(255),
    is_verified BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    email_verified_at TIMESTAMP,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Client profiles
CREATE TABLE client_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    company_name VARCHAR(255),
    total_projects INTEGER DEFAULT 0,
    total_spent DECIMAL(10,2) DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Craftsman profiles
CREATE TABLE craftsman_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    skills TEXT[], -- Array of skills
    experience INTEGER NOT NULL, -- Years of experience
    hourly_rate DECIMAL(8,2),
    availability VARCHAR(50) DEFAULT 'AVAILABLE',
    working_hours VARCHAR(255),
    languages TEXT[], -- Array of languages
    rating DECIMAL(3,2) DEFAULT 0,
    total_projects INTEGER DEFAULT 0,
    total_earnings DECIMAL(10,2) DEFAULT 0,
    bio TEXT,
    certifications TEXT[],
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Portfolio items
CREATE TABLE portfolio_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    craftsman_id UUID NOT NULL REFERENCES craftsman_profiles(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    before_images TEXT[], -- Array of image URLs
    after_images TEXT[], -- Array of image URLs
    tags TEXT[],
    featured BOOLEAN DEFAULT FALSE,
    duration VARCHAR(100),
    budget_range VARCHAR(100),
    client_feedback TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Projects table
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(100) NOT NULL,
    budget_min DECIMAL(10,2),
    budget_max DECIMAL(10,2),
    location VARCHAR(255) NOT NULL,
    priority project_priority DEFAULT 'MEDIUM',
    materials VARCHAR(50) DEFAULT 'NOT_SPECIFIED',
    work_type VARCHAR(255),
    requirements TEXT,
    images TEXT[], -- Array of image URLs
    status project_status DEFAULT 'OPEN',
    client_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    assigned_craftsman_id UUID REFERENCES users(id),
    deadline TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Bids table
CREATE TABLE bids (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    craftsman_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    estimated_duration VARCHAR(100),
    proposal TEXT NOT NULL,
    status bid_status DEFAULT 'PENDING',
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(project_id, craftsman_id)
);

-- Messages table
CREATE TABLE messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    sender_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    receiver_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    attachments TEXT[],
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Reviews table
CREATE TABLE reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    reviewer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reviewee_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    comment TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    UNIQUE(project_id, reviewer_id, reviewee_id)
);

-- Payments table
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    payer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    payee_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    commission DECIMAL(10,2) DEFAULT 0,
    status payment_status DEFAULT 'PENDING',
    payment_method VARCHAR(100),
    transaction_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type notification_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    data JSONB,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_category ON projects(category);
CREATE INDEX idx_projects_client_id ON projects(client_id);
CREATE INDEX idx_bids_project_id ON bids(project_id);
CREATE INDEX idx_bids_craftsman_id ON bids(craftsman_id);
CREATE INDEX idx_messages_project_id ON messages(project_id);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_is_read ON notifications(is_read);

-- Insert sample data
INSERT INTO users (email, password_hash, name, phone, role, location) VALUES
('<EMAIL>', '$2b$10$example', 'أحمد محمد', '+963911234567', 'CLIENT', 'دمشق'),
('<EMAIL>', '$2b$10$example', 'محمد أحمد', '+963922345678', 'CRAFTSMAN', 'حلب'),
('<EMAIL>', '$2b$10$example', 'مدير النظام', '+963933456789', 'ADMIN', 'دمشق');

-- Insert sample project
INSERT INTO projects (title, description, category, budget_min, budget_max, location, client_id) 
SELECT 'إصلاح باب خشبي', 'يحتاج إصلاح باب خشبي في المنزل', 'نجارة', 50000, 100000, 'دمشق', id 
FROM users WHERE email = '<EMAIL>';

COMMIT;
