import { <PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column, CreateDateColumn, ManyToOne, JoinColumn } from 'typeorm';
import { User } from './user.entity';
import { Job } from './job.entity';

@Entity('reviews')
export class Review {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'int', default: 5 })
  rating: number;

  @Column('text')
  comment: string;

  @CreateDateColumn()
  createdAt: Date;

  @ManyToOne(() => User, user => user.givenReviews)
  @JoinColumn({ name: 'clientId' })
  client: User;

  @Column()
  clientId: string;

  @ManyToOne(() => User, user => user.receivedReviews)
  @JoinColumn({ name: 'craftsmanId' })
  craftsman: User;

  @Column()
  craftsmanId: string;

  @ManyToOne(() => Job, job => job.reviews)
  @JoinColumn({ name: 'jobId' })
  job: Job;

  @Column()
  jobId: string;
}
