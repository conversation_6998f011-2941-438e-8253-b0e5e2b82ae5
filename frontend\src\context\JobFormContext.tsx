'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';

export interface JobFormData {
  // Step 1: Basic Details
  title: string;
  description: string;
  category: string;
  location: string;

  // Step 2: Requirements
  requirements: string;
  skills: string[];
  attachments: string[];
  images: File[];

  // Step 3: Budget and Timeline
  budget: number;
  deadline: string;

  // Additional fields
  status: 'draft' | 'open' | 'in_progress' | 'completed' | 'cancelled';
  tags: string[];
}

interface JobFormContextType {
  formData: JobFormData;
  updateFormData: (data: Partial<JobFormData>) => void;
  resetForm: () => void;
  currentStep: number;
  setCurrentStep: (step: number) => void;
  isSubmitting: boolean;
  setIsSubmitting: (isSubmitting: boolean) => void;
}

const defaultFormData: JobFormData = {
  title: '',
  description: '',
  category: '',
  location: '',
  requirements: '',
  skills: [],
  attachments: [],
  images: [],
  budget: 0,
  deadline: '',
  status: 'draft',
  tags: [],
};

const JobFormContext = createContext<JobFormContextType | undefined>(undefined);

export const JobFormProvider = ({ children }: { children: ReactNode }) => {
  const [formData, setFormData] = useState<JobFormData>(defaultFormData);
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const updateFormData = (data: Partial<JobFormData>) => {
    setFormData((prev) => ({ ...prev, ...data }));
  };

  const resetForm = () => {
    setFormData(defaultFormData);
    setCurrentStep(1);
  };

  return (
    <JobFormContext.Provider
      value={{
        formData,
        updateFormData,
        resetForm,
        currentStep,
        setCurrentStep,
        isSubmitting,
        setIsSubmitting,
      }}
    >
      {children}
    </JobFormContext.Provider>
  );
};

export const useJobForm = () => {
  const context = useContext(JobFormContext);
  if (context === undefined) {
    throw new Error('useJobForm must be used within a JobFormProvider');
  }
  return context;
};
