'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import supabase from '@/lib/supabase';

export default function SubmitBidPage() {
  const router = useRouter();
  const params = useParams();
  const projectId = params.id as string;

  const [project, setProject] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [bidData, setBidData] = useState({
    amount: '',
    description: '',
    estimated_duration: '',
    materials_included: false,
    warranty_period: '',
    start_date: ''
  });

  useEffect(() => {
    loadProject();
  }, [projectId]);

  const loadProject = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          client:users!projects_client_id_fkey(name, email)
        `)
        .eq('id', projectId)
        .single();

      if (error) throw error;
      setProject(data);
    } catch (error) {
      console.error('Error loading project:', error);
      alert('خطأ في تحميل المشروع');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setBidData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      // Mock craftsman ID - في التطبيق الحقيقي سيأتي من نظام المصادقة
      const mockCraftsmanId = '2';

      const bidSubmission = {
        project_id: projectId,
        craftsman_id: mockCraftsmanId,
        amount: parseInt(bidData.amount),
        description: bidData.description,
        estimated_duration: bidData.estimated_duration,
        materials_included: bidData.materials_included,
        warranty_period: bidData.warranty_period,
        start_date: bidData.start_date,
        status: 'PENDING'
      };

      const { data, error } = await supabase
        .from('bids')
        .insert([bidSubmission])
        .select();

      if (error) throw error;

      alert('تم تقديم العرض بنجاح!');
      router.push('/craftsman/dashboard');

    } catch (error) {
      console.error('Error submitting bid:', error);
      alert('حدث خطأ أثناء تقديم العرض. يرجى المحاولة مرة أخرى.');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout title="تقديم عرض">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal"></div>
        </div>
      </DashboardLayout>
    );
  }

  if (!project) {
    return (
      <DashboardLayout title="مشروع غير موجود">
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-600">المشروع غير موجود</h2>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout 
      title="تقديم عرض"
      subtitle={`تقديم عرض للمشروع: ${project.title}`}
    >
      <div className="max-w-4xl mx-auto space-y-6">
        {/* معلومات المشروع */}
        <Card>
          <CardHeader>
            <CardTitle>تفاصيل المشروع</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold text-navy">{project.title}</h3>
                <p className="text-gray-600 mt-2">{project.description}</p>
              </div>
              <div className="space-y-2">
                <div><span className="font-medium">التخصص:</span> {project.category}</div>
                <div><span className="font-medium">الموقع:</span> {project.location}</div>
                <div><span className="font-medium">الميزانية:</span> {project.budget_min?.toLocaleString()} - {project.budget_max?.toLocaleString()} ل.س</div>
                <div><span className="font-medium">الموعد النهائي:</span> {new Date(project.deadline).toLocaleDateString('ar-SA')}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* نموذج تقديم العرض */}
        <Card>
          <CardHeader>
            <CardTitle>تفاصيل العرض</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-gray-700 font-medium mb-2">المبلغ المطلوب (ل.س) *</label>
                  <input
                    type="number"
                    name="amount"
                    value={bidData.amount}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                    placeholder="75000"
                    required
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2">المدة المتوقعة *</label>
                  <input
                    type="text"
                    name="estimated_duration"
                    value={bidData.estimated_duration}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                    placeholder="مثال: 10 أيام"
                    required
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2">تاريخ البدء المتوقع</label>
                  <input
                    type="date"
                    name="start_date"
                    value={bidData.start_date}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2">فترة الضمان</label>
                  <input
                    type="text"
                    name="warranty_period"
                    value={bidData.warranty_period}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                    placeholder="مثال: 6 أشهر"
                  />
                </div>
              </div>

              <div>
                <label className="block text-gray-700 font-medium mb-2">وصف العرض والخطة *</label>
                <textarea
                  name="description"
                  value={bidData.description}
                  onChange={handleInputChange}
                  rows={6}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                  placeholder="اشرح كيف ستنفذ المشروع، الخطوات، المواد المطلوبة، وأي تفاصيل أخرى مهمة..."
                  required
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="materials_included"
                  checked={bidData.materials_included}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-teal focus:ring-teal border-gray-300 rounded"
                />
                <label className="mr-2 text-gray-700">المواد مشمولة في السعر</label>
              </div>

              <div className="flex space-x-4 space-x-reverse">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.back()}
                  className="border-gray-300 text-gray-700"
                >
                  إلغاء
                </Button>
                
                <Button
                  type="submit"
                  className="bg-gradient-to-r from-teal to-navy"
                  disabled={submitting}
                >
                  {submitting ? 'جاري الإرسال...' : 'تقديم العرض'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
