'use client';

import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import Link from 'next/link';
import { Button } from '../ui/Button';

// Types للتحسين
interface MousePosition {
  x: number;
  y: number;
}

interface DotConfig {
  top: number;
  left: number;
  size: number;
}

interface TypewriterProps {
  texts: string[];
  speed?: number;
  pauseDuration?: number;
}

// Hook مخصص للـ typewriter effect
const useTypewriter = ({ texts, speed = 100, pauseDuration = 2000 }: TypewriterProps) => {
  const [displayText, setDisplayText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isTyping, setIsTyping] = useState(true);

  useEffect(() => {
    if (texts.length === 0) return;

    const currentText = texts[currentIndex];
    let timeoutId: NodeJS.Timeout;

    if (isTyping) {
      if (displayText.length < currentText.length) {
        timeoutId = setTimeout(() => {
          setDisplayText(currentText.slice(0, displayText.length + 1));
        }, speed);
      } else {
        timeoutId = setTimeout(() => {
          setIsTyping(false);
        }, pauseDuration);
      }
    } else {
      if (displayText.length > 0) {
        timeoutId = setTimeout(() => {
          setDisplayText(displayText.slice(0, -1));
        }, speed / 2);
      } else {
        setCurrentIndex((prev) => (prev + 1) % texts.length);
        setIsTyping(true);
      }
    }

    return () => clearTimeout(timeoutId);
  }, [displayText, currentIndex, isTyping, texts, speed, pauseDuration]);

  return displayText;
};

const Hero = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: 0, y: 0 });
  const heroRef = useRef<HTMLElement>(null);

  const heroTexts = useMemo(() => [
    'منصة تربط بين أصحاب المشاريع والحرفيين المهرة في سوريا',
    'حلول احترافية متكاملة لجميع احتياجاتك في البناء والتشطيب والصيانة',
    'جودة عالية وأسعار منافسة مع ضمان الرضا التام وخدمة عملاء متميزة'
  ], []);

  const typewriterText = useTypewriter({
    texts: heroTexts,
    speed: 80,
    pauseDuration: 3000
  });

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (heroRef.current) {
      const rect = heroRef.current.getBoundingClientRect();
      setMousePosition({
        x: (e.clientX - rect.left) / rect.width,
        y: (e.clientY - rect.top) / rect.height
      });
    }
  }, []);

  useEffect(() => {
    const heroElement = heroRef.current;
    if (heroElement) {
      heroElement.addEventListener('mousemove', handleMouseMove, { passive: true });
      return () => heroElement.removeEventListener('mousemove', handleMouseMove);
    }
  }, [handleMouseMove]);

  // تكوين النقاط التفاعلية - مُحسَّن للأداء
  const interactiveDots = useMemo<DotConfig[]>(() => [
    { top: 15, left: 10, size: 3 },
    { top: 25, left: 85, size: 4 },
    { top: 35, left: 20, size: 3 },
    { top: 45, left: 75, size: 5 },
    { top: 55, left: 15, size: 4 },
    { top: 65, left: 90, size: 3 },
    { top: 75, left: 25, size: 4 },
    { top: 85, left: 80, size: 3 },
    { top: 20, left: 50, size: 4 },
    { top: 40, left: 60, size: 3 },
    { top: 60, left: 40, size: 5 },
    { top: 80, left: 55, size: 4 }
  ], []);

  // مكون النقاط التفاعلية المُحسَّن
  const InteractiveDots = React.memo(() => (
    <div className="absolute inset-0 overflow-hidden pointer-events-none" aria-hidden="true">
      {interactiveDots.map((dot, i) => {
        const dotX = dot.left / 100;
        const dotY = dot.top / 100;

        const distance = Math.sqrt(
          Math.pow(mousePosition.x - dotX, 2) +
          Math.pow(mousePosition.y - dotY, 2)
        );

        const maxDistance = 0.15;
        const proximity = Math.max(0, 1 - distance / maxDistance);

        const pushDistance = proximity * 20;
        const angle = Math.atan2(dotY - mousePosition.y, dotX - mousePosition.x);
        const pushX = Math.cos(angle) * pushDistance;
        const pushY = Math.sin(angle) * pushDistance;

        return (
          <div
            key={i}
            className="absolute bg-white rounded-full transition-all duration-300 ease-out"
            style={{
              top: `${dot.top + pushY}%`,
              left: `${dot.left + pushX}%`,
              width: `${dot.size + proximity * 4}px`,
              height: `${dot.size + proximity * 4}px`,
              opacity: 0.3 + proximity * 0.7,
              boxShadow: proximity > 0.3 ? `0 0 ${proximity * 20}px rgba(255, 255, 255, ${proximity * 0.8})` : 'none',
              transform: `translate(-50%, -50%) scale(${1 + proximity * 0.5})`,
              filter: proximity > 0.2 ? `blur(${proximity * 0.5}px)` : 'none'
            }}
          />
        );
      })}
    </div>
  ));

  InteractiveDots.displayName = 'InteractiveDots';

  return (
    <section
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
      role="banner"
      aria-label="الصفحة الرئيسية لمنصة دوزان"
    >
      {/* خلفية احترافية متناسقة مع قسم الإحصائيات */}
      <div className="absolute inset-0 bg-gradient-to-br from-navy via-teal to-skyblue">
        <div className="absolute inset-0 bg-black/10"></div>

        {/* أنماط هندسية ناعمة */}
        <div className="absolute top-0 left-0 w-full h-full opacity-10" aria-hidden="true">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl"></div>
          <div className="absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl"></div>
          <div className="absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl"></div>
          <div className="absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl"></div>
        </div>

        {/* شبكة نقطية ثابتة */}
        <div className="absolute inset-0 opacity-20" aria-hidden="true">
          <div className="w-full h-full" style={{
            backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',
            backgroundSize: '30px 30px'
          }}></div>
        </div>
      </div>

      {/* النقاط التفاعلية المُحسَّنة */}
      <InteractiveDots />

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center max-w-6xl mx-auto">
          {/* شعار احترافي */}

          {/* العنوان الرئيسي المحسن */}
          <div className={`mb-12 mt-12 transform transition-all duration-1000 delay-300 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-white leading-tight">
              <span className="block mb-4">مرحباً بك في</span>
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 via-orange-300 to-yellow-400 drop-shadow-2xl">
                دوزان
              </span>
            </h1>
            <div className="mt-8 w-32 h-1 bg-gradient-to-r from-yellow-400 to-orange-500 mx-auto rounded-full"></div>
          </div>

          {/* النص المتغير مع typewriter effect */}
          <div className={`mb-12 transform transition-all duration-1000 delay-500 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <div className="min-h-[120px] flex items-center justify-center">
              <p
                className="text-lg sm:text-xl md:text-2xl lg:text-3xl text-white/95 max-w-5xl mx-auto leading-relaxed font-medium"
                aria-live="polite"
                aria-label="وصف منصة دوزان"
              >
                {typewriterText}
                <span className="animate-pulse text-yellow-300">|</span>
              </p>
            </div>
          </div>

          {/* الأزرار المحسنة مع accessibility */}
          <div className={`flex flex-col sm:flex-row gap-6 justify-center items-center mb-20 transform transition-all duration-1000 delay-700 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <Link href="/jobs/create" className="group">
              <Button
                size="lg"
                className="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 hover:from-yellow-500 hover:via-orange-600 hover:to-red-600 text-white px-8 sm:px-12 py-4 sm:py-6 text-lg sm:text-xl font-bold shadow-2xl hover:shadow-yellow-500/30 transform hover:scale-105 transition-all duration-300 border-2 border-white/20 hover:border-white/40 focus:ring-4 focus:ring-yellow-300/50"
                aria-label="ابدأ مشروعك الآن - انتقل إلى صفحة إنشاء مشروع جديد"
              >
                <span className="flex items-center gap-3">
                  <span className="text-xl sm:text-2xl group-hover:scale-125 transition-transform duration-300" aria-hidden="true">🚀</span>
                  <span className="text-base sm:text-xl">ابدأ مشروعك الآن</span>
                </span>
              </Button>
            </Link>
            <Link href="/jobs" className="group">
              <Button
                size="lg"
                className="bg-white/15 backdrop-blur-md border-2 border-white/30 text-white hover:bg-white/25 hover:border-white/50 px-8 sm:px-12 py-4 sm:py-6 text-lg sm:text-xl font-bold transform hover:scale-105 transition-all duration-300 shadow-xl focus:ring-4 focus:ring-white/30"
                aria-label="تصفح المشاريع - انتقل إلى صفحة المشاريع المتاحة"
              >
                <span className="flex items-center gap-3">
                  <span className="text-xl sm:text-2xl group-hover:scale-125 transition-transform duration-300" aria-hidden="true">🔍</span>
                  <span className="text-base sm:text-xl">تصفح المشاريع</span>
                </span>
              </Button>
            </Link>
          </div>

          {/* مؤشر التمرير المحسن مع دعم الحركة المقللة */}
          <div className={`transform transition-all duration-1000 delay-1000 ${isVisible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'}`}>
            <div className="flex flex-col items-center">
              <p className="text-white/80 text-base sm:text-lg mb-6 font-medium">اكتشف المزيد من المميزات</p>
              <button
                className="relative group focus:outline-none focus:ring-4 focus:ring-white/30 rounded-full p-2"
                onClick={() => {
                  const nextSection = document.querySelector('#stats-section');
                  if (nextSection) {
                    nextSection.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
                aria-label="انتقل إلى القسم التالي"
              >
                <div className="w-8 h-14 border-2 border-white/40 rounded-full flex justify-center bg-white/10 backdrop-blur-sm group-hover:border-white/60 transition-colors duration-300">
                  <div className="w-2 h-4 bg-white/70 rounded-full mt-3 motion-safe:animate-bounce group-hover:bg-white transition-colors duration-300"></div>
                </div>
                
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

// تحسين الأداء مع React.memo
export default React.memo(Hero);
