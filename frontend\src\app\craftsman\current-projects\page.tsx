'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';

const CraftsmanCurrentProjectsPage = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const currentProjects = [
    {
      id: 1,
      title: 'تجديد المطبخ الرئيسي',
      client: 'أحمد محمد',
      clientPhone: '+963 123 456 789',
      status: 'جاري التنفيذ',
      progress: 75,
      startDate: '2024-02-01',
      deadline: '2024-03-15',
      budget: '₺7,500',
      location: 'دمشق - المزة',
      description: 'تجديد كامل للمطبخ مع تركيب خزائن جديدة وأجهزة حديثة',
      nextMilestone: 'تركيب الأجهزة',
      daysRemaining: 12,
      category: 'نجارة'
    },
    {
      id: 2,
      title: 'تركيب نظام كهرباء ذكي',
      client: 'سارة خالد',
      clientPhone: '+963 987 654 321',
      status: 'بدء قريباً',
      progress: 0,
      startDate: '2024-02-20',
      deadline: '2024-03-10',
      budget: '₺4,800',
      location: 'دمشق - المالكي',
      description: 'تركيب نظام إضاءة ذكي ومفاتيح تحكم عن بعد',
      nextMilestone: 'شراء المواد',
      daysRemaining: 18,
      category: 'كهرباء'
    },
    {
      id: 3,
      title: 'إصلاح نظام السباكة',
      client: 'فاطمة أحمد',
      clientPhone: '+963 555 123 456',
      status: 'مكتمل',
      progress: 100,
      startDate: '2024-01-15',
      deadline: '2024-02-15',
      budget: '₺2,200',
      location: 'دمشق - كفرسوسة',
      description: 'إصلاح تسريب في الحمام الرئيسي وتجديد الأنابيب',
      nextMilestone: 'تسليم المشروع',
      daysRemaining: 0,
      category: 'سباكة'
    },
    {
      id: 4,
      title: 'دهان شقة كاملة',
      client: 'محمد علي',
      clientPhone: '+963 777 888 999',
      status: 'متأخر',
      progress: 60,
      startDate: '2024-01-20',
      deadline: '2024-02-10',
      budget: '₺3,200',
      location: 'دمشق - أبو رمانة',
      description: 'دهان شقة من 3 غرف مع تحضير الجدران والأسقف',
      nextMilestone: 'دهان الغرف المتبقية',
      daysRemaining: -8,
      category: 'دهان'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'جاري التنفيذ':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'بدء قريباً':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'مكتمل':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'متأخر':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'متوقف':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getProgressColor = (progress: number, status: string) => {
    if (status === 'متأخر') return 'bg-red-500';
    if (progress === 100) return 'bg-green-500';
    if (progress >= 75) return 'bg-blue-500';
    if (progress >= 50) return 'bg-yellow-500';
    return 'bg-gray-400';
  };

  const filteredProjects = currentProjects.filter(project => {
    if (activeFilter === 'all') return true;
    return project.status === activeFilter;
  });

  const stats = [
    {
      title: 'المشاريع النشطة',
      value: currentProjects.filter(p => ['جاري التنفيذ', 'بدء قريباً'].includes(p.status)).length.toString(),
      color: 'text-blue-600',
      icon: '🔄'
    },
    {
      title: 'المشاريع المكتملة',
      value: currentProjects.filter(p => p.status === 'مكتمل').length.toString(),
      color: 'text-green-600',
      icon: '✅'
    },
    {
      title: 'المشاريع المتأخرة',
      value: currentProjects.filter(p => p.status === 'متأخر').length.toString(),
      color: 'text-red-600',
      icon: '⚠️'
    },
    {
      title: 'متوسط التقدم',
      value: `${Math.round(currentProjects.reduce((sum, p) => sum + p.progress, 0) / currentProjects.length)}%`,
      color: 'text-purple-600',
      icon: '📊'
    }
  ];

  return (
    <ProtectedRoute requiredRoles="craftsman">
      <DashboardLayout 
        title="مشاريعي الحالية"
        subtitle="متابعة وإدارة المشاريع التي تعمل عليها حالياً"
      >
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className={`text-3xl ${stat.color}`}>
                    {stat.icon}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-wrap gap-2">
              {[
                { id: 'all', label: 'جميع المشاريع' },
                { id: 'جاري التنفيذ', label: 'جاري التنفيذ' },
                { id: 'بدء قريباً', label: 'بدء قريباً' },
                { id: 'مكتمل', label: 'مكتملة' },
                { id: 'متأخر', label: 'متأخرة' }
              ].map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => setActiveFilter(filter.id)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    activeFilter === filter.id
                      ? 'bg-navy text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {filter.label}
                </button>
              ))}
            </div>
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredProjects.map((project) => (
              <div key={project.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{project.title}</h3>
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">{project.description}</p>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(project.status)}`}>
                    {project.status}
                  </span>
                </div>

                {/* Client Info */}
                <div className="flex items-center space-x-3 space-x-reverse mb-4 p-3 bg-gray-50 rounded-lg">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                    {project.client.charAt(0)}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{project.client}</p>
                    <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                      <span>📞 {project.clientPhone}</span>
                      <span>•</span>
                      <span>📍 {project.location}</span>
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mb-4">
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm font-medium text-gray-700">التقدم</span>
                    <span className="text-sm font-bold text-gray-900">{project.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(project.progress, project.status)}`}
                      style={{ width: `${project.progress}%` }}
                    ></div>
                  </div>
                </div>

                {/* Project Details */}
                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div>
                    <span className="text-gray-500">الميزانية:</span>
                    <span className="font-semibold text-gray-900 mr-2">{project.budget}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">بدء العمل:</span>
                    <span className="font-semibold text-gray-900 mr-2">{project.startDate}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">الموعد النهائي:</span>
                    <span className="font-semibold text-gray-900 mr-2">{project.deadline}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">الأيام المتبقية:</span>
                    <span className={`font-semibold mr-2 ${
                      project.daysRemaining < 0 ? 'text-red-600' : 
                      project.daysRemaining <= 3 ? 'text-yellow-600' : 'text-green-600'
                    }`}>
                      {project.daysRemaining < 0 ? `متأخر ${Math.abs(project.daysRemaining)} يوم` : 
                       project.daysRemaining === 0 ? 'اليوم الأخير' : 
                       `${project.daysRemaining} يوم`}
                    </span>
                  </div>
                </div>

                {/* Next Milestone */}
                <div className="mb-4 p-3 bg-blue-50 rounded-lg">
                  <p className="text-sm text-blue-800">
                    <span className="font-medium">المرحلة التالية:</span> {project.nextMilestone}
                  </p>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-2 space-x-reverse text-xs text-gray-500">
                    <span className="bg-gray-100 px-2 py-1 rounded">{project.category}</span>
                  </div>
                  <div className="flex space-x-2 space-x-reverse">
                    <Button size="sm" variant="outline" className="border-blue-300 text-blue-700 hover:bg-blue-100">
                      📞 اتصال
                    </Button>
                    <Button size="sm" variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                      عرض التفاصيل
                    </Button>
                    {project.status === 'جاري التنفيذ' && (
                      <Button size="sm" className="bg-gradient-to-r from-navy to-teal">
                        تحديث التقدم
                      </Button>
                    )}
                    {project.status === 'مكتمل' && (
                      <Button size="sm" className="bg-gradient-to-r from-green-500 to-green-600">
                        طلب التقييم
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredProjects.length === 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-6xl mb-4">🔨</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد مشاريع</h3>
              <p className="text-gray-600 mb-6">لم يتم العثور على مشاريع بالمعايير المحددة</p>
              <Button 
                onClick={() => setActiveFilter('all')}
                className="bg-gradient-to-r from-navy to-teal"
              >
                عرض جميع المشاريع
              </Button>
            </div>
          )}

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h3>
              <div className="space-y-3">
                <Button variant="outline" className="w-full justify-start border-blue-300 text-blue-700 hover:bg-blue-100">
                  📋 تحديث تقدم المشاريع
                </Button>
                <Button variant="outline" className="w-full justify-start border-green-300 text-green-700 hover:bg-green-100">
                  📷 رفع صور التقدم
                </Button>
                <Button variant="outline" className="w-full justify-start border-purple-300 text-purple-700 hover:bg-purple-100">
                  💬 إرسال تحديث للعملاء
                </Button>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">المواعيد القريبة</h3>
              <div className="space-y-3">
                {currentProjects
                  .filter(p => p.daysRemaining <= 7 && p.daysRemaining >= 0)
                  .map(project => (
                    <div key={project.id} className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                      <div>
                        <p className="font-medium text-yellow-900 text-sm">{project.title}</p>
                        <p className="text-xs text-yellow-600">{project.daysRemaining} يوم متبقي</p>
                      </div>
                    </div>
                  ))}
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">إحصائيات سريعة</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">معدل الإنجاز:</span>
                  <span className="font-bold text-blue-600">
                    {Math.round(currentProjects.reduce((sum, p) => sum + p.progress, 0) / currentProjects.length)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">مشاريع في الموعد:</span>
                  <span className="font-bold text-green-600">
                    {currentProjects.filter(p => p.daysRemaining >= 0).length}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">إجمالي القيمة:</span>
                  <span className="font-bold text-purple-600">
                    ₺{currentProjects.reduce((sum, p) => sum + parseFloat(p.budget.replace('₺', '').replace(',', '')), 0).toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default CraftsmanCurrentProjectsPage;
