{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// دالة تنسيق التاريخ بشكل ثابت لتجنب مشاكل Hydration\nexport function formatDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n\n    // التحقق من صحة التاريخ\n    if (isNaN(date.getTime())) {\n      return 'تاريخ غير صحيح';\n    }\n\n    // تنسيق ثابت للتاريخ\n    const year = date.getFullYear();\n    const month = date.getMonth() + 1;\n    const day = date.getDate();\n\n    // أسماء الشهور بالعربية\n    const monthNames = [\n      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',\n      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'\n    ];\n\n    return `${day} ${monthNames[month - 1]} ${year}`;\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق التاريخ النسبي (منذ كم يوم)\nexport function formatRelativeDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInMs = now.getTime() - date.getTime();\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\n\n    if (diffInDays === 0) {\n      return 'اليوم';\n    } else if (diffInDays === 1) {\n      return 'أمس';\n    } else if (diffInDays < 7) {\n      return `منذ ${diffInDays} أيام`;\n    } else if (diffInDays < 30) {\n      const weeks = Math.floor(diffInDays / 7);\n      return `منذ ${weeks} ${weeks === 1 ? 'أسبوع' : 'أسابيع'}`;\n    } else if (diffInDays < 365) {\n      const months = Math.floor(diffInDays / 30);\n      return `منذ ${months} ${months === 1 ? 'شهر' : 'أشهر'}`;\n    } else {\n      const years = Math.floor(diffInDays / 365);\n      return `منذ ${years} ${years === 1 ? 'سنة' : 'سنوات'}`;\n    }\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق الأرقام بالفواصل\nexport function formatNumber(num: number): string {\n  return num.toLocaleString('ar-SA');\n}\n\n// دالة للتحقق من صحة البريد الإلكتروني\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// دالة للتحقق من صحة رقم الهاتف السوري\nexport function isValidSyrianPhone(phone: string): boolean {\n  const phoneRegex = /^(\\+963|0)?[0-9]{9,10}$/;\n  return phoneRegex.test(phone);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,UAAkB;IAC3C,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QAEtB,wBAAwB;QACxB,IAAI,MAAM,KAAK,OAAO,KAAK;YACzB,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,KAAK,QAAQ,KAAK;QAChC,MAAM,MAAM,KAAK,OAAO;QAExB,wBAAwB;QACxB,MAAM,aAAa;YACjB;YAAS;YAAU;YAAQ;YAAS;YAAQ;YAC5C;YAAS;YAAS;YAAU;YAAU;YAAU;SACjD;QAED,OAAO,GAAG,IAAI,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,MAAM;IAClD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,mBAAmB,UAAkB;IACnD,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;QAC7C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG;YACpB,OAAO;QACT,OAAO,IAAI,eAAe,GAAG;YAC3B,OAAO;QACT,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO,CAAC,IAAI,EAAE,WAAW,KAAK,CAAC;QACjC,OAAO,IAAI,aAAa,IAAI;YAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,UAAU,UAAU;QAC3D,OAAO,IAAI,aAAa,KAAK;YAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,aAAa;YACvC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,WAAW,IAAI,QAAQ,QAAQ;QACzD,OAAO;YACL,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,QAAQ,SAAS;QACxD;IACF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,cAAc,CAAC;AAC5B;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,mBAAmB,KAAa;IAC9C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB"}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 130, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',\n  {\n    variants: {\n      variant: {\n        default: 'bg-navy text-white hover:bg-navy/90',\n        destructive: 'bg-red-500 text-white hover:bg-red-500/90',\n        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',\n        secondary: 'bg-teal text-white hover:bg-teal/90',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'underline-offset-4 hover:underline text-primary',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 rounded-md',\n        lg: 'h-11 px-8 rounded-md',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,wQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { Button } from '../ui/Button';\n\nconst Header = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex justify-between items-center\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"group flex items-center space-x-3 space-x-reverse\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-navy to-teal\">\n                دوزان\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:block\">\n            <ul className=\"flex space-x-8 space-x-reverse\">\n              <li>\n                <Link href=\"/\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الرئيسية\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  المشاريع\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الحرفيون\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  من نحن\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الأسئلة الشائعة\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  اتصل بنا\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n            </ul>\n          </nav>\n\n          {/* Desktop Buttons */}\n          <div className=\"hidden lg:flex items-center space-x-4 space-x-reverse\">\n            <Link href=\"/login\">\n              <Button variant=\"outline\" size=\"sm\" className=\"border-navy text-navy hover:bg-navy hover:text-white transition-all duration-300\">\n                تسجيل الدخول\n              </Button>\n            </Link>\n            <Link href=\"/register\">\n              <Button size=\"sm\" className=\"bg-gradient-to-r from-navy to-teal hover:from-teal hover:to-navy text-white shadow-lg hover:shadow-xl transition-all duration-300\">\n                إنشاء حساب\n              </Button>\n            </Link>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300\"\n            aria-label=\"فتح القائمة\"\n          >\n            <svg className=\"w-6 h-6 text-navy\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              {isMobileMenuOpen ? (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              ) : (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden mt-4 py-4 border-t border-gray-200\">\n            <nav className=\"space-y-4\">\n              <Link href=\"/\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الرئيسية\n              </Link>\n              <Link href=\"/jobs\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                المشاريع\n              </Link>\n              <Link href=\"/craftsmen\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الحرفيون\n              </Link>\n              <Link href=\"/about\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                من نحن\n              </Link>\n              <Link href=\"/faq\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الأسئلة الشائعة\n              </Link>\n              <Link href=\"/contact\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                اتصل بنا\n              </Link>\n            </nav>\n            <div className=\"mt-6 space-y-3\">\n              <Link href=\"/login\" className=\"block\">\n                <Button variant=\"outline\" className=\"w-full border-navy text-navy hover:bg-navy hover:text-white\">\n                  تسجيل الدخول\n                </Button>\n              </Link>\n              <Link href=\"/register\" className=\"block\">\n                <Button className=\"w-full bg-gradient-to-r from-navy to-teal text-white\">\n                  إنشاء حساب\n                </Button>\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,SAAS;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDAA2L;;;;;;kDAG1M,8OAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;;;;;;sCAO1G,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAA0F;8DAEjH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;gDAA0F;8DAErH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;;gDAA0F;8DAE1H,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;;gDAA0F;8DAEtH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAO,WAAU;;gDAA0F;8DAEpH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;gDAA0F;8DAExH,8OAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAmF;;;;;;;;;;;8CAInI,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAAoI;;;;;;;;;;;;;;;;;sCAOpK,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAoB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC1E,iCACC,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;yDAErE,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAO5E,kCACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAiF;;;;;;8CAG1G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAiF;;;;;;8CAG9G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAiF;;;;;;8CAGnH,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiF;;;;;;8CAG/G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAO,WAAU;8CAAiF;;;;;;8CAG7G,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiF;;;;;;;;;;;;sCAInH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAC5B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAA8D;;;;;;;;;;;8CAIpG,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAC/B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzF;uCAEe"}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 621, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden\">\n      {/* خلفية هندسية متناسقة */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl\"></div>\n      </div>\n\n      {/* شبكة نقطية ناعمة */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',\n          backgroundSize: '30px 30px'\n        }}></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-16 relative z-10\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Logo and Description */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n              <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center text-white text-2xl font-bold shadow-lg\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-white\">\n                دوزان\n              </span>\n            </div>\n            <p className=\"text-white/90 leading-relaxed mb-6\">\n              منصة تربط بين أصحاب المشاريع والحرفيين المهرة لتنفيذ المشاريع بكفاءة وجودة عالية في جميع أنحاء سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              روابط سريعة\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الرئيسية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  المشاريع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الحرفيون\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  من نحن\n                </Link>\n              </li>\n            </ul>\n          </div>\n          {/* Support */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              الدعم\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/contact\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  اتصل بنا\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  شروط الاستخدام\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              تواصل معنا\n            </h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">البريد الإلكتروني</p>\n                  <a href=\"mailto:<EMAIL>\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">الهاتف</p>\n                  <a href=\"tel:+963112345678\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    +963 11 234 5678\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">العنوان</p>\n                  <p className=\"text-white font-medium\">دمشق، سوريا</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"mt-12 pt-8 border-t border-white/20\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-white/80 text-sm\">\n                &copy; {new Date().getFullYear()} دوزان. جميع الحقوق محفوظة.\n              </p>\n              <p className=\"text-white/60 text-xs mt-1\">\n                صُنع بـ ❤️ في سوريا\n              </p>\n            </div>\n\n            <div className=\"flex items-center space-x-6 space-x-reverse\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">2500+</div>\n                <div className=\"text-white/70 text-xs\">حرفي</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">15000+</div>\n                <div className=\"text-white/70 text-xs\">مشروع</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">98%</div>\n                <div className=\"text-white/70 text-xs\">رضا</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,SAAS;IACb,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6H;;;;;;0DAG5I,8OAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAIlD,8OAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAyQ,UAAS;;;;;;;;;;;;;;;;0DAGjT,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,8OAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,8OAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA8jD,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO1mD,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;;sEACvB,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;;sEAC3B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;;sEAChC,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAO3H,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAO,WAAU;;sEAC1B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,8OAAC;0DACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,8OAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3H,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,MAAK;gEAAuB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAM3H,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,MAAK;gEAAoB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAMxH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EACjE,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;8EACrE,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;kEAGzE,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,8OAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,WAAU;;gDAAwB;gDAC3B,IAAI,OAAO,WAAW;gDAAG;;;;;;;sDAEnC,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;uCAEe"}}, {"offset": {"line": 1472, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1478, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout = ({ children }: MainLayoutProps) => {\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      <Header />\n      <main className=\"flex-grow\">{children}</main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB;IAC/C,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;0BACP,8OAAC;gBAAK,WAAU;0BAAa;;;;;;0BAC7B,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;uCAEe"}}, {"offset": {"line": 1518, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1524, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-white shadow-sm transition-all duration-200 hover:shadow-md',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('font-semibold leading-none tracking-tight text-navy', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-600', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,qBAAO,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACpE,GAAG,KAAK;kBAER;;;;;;AAGL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,UAAK,CAAC,UAAU,CAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;AAGL,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;AAIP,WAAW,WAAW,GAAG"}}, {"offset": {"line": 1606, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1612, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Badge.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n  {\n    variants: {\n      variant: {\n        default: 'border-transparent bg-navy text-white hover:bg-navy/80',\n        secondary: 'border-transparent bg-teal text-white hover:bg-teal/80',\n        destructive: 'border-transparent bg-red-500 text-white hover:bg-red-500/80',\n        outline: 'text-navy border-navy',\n        success: 'border-transparent bg-green-500 text-white hover:bg-green-500/80',\n        warning: 'border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80',\n        info: 'border-transparent bg-skyblue text-navy hover:bg-skyblue/80',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  }\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE"}}, {"offset": {"line": 1652, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1658, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/DateDisplay.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { formatDate } from '@/lib/utils';\n\ninterface DateDisplayProps {\n  date: string;\n  className?: string;\n}\n\nconst DateDisplay: React.FC<DateDisplayProps> = ({ date, className = '' }) => {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  // عرض placeholder أثناء التحميل لتجنب مشاكل hydration\n  if (!mounted) {\n    return <span className={className}>تحميل...</span>;\n  }\n\n  return <span className={className}>{formatDate(date)}</span>;\n};\n\nexport default DateDisplay;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUA,MAAM,cAA0C,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,sDAAsD;IACtD,IAAI,CAAC,SAAS;QACZ,qBAAO,8OAAC;YAAK,WAAW;sBAAW;;;;;;IACrC;IAEA,qBAAO,8OAAC;QAAK,WAAW;kBAAY,CAAA,GAAA,mHAAA,CAAA,aAAU,AAAD,EAAE;;;;;;AACjD;uCAEe"}}, {"offset": {"line": 1694, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1700, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/portfolio/CompletedProject.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card';\nimport { Badge } from '../ui/Badge';\nimport DateDisplay from '../ui/DateDisplay';\n\ninterface CompletedProject {\n  id: number;\n  title: string;\n  description: string;\n  category: string;\n  completedAt: string;\n  duration: number; // بالأيام\n  budget: number;\n  beforeImages: string[];\n  afterImages: string[];\n  skills: string[];\n  clientRating: number;\n  clientReview: string;\n  client: {\n    name: string;\n    location: string;\n  };\n}\n\ninterface CompletedProjectProps {\n  project: CompletedProject;\n  showClientInfo?: boolean;\n}\n\nconst CompletedProject: React.FC<CompletedProjectProps> = ({\n  project,\n  showClientInfo = true\n}) => {\n  const [activeTab, setActiveTab] = useState<'before' | 'after'>('before');\n  const [selectedImageIndex, setSelectedImageIndex] = useState(0);\n\n  const currentImages = activeTab === 'before' ? project.beforeImages : project.afterImages;\n\n  return (\n    <Card className=\"border-0 bg-white/90 backdrop-blur-sm hover:shadow-xl transition-all duration-300\">\n      <CardHeader>\n        <div className=\"flex justify-between items-start mb-2\">\n          <CardTitle className=\"text-lg leading-tight\">{project.title}</CardTitle>\n          <Badge variant=\"success\">مكتمل</Badge>\n        </div>\n        <div className=\"flex items-center text-sm text-gray-500 space-x-4 space-x-reverse\">\n          <span className=\"flex items-center\">\n            <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            اكتمل في <DateDisplay date={project.completedAt} />\n          </span>\n          <span className=\"flex items-center\">\n            <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h2a2 2 0 012 2v1m-6 0h6m-6 0l-.5 3.5M18 7l-.5 3.5M8 7l-.5 3.5m10 0L17 14H7l-.5-3.5M17 14v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v5.02\" />\n            </svg>\n            {project.duration} يوم\n          </span>\n        </div>\n      </CardHeader>\n\n      <CardContent>\n        <p className=\"text-gray-700 mb-4 line-clamp-2\">{project.description}</p>\n\n        {/* تبويب قبل وبعد */}\n        <div className=\"mb-4\">\n          <div className=\"flex border-b border-gray-200\">\n            <button\n              onClick={() => setActiveTab('before')}\n              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${\n                activeTab === 'before'\n                  ? 'border-teal text-teal'\n                  : 'border-transparent text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              قبل ({project.beforeImages.length})\n            </button>\n            <button\n              onClick={() => setActiveTab('after')}\n              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${\n                activeTab === 'after'\n                  ? 'border-teal text-teal'\n                  : 'border-transparent text-gray-500 hover:text-gray-700'\n              }`}\n            >\n              بعد ({project.afterImages.length})\n            </button>\n          </div>\n        </div>\n\n        {/* عرض الصور */}\n        {currentImages.length > 0 && (\n          <div className=\"mb-4\">\n            {/* الصورة الرئيسية */}\n            <div className=\"mb-3\">\n              <img\n                src={currentImages[selectedImageIndex]}\n                alt={`${activeTab === 'before' ? 'قبل' : 'بعد'} العمل`}\n                className=\"w-full h-64 object-cover rounded-lg border border-gray-200 hover:scale-105 transition-transform duration-300\"\n              />\n            </div>\n\n            {/* الصور المصغرة */}\n            {currentImages.length > 1 && (\n              <div className=\"grid grid-cols-4 gap-2\">\n                {currentImages.map((image, index) => (\n                  <button\n                    key={index}\n                    onClick={() => setSelectedImageIndex(index)}\n                    className={`relative overflow-hidden rounded-md border-2 transition-all ${\n                      selectedImageIndex === index\n                        ? 'border-teal'\n                        : 'border-gray-200 hover:border-gray-300'\n                    }`}\n                  >\n                    <img\n                      src={image}\n                      alt={`صورة ${index + 1}`}\n                      className=\"w-full h-full object-cover\"\n                    />\n                  </button>\n                ))}\n              </div>\n            )}\n          </div>\n        )}\n\n        {/* المهارات */}\n        <div className=\"flex flex-wrap gap-2 mb-4\">\n          {project.skills.map((skill, index) => (\n            <Badge key={index} variant=\"outline\" className=\"text-xs\">\n              {skill}\n            </Badge>\n          ))}\n        </div>\n\n        {/* معلومات المشروع */}\n        <div className=\"grid grid-cols-2 gap-4 mb-4\">\n          <div className=\"bg-green-50 p-3 rounded-lg\">\n            <div className=\"text-sm text-green-600 font-medium\">قيمة المشروع</div>\n            <div className=\"text-lg font-bold text-green-700\">\n              {project.budget.toLocaleString()} ل.س\n            </div>\n          </div>\n          <div className=\"bg-yellow-50 p-3 rounded-lg\">\n            <div className=\"text-sm text-yellow-600 font-medium\">تقييم العميل</div>\n            <div className=\"flex items-center\">\n              <div className=\"flex items-center\">\n                {[...Array(5)].map((_, i) => (\n                  <svg\n                    key={i}\n                    className={`w-4 h-4 ${\n                      i < project.clientRating ? 'text-yellow-500' : 'text-gray-300'\n                    }`}\n                    fill=\"currentColor\"\n                    viewBox=\"0 0 20 20\"\n                  >\n                    <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                  </svg>\n                ))}\n              </div>\n              <span className=\"mr-1 text-sm font-bold text-yellow-700\">\n                {project.clientRating}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* تقييم العميل */}\n        {project.clientReview && (\n          <div className=\"bg-gray-50 p-4 rounded-lg mb-4\">\n            <h4 className=\"font-semibold text-gray-700 mb-2\">تقييم العميل:</h4>\n            <p className=\"text-gray-600 text-sm italic\">\"{project.clientReview}\"</p>\n          </div>\n        )}\n\n        {/* معلومات العميل */}\n        {showClientInfo && (\n          <div className=\"border-t border-gray-200 pt-4\">\n            <div className=\"flex items-center justify-between text-sm text-gray-600\">\n              <span>العميل: {project.client.name}</span>\n              <span>{project.client.location}</span>\n            </div>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default CompletedProject;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AA+BA,MAAM,mBAAoD,CAAC,EACzD,OAAO,EACP,iBAAiB,IAAI,EACtB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,MAAM,gBAAgB,cAAc,WAAW,QAAQ,YAAY,GAAG,QAAQ,WAAW;IAEzF,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,8OAAC,gIAAA,CAAA,aAAU;;kCACT,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,YAAS;gCAAC,WAAU;0CAAyB,QAAQ,KAAK;;;;;;0CAC3D,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;0CAAU;;;;;;;;;;;;kCAE3B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;kDACG,8OAAC,uIAAA,CAAA,UAAW;wCAAC,MAAM,QAAQ,WAAW;;;;;;;;;;;;0CAEjD,8OAAC;gCAAK,WAAU;;kDACd,8OAAC;wCAAI,WAAU;wCAAe,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACtE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCAEtE,QAAQ,QAAQ;oCAAC;;;;;;;;;;;;;;;;;;;0BAKxB,8OAAC,gIAAA,CAAA,cAAW;;kCACV,8OAAC;wBAAE,WAAU;kCAAmC,QAAQ,WAAW;;;;;;kCAGnE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,2DAA2D,EACrE,cAAc,WACV,0BACA,wDACJ;;wCACH;wCACO,QAAQ,YAAY,CAAC,MAAM;wCAAC;;;;;;;8CAEpC,8OAAC;oCACC,SAAS,IAAM,aAAa;oCAC5B,WAAW,CAAC,2DAA2D,EACrE,cAAc,UACV,0BACA,wDACJ;;wCACH;wCACO,QAAQ,WAAW,CAAC,MAAM;wCAAC;;;;;;;;;;;;;;;;;;oBAMtC,cAAc,MAAM,GAAG,mBACtB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,KAAK,aAAa,CAAC,mBAAmB;oCACtC,KAAK,GAAG,cAAc,WAAW,QAAQ,MAAM,MAAM,CAAC;oCACtD,WAAU;;;;;;;;;;;4BAKb,cAAc,MAAM,GAAG,mBACtB,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;wCAEC,SAAS,IAAM,sBAAsB;wCACrC,WAAW,CAAC,4DAA4D,EACtE,uBAAuB,QACnB,gBACA,yCACJ;kDAEF,cAAA,8OAAC;4CACC,KAAK;4CACL,KAAK,CAAC,KAAK,EAAE,QAAQ,GAAG;4CACxB,WAAU;;;;;;uCAXP;;;;;;;;;;;;;;;;kCAqBjB,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1B,8OAAC,iIAAA,CAAA,QAAK;gCAAa,SAAQ;gCAAU,WAAU;0CAC5C;+BADS;;;;;;;;;;kCAOhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAqC;;;;;;kDACpD,8OAAC;wCAAI,WAAU;;4CACZ,QAAQ,MAAM,CAAC,cAAc;4CAAG;;;;;;;;;;;;;0CAGrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAsC;;;;;;kDACrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;wDAEC,WAAW,CAAC,QAAQ,EAClB,IAAI,QAAQ,YAAY,GAAG,oBAAoB,iBAC/C;wDACF,MAAK;wDACL,SAAQ;kEAER,cAAA,8OAAC;4DAAK,GAAE;;;;;;uDAPH;;;;;;;;;;0DAWX,8OAAC;gDAAK,WAAU;0DACb,QAAQ,YAAY;;;;;;;;;;;;;;;;;;;;;;;;oBAO5B,QAAQ,YAAY,kBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;;oCAA+B;oCAAE,QAAQ,YAAY;oCAAC;;;;;;;;;;;;;oBAKtE,gCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;wCAAK;wCAAS,QAAQ,MAAM,CAAC,IAAI;;;;;;;8CAClC,8OAAC;8CAAM,QAAQ,MAAM,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5C;uCAEe"}}, {"offset": {"line": 2120, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2126, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/<PERSON>zan%20Website/frontend/src/app/craftsmen/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { useParams } from 'next/navigation';\nimport MainLayout from '@/components/layout/MainLayout';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { Badge } from '@/components/ui/Badge';\nimport { Button } from '@/components/ui/Button';\nimport CompletedProject from '@/components/portfolio/CompletedProject';\n\ninterface CompletedProject {\n  id: number;\n  title: string;\n  description: string;\n  category: string;\n  completedAt: string;\n  duration: number;\n  budget: number;\n  beforeImages: string[];\n  afterImages: string[];\n  skills: string[];\n  clientRating: number;\n  clientReview: string;\n  client: {\n    name: string;\n    location: string;\n  };\n}\n\nconst CraftsmanProfilePage = () => {\n  const params = useParams();\n  const [activeTab, setActiveTab] = useState<'about' | 'portfolio' | 'reviews'>('about');\n\n  // بيانات وهمية للحرفي\n  const craftsman = {\n    id: params.id,\n    name: 'محمد النجار',\n    profession: 'نجار محترف',\n    bio: 'نجار محترف مع خبرة 10 سنوات في تصميم وتنفيذ الأثاث المنزلي والمكتبي. متخصص في المطابخ والخزائن العصرية. أعمل بأحدث التقنيات وأضمن الجودة العالية في جميع أعمالي.',\n    location: 'دمشق، المزة',\n    rating: 4.9,\n    reviewsCount: 47,\n    completedJobs: 89,\n    hourlyRate: 2500,\n    skills: ['نجارة', 'تصميم أثاث', 'تركيب', 'ترميم', 'خزائن مطابخ', 'أثاث مكتبي'],\n    avatar: 'https://randomuser.me/api/portraits/men/1.jpg',\n    isOnline: true,\n    responseTime: 'خلال ساعة',\n    joinedDate: '2022-03-15',\n    verified: true,\n    languages: ['العربية', 'الإنجليزية'],\n    workingHours: 'الأحد - الخميس: 8:00 - 18:00'\n  };\n\n  // مشاريع مكتملة مع صور قبل وبعد\n  const completedProjects: CompletedProject[] = [\n    {\n      id: 1,\n      title: 'تصميم وتنفيذ مطبخ عصري',\n      description: 'تصميم وتنفيذ مطبخ عصري بخزائن خشبية عالية الجودة مع جزيرة وسطية وإضاءة LED مدمجة.',\n      category: 'مطابخ',\n      completedAt: '2024-01-10',\n      duration: 14,\n      budget: 350000,\n      beforeImages: [\n        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop',\n        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&crop=entropy&cs=tinysrgb'\n      ],\n      afterImages: [\n        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&sat=2',\n        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop&sat=2&crop=entropy'\n      ],\n      skills: ['نجارة', 'تصميم', 'تركيب'],\n      clientRating: 5,\n      clientReview: 'عمل ممتاز وجودة عالية. محمد حرفي محترف ومتقن لعمله. أنصح بالتعامل معه.',\n      client: {\n        name: 'أحمد محمد',\n        location: 'دمشق، المزة'\n      }\n    },\n    {\n      id: 2,\n      title: 'تجديد غرفة نوم كاملة',\n      description: 'تجديد وترميم غرفة نوم كاملة مع تصميم خزائن جديدة وتركيب أرضية خشبية.',\n      category: 'غرف نوم',\n      completedAt: '2023-12-20',\n      duration: 10,\n      budget: 280000,\n      beforeImages: [\n        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop',\n        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=entropy'\n      ],\n      afterImages: [\n        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&sat=2',\n        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&sat=2&crop=entropy'\n      ],\n      skills: ['نجارة', 'ترميم', 'تركيب'],\n      clientRating: 5,\n      clientReview: 'تجاوز توقعاتي بكثير. العمل نظيف ومتقن والنتيجة رائعة.',\n      client: {\n        name: 'فاطمة أحمد',\n        location: 'دمشق، جرمانا'\n      }\n    },\n    {\n      id: 3,\n      title: 'مكتب منزلي مخصص',\n      description: 'تصميم وتنفيذ مكتب منزلي مخصص مع أرفف ووحدات تخزين متعددة.',\n      category: 'مكاتب',\n      completedAt: '2023-11-15',\n      duration: 7,\n      budget: 150000,\n      beforeImages: [\n        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=faces',\n      ],\n      afterImages: [\n        'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400&h=300&fit=crop&crop=faces&sat=2',\n      ],\n      skills: ['نجارة', 'تصميم', 'أثاث مكتبي'],\n      clientRating: 4,\n      clientReview: 'عمل جيد ولكن كان هناك تأخير بسيط في التسليم.',\n      client: {\n        name: 'محمد علي',\n        location: 'دمشق، المالكي'\n      }\n    }\n  ];\n\n  const reviews = [\n    {\n      id: 1,\n      clientName: 'أحمد محمد',\n      rating: 5,\n      comment: 'عمل ممتاز وجودة عالية. محمد حرفي محترف ومتقن لعمله.',\n      date: '2024-01-12',\n      projectTitle: 'تصميم مطبخ عصري'\n    },\n    {\n      id: 2,\n      clientName: 'فاطمة أحمد',\n      rating: 5,\n      comment: 'تجاوز توقعاتي بكثير. العمل نظيف ومتقن والنتيجة رائعة.',\n      date: '2023-12-22',\n      projectTitle: 'تجديد غرفة نوم'\n    },\n    {\n      id: 3,\n      clientName: 'محمد علي',\n      rating: 4,\n      comment: 'عمل جيد ولكن كان هناك تأخير بسيط في التسليم.',\n      date: '2023-11-18',\n      projectTitle: 'مكتب منزلي مخصص'\n    }\n  ];\n\n  return (\n    <MainLayout>\n      <div className=\"min-h-screen bg-gradient-to-br from-beige via-white to-skyblue/20 py-8\">\n        <div className=\"container mx-auto px-4\">\n          {/* معلومات الحرفي */}\n          <Card className=\"border-0 bg-white/90 backdrop-blur-sm mb-8\">\n            <CardContent className=\"p-8\">\n              <div className=\"flex flex-col md:flex-row items-start md:items-center gap-6\">\n                <div className=\"relative\">\n                  <img\n                    src={craftsman.avatar}\n                    alt={craftsman.name}\n                    className=\"w-32 h-32 rounded-full object-cover border-4 border-white shadow-lg\"\n                  />\n                  {craftsman.isOnline && (\n                    <div className=\"absolute -bottom-2 -right-2 w-8 h-8 bg-green-500 border-4 border-white rounded-full\"></div>\n                  )}\n                  {craftsman.verified && (\n                    <div className=\"absolute -top-2 -left-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\n                      <svg className=\"w-5 h-5 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </div>\n                  )}\n                </div>\n\n                <div className=\"flex-1\">\n                  <div className=\"flex flex-col md:flex-row md:items-center md:justify-between mb-4\">\n                    <div>\n                      <h1 className=\"text-3xl font-bold text-navy mb-2\">{craftsman.name}</h1>\n                      <p className=\"text-lg text-gray-600 mb-2\">{craftsman.profession}</p>\n                      <div className=\"flex items-center text-gray-500 mb-2\">\n                        <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                        </svg>\n                        {craftsman.location}\n                      </div>\n                    </div>\n\n                    <div className=\"text-left\">\n                      <div className=\"flex items-center mb-2\">\n                        <div className=\"flex items-center\">\n                          {[...Array(5)].map((_, i) => (\n                            <svg\n                              key={i}\n                              className={`w-5 h-5 ${\n                                i < Math.floor(craftsman.rating) ? 'text-yellow-500' : 'text-gray-300'\n                              }`}\n                              fill=\"currentColor\"\n                              viewBox=\"0 0 20 20\"\n                            >\n                              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                            </svg>\n                          ))}\n                        </div>\n                        <span className=\"mr-2 font-semibold\">{craftsman.rating}</span>\n                        <span className=\"text-gray-500\">({craftsman.reviewsCount} تقييم)</span>\n                      </div>\n                      <div className=\"text-2xl font-bold text-green-600\">\n                        {craftsman.hourlyRate.toLocaleString()} ل.س/ساعة\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\">\n                    <div className=\"text-center\">\n                      <div className=\"text-xl font-bold text-navy\">{craftsman.completedJobs}</div>\n                      <div className=\"text-sm text-gray-600\">مشروع مكتمل</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-xl font-bold text-navy\">{craftsman.reviewsCount}</div>\n                      <div className=\"text-sm text-gray-600\">تقييم</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-xl font-bold text-navy\">10</div>\n                      <div className=\"text-sm text-gray-600\">سنوات خبرة</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-xl font-bold text-navy\">{craftsman.responseTime}</div>\n                      <div className=\"text-sm text-gray-600\">وقت الرد</div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex flex-wrap gap-2 mb-4\">\n                    {craftsman.skills.slice(0, 6).map((skill, index) => (\n                      <Badge key={index} variant=\"outline\">\n                        {skill}\n                      </Badge>\n                    ))}\n                  </div>\n\n                  <div className=\"flex space-x-3 space-x-reverse\">\n                    <Button className=\"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90\">\n                      تواصل معي\n                    </Button>\n                    <Button variant=\"outline\">\n                      احفظ الملف الشخصي\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* التبويبات */}\n          <div className=\"mb-8\">\n            <div className=\"flex border-b border-gray-200 bg-white rounded-t-lg\">\n              {[\n                { id: 'about', label: 'نبذة عني', count: null },\n                { id: 'portfolio', label: 'معرض الأعمال', count: completedProjects.length },\n                { id: 'reviews', label: 'التقييمات', count: reviews.length }\n              ].map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id as any)}\n                  className={`px-6 py-4 text-sm font-medium border-b-2 transition-colors ${\n                    activeTab === tab.id\n                      ? 'border-teal text-teal'\n                      : 'border-transparent text-gray-500 hover:text-gray-700'\n                  }`}\n                >\n                  {tab.label}\n                  {tab.count !== null && (\n                    <span className=\"mr-2 bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs\">\n                      {tab.count}\n                    </span>\n                  )}\n                </button>\n              ))}\n            </div>\n          </div>\n\n          {/* محتوى التبويبات */}\n          {activeTab === 'about' && (\n            <Card className=\"border-0 bg-white/90 backdrop-blur-sm\">\n              <CardHeader>\n                <CardTitle>نبذة عني</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <p className=\"text-gray-700 leading-relaxed mb-6\">{craftsman.bio}</p>\n                \n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <h4 className=\"font-semibold text-navy mb-3\">معلومات إضافية</h4>\n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">تاريخ الانضمام:</span>\n                        <span>مارس 2022</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">اللغات:</span>\n                        <span>{craftsman.languages.join(', ')}</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">ساعات العمل:</span>\n                        <span>{craftsman.workingHours}</span>\n                      </div>\n                    </div>\n                  </div>\n                  \n                  <div>\n                    <h4 className=\"font-semibold text-navy mb-3\">المهارات</h4>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {craftsman.skills.map((skill, index) => (\n                        <Badge key={index} variant=\"secondary\">\n                          {skill}\n                        </Badge>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          )}\n\n          {activeTab === 'portfolio' && (\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              {completedProjects.map((project) => (\n                <CompletedProject\n                  key={project.id}\n                  project={project}\n                  showClientInfo={true}\n                />\n              ))}\n            </div>\n          )}\n\n          {activeTab === 'reviews' && (\n            <div className=\"space-y-4\">\n              {reviews.map((review) => (\n                <Card key={review.id} className=\"border-0 bg-white/90 backdrop-blur-sm\">\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex items-start justify-between mb-4\">\n                      <div>\n                        <h4 className=\"font-semibold text-navy\">{review.clientName}</h4>\n                        <p className=\"text-sm text-gray-500\">{review.projectTitle}</p>\n                      </div>\n                      <div className=\"flex items-center\">\n                        <div className=\"flex items-center ml-2\">\n                          {[...Array(5)].map((_, i) => (\n                            <svg\n                              key={i}\n                              className={`w-4 h-4 ${\n                                i < review.rating ? 'text-yellow-500' : 'text-gray-300'\n                              }`}\n                              fill=\"currentColor\"\n                              viewBox=\"0 0 20 20\"\n                            >\n                              <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                            </svg>\n                          ))}\n                        </div>\n                        <span className=\"text-sm text-gray-500\">{review.date}</span>\n                      </div>\n                    </div>\n                    <p className=\"text-gray-700 italic\">\"{review.comment}\"</p>\n                  </CardContent>\n                </Card>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </MainLayout>\n  );\n};\n\nexport default CraftsmanProfilePage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AA6BA,MAAM,uBAAuB;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqC;IAE9E,sBAAsB;IACtB,MAAM,YAAY;QAChB,IAAI,OAAO,EAAE;QACb,MAAM;QACN,YAAY;QACZ,KAAK;QACL,UAAU;QACV,QAAQ;QACR,cAAc;QACd,eAAe;QACf,YAAY;QACZ,QAAQ;YAAC;YAAS;YAAc;YAAS;YAAS;YAAe;SAAa;QAC9E,QAAQ;QACR,UAAU;QACV,cAAc;QACd,YAAY;QACZ,UAAU;QACV,WAAW;YAAC;YAAW;SAAa;QACpC,cAAc;IAChB;IAEA,gCAAgC;IAChC,MAAM,oBAAwC;QAC5C;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;YACR,cAAc;gBACZ;gBACA;aACD;YACD,aAAa;gBACX;gBACA;aACD;YACD,QAAQ;gBAAC;gBAAS;gBAAS;aAAQ;YACnC,cAAc;YACd,cAAc;YACd,QAAQ;gBACN,MAAM;gBACN,UAAU;YACZ;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;YACR,cAAc;gBACZ;gBACA;aACD;YACD,aAAa;gBACX;gBACA;aACD;YACD,QAAQ;gBAAC;gBAAS;gBAAS;aAAQ;YACnC,cAAc;YACd,cAAc;YACd,QAAQ;gBACN,MAAM;gBACN,UAAU;YACZ;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,aAAa;YACb,UAAU;YACV,QAAQ;YACR,cAAc;gBACZ;aACD;YACD,aAAa;gBACX;aACD;YACD,QAAQ;gBAAC;gBAAS;gBAAS;aAAa;YACxC,cAAc;YACd,cAAc;YACd,QAAQ;gBACN,MAAM;gBACN,UAAU;YACZ;QACF;KACD;IAED,MAAM,UAAU;QACd;YACE,IAAI;YACJ,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,MAAM;YACN,cAAc;QAChB;QACA;YACE,IAAI;YACJ,YAAY;YACZ,QAAQ;YACR,SAAS;YACT,MAAM;YACN,cAAc;QAChB;KACD;IAED,qBACE,8OAAC,0IAAA,CAAA,UAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,KAAK,UAAU,MAAM;gDACrB,KAAK,UAAU,IAAI;gDACnB,WAAU;;;;;;4CAEX,UAAU,QAAQ,kBACjB,8OAAC;gDAAI,WAAU;;;;;;4CAEhB,UAAU,QAAQ,kBACjB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAqB,MAAK;oDAAe,SAAQ;8DAC9D,cAAA,8OAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAkiB,UAAS;;;;;;;;;;;;;;;;;;;;;;kDAM9kB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAqC,UAAU,IAAI;;;;;;0EACjE,8OAAC;gEAAE,WAAU;0EAA8B,UAAU,UAAU;;;;;;0EAC/D,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;wEAAe,MAAK;wEAAO,QAAO;wEAAe,SAAQ;kFACtE,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;oEAEtE,UAAU,QAAQ;;;;;;;;;;;;;kEAIvB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ;+EAAI,MAAM;yEAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gFAEC,WAAW,CAAC,QAAQ,EAClB,IAAI,KAAK,KAAK,CAAC,UAAU,MAAM,IAAI,oBAAoB,iBACvD;gFACF,MAAK;gFACL,SAAQ;0FAER,cAAA,8OAAC;oFAAK,GAAE;;;;;;+EAPH;;;;;;;;;;kFAWX,8OAAC;wEAAK,WAAU;kFAAsB,UAAU,MAAM;;;;;;kFACtD,8OAAC;wEAAK,WAAU;;4EAAgB;4EAAE,UAAU,YAAY;4EAAC;;;;;;;;;;;;;0EAE3D,8OAAC;gEAAI,WAAU;;oEACZ,UAAU,UAAU,CAAC,cAAc;oEAAG;;;;;;;;;;;;;;;;;;;0DAK7C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA+B,UAAU,aAAa;;;;;;0EACrE,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA+B,UAAU,YAAY;;;;;;0EACpE,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA8B;;;;;;0EAC7C,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;kEAEzC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAA+B,UAAU,YAAY;;;;;;0EACpE,8OAAC;gEAAI,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;0DAI3C,8OAAC;gDAAI,WAAU;0DACZ,UAAU,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACxC,8OAAC,iIAAA,CAAA,QAAK;wDAAa,SAAQ;kEACxB;uDADS;;;;;;;;;;0DAMhB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDAAC,WAAU;kEAAyE;;;;;;kEAG3F,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAQ;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUpC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAS,OAAO;oCAAY,OAAO;gCAAK;gCAC9C;oCAAE,IAAI;oCAAa,OAAO;oCAAgB,OAAO,kBAAkB,MAAM;gCAAC;gCAC1E;oCAAE,IAAI;oCAAW,OAAO;oCAAa,OAAO,QAAQ,MAAM;gCAAC;6BAC5D,CAAC,GAAG,CAAC,CAAC,oBACL,8OAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,2DAA2D,EACrE,cAAc,IAAI,EAAE,GAChB,0BACA,wDACJ;;wCAED,IAAI,KAAK;wCACT,IAAI,KAAK,KAAK,sBACb,8OAAC;4CAAK,WAAU;sDACb,IAAI,KAAK;;;;;;;mCAXT,IAAI,EAAE;;;;;;;;;;;;;;;oBAoBlB,cAAc,yBACb,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;8CAAC;;;;;;;;;;;0CAEb,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAE,WAAU;kDAAsC,UAAU,GAAG;;;;;;kDAEhE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;kFAAK;;;;;;;;;;;;0EAER,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;kFAAM,UAAU,SAAS,CAAC,IAAI,CAAC;;;;;;;;;;;;0EAElC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,8OAAC;kFAAM,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;;;0DAKnC,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAI,WAAU;kEACZ,UAAU,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC5B,8OAAC,iIAAA,CAAA,QAAK;gEAAa,SAAQ;0EACxB;+DADS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWzB,cAAc,6BACb,8OAAC;wBAAI,WAAU;kCACZ,kBAAkB,GAAG,CAAC,CAAC,wBACtB,8OAAC,mJAAA,CAAA,UAAgB;gCAEf,SAAS;gCACT,gBAAgB;+BAFX,QAAQ,EAAE;;;;;;;;;;oBAQtB,cAAc,2BACb,8OAAC;wBAAI,WAAU;kCACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,gIAAA,CAAA,OAAI;gCAAiB,WAAU;0CAC9B,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA2B,OAAO,UAAU;;;;;;sEAC1D,8OAAC;4DAAE,WAAU;sEAAyB,OAAO,YAAY;;;;;;;;;;;;8DAE3D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEACZ;mEAAI,MAAM;6DAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;oEAEC,WAAW,CAAC,QAAQ,EAClB,IAAI,OAAO,MAAM,GAAG,oBAAoB,iBACxC;oEACF,MAAK;oEACL,SAAQ;8EAER,cAAA,8OAAC;wEAAK,GAAE;;;;;;mEAPH;;;;;;;;;;sEAWX,8OAAC;4DAAK,WAAU;sEAAyB,OAAO,IAAI;;;;;;;;;;;;;;;;;;sDAGxD,8OAAC;4CAAE,WAAU;;gDAAuB;gDAAE,OAAO,OAAO;gDAAC;;;;;;;;;;;;;+BAzB9C,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAmCpC;uCAEe"}}, {"offset": {"line": 3031, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}