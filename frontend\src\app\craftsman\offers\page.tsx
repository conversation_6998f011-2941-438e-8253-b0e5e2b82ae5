'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';

const CraftsmanOffersPage = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const offers = [
    {
      id: 1,
      projectId: 1,
      projectTitle: 'تركيب خزائن مطبخ حديثة',
      clientName: 'أحمد محمد',
      clientRating: 4.8,
      myPrice: '₺7,200',
      myDuration: '14 يوم',
      status: 'مرسل',
      submittedAt: '2024-02-15 10:30',
      description: 'أقدم خدمات تركيب خزائن المطابخ بأعلى جودة مع ضمان سنة كاملة. لدي خبرة 8 سنوات في هذا المجال.',
      materials: 'متضمنة',
      warranty: '12 شهر',
      competingOffers: 8,
      projectBudget: '₺6,000 - ₺8,000',
      location: 'دمشق - المزة'
    },
    {
      id: 2,
      projectId: 2,
      projectTitle: 'إصلاح نظام كهرباء منزلي',
      clientName: 'فاطمة أحمد',
      clientRating: 4.9,
      myPrice: '₺2,800',
      myDuration: '5 أيام',
      status: 'مقبول',
      submittedAt: '2024-02-10 14:20',
      description: 'متخصص في إصلاح الأنظمة الكهربائية مع استخدام أحدث المعدات والمواد عالية الجودة.',
      materials: 'متضمنة',
      warranty: '6 أشهر',
      competingOffers: 12,
      projectBudget: '₺2,500 - ₺3,500',
      location: 'حلب - الفرقان'
    },
    {
      id: 3,
      projectId: 3,
      projectTitle: 'دهان شقة كاملة',
      clientName: 'محمد علي',
      clientRating: 4.6,
      myPrice: '₺3,500',
      myDuration: '8 أيام',
      status: 'مرفوض',
      submittedAt: '2024-02-08 09:15',
      description: 'خدمات دهان احترافية باستخدام أفضل أنواع الدهانات المقاومة للرطوبة والعوامل الجوية.',
      materials: 'غير متضمنة',
      warranty: '3 أشهر',
      competingOffers: 15,
      projectBudget: '₺3,000 - ₺4,000',
      location: 'دمشق - كفرسوسة'
    },
    {
      id: 4,
      projectId: 4,
      projectTitle: 'تركيب نظام سباكة حديث',
      clientName: 'سارة خالد',
      clientRating: 4.7,
      myPrice: '₺5,200',
      myDuration: '10 أيام',
      status: 'قيد المراجعة',
      submittedAt: '2024-02-12 16:45',
      description: 'تركيب أنظمة سباكة حديثة مع ضمان عدم التسريب وصيانة دورية مجانية.',
      materials: 'متضمنة جزئياً',
      warranty: '18 شهر',
      competingOffers: 6,
      projectBudget: '₺4,500 - ₺6,000',
      location: 'دمشق - أبو رمانة'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'مرسل':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'قيد المراجعة':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'مقبول':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'مرفوض':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'مرسل':
        return '📤';
      case 'قيد المراجعة':
        return '👀';
      case 'مقبول':
        return '✅';
      case 'مرفوض':
        return '❌';
      default:
        return '📋';
    }
  };

  const filteredOffers = offers.filter(offer => {
    if (activeFilter === 'all') return true;
    return offer.status === activeFilter;
  });

  const stats = [
    {
      title: 'إجمالي العروض',
      value: offers.length.toString(),
      color: 'text-blue-600'
    },
    {
      title: 'عروض مقبولة',
      value: offers.filter(o => o.status === 'مقبول').length.toString(),
      color: 'text-green-600'
    },
    {
      title: 'قيد المراجعة',
      value: offers.filter(o => o.status === 'قيد المراجعة').length.toString(),
      color: 'text-yellow-600'
    },
    {
      title: 'معدل القبول',
      value: `${Math.round((offers.filter(o => o.status === 'مقبول').length / offers.length) * 100)}%`,
      color: 'text-purple-600'
    }
  ];

  return (
    <ProtectedRoute requiredRoles="craftsman">
      <DashboardLayout 
        title="عروضي المرسلة"
        subtitle="متابعة حالة العروض التي قدمتها"
      >
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className={`text-3xl ${stat.color}`}>
                    📤
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-wrap gap-2">
              {[
                { id: 'all', label: 'جميع العروض' },
                { id: 'مرسل', label: 'مرسلة' },
                { id: 'قيد المراجعة', label: 'قيد المراجعة' },
                { id: 'مقبول', label: 'مقبولة' },
                { id: 'مرفوض', label: 'مرفوضة' }
              ].map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => setActiveFilter(filter.id)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    activeFilter === filter.id
                      ? 'bg-navy text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {filter.label}
                </button>
              ))}
            </div>
          </div>

          {/* Offers List */}
          <div className="space-y-6">
            {filteredOffers.map((offer) => (
              <div key={offer.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{offer.projectTitle}</h3>
                    <p className="text-gray-600 text-sm">عرض مقدم لـ {offer.clientName}</p>
                  </div>
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <span className="text-lg">{getStatusIcon(offer.status)}</span>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(offer.status)}`}>
                      {offer.status}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  {/* My Offer Details */}
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900">تفاصيل عرضي</h4>
                    <div className="bg-blue-50 p-4 rounded-lg">
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-gray-600">سعري:</span>
                          <span className="font-bold text-blue-600 mr-2">{offer.myPrice}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">المدة:</span>
                          <span className="font-semibold text-gray-900 mr-2">{offer.myDuration}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">المواد:</span>
                          <span className="font-semibold text-gray-900 mr-2">{offer.materials}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">الضمان:</span>
                          <span className="font-semibold text-gray-900 mr-2">{offer.warranty}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Project & Competition Info */}
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900">معلومات المشروع</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <div className="w-8 h-8 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white text-sm font-bold">
                          {offer.clientName.charAt(0)}
                        </div>
                        <div>
                          <span className="font-medium text-gray-900">{offer.clientName}</span>
                          <div className="flex items-center space-x-1 space-x-reverse text-gray-600">
                            <span>⭐ {offer.clientRating}</span>
                            <span>•</span>
                            <span>📍 {offer.location}</span>
                          </div>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4 mt-3">
                        <div>
                          <span className="text-gray-600">ميزانية المشروع:</span>
                          <span className="font-semibold text-gray-900 mr-2">{offer.projectBudget}</span>
                        </div>
                        <div>
                          <span className="text-gray-600">العروض المنافسة:</span>
                          <span className="font-semibold text-orange-600 mr-2">{offer.competingOffers}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* My Description */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-2">وصف عرضي</h4>
                  <p className="text-gray-600 text-sm leading-relaxed bg-gray-50 p-3 rounded-lg">{offer.description}</p>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="text-xs text-gray-500">
                    تم الإرسال في {offer.submittedAt}
                  </div>
                  <div className="flex space-x-2 space-x-reverse">
                    <Link href={`/projects/${offer.projectId}`}>
                      <Button size="sm" variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-100">
                        عرض المشروع
                      </Button>
                    </Link>
                    <Link href={`/messages?user=${offer.clientName}`}>
                      <Button size="sm" variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                        إرسال رسالة
                      </Button>
                    </Link>
                    {offer.status === 'مرسل' && (
                      <Button size="sm" className="bg-gradient-to-r from-navy to-teal">
                        تعديل العرض
                      </Button>
                    )}
                    {offer.status === 'مقبول' && (
                      <Button size="sm" className="bg-gradient-to-r from-green-500 to-green-600">
                        بدء العمل
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredOffers.length === 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-6xl mb-4">📤</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد عروض</h3>
              <p className="text-gray-600 mb-6">لم يتم العثور على عروض بالمعايير المحددة</p>
              <Link href="/craftsman/projects">
                <Button className="bg-gradient-to-r from-navy to-teal">
                  تصفح المشاريع المتاحة
                </Button>
              </Link>
            </div>
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default CraftsmanOffersPage;
