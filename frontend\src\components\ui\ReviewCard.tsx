'use client';

import React from 'react';
import { Card, CardContent } from './Card';
import StarRating from './StarRating';

interface Review {
  id: string;
  rating: number;
  comment: string;
  reviewerName: string;
  reviewerAvatar?: string;
  projectTitle?: string;
  createdAt: string;
  helpful?: number;
}

interface ReviewCardProps {
  review: Review;
  showProject?: boolean;
  className?: string;
}

const ReviewCard: React.FC<ReviewCardProps> = ({
  review,
  showProject = false,
  className = ''
}) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Card className={`border-0 bg-white/90 backdrop-blur-md shadow-lg ${className}`}>
      <CardContent className="p-6">
        <div className="flex items-start space-x-4 space-x-reverse">
          {/* Avatar */}
          <div className="flex-shrink-0">
            {review.reviewerAvatar ? (
              <img
                src={review.reviewerAvatar}
                alt={review.reviewerName}
                className="w-12 h-12 rounded-full object-cover"
              />
            ) : (
              <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                <span className="text-gray-600 font-medium">
                  {review.reviewerName.charAt(0)}
                </span>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="flex-1">
            <div className="flex items-center justify-between mb-2">
              <div>
                <h4 className="font-semibold text-navy">{review.reviewerName}</h4>
                {showProject && review.projectTitle && (
                  <p className="text-sm text-gray-600">مشروع: {review.projectTitle}</p>
                )}
              </div>
              <div className="text-left">
                <StarRating rating={review.rating} readonly size="sm" />
                <p className="text-xs text-gray-500 mt-1">
                  {formatDate(review.createdAt)}
                </p>
              </div>
            </div>

            <p className="text-gray-700 leading-relaxed mb-4">
              {review.comment}
            </p>

            {/* Actions */}
            <div className="flex items-center space-x-4 space-x-reverse text-sm">
              <button className="flex items-center space-x-1 space-x-reverse text-gray-500 hover:text-gray-700">
                <span>👍</span>
                <span>مفيد ({review.helpful || 0})</span>
              </button>
              <button className="text-gray-500 hover:text-gray-700">
                رد
              </button>
              <button className="text-gray-500 hover:text-gray-700">
                إبلاغ
              </button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ReviewCard;
