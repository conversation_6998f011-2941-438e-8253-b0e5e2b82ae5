{"version": 3, "sources": [], "sections": [{"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/api/upload/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { writeFile, mkdir } from 'fs/promises';\nimport { join } from 'path';\nimport { existsSync } from 'fs';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const formData = await request.formData();\n    const files = formData.getAll('files') as File[];\n\n    if (!files || files.length === 0) {\n      return NextResponse.json(\n        { error: 'لم يتم اختيار أي ملفات' },\n        { status: 400 }\n      );\n    }\n\n    const uploadedFiles: string[] = [];\n    const uploadDir = join(process.cwd(), 'public', 'uploads');\n\n    // إنشاء مجلد الرفع إذا لم يكن موجوداً\n    if (!existsSync(uploadDir)) {\n      await mkdir(uploadDir, { recursive: true });\n    }\n\n    for (const file of files) {\n      // التحقق من صيغة الملف\n      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];\n      if (!allowedTypes.includes(file.type)) {\n        return NextResponse.json(\n          { error: `صيغة الملف ${file.name} غير مدعومة` },\n          { status: 400 }\n        );\n      }\n\n      // التحقق من حجم الملف (5MB)\n      if (file.size > 5 * 1024 * 1024) {\n        return NextResponse.json(\n          { error: `حجم الملف ${file.name} كبير جداً (أكثر من 5MB)` },\n          { status: 400 }\n        );\n      }\n\n      // إنشاء اسم ملف فريد\n      const timestamp = Date.now();\n      const randomString = Math.random().toString(36).substring(2, 15);\n      const extension = file.name.split('.').pop();\n      const fileName = `${timestamp}_${randomString}.${extension}`;\n\n      // تحويل الملف إلى buffer وحفظه\n      const bytes = await file.arrayBuffer();\n      const buffer = Buffer.from(bytes);\n      const filePath = join(uploadDir, fileName);\n\n      await writeFile(filePath, buffer);\n\n      // إضافة رابط الملف للقائمة\n      uploadedFiles.push(`/uploads/${fileName}`);\n    }\n\n    return NextResponse.json({\n      message: 'تم رفع الملفات بنجاح',\n      files: uploadedFiles\n    });\n\n  } catch (error) {\n    console.error('Upload error:', error);\n    return NextResponse.json(\n      { error: 'حدث خطأ أثناء رفع الملفات' },\n      { status: 500 }\n    );\n  }\n}\n\n// GET endpoint لجلب قائمة الملفات المرفوعة\nexport async function GET() {\n  try {\n    const uploadDir = join(process.cwd(), 'public', 'uploads');\n    \n    if (!existsSync(uploadDir)) {\n      return NextResponse.json({ files: [] });\n    }\n\n    // في التطبيق الحقيقي، ستجلب قائمة الملفات من قاعدة البيانات\n    // هنا نعيد قائمة فارغة للتطوير\n    return NextResponse.json({ files: [] });\n\n  } catch (error) {\n    console.error('Get files error:', error);\n    return NextResponse.json(\n      { error: 'حدث خطأ في جلب الملفات' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,WAAW,MAAM,QAAQ,QAAQ;QACvC,MAAM,QAAQ,SAAS,MAAM,CAAC;QAE9B,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,gBAA0B,EAAE;QAClC,MAAM,YAAY,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI,UAAU;QAEhD,sCAAsC;QACtC,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,YAAY;YAC1B,MAAM,CAAA,GAAA,qHAAA,CAAA,QAAK,AAAD,EAAE,WAAW;gBAAE,WAAW;YAAK;QAC3C;QAEA,KAAK,MAAM,QAAQ,MAAO;YACxB,uBAAuB;YACvB,MAAM,eAAe;gBAAC;gBAAc;gBAAa;gBAAa;aAAa;YAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC;gBAAC,GAC9C;oBAAE,QAAQ;gBAAI;YAElB;YAEA,4BAA4B;YAC5B,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;gBAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO,CAAC,UAAU,EAAE,KAAK,IAAI,CAAC,wBAAwB,CAAC;gBAAC,GAC1D;oBAAE,QAAQ;gBAAI;YAElB;YAEA,qBAAqB;YACrB,MAAM,YAAY,KAAK,GAAG;YAC1B,MAAM,eAAe,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;YAC7D,MAAM,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;YAC1C,MAAM,WAAW,GAAG,UAAU,CAAC,EAAE,aAAa,CAAC,EAAE,WAAW;YAE5D,+BAA+B;YAC/B,MAAM,QAAQ,MAAM,KAAK,WAAW;YACpC,MAAM,SAAS,OAAO,IAAI,CAAC;YAC3B,MAAM,WAAW,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,WAAW;YAEjC,MAAM,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,UAAU;YAE1B,2BAA2B;YAC3B,cAAc,IAAI,CAAC,CAAC,SAAS,EAAE,UAAU;QAC3C;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,OAAO;QACT;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,YAAY,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI,UAAU;QAEhD,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,YAAY;YAC1B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO,EAAE;YAAC;QACvC;QAEA,4DAA4D;QAC5D,+BAA+B;QAC/B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO,EAAE;QAAC;IAEvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAyB,GAClC;YAAE,QAAQ;QAAI;IAElB;AACF"}}, {"offset": {"line": 181, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}