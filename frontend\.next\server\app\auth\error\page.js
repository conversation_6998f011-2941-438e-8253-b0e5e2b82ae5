(()=>{var e={};e.id=8004,e.ids=[8004],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},42905:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var s=r(70260),a=r(28203),n=r(25155),l=r.n(n),i=r(67292),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d=["",{children:["auth",{children:["error",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,81211)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\auth\\error\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\auth\\error\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/error/page",pathname:"/auth/error",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},23519:(e,t,r)=>{Promise.resolve().then(r.bind(r,81211))},60471:(e,t,r)=>{Promise.resolve().then(r.bind(r,62567))},62567:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(45512);r(58009);var a=r(28531),n=r.n(a),l=r(79334),i=r(4209),o=r(18542),d=r(87272);let c=()=>{let e=(0,l.useSearchParams)().get("error"),t=(e=>{switch(e){case"Configuration":return{title:"خطأ في الإعدادات",message:"يوجد خطأ في إعدادات المصادقة. يرجى المحاولة مرة أخرى لاحقاً.",icon:"⚙️"};case"AccessDenied":return{title:"تم رفض الوصول",message:"ليس لديك الصلاحية للوصول إلى هذا المورد.",icon:"\uD83D\uDEAB"};case"Verification":return{title:"فشل التحقق",message:"فشل في التحقق من هويتك. يرجى المحاولة مرة أخرى.",icon:"❌"};default:return{title:"خطأ في المصادقة",message:"حدث خطأ أثناء عملية تسجيل الدخول. يرجى المحاولة مرة أخرى.",icon:"\uD83D\uDD10"}}})(e);return(0,s.jsx)(i.default,{children:(0,s.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white relative overflow-hidden",children:[(0,s.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,s.jsx)("div",{className:"absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl"}),(0,s.jsx)("div",{className:"absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl"}),(0,s.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl"})]}),(0,s.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,s.jsx)("div",{className:"w-full h-full",style:{backgroundImage:"radial-gradient(circle, #567C8D 1px, transparent 1px)",backgroundSize:"50px 50px"}})}),(0,s.jsx)("div",{className:"container mx-auto px-4 py-16 relative z-10",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[(0,s.jsx)("div",{className:"text-8xl mb-8",children:t.icon}),(0,s.jsx)(o.Zp,{className:"border-0 bg-white/90 backdrop-blur-md shadow-lg",children:(0,s.jsxs)(o.Wu,{className:"p-12",children:[(0,s.jsx)("h1",{className:"text-4xl font-bold text-navy mb-6",children:t.title}),(0,s.jsx)("p",{className:"text-xl text-gray-600 mb-8 leading-relaxed",children:t.message}),e&&(0,s.jsxs)("div",{className:"bg-gray-50 rounded-xl p-6 mb-8",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-navy mb-2",children:"تفاصيل الخطأ:"}),(0,s.jsx)("p",{className:"text-gray-700 font-mono text-sm",children:e})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse",children:[(0,s.jsx)(n(),{href:"/login",children:(0,s.jsx)(d.Button,{size:"lg",className:"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90",children:"المحاولة مرة أخرى"})}),(0,s.jsx)(n(),{href:"/",children:(0,s.jsx)(d.Button,{size:"lg",variant:"outline",className:"border-navy text-navy hover:bg-navy hover:text-white",children:"العودة للصفحة الرئيسية"})})]}),(0,s.jsxs)("div",{className:"pt-6 border-t border-gray-200",children:[(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"إذا استمر الخطأ، يمكنك:"}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse",children:[(0,s.jsx)(n(),{href:"/contact",children:(0,s.jsx)(d.Button,{variant:"outline",className:"border-gray-300 text-gray-700 hover:bg-gray-50",children:"التواصل مع الدعم"})}),(0,s.jsx)(n(),{href:"/faq",children:(0,s.jsx)(d.Button,{variant:"outline",className:"border-gray-300 text-gray-700 hover:bg-gray-50",children:"الأسئلة الشائعة"})})]})]})]})]})}),(0,s.jsxs)("div",{className:"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsx)(o.Zp,{className:"border-0 bg-white/80 backdrop-blur-md shadow-md hover:shadow-lg transition-all duration-300",children:(0,s.jsxs)(o.Wu,{className:"p-6 text-center",children:[(0,s.jsx)("div",{className:"text-3xl mb-4",children:"\uD83D\uDCDE"}),(0,s.jsx)("h3",{className:"font-semibold text-navy mb-2",children:"اتصل بنا"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"تواصل مع فريق الدعم للحصول على المساعدة"}),(0,s.jsx)(n(),{href:"/contact",className:"text-teal hover:text-teal/80 text-sm font-medium",children:"اتصل الآن"})]})}),(0,s.jsx)(o.Zp,{className:"border-0 bg-white/80 backdrop-blur-md shadow-md hover:shadow-lg transition-all duration-300",children:(0,s.jsxs)(o.Wu,{className:"p-6 text-center",children:[(0,s.jsx)("div",{className:"text-3xl mb-4",children:"❓"}),(0,s.jsx)("h3",{className:"font-semibold text-navy mb-2",children:"الأسئلة الشائعة"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"ابحث عن إجابات للأسئلة الشائعة"}),(0,s.jsx)(n(),{href:"/faq",className:"text-teal hover:text-teal/80 text-sm font-medium",children:"تصفح الأسئلة"})]})}),(0,s.jsx)(o.Zp,{className:"border-0 bg-white/80 backdrop-blur-md shadow-md hover:shadow-lg transition-all duration-300",children:(0,s.jsxs)(o.Wu,{className:"p-6 text-center",children:[(0,s.jsx)("div",{className:"text-3xl mb-4",children:"\uD83D\uDD04"}),(0,s.jsx)("h3",{className:"font-semibold text-navy mb-2",children:"إعادة المحاولة"}),(0,s.jsx)("p",{className:"text-gray-600 text-sm",children:"جرب تسجيل الدخول مرة أخرى"}),(0,s.jsx)(n(),{href:"/login",className:"text-teal hover:text-teal/80 text-sm font-medium",children:"تسجيل الدخول"})]})})]})]})})]})})}},81211:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\g\\\\Duzan Website\\\\frontend\\\\src\\\\app\\\\auth\\\\error\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\auth\\error\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,3351,7180,1057,4209],()=>r(42905));module.exports=s})();