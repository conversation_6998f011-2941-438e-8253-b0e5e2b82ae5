'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import supabase from '@/lib/supabase';

export default function ProjectBidsPage() {
  const params = useParams();
  const projectId = params.id as string;

  const [project, setProject] = useState<any>(null);
  const [bids, setBids] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [accepting, setAccepting] = useState<string | null>(null);

  useEffect(() => {
    loadProjectAndBids();
  }, [projectId]);

  const loadProjectAndBids = async () => {
    try {
      // تحميل المشروع
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .select('*')
        .eq('id', projectId)
        .single();

      if (projectError) throw projectError;
      setProject(projectData);

      // تحميل العروض
      const { data: bidsData, error: bidsError } = await supabase
        .from('bids')
        .select(`
          *,
          craftsman:users!bids_craftsman_id_fkey(name, email, phone)
        `)
        .eq('project_id', projectId)
        .order('created_at', { ascending: false });

      if (bidsError) throw bidsError;
      setBids(bidsData || []);

    } catch (error) {
      console.error('Error loading project and bids:', error);
      alert('خطأ في تحميل البيانات');
    } finally {
      setLoading(false);
    }
  };

  const acceptBid = async (bidId: string) => {
    setAccepting(bidId);
    
    try {
      // قبول العرض
      const { error: bidError } = await supabase
        .from('bids')
        .update({ status: 'ACCEPTED' })
        .eq('id', bidId);

      if (bidError) throw bidError;

      // رفض باقي العروض
      const { error: rejectError } = await supabase
        .from('bids')
        .update({ status: 'REJECTED' })
        .eq('project_id', projectId)
        .neq('id', bidId);

      if (rejectError) throw rejectError;

      // تحديث حالة المشروع
      const acceptedBid = bids.find(bid => bid.id === bidId);
      const { error: projectError } = await supabase
        .from('projects')
        .update({ 
          status: 'IN_PROGRESS',
          craftsman_id: acceptedBid.craftsman_id
        })
        .eq('id', projectId);

      if (projectError) throw projectError;

      alert('تم قبول العرض بنجاح!');
      loadProjectAndBids(); // إعادة تحميل البيانات

    } catch (error) {
      console.error('Error accepting bid:', error);
      alert('حدث خطأ أثناء قبول العرض');
    } finally {
      setAccepting(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING': return 'bg-yellow-100 text-yellow-800';
      case 'ACCEPTED': return 'bg-green-100 text-green-800';
      case 'REJECTED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'PENDING': return 'في الانتظار';
      case 'ACCEPTED': return 'مقبول';
      case 'REJECTED': return 'مرفوض';
      default: return status;
    }
  };

  if (loading) {
    return (
      <DashboardLayout title="العروض المقدمة">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout 
      title="العروض المقدمة"
      subtitle={`العروض المقدمة للمشروع: ${project?.title}`}
    >
      <div className="space-y-6">
        {/* معلومات المشروع */}
        <Card>
          <CardHeader>
            <CardTitle>تفاصيل المشروع</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <span className="text-gray-500">العنوان:</span>
                <p className="font-medium">{project?.title}</p>
              </div>
              <div>
                <span className="text-gray-500">التخصص:</span>
                <p className="font-medium">{project?.category}</p>
              </div>
              <div>
                <span className="text-gray-500">الميزانية:</span>
                <p className="font-medium">
                  {project?.budget_min?.toLocaleString()} - {project?.budget_max?.toLocaleString()} ل.س
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* العروض */}
        <div className="space-y-4">
          <h2 className="text-2xl font-bold text-navy">العروض المقدمة ({bids.length})</h2>
          
          {bids.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <div className="text-6xl mb-4">📭</div>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">لا توجد عروض بعد</h3>
                <p className="text-gray-500">لم يتم تقديم أي عروض لهذا المشروع حتى الآن</p>
              </CardContent>
            </Card>
          ) : (
            bids.map((bid) => (
              <Card key={bid.id} className="border-l-4 border-l-teal">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-4 space-x-reverse mb-4">
                        <div className="w-12 h-12 bg-gradient-to-r from-teal to-navy rounded-full flex items-center justify-center text-white font-bold">
                          {bid.craftsman?.name?.charAt(0) || 'ح'}
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-navy">{bid.craftsman?.name}</h3>
                          <p className="text-gray-600">{bid.craftsman?.email}</p>
                        </div>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(bid.status)}`}>
                          {getStatusText(bid.status)}
                        </span>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                        <div>
                          <span className="text-gray-500 text-sm">المبلغ:</span>
                          <p className="font-bold text-lg text-green-600">{bid.amount?.toLocaleString()} ل.س</p>
                        </div>
                        <div>
                          <span className="text-gray-500 text-sm">المدة المتوقعة:</span>
                          <p className="font-medium">{bid.estimated_duration}</p>
                        </div>
                        <div>
                          <span className="text-gray-500 text-sm">تاريخ البدء:</span>
                          <p className="font-medium">
                            {bid.start_date ? new Date(bid.start_date).toLocaleDateString('ar-SA') : 'غير محدد'}
                          </p>
                        </div>
                        <div>
                          <span className="text-gray-500 text-sm">الضمان:</span>
                          <p className="font-medium">{bid.warranty_period || 'غير محدد'}</p>
                        </div>
                      </div>

                      <div className="mb-4">
                        <span className="text-gray-500 text-sm">وصف العرض:</span>
                        <p className="mt-1 text-gray-700">{bid.description}</p>
                      </div>

                      <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
                        <div className="flex items-center">
                          {bid.materials_included ? '✅' : '❌'}
                          <span className="mr-1">المواد مشمولة</span>
                        </div>
                        <div>
                          📅 تم التقديم: {new Date(bid.created_at).toLocaleDateString('ar-SA')}
                        </div>
                      </div>
                    </div>

                    {bid.status === 'PENDING' && project?.status === 'OPEN' && (
                      <div className="mr-4">
                        <Button
                          onClick={() => acceptBid(bid.id)}
                          disabled={accepting === bid.id}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          {accepting === bid.id ? 'جاري القبول...' : 'قبول العرض'}
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
