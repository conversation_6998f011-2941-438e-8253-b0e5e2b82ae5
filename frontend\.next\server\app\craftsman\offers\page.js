(()=>{var e={};e.id=9766,e.ids=[9766],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},30213:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var r=s(70260),a=s(28203),i=s(25155),n=s.n(i),l=s(67292),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(t,d);let c=["",{children:["craftsman",{children:["offers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,36201)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\craftsman\\offers\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\craftsman\\offers\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/craftsman/offers/page",pathname:"/craftsman/offers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},26253:(e,t,s)=>{Promise.resolve().then(s.bind(s,36201))},73517:(e,t,s)=>{Promise.resolve().then(s.bind(s,27117))},27117:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(45512),a=s(58009),i=s(28531),n=s.n(i),l=s(60297),d=s(42847),c=s(87272);let o=()=>{let[e,t]=(0,a.useState)("all"),s=[{id:1,projectId:1,projectTitle:"تركيب خزائن مطبخ حديثة",clientName:"أحمد محمد",clientRating:4.8,myPrice:"₺7,200",myDuration:"14 يوم",status:"مرسل",submittedAt:"2024-02-15 10:30",description:"أقدم خدمات تركيب خزائن المطابخ بأعلى جودة مع ضمان سنة كاملة. لدي خبرة 8 سنوات في هذا المجال.",materials:"متضمنة",warranty:"12 شهر",competingOffers:8,projectBudget:"₺6,000 - ₺8,000",location:"دمشق - المزة"},{id:2,projectId:2,projectTitle:"إصلاح نظام كهرباء منزلي",clientName:"فاطمة أحمد",clientRating:4.9,myPrice:"₺2,800",myDuration:"5 أيام",status:"مقبول",submittedAt:"2024-02-10 14:20",description:"متخصص في إصلاح الأنظمة الكهربائية مع استخدام أحدث المعدات والمواد عالية الجودة.",materials:"متضمنة",warranty:"6 أشهر",competingOffers:12,projectBudget:"₺2,500 - ₺3,500",location:"حلب - الفرقان"},{id:3,projectId:3,projectTitle:"دهان شقة كاملة",clientName:"محمد علي",clientRating:4.6,myPrice:"₺3,500",myDuration:"8 أيام",status:"مرفوض",submittedAt:"2024-02-08 09:15",description:"خدمات دهان احترافية باستخدام أفضل أنواع الدهانات المقاومة للرطوبة والعوامل الجوية.",materials:"غير متضمنة",warranty:"3 أشهر",competingOffers:15,projectBudget:"₺3,000 - ₺4,000",location:"دمشق - كفرسوسة"},{id:4,projectId:4,projectTitle:"تركيب نظام سباكة حديث",clientName:"سارة خالد",clientRating:4.7,myPrice:"₺5,200",myDuration:"10 أيام",status:"قيد المراجعة",submittedAt:"2024-02-12 16:45",description:"تركيب أنظمة سباكة حديثة مع ضمان عدم التسريب وصيانة دورية مجانية.",materials:"متضمنة جزئياً",warranty:"18 شهر",competingOffers:6,projectBudget:"₺4,500 - ₺6,000",location:"دمشق - أبو رمانة"}],i=e=>{switch(e){case"مرسل":return"bg-blue-100 text-blue-800 border-blue-200";case"قيد المراجعة":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"مقبول":return"bg-green-100 text-green-800 border-green-200";case"مرفوض":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},o=e=>{switch(e){case"مرسل":return"\uD83D\uDCE4";case"قيد المراجعة":return"\uD83D\uDC40";case"مقبول":return"✅";case"مرفوض":return"❌";default:return"\uD83D\uDCCB"}},m=s.filter(t=>"all"===e||t.status===e),x=[{title:"إجمالي العروض",value:s.length.toString(),color:"text-blue-600"},{title:"عروض مقبولة",value:s.filter(e=>"مقبول"===e.status).length.toString(),color:"text-green-600"},{title:"قيد المراجعة",value:s.filter(e=>"قيد المراجعة"===e.status).length.toString(),color:"text-yellow-600"},{title:"معدل القبول",value:`${Math.round(s.filter(e=>"مقبول"===e.status).length/s.length*100)}%`,color:"text-purple-600"}];return(0,r.jsx)(l.A,{requiredRoles:"craftsman",children:(0,r.jsx)(d.A,{title:"عروضي المرسلة",subtitle:"متابعة حالة العروض التي قدمتها",children:(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:x.map((e,t)=>(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-1",children:e.title}),(0,r.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:e.value})]}),(0,r.jsx)("div",{className:`text-3xl ${e.color}`,children:"\uD83D\uDCE4"})]})},t))}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:[{id:"all",label:"جميع العروض"},{id:"مرسل",label:"مرسلة"},{id:"قيد المراجعة",label:"قيد المراجعة"},{id:"مقبول",label:"مقبولة"},{id:"مرفوض",label:"مرفوضة"}].map(s=>(0,r.jsx)("button",{onClick:()=>t(s.id),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${e===s.id?"bg-navy text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:s.label},s.id))})}),(0,r.jsx)("div",{className:"space-y-6",children:m.map(e=>(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-1",children:e.projectTitle}),(0,r.jsxs)("p",{className:"text-gray-600 text-sm",children:["عرض مقدم لـ ",e.clientName]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[(0,r.jsx)("span",{className:"text-lg",children:o(e.status)}),(0,r.jsx)("span",{className:`px-3 py-1 rounded-full text-xs font-medium border ${i(e.status)}`,children:e.status})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6",children:[(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"تفاصيل عرضي"}),(0,r.jsx)("div",{className:"bg-blue-50 p-4 rounded-lg",children:(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"سعري:"}),(0,r.jsx)("span",{className:"font-bold text-blue-600 mr-2",children:e.myPrice})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"المدة:"}),(0,r.jsx)("span",{className:"font-semibold text-gray-900 mr-2",children:e.myDuration})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"المواد:"}),(0,r.jsx)("span",{className:"font-semibold text-gray-900 mr-2",children:e.materials})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"الضمان:"}),(0,r.jsx)("span",{className:"font-semibold text-gray-900 mr-2",children:e.warranty})]})]})})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900",children:"معلومات المشروع"}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white text-sm font-bold",children:e.clientName.charAt(0)}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium text-gray-900",children:e.clientName}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 space-x-reverse text-gray-600",children:[(0,r.jsxs)("span",{children:["⭐ ",e.clientRating]}),(0,r.jsx)("span",{children:"•"}),(0,r.jsxs)("span",{children:["\uD83D\uDCCD ",e.location]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 mt-3",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"ميزانية المشروع:"}),(0,r.jsx)("span",{className:"font-semibold text-gray-900 mr-2",children:e.projectBudget})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"العروض المنافسة:"}),(0,r.jsx)("span",{className:"font-semibold text-orange-600 mr-2",children:e.competingOffers})]})]})]})]})]}),(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"وصف عرضي"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed bg-gray-50 p-3 rounded-lg",children:e.description})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between pt-4 border-t border-gray-200",children:[(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["تم الإرسال في ",e.submittedAt]}),(0,r.jsxs)("div",{className:"flex space-x-2 space-x-reverse",children:[(0,r.jsx)(n(),{href:`/projects/${e.projectId}`,children:(0,r.jsx)(c.Button,{size:"sm",variant:"outline",className:"border-gray-300 text-gray-700 hover:bg-gray-100",children:"عرض المشروع"})}),(0,r.jsx)(n(),{href:`/messages?user=${e.clientName}`,children:(0,r.jsx)(c.Button,{size:"sm",variant:"outline",className:"border-navy text-navy hover:bg-navy hover:text-white",children:"إرسال رسالة"})}),"مرسل"===e.status&&(0,r.jsx)(c.Button,{size:"sm",className:"bg-gradient-to-r from-navy to-teal",children:"تعديل العرض"}),"مقبول"===e.status&&(0,r.jsx)(c.Button,{size:"sm",className:"bg-gradient-to-r from-green-500 to-green-600",children:"بدء العمل"})]})]})]},e.id))}),0===m.length&&(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCE4"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"لا توجد عروض"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"لم يتم العثور على عروض بالمعايير المحددة"}),(0,r.jsx)(n(),{href:"/craftsman/projects",children:(0,r.jsx)(c.Button,{className:"bg-gradient-to-r from-navy to-teal",children:"تصفح المشاريع المتاحة"})})]})]})})})}},36201:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\g\\\\Duzan Website\\\\frontend\\\\src\\\\app\\\\craftsman\\\\offers\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\craftsman\\offers\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,3351,7180,1057,1799],()=>s(30213));module.exports=r})();