'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { useAuth } from '@/hooks/useAuth';

const NotificationsPage = () => {
  const { isClient, isCraftsman, isAdmin } = useAuth();
  const [activeFilter, setActiveFilter] = useState('all');

  const notifications = [
    {
      id: 1,
      type: 'offer_received',
      title: 'عرض جديد على مشروعك',
      message: 'تم استلام عرض جديد من أحمد النجار على مشروع تجديد المطبخ',
      timestamp: '2024-02-15 10:30',
      read: false,
      actionUrl: '/client/offers',
      icon: '📥',
      color: 'blue'
    },
    {
      id: 2,
      type: 'offer_accepted',
      title: 'تم قبول عرضك',
      message: 'تم قبول عرضك على مشروع إصلاح نظام السباكة من قبل فاطمة أحمد',
      timestamp: '2024-02-14 14:20',
      read: true,
      actionUrl: '/craftsman/offers',
      icon: '✅',
      color: 'green'
    },
    {
      id: 3,
      type: 'project_completed',
      title: 'تم إكمال مشروع',
      message: 'تم إكمال مشروع دهان الشقة الكاملة بنجاح. يرجى تقييم الحرفي',
      timestamp: '2024-02-13 16:45',
      read: false,
      actionUrl: '/client/projects',
      icon: '🎉',
      color: 'purple'
    },
    {
      id: 4,
      type: 'payment_received',
      title: 'تم استلام دفعة',
      message: 'تم استلام دفعة بقيمة ₺2,200 من مشروع إصلاح السباكة',
      timestamp: '2024-02-12 09:15',
      read: true,
      actionUrl: '/craftsman/earnings',
      icon: '💰',
      color: 'yellow'
    },
    {
      id: 5,
      type: 'message_received',
      title: 'رسالة جديدة',
      message: 'رسالة جديدة من سارة الكهربائية حول مشروع تركيب الإضاءة',
      timestamp: '2024-02-11 11:30',
      read: false,
      actionUrl: '/messages',
      icon: '💬',
      color: 'indigo'
    },
    {
      id: 6,
      type: 'deadline_reminder',
      title: 'تذكير بالموعد النهائي',
      message: 'يقترب الموعد النهائي لمشروع تجديد المطبخ (باقي 3 أيام)',
      timestamp: '2024-02-10 08:00',
      read: true,
      actionUrl: '/client/projects',
      icon: '⏰',
      color: 'red'
    },
    {
      id: 7,
      type: 'new_project',
      title: 'مشروع جديد متاح',
      message: 'مشروع جديد في تخصصك: تركيب خزائن مطبخ في دمشق',
      timestamp: '2024-02-09 15:20',
      read: true,
      actionUrl: '/craftsman/projects',
      icon: '🔨',
      color: 'teal'
    },
    {
      id: 8,
      type: 'system_update',
      title: 'تحديث النظام',
      message: 'تم تحديث النظام بميزات جديدة. اطلع على التحديثات الجديدة',
      timestamp: '2024-02-08 12:00',
      read: true,
      actionUrl: '/dashboard',
      icon: '🔄',
      color: 'gray'
    }
  ];

  const getNotificationColor = (color: string) => {
    const colors = {
      blue: 'bg-blue-100 text-blue-800 border-blue-200',
      green: 'bg-green-100 text-green-800 border-green-200',
      purple: 'bg-purple-100 text-purple-800 border-purple-200',
      yellow: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      indigo: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      red: 'bg-red-100 text-red-800 border-red-200',
      teal: 'bg-teal-100 text-teal-800 border-teal-200',
      gray: 'bg-gray-100 text-gray-800 border-gray-200'
    };
    return colors[color as keyof typeof colors] || colors.gray;
  };

  const filteredNotifications = notifications.filter(notification => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'unread') return !notification.read;
    if (activeFilter === 'read') return notification.read;
    return notification.type === activeFilter;
  });

  const markAsRead = (id: number) => {
    // هنا سيتم تحديث حالة الإشعار في الخادم
    console.log('Marking notification as read:', id);
  };

  const markAllAsRead = () => {
    // هنا سيتم تحديث جميع الإشعارات كمقروءة
    console.log('Marking all notifications as read');
  };

  const deleteNotification = (id: number) => {
    // هنا سيتم حذف الإشعار
    console.log('Deleting notification:', id);
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'منذ قليل';
    if (diffInHours < 24) return `منذ ${diffInHours} ساعة`;
    if (diffInHours < 48) return 'أمس';
    return date.toLocaleDateString('ar-SA');
  };

  const stats = [
    {
      title: 'إجمالي الإشعارات',
      value: notifications.length.toString(),
      color: 'text-blue-600',
      icon: '🔔'
    },
    {
      title: 'غير مقروءة',
      value: notifications.filter(n => !n.read).length.toString(),
      color: 'text-red-600',
      icon: '🔴'
    },
    {
      title: 'اليوم',
      value: notifications.filter(n => {
        const today = new Date().toDateString();
        const notifDate = new Date(n.timestamp).toDateString();
        return today === notifDate;
      }).length.toString(),
      color: 'text-green-600',
      icon: '📅'
    },
    {
      title: 'هذا الأسبوع',
      value: notifications.filter(n => {
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        return new Date(n.timestamp) > weekAgo;
      }).length.toString(),
      color: 'text-purple-600',
      icon: '📊'
    }
  ];

  return (
    <ProtectedRoute>
      <DashboardLayout 
        title="الإشعارات"
        subtitle="متابعة جميع التحديثات والأنشطة"
        actions={
          <div className="flex space-x-2 space-x-reverse">
            <Button 
              onClick={markAllAsRead}
              variant="outline" 
              className="border-navy text-navy hover:bg-navy hover:text-white"
            >
              تحديد الكل كمقروء
            </Button>
            <Button className="bg-gradient-to-r from-navy to-teal">
              إعدادات الإشعارات
            </Button>
          </div>
        }
      >
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className={`text-3xl ${stat.color}`}>
                    {stat.icon}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-wrap gap-2">
              {[
                { id: 'all', label: 'جميع الإشعارات' },
                { id: 'unread', label: 'غير مقروءة' },
                { id: 'read', label: 'مقروءة' },
                { id: 'offer_received', label: 'العروض' },
                { id: 'message_received', label: 'الرسائل' },
                { id: 'project_completed', label: 'المشاريع' },
                { id: 'payment_received', label: 'المدفوعات' }
              ].map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => setActiveFilter(filter.id)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    activeFilter === filter.id
                      ? 'bg-navy text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {filter.label}
                </button>
              ))}
            </div>
          </div>

          {/* Notifications List */}
          <div className="space-y-4">
            {filteredNotifications.map((notification) => (
              <div
                key={notification.id}
                className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 transition-all duration-200 hover:shadow-md ${
                  !notification.read ? 'border-l-4 border-l-blue-500' : ''
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 space-x-reverse flex-1">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center text-2xl ${
                      !notification.read ? 'bg-blue-50' : 'bg-gray-50'
                    }`}>
                      {notification.icon}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className={`font-semibold ${
                          !notification.read ? 'text-gray-900' : 'text-gray-700'
                        }`}>
                          {notification.title}
                        </h3>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getNotificationColor(notification.color)}`}>
                            {notification.type.replace('_', ' ')}
                          </span>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                      </div>
                      
                      <p className="text-gray-600 mb-3">{notification.message}</p>
                      
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-500">{formatTime(notification.timestamp)}</span>
                        
                        <div className="flex space-x-2 space-x-reverse">
                          {notification.actionUrl && (
                            <Button 
                              size="sm" 
                              variant="outline" 
                              className="border-navy text-navy hover:bg-navy hover:text-white"
                            >
                              عرض
                            </Button>
                          )}
                          {!notification.read && (
                            <Button 
                              size="sm" 
                              variant="outline" 
                              className="border-gray-300 text-gray-700 hover:bg-gray-100"
                              onClick={() => markAsRead(notification.id)}
                            >
                              تحديد كمقروء
                            </Button>
                          )}
                          <Button 
                            size="sm" 
                            variant="outline" 
                            className="border-red-300 text-red-700 hover:bg-red-100"
                            onClick={() => deleteNotification(notification.id)}
                          >
                            حذف
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredNotifications.length === 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-6xl mb-4">🔔</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد إشعارات</h3>
              <p className="text-gray-600 mb-6">لم يتم العثور على إشعارات بالمعايير المحددة</p>
              <Button 
                onClick={() => setActiveFilter('all')}
                className="bg-gradient-to-r from-navy to-teal"
              >
                عرض جميع الإشعارات
              </Button>
            </div>
          )}

          {/* Notification Settings */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">إعدادات الإشعارات</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">إشعارات البريد الإلكتروني</h4>
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-navy focus:ring-navy" defaultChecked />
                    <span className="mr-3 text-gray-700">العروض الجديدة</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-navy focus:ring-navy" defaultChecked />
                    <span className="mr-3 text-gray-700">الرسائل الجديدة</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-navy focus:ring-navy" />
                    <span className="mr-3 text-gray-700">تحديثات المشاريع</span>
                  </label>
                </div>
              </div>

              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">إشعارات الهاتف</h4>
                <div className="space-y-3">
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-navy focus:ring-navy" defaultChecked />
                    <span className="mr-3 text-gray-700">الإشعارات الفورية</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-navy focus:ring-navy" />
                    <span className="mr-3 text-gray-700">التذكيرات اليومية</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-navy focus:ring-navy" defaultChecked />
                    <span className="mr-3 text-gray-700">تحديثات المدفوعات</span>
                  </label>
                </div>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-gray-200">
              <Button className="bg-gradient-to-r from-navy to-teal">
                حفظ الإعدادات
              </Button>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default NotificationsPage;
