(()=>{var e={};e.id=698,e.ids=[698],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},39977:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var t=s(70260),a=s(28203),n=s(25155),l=s.n(n),i=s(67292),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);s.d(r,o);let d=["",{children:["projects",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,80837)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\projects\\create\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\projects\\create\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/projects/create/page",pathname:"/projects/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},62605:(e,r,s)=>{Promise.resolve().then(s.bind(s,80837))},86573:(e,r,s)=>{Promise.resolve().then(s.bind(s,593))},593:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>d});var t=s(45512),a=s(58009),n=s(79334),l=s(60297),i=s(42847),o=s(87272);let d=()=>{let e=(0,n.useRouter)(),[r,s]=(0,a.useState)(1),[d,c]=(0,a.useState)({title:"",description:"",category:"",budget:"",deadline:"",location:"",priority:"متوسطة",materials:"غير محدد",workType:"",requirements:"",images:[]}),m=e=>{let{name:r,value:s}=e.target;c(e=>({...e,[r]:s}))},x=e=>{c(r=>({...r,images:r.images.filter((r,s)=>s!==e)}))},u=[{number:1,title:"المعلومات الأساسية",description:"عنوان ووصف المشروع"},{number:2,title:"التفاصيل",description:"الميزانية والموعد والمتطلبات"},{number:3,title:"الصور والمراجعة",description:"إضافة صور ومراجعة البيانات"}];return(0,t.jsx)(l.A,{requiredRoles:"client",children:(0,t.jsx)(i.A,{title:"إنشاء مشروع جديد",subtitle:"أضف مشروعك واحصل على عروض من الحرفيين المؤهلين",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)("div",{className:"flex items-center justify-between",children:u.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("div",{className:`flex items-center justify-center w-10 h-10 rounded-full border-2 ${r>=e.number?"bg-navy border-navy text-white":"border-gray-300 text-gray-500"}`,children:r>e.number?"✓":e.number}),(0,t.jsxs)("div",{className:"mr-4",children:[(0,t.jsx)("p",{className:`text-sm font-medium ${r>=e.number?"text-navy":"text-gray-500"}`,children:e.title}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:e.description})]}),s<u.length-1&&(0,t.jsx)("div",{className:`w-16 h-0.5 mx-4 ${r>e.number?"bg-navy":"bg-gray-300"}`})]},e.number))})}),(0,t.jsxs)("form",{onSubmit:r=>{r.preventDefault(),console.log("Project data:",d),e.push("/client/projects")},className:"space-y-8",children:[1===r&&(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-8",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"المعلومات الأساسية"}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"عنوان المشروع *"}),(0,t.jsx)("input",{type:"text",name:"title",value:d.title,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy",placeholder:"مثال: تجديد المطبخ الرئيسي",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"وصف المشروع *"}),(0,t.jsx)("textarea",{name:"description",value:d.description,onChange:m,rows:4,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy",placeholder:"اكتب وصفاً مفصلاً عن المشروع المطلوب...",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"التخصص المطلوب *"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[{id:"نجارة",label:"نجارة",icon:"\uD83E\uDE9A"},{id:"كهرباء",label:"كهرباء",icon:"⚡"},{id:"سباكة",label:"سباكة",icon:"\uD83D\uDD27"},{id:"دهان",label:"دهان",icon:"\uD83C\uDFA8"},{id:"بناء",label:"بناء وإنشاءات",icon:"\uD83C\uDFD7️"},{id:"تكييف",label:"تكييف وتبريد",icon:"❄️"},{id:"تنظيف",label:"تنظيف",icon:"\uD83E\uDDF9"},{id:"أخرى",label:"أخرى",icon:"\uD83D\uDD28"}].map(e=>(0,t.jsxs)("button",{type:"button",onClick:()=>c(r=>({...r,category:e.id})),className:`p-4 border-2 rounded-xl text-center transition-colors duration-200 ${d.category===e.id?"border-navy bg-navy/5 text-navy":"border-gray-200 hover:border-gray-300"}`,children:[(0,t.jsx)("div",{className:"text-2xl mb-2",children:e.icon}),(0,t.jsx)("div",{className:"text-sm font-medium",children:e.label})]},e.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"الموقع *"}),(0,t.jsx)("input",{type:"text",name:"location",value:d.location,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy",placeholder:"مثال: دمشق - المزة",required:!0})]})]})]}),2===r&&(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-8",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"تفاصيل المشروع"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"الميزانية المتوقعة *"}),(0,t.jsx)("input",{type:"text",name:"budget",value:d.budget,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy",placeholder:"مثال: ₺5,000 - ₺8,000",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"الموعد النهائي *"}),(0,t.jsx)("input",{type:"date",name:"deadline",value:d.deadline,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"الأولوية"}),(0,t.jsxs)("select",{name:"priority",value:d.priority,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy",children:[(0,t.jsx)("option",{value:"منخفضة",children:"منخفضة"}),(0,t.jsx)("option",{value:"متوسطة",children:"متوسطة"}),(0,t.jsx)("option",{value:"عالية",children:"عالية"}),(0,t.jsx)("option",{value:"عاجلة",children:"عاجلة"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"المواد"}),(0,t.jsxs)("select",{name:"materials",value:d.materials,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy",children:[(0,t.jsx)("option",{value:"غير محدد",children:"غير محدد"}),(0,t.jsx)("option",{value:"متوفرة",children:"متوفرة من العميل"}),(0,t.jsx)("option",{value:"مطلوبة من الحرفي",children:"مطلوبة من الحرفي"}),(0,t.jsx)("option",{value:"متوفرة جزئياً",children:"متوفرة جزئياً"})]})]})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"نوع العمل"}),(0,t.jsx)("input",{type:"text",name:"workType",value:d.workType,onChange:m,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy",placeholder:"مثال: تركيب، إصلاح، تجديد، صيانة"})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"متطلبات إضافية"}),(0,t.jsx)("textarea",{name:"requirements",value:d.requirements,onChange:m,rows:3,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy",placeholder:"أي متطلبات أو ملاحظات إضافية..."})]})]}),3===r&&(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-8",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"إضافة صور (اختياري)"}),(0,t.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-xl p-8 text-center",children:[(0,t.jsx)("input",{type:"file",multiple:!0,accept:"image/*",onChange:e=>{if(e.target.files){let r=Array.from(e.target.files);c(e=>({...e,images:[...e.images,...r]}))}},className:"hidden",id:"image-upload"}),(0,t.jsxs)("label",{htmlFor:"image-upload",className:"cursor-pointer",children:[(0,t.jsx)("div",{className:"text-4xl text-gray-400 mb-4",children:"\uD83D\uDCF7"}),(0,t.jsx)("p",{className:"text-gray-600 mb-2",children:"اضغط لإضافة صور أو اسحب الصور هنا"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"PNG, JPG, GIF حتى 10MB"})]})]}),d.images.length>0&&(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsxs)("h4",{className:"font-medium text-gray-900 mb-4",children:["الصور المرفوعة (",d.images.length,")"]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:d.images.map((e,r)=>(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("img",{src:URL.createObjectURL(e),alt:`صورة ${r+1}`,className:"w-full h-24 object-cover rounded-lg"}),(0,t.jsx)("button",{type:"button",onClick:()=>x(r),className:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600",children:"\xd7"})]},r))})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-8",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-6",children:"مراجعة المشروع"}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"العنوان:"}),(0,t.jsx)("span",{className:"font-medium text-gray-900 mr-2",children:d.title})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"التخصص:"}),(0,t.jsx)("span",{className:"font-medium text-gray-900 mr-2",children:d.category})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"الميزانية:"}),(0,t.jsx)("span",{className:"font-medium text-gray-900 mr-2",children:d.budget})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"الموعد النهائي:"}),(0,t.jsx)("span",{className:"font-medium text-gray-900 mr-2",children:d.deadline})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"الموقع:"}),(0,t.jsx)("span",{className:"font-medium text-gray-900 mr-2",children:d.location})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"الأولوية:"}),(0,t.jsx)("span",{className:"font-medium text-gray-900 mr-2",children:d.priority})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"الوصف:"}),(0,t.jsx)("p",{className:"text-gray-900 mt-1",children:d.description})]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("div",{children:r>1&&(0,t.jsx)(o.Button,{type:"button",onClick:()=>{r>1&&s(r-1)},variant:"outline",className:"border-gray-300 text-gray-700",children:"السابق"})}),(0,t.jsxs)("div",{className:"flex space-x-4 space-x-reverse",children:[(0,t.jsx)(o.Button,{type:"button",variant:"outline",className:"border-gray-300 text-gray-700",onClick:()=>e.push("/client/projects"),children:"إلغاء"}),r<3?(0,t.jsx)(o.Button,{type:"button",onClick:()=>{r<3&&s(r+1)},className:"bg-gradient-to-r from-navy to-teal",disabled:1===r&&(!d.title||!d.description||!d.category||!d.location)||2===r&&(!d.budget||!d.deadline),children:"التالي"}):(0,t.jsx)(o.Button,{type:"submit",className:"bg-gradient-to-r from-navy to-teal",children:"نشر المشروع"})]})]})]})]})})})}},80837:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>t});let t=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\g\\\\Duzan Website\\\\frontend\\\\src\\\\app\\\\projects\\\\create\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\projects\\create\\page.tsx","default")}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[638,3351,7180,1057,1799],()=>s(39977));module.exports=t})();