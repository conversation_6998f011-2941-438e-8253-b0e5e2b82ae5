{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/hooks/useSimpleAuth.ts"], "sourcesContent": ["import { useState, useEffect } from 'react';\n\ninterface User {\n  id: string;\n  name: string;\n  email: string;\n  role: 'CLIENT' | 'CRAFTSMAN' | 'ADMIN';\n}\n\nexport function useSimpleAuth() {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  useEffect(() => {\n    if (!mounted) return;\n\n    // تحميل المستخدم من localStorage فقط بعد mount\n    try {\n      const savedUser = localStorage.getItem('user');\n      if (savedUser) {\n        const userData = JSON.parse(savedUser);\n        setUser(userData);\n      }\n    } catch (error) {\n      console.error('Error parsing user data:', error);\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem('user');\n      }\n    }\n    setLoading(false);\n  }, [mounted]);\n\n  const logout = () => {\n    setUser(null);\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('user');\n      window.location.href = '/';\n    }\n  };\n\n  const login = (userData: User) => {\n    setUser(userData);\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('user', JSON.stringify(userData));\n    }\n  };\n\n  // Helper functions\n  const isClient = user?.role === 'CLIENT';\n  const isCraftsman = user?.role === 'CRAFTSMAN';\n  const isAdmin = user?.role === 'ADMIN';\n\n  return {\n    user,\n    loading,\n    logout,\n    login,\n    isAuthenticated: !!user,\n    isClient,\n    isCraftsman,\n    isAdmin\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;;;AASO,SAAS;;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,WAAW;QACb;kCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,SAAS;YAEd,+CAA+C;YAC/C,IAAI;gBACF,MAAM,YAAY,aAAa,OAAO,CAAC;gBACvC,IAAI,WAAW;oBACb,MAAM,WAAW,KAAK,KAAK,CAAC;oBAC5B,QAAQ;gBACV;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC1C,wCAAmC;oBACjC,aAAa,UAAU,CAAC;gBAC1B;YACF;YACA,WAAW;QACb;kCAAG;QAAC;KAAQ;IAEZ,MAAM,SAAS;QACb,QAAQ;QACR,wCAAmC;YACjC,aAAa,UAAU,CAAC;YACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,QAAQ,CAAC;QACb,QAAQ;QACR,wCAAmC;YACjC,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC;QAC9C;IACF;IAEA,mBAAmB;IACnB,MAAM,WAAW,MAAM,SAAS;IAChC,MAAM,cAAc,MAAM,SAAS;IACnC,MAAM,UAAU,MAAM,SAAS;IAE/B,OAAO;QACL;QACA;QACA;QACA;QACA,iBAAiB,CAAC,CAAC;QACnB;QACA;QACA;IACF;AACF;GA1DgB"}}, {"offset": {"line": 76, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useState } from 'react';\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  role: 'client' | 'craftsman';\n  phone?: string;\n}\n\nexport const useAuth = () => {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const login = async (credentials: LoginCredentials) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await signIn('credentials', {\n        email: credentials.email,\n        password: credentials.password,\n        redirect: false,\n      });\n\n      if (result?.error) {\n        setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');\n        return false;\n      }\n\n      if (result?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        setError(errorData.message || 'حدث خطأ أثناء إنشاء الحساب');\n        return false;\n      }\n\n      // تسجيل الدخول تلقائياً بعد التسجيل\n      const loginResult = await signIn('credentials', {\n        email: data.email,\n        password: data.password,\n        redirect: false,\n      });\n\n      if (loginResult?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء إنشاء الحساب');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    setIsLoading(true);\n    try {\n      await signOut({ redirect: false });\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithGoogle = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('google', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Google');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithFacebook = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('facebook', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Facebook');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const isAuthenticated = status === 'authenticated';\n  const isLoading_session = status === 'loading';\n  const user = session?.user;\n\n  // التحقق من الأدوار مع fallback\n  const isClient = user?.role === 'client';\n  const isCraftsman = user?.role === 'craftsman';\n  const isAdmin = user?.role === 'admin';\n\n  // التحقق من الصلاحيات\n  const hasRole = (role: string | string[]) => {\n    if (!user) return false;\n    if (Array.isArray(role)) {\n      return role.includes(user.role);\n    }\n    return user.role === role;\n  };\n\n  const canAccess = (requiredRoles: string | string[]) => {\n    return isAuthenticated && hasRole(requiredRoles);\n  };\n\n  return {\n    // Session data\n    user,\n    session,\n    isAuthenticated,\n    isLoading: isLoading || isLoading_session,\n    error,\n\n    // Role checks\n    isClient,\n    isCraftsman,\n    isAdmin,\n    hasRole,\n    canAccess,\n\n    // Auth methods\n    login,\n    register,\n    logout,\n    loginWithGoogle,\n    loginWithFacebook,\n\n    // Utility\n    setError,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;AAJA;;;;AAmBO,MAAM,UAAU;;IACrB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,QAAQ,OAAO;QACnB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;gBAC9B,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;gBACT,OAAO;YACT;YAEA,IAAI,QAAQ,IAAI;gBACd,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,OAAO,IAAI;gBAC9B,OAAO;YACT;YAEA,oCAAoC;YACpC,MAAM,cAAc,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBAC9C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;YACZ;YAEA,IAAI,aAAa,IAAI;gBACnB,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;gBAAE,UAAU;YAAM;YAChC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBAAE,aAAa;YAAa;QACrD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,YAAY;gBAAE,aAAa;YAAa;QACvD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,WAAW;IACnC,MAAM,oBAAoB,WAAW;IACrC,MAAM,OAAO,SAAS;IAEtB,gCAAgC;IAChC,MAAM,WAAW,MAAM,SAAS;IAChC,MAAM,cAAc,MAAM,SAAS;IACnC,MAAM,UAAU,MAAM,SAAS;IAE/B,sBAAsB;IACtB,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI;QAChC;QACA,OAAO,KAAK,IAAI,KAAK;IACvB;IAEA,MAAM,YAAY,CAAC;QACjB,OAAO,mBAAmB,QAAQ;IACpC;IAEA,OAAO;QACL,eAAe;QACf;QACA;QACA;QACA,WAAW,aAAa;QACxB;QAEA,cAAc;QACd;QACA;QACA;QACA;QACA;QAEA,eAAe;QACf;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;IACF;AACF;GA7Ja;;QACuB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS"}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { useAuth } from '@/hooks/useAuth';\n\ninterface SidebarProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst Sidebar = ({ isOpen, onClose }: SidebarProps) => {\n  const pathname = usePathname();\n  const { user, isClient, isCraftsman, isAdmin, logout } = useAuth();\n\n  const getNavigationItems = () => {\n    const commonItems = [\n      {\n        name: 'لوحة التحكم',\n        href: '/dashboard',\n        icon: (\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\" />\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z\" />\n          </svg>\n        )\n      },\n      {\n        name: 'الملف الشخصي',\n        href: '/dashboard/profile',\n        icon: (\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n          </svg>\n        )\n      },\n      {\n        name: 'الرسائل',\n        href: '/messages',\n        icon: (\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n          </svg>\n        ),\n        badge: '3'\n      },\n      {\n        name: 'الإشعارات',\n        href: '/notifications',\n        icon: (\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84l3.12 3.12M4.03 8.86l3.12 3.12\" />\n          </svg>\n        ),\n        badge: '5'\n      }\n    ];\n\n    if (isClient) {\n      return [\n        ...commonItems,\n        {\n          name: 'مشاريعي',\n          href: '/client/projects',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n            </svg>\n          )\n        },\n        {\n          name: 'العروض المستلمة',\n          href: '/client/offers',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4\" />\n            </svg>\n          ),\n          badge: '12'\n        },\n        {\n          name: 'المدفوعات',\n          href: '/client/payments',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z\" />\n            </svg>\n          )\n        }\n      ];\n    }\n\n    if (isCraftsman) {\n      return [\n        ...commonItems,\n        {\n          name: 'المشاريع المتاحة',\n          href: '/craftsman/projects',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          )\n        },\n        {\n          name: 'عروضي',\n          href: '/craftsman/offers',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n            </svg>\n          )\n        },\n        {\n          name: 'مشاريعي الحالية',\n          href: '/craftsman/current-projects',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n            </svg>\n          )\n        },\n        {\n          name: 'معرض أعمالي',\n          href: '/craftsman/portfolio',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2-2v16a2 2 0 002 2z\" />\n            </svg>\n          )\n        },\n        {\n          name: 'الأرباح',\n          href: '/craftsman/earnings',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n            </svg>\n          )\n        }\n      ];\n    }\n\n    if (isAdmin) {\n      return [\n        ...commonItems,\n        {\n          name: 'إدارة المستخدمين',\n          href: '/admin/users',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n            </svg>\n          )\n        },\n        {\n          name: 'إدارة المشاريع',\n          href: '/admin/projects',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2\" />\n            </svg>\n          )\n        },\n        {\n          name: 'التقارير',\n          href: '/admin/reports',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n            </svg>\n          )\n        },\n        {\n          name: 'الإعدادات',\n          href: '/admin/settings',\n          icon: (\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n            </svg>\n          )\n        }\n      ];\n    }\n\n    return commonItems;\n  };\n\n  const navigationItems = getNavigationItems();\n\n  const isActive = (href: string) => {\n    if (href === '/dashboard') {\n      return pathname === '/dashboard';\n    }\n    return pathname.startsWith(href);\n  };\n\n  return (\n    <>\n      {/* Mobile Overlay */}\n      {isOpen && (\n        <div\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n          onClick={onClose}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={`\n        fixed top-0 right-0 h-full w-64 bg-white border-l border-gray-200 shadow-xl z-50 transform transition-transform duration-300 ease-in-out\n        ${isOpen ? 'translate-x-0' : 'translate-x-full'}\n        lg:translate-x-0 lg:static lg:z-auto lg:flex lg:flex-col\n      `}>\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <Link href=\"/\" className=\"flex items-center space-x-3 space-x-reverse\">\n            <div className=\"w-8 h-8 bg-gradient-to-br from-navy to-teal rounded-lg flex items-center justify-center text-white text-lg font-bold\">\n              🔨\n            </div>\n            <span className=\"text-xl font-bold text-navy\">دوزان</span>\n          </Link>\n          <button\n            onClick={onClose}\n            className=\"lg:hidden p-2 rounded-lg hover:bg-gray-100\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* User Info */}\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex items-center space-x-3 space-x-reverse\">\n            <div className=\"w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white font-bold\">\n              {user?.name?.charAt(0) || 'U'}\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <p className=\"text-sm font-medium text-gray-900 truncate\">\n                {user?.name || 'مستخدم'}\n              </p>\n              <p className=\"text-xs text-gray-500 truncate\">\n                {user?.email}\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"flex-1 p-4 space-y-1\">\n          {navigationItems.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              className={`\n                flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-colors duration-200\n                ${isActive(item.href)\n                  ? 'bg-navy text-white'\n                  : 'text-gray-700 hover:bg-gray-100'\n                }\n              `}\n              onClick={() => {\n                if (window.innerWidth < 1024) {\n                  onClose();\n                }\n              }}\n            >\n              <div className=\"flex items-center space-x-3 space-x-reverse\">\n                {item.icon}\n                <span>{item.name}</span>\n              </div>\n              {item.badge && (\n                <span className={`\n                  px-2 py-1 text-xs rounded-full\n                  ${isActive(item.href)\n                    ? 'bg-white text-navy'\n                    : 'bg-red-100 text-red-600'\n                  }\n                `}>\n                  {item.badge}\n                </span>\n              )}\n            </Link>\n          ))}\n        </nav>\n\n        {/* Footer */}\n        <div className=\"p-4 border-t border-gray-200\">\n          <button\n            onClick={logout}\n            className=\"flex items-center space-x-3 space-x-reverse w-full px-3 py-2 text-sm font-medium text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200\"\n          >\n            <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n            </svg>\n            <span>تسجيل الخروج</span>\n          </button>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;;;AALA;;;;AAYA,MAAM,UAAU,CAAC,EAAE,MAAM,EAAE,OAAO,EAAgB;;IAChD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAE/D,MAAM,qBAAqB;QACzB,MAAM,cAAc;YAClB;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;;sCACjE,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;sCACrE,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;YAG3E;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;gBAGzE,OAAO;YACT;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,oBACE,6LAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAO,QAAO;oBAAe,SAAQ;8BACjE,cAAA,6LAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;gBAGzE,OAAO;YACT;SACD;QAED,IAAI,UAAU;YACZ,OAAO;mBACF;gBACH;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGzE,OAAO;gBACT;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;aACD;QACH;QAEA,IAAI,aAAa;YACf,OAAO;mBACF;gBACH;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;aACD;QACH;QAEA,IAAI,SAAS;YACX,OAAO;mBACF;gBACH;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;gBAG3E;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,oBACE,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;;0CACjE,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;0CACrE,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;gBAG3E;aACD;QACH;QAEA,OAAO;IACT;IAEA,MAAM,kBAAkB;IAExB,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,qBACE;;YAEG,wBACC,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAKb,6LAAC;gBAAI,WAAW,CAAC;;QAEf,EAAE,SAAS,kBAAkB,mBAAmB;;MAElD,CAAC;;kCAEC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDAAuH;;;;;;kDAGtI,6LAAC;wCAAK,WAAU;kDAA8B;;;;;;;;;;;;0CAEhD,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3E,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,MAAM,MAAM,OAAO,MAAM;;;;;;8CAE5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDACV,MAAM,QAAQ;;;;;;sDAEjB,6LAAC;4CAAE,WAAU;sDACV,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAOf,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAC;;gBAEV,EAAE,SAAS,KAAK,IAAI,IAChB,uBACA,kCACH;cACH,CAAC;gCACD,SAAS;oCACP,IAAI,OAAO,UAAU,GAAG,MAAM;wCAC5B;oCACF;gCACF;;kDAEA,6LAAC;wCAAI,WAAU;;4CACZ,KAAK,IAAI;0DACV,6LAAC;0DAAM,KAAK,IAAI;;;;;;;;;;;;oCAEjB,KAAK,KAAK,kBACT,6LAAC;wCAAK,WAAW,CAAC;;kBAEhB,EAAE,SAAS,KAAK,IAAI,IAChB,uBACA,0BACH;gBACH,CAAC;kDACE,KAAK,KAAK;;;;;;;+BA3BV,KAAK,IAAI;;;;;;;;;;kCAmCpB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;8CAEvE,6LAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;AAMlB;GAnSM;;QACa,qIAAA,CAAA,cAAW;QAC6B,0HAAA,CAAA,UAAO;;;KAF5D;uCAqSS"}}, {"offset": {"line": 954, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 960, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Sidebar from './Sidebar';\nimport { useAuth } from '@/hooks/useAuth';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n  subtitle?: string;\n  actions?: React.ReactNode;\n}\n\nconst DashboardLayout = ({ children, title, subtitle, actions }: DashboardLayoutProps) => {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { user } = useAuth();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex\">\n      {/* Sidebar */}\n      <Sidebar isOpen={sidebarOpen} onClose={() => setSidebarOpen(false)} />\n\n      {/* Main Content */}\n      <div className=\"flex-1 \">\n        {/* Top Bar */}\n        <div className=\"bg-white border-b border-gray-200 px-4 py-4 sm:px-6 lg:px-8\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              {/* Mobile menu button */}\n              <button\n                onClick={() => setSidebarOpen(true)}\n                className=\"lg:hidden p-2 rounded-lg text-gray-600 hover:bg-gray-100\"\n              >\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n                </svg>\n              </button>\n\n              {/* Page Title */}\n              <div>\n                {title && (\n                  <h1 className=\"text-2xl font-bold text-gray-900\">{title}</h1>\n                )}\n                {subtitle && (\n                  <p className=\"text-sm text-gray-600 mt-1\">{subtitle}</p>\n                )}\n              </div>\n            </div>\n\n            {/* Top Bar Actions */}\n            <div className=\"flex items-center space-x-4 space-x-reverse\">\n              {actions}\n\n              {/* Search */}\n              <div className=\"hidden md:block\">\n                <div className=\"relative\">\n                  <input\n                    type=\"text\"\n                    placeholder=\"البحث...\"\n                    className=\"w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy\"\n                  />\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                    </svg>\n                  </div>\n                </div>\n              </div>\n\n              {/* Notifications */}\n              <button className=\"relative p-2 text-gray-600 hover:bg-gray-100 rounded-lg\">\n                <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-5 5v-5zM10.07 2.82l3.12 3.12M7.05 5.84l3.12 3.12M4.03 8.86l3.12 3.12\" />\n                </svg>\n                <span className=\"absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full\"></span>\n              </button>\n\n              {/* User Menu */}\n              <div className=\"relative\">\n                <button className=\"flex items-center space-x-2 space-x-reverse p-2 rounded-lg hover:bg-gray-100\">\n                  <div className=\"w-8 h-8 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white text-sm font-bold\">\n                    {user?.name?.charAt(0) || 'U'}\n                  </div>\n                  <div className=\"hidden md:block text-right\">\n                    <p className=\"text-sm font-medium text-gray-900\">{user?.name}</p>\n                    <p className=\"text-xs text-gray-500\">{user?.role}</p>\n                  </div>\n                  <svg className=\"w-4 h-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                  </svg>\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Page Content */}\n        <main className=\"p-2 min-h-screen\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n};\n\nexport default DashboardLayout;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAaA,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAwB;;IACnF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAEvB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,0IAAA,CAAA,UAAO;gBAAC,QAAQ;gBAAa,SAAS,IAAM,eAAe;;;;;;0BAG5D,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CACC,SAAS,IAAM,eAAe;4CAC9B,WAAU;sDAEV,cAAA,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;sDAKzE,6LAAC;;gDACE,uBACC,6LAAC;oDAAG,WAAU;8DAAoC;;;;;;gDAEnD,0BACC,6LAAC;oDAAE,WAAU;8DAA8B;;;;;;;;;;;;;;;;;;8CAMjD,6LAAC;oCAAI,WAAU;;wCACZ;sDAGD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,aAAY;wDACZ,WAAU;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAwB,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEAC/E,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAO7E,6LAAC;4CAAO,WAAU;;8DAChB,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACjE,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIlB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAI,WAAU;kEACZ,MAAM,MAAM,OAAO,MAAM;;;;;;kEAE5B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAE,WAAU;0EAAqC,MAAM;;;;;;0EACxD,6LAAC;gEAAE,WAAU;0EAAyB,MAAM;;;;;;;;;;;;kEAE9C,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASjF,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GA1FM;;QAEa,0HAAA,CAAA,UAAO;;;KAFpB;uCA4FS"}}, {"offset": {"line": 1268, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1274, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// دالة تنسيق التاريخ بشكل ثابت لتجنب مشاكل Hydration\nexport function formatDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n\n    // التحقق من صحة التاريخ\n    if (isNaN(date.getTime())) {\n      return 'تاريخ غير صحيح';\n    }\n\n    // تنسيق ثابت للتاريخ\n    const year = date.getFullYear();\n    const month = date.getMonth() + 1;\n    const day = date.getDate();\n\n    // أسماء الشهور بالعربية\n    const monthNames = [\n      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',\n      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'\n    ];\n\n    return `${day} ${monthNames[month - 1]} ${year}`;\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق التاريخ النسبي (منذ كم يوم)\nexport function formatRelativeDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInMs = now.getTime() - date.getTime();\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\n\n    if (diffInDays === 0) {\n      return 'اليوم';\n    } else if (diffInDays === 1) {\n      return 'أمس';\n    } else if (diffInDays < 7) {\n      return `منذ ${diffInDays} أيام`;\n    } else if (diffInDays < 30) {\n      const weeks = Math.floor(diffInDays / 7);\n      return `منذ ${weeks} ${weeks === 1 ? 'أسبوع' : 'أسابيع'}`;\n    } else if (diffInDays < 365) {\n      const months = Math.floor(diffInDays / 30);\n      return `منذ ${months} ${months === 1 ? 'شهر' : 'أشهر'}`;\n    } else {\n      const years = Math.floor(diffInDays / 365);\n      return `منذ ${years} ${years === 1 ? 'سنة' : 'سنوات'}`;\n    }\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق الأرقام بالفواصل\nexport function formatNumber(num: number): string {\n  return num.toLocaleString('ar-SA');\n}\n\n// دالة للتحقق من صحة البريد الإلكتروني\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// دالة للتحقق من صحة رقم الهاتف السوري\nexport function isValidSyrianPhone(phone: string): boolean {\n  const phoneRegex = /^(\\+963|0)?[0-9]{9,10}$/;\n  return phoneRegex.test(phone);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,UAAkB;IAC3C,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QAEtB,wBAAwB;QACxB,IAAI,MAAM,KAAK,OAAO,KAAK;YACzB,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,KAAK,QAAQ,KAAK;QAChC,MAAM,MAAM,KAAK,OAAO;QAExB,wBAAwB;QACxB,MAAM,aAAa;YACjB;YAAS;YAAU;YAAQ;YAAS;YAAQ;YAC5C;YAAS;YAAS;YAAU;YAAU;YAAU;SACjD;QAED,OAAO,GAAG,IAAI,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,MAAM;IAClD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,mBAAmB,UAAkB;IACnD,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;QAC7C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG;YACpB,OAAO;QACT,OAAO,IAAI,eAAe,GAAG;YAC3B,OAAO;QACT,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO,CAAC,IAAI,EAAE,WAAW,KAAK,CAAC;QACjC,OAAO,IAAI,aAAa,IAAI;YAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,UAAU,UAAU;QAC3D,OAAO,IAAI,aAAa,KAAK;YAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,aAAa;YACvC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,WAAW,IAAI,QAAQ,QAAQ;QACzD,OAAO;YACL,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,QAAQ,SAAS;QACxD;IACF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,cAAc,CAAC;AAC5B;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,mBAAmB,KAAa;IAC9C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB"}}, {"offset": {"line": 1362, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1368, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-white shadow-sm transition-all duration-200 hover:shadow-md',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('font-semibold leading-none tracking-tight text-navy', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-600', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACpE,GAAG,KAAK;kBAER;;;;;;;AAGL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;;AAGL,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG"}}, {"offset": {"line": 1472, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1478, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',\n  {\n    variants: {\n      variant: {\n        default: 'bg-navy text-white hover:bg-navy/90',\n        destructive: 'bg-red-500 text-white hover:bg-red-500/90',\n        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',\n        secondary: 'bg-teal text-white hover:bg-teal/90',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'underline-offset-4 hover:underline text-primary',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 rounded-md',\n        lg: 'h-11 px-8 rounded-md',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,wQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 1536, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1542, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = 'https://lyjelanmcbzymgauwamc.supabase.co'\nconst supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\nconst supabase = createClient(supabaseUrl, supabaseKey)\n\nexport default supabase\n"], "names": [], "mappings": ";;;AAGoB;AAHpB;;AAEA,MAAM,cAAc;AACpB,MAAM;AACN,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;uCAE5B"}}, {"offset": {"line": 1555, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1561, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport { useSimpleAuth } from '@/hooks/useSimpleAuth';\r\nimport DashboardLayout from '@/components/layout/DashboardLayout';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\r\nimport { Button } from '@/components/ui/Button';\r\nimport supabase from '@/lib/supabase';\r\n\r\nexport default function DashboardPage() {\r\n  const { user, loading, isAuthenticated } = useSimpleAuth();\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const [activeTab, setActiveTab] = useState('overview');\r\n  const [projects, setProjects] = useState<any[]>([]);\r\n  const [bids, setBids] = useState<any[]>([]);\r\n  const [dataLoading, setDataLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    if (!loading && !isAuthenticated) {\r\n      router.push('/auth/login');\r\n      return;\r\n    }\r\n\r\n    // تحديد التبويب النشط من URL\r\n    const tab = searchParams.get('tab') || 'overview';\r\n    setActiveTab(tab);\r\n\r\n    if (isAuthenticated) {\r\n      loadDashboardData();\r\n    }\r\n  }, [loading, isAuthenticated, router, searchParams]);\r\n\r\n  const loadDashboardData = async () => {\r\n    try {\r\n      console.log('Loading dashboard data for user:', user);\r\n\r\n      // اختبار الاتصال بقاعدة البيانات أولاً\r\n      const { data: testData, error: testError } = await supabase\r\n        .from('users')\r\n        .select('id')\r\n        .limit(1);\r\n\r\n      if (testError) {\r\n        console.error('Database connection error:', testError);\r\n        console.log('Using mock data instead...');\r\n        // استخدام بيانات تجريبية محلية\r\n        setProjects(getMockProjects(user?.role));\r\n        setBids(getMockBids(user?.role));\r\n        setDataLoading(false);\r\n        return;\r\n      }\r\n\r\n      if (user?.role === 'CLIENT') {\r\n        // تحميل مشاريع العميل\r\n        const clientId = user.id || '550e8400-e29b-41d4-a716-446655440001';\r\n\r\n        const { data: projectsData, error: projectsError } = await supabase\r\n          .from('projects')\r\n          .select('*')\r\n          .eq('client_id', clientId)\r\n          .order('created_at', { ascending: false });\r\n\r\n        if (projectsError) {\r\n          console.error('Error loading client projects:', projectsError);\r\n          setProjects(getMockProjects('CLIENT'));\r\n        } else {\r\n          setProjects(projectsData || []);\r\n        }\r\n\r\n      } else if (user?.role === 'CRAFTSMAN') {\r\n        // تحميل عروض الحرفي\r\n        const craftsmanId = user.id || '550e8400-e29b-41d4-a716-446655440011';\r\n\r\n        try {\r\n          const { data: bidsData, error: bidsError } = await supabase\r\n            .from('bids')\r\n            .select(`\r\n              id,\r\n              amount,\r\n              description,\r\n              estimated_duration,\r\n              materials_included,\r\n              warranty_period,\r\n              start_date,\r\n              status,\r\n              created_at,\r\n              project_id,\r\n              projects!inner(title, location, category, status)\r\n            `)\r\n            .eq('craftsman_id', craftsmanId)\r\n            .order('created_at', { ascending: false });\r\n\r\n          if (bidsError) {\r\n            console.error('Error loading craftsman bids:', bidsError);\r\n            setBids(getMockBids('CRAFTSMAN'));\r\n          } else {\r\n            // تحويل البيانات للتوافق مع الواجهة\r\n            const formattedBids = (bidsData || []).map(bid => ({\r\n              ...bid,\r\n              project: bid.projects\r\n            }));\r\n            setBids(formattedBids);\r\n          }\r\n        } catch (bidError) {\r\n          console.error('Exception loading bids:', bidError);\r\n          setBids(getMockBids('CRAFTSMAN'));\r\n        }\r\n\r\n        // تحميل المشاريع المتاحة\r\n        const { data: availableProjects, error: availableError } = await supabase\r\n          .from('projects')\r\n          .select('*')\r\n          .eq('status', 'OPEN')\r\n          .order('created_at', { ascending: false })\r\n          .limit(10);\r\n\r\n        if (availableError) {\r\n          console.error('Error loading available projects:', availableError);\r\n          setProjects(getMockProjects('CRAFTSMAN'));\r\n        } else {\r\n          setProjects(availableProjects || []);\r\n        }\r\n      } else {\r\n        // للمدير أو أي دور آخر، تحميل إحصائيات عامة\r\n        const { data: allProjects, error: projectsError } = await supabase\r\n          .from('projects')\r\n          .select('*')\r\n          .order('created_at', { ascending: false })\r\n          .limit(10);\r\n\r\n        if (projectsError) {\r\n          setProjects(getMockProjects('ADMIN'));\r\n        } else {\r\n          setProjects(allProjects || []);\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading dashboard data:', error);\r\n      // في حالة الخطأ، استخدام بيانات تجريبية\r\n      setProjects(getMockProjects(user?.role));\r\n      setBids(getMockBids(user?.role));\r\n    } finally {\r\n      setDataLoading(false);\r\n    }\r\n  };\r\n\r\n  // بيانات تجريبية للاختبار\r\n  const getMockProjects = (role?: string) => {\r\n    if (role === 'CLIENT') {\r\n      return [\r\n        {\r\n          id: 'mock-1',\r\n          title: 'تجديد مطبخ عصري',\r\n          description: 'تجديد مطبخ كامل بتصميم عصري مع خزائن جديدة',\r\n          category: 'نجارة',\r\n          location: 'دمشق',\r\n          budget_min: 200000,\r\n          budget_max: 350000,\r\n          status: 'OPEN',\r\n          created_at: new Date().toISOString()\r\n        },\r\n        {\r\n          id: 'mock-2',\r\n          title: 'إصلاح نظام السباكة',\r\n          description: 'إصلاح وتجديد نظام السباكة في الشقة',\r\n          category: 'سباكة',\r\n          location: 'دمشق',\r\n          budget_min: 80000,\r\n          budget_max: 150000,\r\n          status: 'IN_PROGRESS',\r\n          created_at: new Date().toISOString()\r\n        }\r\n      ];\r\n    } else {\r\n      return [\r\n        {\r\n          id: 'mock-3',\r\n          title: 'تركيب نظام إضاءة LED',\r\n          description: 'تركيب نظام إضاءة LED متطور في المنزل',\r\n          category: 'كهرباء',\r\n          location: 'دمشق',\r\n          budget_min: 120000,\r\n          budget_max: 200000,\r\n          status: 'OPEN',\r\n          created_at: new Date().toISOString()\r\n        }\r\n      ];\r\n    }\r\n  };\r\n\r\n  const getMockBids = (role?: string) => {\r\n    if (role === 'CRAFTSMAN') {\r\n      return [\r\n        {\r\n          id: 'bid-1',\r\n          amount: 280000,\r\n          status: 'PENDING',\r\n          created_at: new Date().toISOString(),\r\n          project: {\r\n            title: 'تجديد مطبخ عصري',\r\n            location: 'دمشق',\r\n            category: 'نجارة',\r\n            status: 'OPEN'\r\n          }\r\n        }\r\n      ];\r\n    }\r\n    return [];\r\n  };\r\n\r\n  const navigateToTab = (tab: string) => {\r\n    setActiveTab(tab);\r\n    router.push(`/dashboard?tab=${tab}`);\r\n  };\r\n\r\n  if (loading) {\r\n    return (\r\n      <div className=\"min-h-screen flex items-center justify-center\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-teal\"></div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (!isAuthenticated) {\r\n    return null;\r\n  }\r\n\r\n  return (\r\n    <DashboardLayout\r\n      title={`مرحباً ${user?.name || 'بك'}`}\r\n      subtitle={user?.role === 'CLIENT' ? 'لوحة تحكم العميل' : user?.role === 'CRAFTSMAN' ? 'لوحة تحكم الحرفي' : 'لوحة تحكم المدير'}\r\n    >\r\n      <div className=\"space-y-6\">\r\n        {/* التبويبات */}\r\n        <div className=\"border-b border-gray-200\">\r\n          <nav className=\"-mb-px flex space-x-8 space-x-reverse\">\r\n            {user?.role === 'CLIENT' && (\r\n              <>\r\n                <button\r\n                  onClick={() => navigateToTab('overview')}\r\n                  className={`py-2 px-1 border-b-2 font-medium text-sm ${\r\n                    activeTab === 'overview'\r\n                      ? 'border-teal text-teal'\r\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\r\n                  }`}\r\n                >\r\n                  نظرة عامة\r\n                </button>\r\n                <button\r\n                  onClick={() => navigateToTab('projects')}\r\n                  className={`py-2 px-1 border-b-2 font-medium text-sm ${\r\n                    activeTab === 'projects'\r\n                      ? 'border-teal text-teal'\r\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\r\n                  }`}\r\n                >\r\n                  مشاريعي\r\n                </button>\r\n                <button\r\n                  onClick={() => navigateToTab('create-project')}\r\n                  className={`py-2 px-1 border-b-2 font-medium text-sm ${\r\n                    activeTab === 'create-project'\r\n                      ? 'border-teal text-teal'\r\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\r\n                  }`}\r\n                >\r\n                  إنشاء مشروع\r\n                </button>\r\n              </>\r\n            )}\r\n\r\n            {user?.role === 'CRAFTSMAN' && (\r\n              <>\r\n                <button\r\n                  onClick={() => navigateToTab('overview')}\r\n                  className={`py-2 px-1 border-b-2 font-medium text-sm ${\r\n                    activeTab === 'overview'\r\n                      ? 'border-teal text-teal'\r\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\r\n                  }`}\r\n                >\r\n                  نظرة عامة\r\n                </button>\r\n                <button\r\n                  onClick={() => navigateToTab('available-projects')}\r\n                  className={`py-2 px-1 border-b-2 font-medium text-sm ${\r\n                    activeTab === 'available-projects'\r\n                      ? 'border-teal text-teal'\r\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\r\n                  }`}\r\n                >\r\n                  المشاريع المتاحة\r\n                </button>\r\n                <button\r\n                  onClick={() => navigateToTab('my-bids')}\r\n                  className={`py-2 px-1 border-b-2 font-medium text-sm ${\r\n                    activeTab === 'my-bids'\r\n                      ? 'border-teal text-teal'\r\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\r\n                  }`}\r\n                >\r\n                  عروضي\r\n                </button>\r\n              </>\r\n            )}\r\n          </nav>\r\n        </div>\r\n\r\n        {/* محتوى التبويبات */}\r\n        {activeTab === 'overview' && (\r\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle>إحصائيات سريعة</CardTitle>\r\n              </CardHeader>\r\n              <CardContent>\r\n                {dataLoading ? (\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"animate-pulse\">\r\n                      <div className=\"h-4 bg-gray-200 rounded mb-2\"></div>\r\n                      <div className=\"h-4 bg-gray-200 rounded mb-2\"></div>\r\n                      <div className=\"h-4 bg-gray-200 rounded\"></div>\r\n                    </div>\r\n                  </div>\r\n                ) : user?.role === 'CLIENT' ? (\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex justify-between\">\r\n                      <span>إجمالي المشاريع:</span>\r\n                      <span className=\"font-bold\">{projects.length}</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between\">\r\n                      <span>المشاريع النشطة:</span>\r\n                      <span className=\"font-bold text-green-600\">\r\n                        {projects.filter(p => p.status === 'OPEN' || p.status === 'IN_PROGRESS').length}\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"flex justify-between\">\r\n                      <span>المشاريع المكتملة:</span>\r\n                      <span className=\"font-bold text-blue-600\">\r\n                        {projects.filter(p => p.status === 'COMPLETED').length}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                ) : (\r\n                  <div className=\"space-y-2\">\r\n                    <div className=\"flex justify-between\">\r\n                      <span>إجمالي العروض:</span>\r\n                      <span className=\"font-bold\">{bids.length}</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between\">\r\n                      <span>العروض المقبولة:</span>\r\n                      <span className=\"font-bold text-green-600\">\r\n                        {bids.filter(b => b.status === 'ACCEPTED').length}\r\n                      </span>\r\n                    </div>\r\n                    <div className=\"flex justify-between\">\r\n                      <span>في الانتظار:</span>\r\n                      <span className=\"font-bold text-yellow-600\">\r\n                        {bids.filter(b => b.status === 'PENDING').length}\r\n                      </span>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </CardContent>\r\n            </Card>\r\n\r\n            <Card>\r\n              <CardHeader>\r\n                <CardTitle>الإجراءات السريعة</CardTitle>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <div className=\"space-y-3\">\r\n                  {user?.role === 'CLIENT' ? (\r\n                    <>\r\n                      <Button\r\n                        onClick={() => navigateToTab('create-project')}\r\n                        className=\"w-full bg-gradient-to-r from-teal to-navy\"\r\n                      >\r\n                        إنشاء مشروع جديد\r\n                      </Button>\r\n                      <Button\r\n                        onClick={() => navigateToTab('projects')}\r\n                        variant=\"outline\"\r\n                        className=\"w-full\"\r\n                      >\r\n                        عرض مشاريعي\r\n                      </Button>\r\n                    </>\r\n                  ) : (\r\n                    <>\r\n                      <Button\r\n                        onClick={() => navigateToTab('available-projects')}\r\n                        className=\"w-full bg-gradient-to-r from-teal to-navy\"\r\n                      >\r\n                        تصفح المشاريع\r\n                      </Button>\r\n                      <Button\r\n                        onClick={() => navigateToTab('my-bids')}\r\n                        variant=\"outline\"\r\n                        className=\"w-full\"\r\n                      >\r\n                        عرض عروضي\r\n                      </Button>\r\n                    </>\r\n                  )}\r\n                </div>\r\n              </CardContent>\r\n            </Card>\r\n          </div>\r\n        )}\r\n\r\n        {/* تبويب مشاريعي للعملاء */}\r\n        {activeTab === 'projects' && user?.role === 'CLIENT' && (\r\n          <div className=\"space-y-6\">\r\n            <div className=\"flex justify-between items-center\">\r\n              <h2 className=\"text-2xl font-bold text-navy\">مشاريعي</h2>\r\n              <Button\r\n                onClick={() => navigateToTab('create-project')}\r\n                className=\"bg-gradient-to-r from-teal to-navy\"\r\n              >\r\n                إنشاء مشروع جديد\r\n              </Button>\r\n            </div>\r\n\r\n            {projects.length === 0 ? (\r\n              <Card>\r\n                <CardContent className=\"text-center py-12\">\r\n                  <div className=\"text-6xl mb-4\">📋</div>\r\n                  <h3 className=\"text-xl font-bold text-gray-600 mb-2\">لا توجد مشاريع بعد</h3>\r\n                  <p className=\"text-gray-500 mb-4\">ابدأ بإنشاء مشروعك الأول</p>\r\n                  <Button\r\n                    onClick={() => navigateToTab('create-project')}\r\n                    className=\"bg-gradient-to-r from-teal to-navy\"\r\n                  >\r\n                    إنشاء مشروع\r\n                  </Button>\r\n                </CardContent>\r\n              </Card>\r\n            ) : (\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\r\n                {projects.map((project) => (\r\n                  <Card key={project.id} className=\"hover:shadow-lg transition-shadow\">\r\n                    <CardHeader>\r\n                      <div className=\"flex justify-between items-start\">\r\n                        <CardTitle className=\"text-lg\">{project.title}</CardTitle>\r\n                        <span className={`px-2 py-1 rounded text-xs font-medium ${\r\n                          project.status === 'OPEN' ? 'bg-green-100 text-green-800' :\r\n                          project.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :\r\n                          project.status === 'COMPLETED' ? 'bg-purple-100 text-purple-800' :\r\n                          'bg-gray-100 text-gray-800'\r\n                        }`}>\r\n                          {project.status === 'OPEN' ? 'مفتوح' :\r\n                           project.status === 'IN_PROGRESS' ? 'قيد التنفيذ' :\r\n                           project.status === 'COMPLETED' ? 'مكتمل' : project.status}\r\n                        </span>\r\n                      </div>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                      <div className=\"space-y-3\">\r\n                        <p className=\"text-gray-600 text-sm\">{project.description}</p>\r\n                        <div className=\"flex justify-between text-sm\">\r\n                          <span className=\"text-gray-500\">الفئة:</span>\r\n                          <span className=\"font-medium\">{project.category}</span>\r\n                        </div>\r\n                        <div className=\"flex justify-between text-sm\">\r\n                          <span className=\"text-gray-500\">الموقع:</span>\r\n                          <span className=\"font-medium\">{project.location}</span>\r\n                        </div>\r\n                        <div className=\"flex justify-between text-sm\">\r\n                          <span className=\"text-gray-500\">الميزانية:</span>\r\n                          <span className=\"font-medium text-green-600\">\r\n                            {project.budget_min?.toLocaleString()} - {project.budget_max?.toLocaleString()} ل.س\r\n                          </span>\r\n                        </div>\r\n                        <div className=\"pt-3 border-t\">\r\n                          <Button\r\n                            onClick={() => router.push(`/projects/${project.id}`)}\r\n                            variant=\"outline\"\r\n                            className=\"w-full\"\r\n                          >\r\n                            عرض التفاصيل\r\n                          </Button>\r\n                        </div>\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* تبويب المشاريع المتاحة للحرفيين */}\r\n        {activeTab === 'available-projects' && user?.role === 'CRAFTSMAN' && (\r\n          <div className=\"space-y-6\">\r\n            <h2 className=\"text-2xl font-bold text-navy\">المشاريع المتاحة</h2>\r\n\r\n            {projects.length === 0 ? (\r\n              <Card>\r\n                <CardContent className=\"text-center py-12\">\r\n                  <div className=\"text-6xl mb-4\">🔍</div>\r\n                  <h3 className=\"text-xl font-bold text-gray-600 mb-2\">لا توجد مشاريع متاحة حالياً</h3>\r\n                  <p className=\"text-gray-500\">تحقق مرة أخرى لاحقاً</p>\r\n                </CardContent>\r\n              </Card>\r\n            ) : (\r\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n                {projects.map((project) => (\r\n                  <Card key={project.id} className=\"hover:shadow-lg transition-shadow\">\r\n                    <CardHeader>\r\n                      <CardTitle className=\"text-lg\">{project.title}</CardTitle>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                      <div className=\"space-y-3\">\r\n                        <p className=\"text-gray-600 text-sm\">{project.description}</p>\r\n                        <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n                          <div>\r\n                            <span className=\"text-gray-500\">الفئة:</span>\r\n                            <p className=\"font-medium\">{project.category}</p>\r\n                          </div>\r\n                          <div>\r\n                            <span className=\"text-gray-500\">الموقع:</span>\r\n                            <p className=\"font-medium\">{project.location}</p>\r\n                          </div>\r\n                        </div>\r\n                        <div className=\"text-sm\">\r\n                          <span className=\"text-gray-500\">الميزانية:</span>\r\n                          <p className=\"font-bold text-green-600\">\r\n                            {project.budget_min?.toLocaleString()} - {project.budget_max?.toLocaleString()} ل.س\r\n                          </p>\r\n                        </div>\r\n                        <div className=\"pt-3 border-t flex space-x-2 space-x-reverse\">\r\n                          <Button\r\n                            onClick={() => router.push(`/projects/${project.id}`)}\r\n                            variant=\"outline\"\r\n                            className=\"flex-1\"\r\n                          >\r\n                            عرض التفاصيل\r\n                          </Button>\r\n                          <Button\r\n                            onClick={() => router.push(`/projects/${project.id}/submit-bid`)}\r\n                            className=\"flex-1 bg-gradient-to-r from-teal to-navy\"\r\n                          >\r\n                            تقديم عرض\r\n                          </Button>\r\n                        </div>\r\n                      </div>\r\n                    </CardContent>\r\n                  </Card>\r\n                ))}\r\n              </div>\r\n            )}\r\n          </div>\r\n        )}\r\n\r\n        {/* باقي التبويبات */}\r\n        {(activeTab === 'create-project' || activeTab === 'my-bids') && (\r\n          <Card>\r\n            <CardContent className=\"text-center py-12\">\r\n              <div className=\"text-6xl mb-4\">🚧</div>\r\n              <h3 className=\"text-xl font-bold text-gray-600 mb-2\">قيد التطوير</h3>\r\n              <p className=\"text-gray-500\">هذا القسم قيد التطوير وسيكون متاحاً قريباً</p>\r\n            </CardContent>\r\n          </Card>\r\n        )}\r\n      </div>\r\n    </DashboardLayout>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,gBAAa,AAAD;IACvD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,CAAC,iBAAiB;gBAChC,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,6BAA6B;YAC7B,MAAM,MAAM,aAAa,GAAG,CAAC,UAAU;YACvC,aAAa;YAEb,IAAI,iBAAiB;gBACnB;YACF;QACF;kCAAG;QAAC;QAAS;QAAiB;QAAQ;KAAa;IAEnD,MAAM,oBAAoB;QACxB,IAAI;YACF,QAAQ,GAAG,CAAC,oCAAoC;YAEhD,uCAAuC;YACvC,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,yHAAA,CAAA,UAAQ,CACxD,IAAI,CAAC,SACL,MAAM,CAAC,MACP,KAAK,CAAC;YAET,IAAI,WAAW;gBACb,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,QAAQ,GAAG,CAAC;gBACZ,+BAA+B;gBAC/B,YAAY,gBAAgB,MAAM;gBAClC,QAAQ,YAAY,MAAM;gBAC1B,eAAe;gBACf;YACF;YAEA,IAAI,MAAM,SAAS,UAAU;gBAC3B,sBAAsB;gBACtB,MAAM,WAAW,KAAK,EAAE,IAAI;gBAE5B,MAAM,EAAE,MAAM,YAAY,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,yHAAA,CAAA,UAAQ,CAChE,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,UAChB,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAE1C,IAAI,eAAe;oBACjB,QAAQ,KAAK,CAAC,kCAAkC;oBAChD,YAAY,gBAAgB;gBAC9B,OAAO;oBACL,YAAY,gBAAgB,EAAE;gBAChC;YAEF,OAAO,IAAI,MAAM,SAAS,aAAa;gBACrC,oBAAoB;gBACpB,MAAM,cAAc,KAAK,EAAE,IAAI;gBAE/B,IAAI;oBACF,MAAM,EAAE,MAAM,QAAQ,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,yHAAA,CAAA,UAAQ,CACxD,IAAI,CAAC,QACL,MAAM,CAAC,CAAC;;;;;;;;;;;;YAYT,CAAC,EACA,EAAE,CAAC,gBAAgB,aACnB,KAAK,CAAC,cAAc;wBAAE,WAAW;oBAAM;oBAE1C,IAAI,WAAW;wBACb,QAAQ,KAAK,CAAC,iCAAiC;wBAC/C,QAAQ,YAAY;oBACtB,OAAO;wBACL,oCAAoC;wBACpC,MAAM,gBAAgB,CAAC,YAAY,EAAE,EAAE,GAAG,CAAC,CAAA,MAAO,CAAC;gCACjD,GAAG,GAAG;gCACN,SAAS,IAAI,QAAQ;4BACvB,CAAC;wBACD,QAAQ;oBACV;gBACF,EAAE,OAAO,UAAU;oBACjB,QAAQ,KAAK,CAAC,2BAA2B;oBACzC,QAAQ,YAAY;gBACtB;gBAEA,yBAAyB;gBACzB,MAAM,EAAE,MAAM,iBAAiB,EAAE,OAAO,cAAc,EAAE,GAAG,MAAM,yHAAA,CAAA,UAAQ,CACtE,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,UAAU,QACb,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM,GACvC,KAAK,CAAC;gBAET,IAAI,gBAAgB;oBAClB,QAAQ,KAAK,CAAC,qCAAqC;oBACnD,YAAY,gBAAgB;gBAC9B,OAAO;oBACL,YAAY,qBAAqB,EAAE;gBACrC;YACF,OAAO;gBACL,4CAA4C;gBAC5C,MAAM,EAAE,MAAM,WAAW,EAAE,OAAO,aAAa,EAAE,GAAG,MAAM,yHAAA,CAAA,UAAQ,CAC/D,IAAI,CAAC,YACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM,GACvC,KAAK,CAAC;gBAET,IAAI,eAAe;oBACjB,YAAY,gBAAgB;gBAC9B,OAAO;oBACL,YAAY,eAAe,EAAE;gBAC/B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,wCAAwC;YACxC,YAAY,gBAAgB,MAAM;YAClC,QAAQ,YAAY,MAAM;QAC5B,SAAU;YACR,eAAe;QACjB;IACF;IAEA,0BAA0B;IAC1B,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,UAAU;YACrB,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,UAAU;oBACV,YAAY;oBACZ,YAAY;oBACZ,QAAQ;oBACR,YAAY,IAAI,OAAO,WAAW;gBACpC;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,UAAU;oBACV,YAAY;oBACZ,YAAY;oBACZ,QAAQ;oBACR,YAAY,IAAI,OAAO,WAAW;gBACpC;aACD;QACH,OAAO;YACL,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,aAAa;oBACb,UAAU;oBACV,UAAU;oBACV,YAAY;oBACZ,YAAY;oBACZ,QAAQ;oBACR,YAAY,IAAI,OAAO,WAAW;gBACpC;aACD;QACH;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,SAAS,aAAa;YACxB,OAAO;gBACL;oBACE,IAAI;oBACJ,QAAQ;oBACR,QAAQ;oBACR,YAAY,IAAI,OAAO,WAAW;oBAClC,SAAS;wBACP,OAAO;wBACP,UAAU;wBACV,UAAU;wBACV,QAAQ;oBACV;gBACF;aACD;QACH;QACA,OAAO,EAAE;IACX;IAEA,MAAM,gBAAgB,CAAC;QACrB,aAAa;QACb,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,KAAK;IACrC;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC,kJAAA,CAAA,UAAe;QACd,OAAO,CAAC,OAAO,EAAE,MAAM,QAAQ,MAAM;QACrC,UAAU,MAAM,SAAS,WAAW,qBAAqB,MAAM,SAAS,cAAc,qBAAqB;kBAE3G,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,MAAM,SAAS,0BACd;;kDACE,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,0BACA,8EACJ;kDACH;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,0BACA,8EACJ;kDACH;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,yCAAyC,EACnD,cAAc,mBACV,0BACA,8EACJ;kDACH;;;;;;;;4BAMJ,MAAM,SAAS,6BACd;;kDACE,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,yCAAyC,EACnD,cAAc,aACV,0BACA,8EACJ;kDACH;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,yCAAyC,EACnD,cAAc,uBACV,0BACA,8EACJ;kDACH;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,cAAc;wCAC7B,WAAW,CAAC,yCAAyC,EACnD,cAAc,YACV,0BACA,8EACJ;kDACH;;;;;;;;;;;;;;;;;;;gBASR,cAAc,4BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACT,4BACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;+CAGjB,MAAM,SAAS,yBACjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;kEAAa,SAAS,MAAM;;;;;;;;;;;;0DAE9C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;kEACb,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,EAAE,MAAM,KAAK,eAAe,MAAM;;;;;;;;;;;;0DAGnF,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;kEACb,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;;;;;;;;;;;;;;;;;6DAK5D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;kEAAa,KAAK,MAAM;;;;;;;;;;;;0DAE1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;kEACb,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,YAAY,MAAM;;;;;;;;;;;;0DAGrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAK;;;;;;kEACN,6LAAC;wDAAK,WAAU;kEACb,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ5D,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;8CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;;;;;;8CAEb,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,MAAM,SAAS,yBACd;;8DACE,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,cAAc;oDAC7B,WAAU;8DACX;;;;;;8DAGD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,cAAc;oDAC7B,SAAQ;oDACR,WAAU;8DACX;;;;;;;yEAKH;;8DACE,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,cAAc;oDAC7B,WAAU;8DACX;;;;;;8DAGD,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAS,IAAM,cAAc;oDAC7B,SAAQ;oDACR,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gBAYd,cAAc,cAAc,MAAM,SAAS,0BAC1C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CACX;;;;;;;;;;;;wBAKF,SAAS,MAAM,KAAK,kBACnB,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,6LAAC;wCAAE,WAAU;kDAAqB;;;;;;kDAClC,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,cAAc;wCAC7B,WAAU;kDACX;;;;;;;;;;;;;;;;iDAML,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,mIAAA,CAAA,OAAI;oCAAkB,WAAU;;sDAC/B,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mIAAA,CAAA,YAAS;wDAAC,WAAU;kEAAW,QAAQ,KAAK;;;;;;kEAC7C,6LAAC;wDAAK,WAAW,CAAC,sCAAsC,EACtD,QAAQ,MAAM,KAAK,SAAS,gCAC5B,QAAQ,MAAM,KAAK,gBAAgB,8BACnC,QAAQ,MAAM,KAAK,cAAc,kCACjC,6BACA;kEACC,QAAQ,MAAM,KAAK,SAAS,UAC5B,QAAQ,MAAM,KAAK,gBAAgB,gBACnC,QAAQ,MAAM,KAAK,cAAc,UAAU,QAAQ,MAAM;;;;;;;;;;;;;;;;;sDAIhE,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyB,QAAQ,WAAW;;;;;;kEACzD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAe,QAAQ,QAAQ;;;;;;;;;;;;kEAEjD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAe,QAAQ,QAAQ;;;;;;;;;;;;kEAEjD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;;oEACb,QAAQ,UAAU,EAAE;oEAAiB;oEAAI,QAAQ,UAAU,EAAE;oEAAiB;;;;;;;;;;;;;kEAGnF,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4DACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;4DACpD,SAAQ;4DACR,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;mCAtCE,QAAQ,EAAE;;;;;;;;;;;;;;;;gBAoD9B,cAAc,wBAAwB,MAAM,SAAS,6BACpD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA+B;;;;;;wBAE5C,SAAS,MAAM,KAAK,kBACnB,6LAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAAuC;;;;;;kDACrD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;iDAIjC,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC,mIAAA,CAAA,OAAI;oCAAkB,WAAU;;sDAC/B,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;0DAAW,QAAQ,KAAK;;;;;;;;;;;sDAE/C,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyB,QAAQ,WAAW;;;;;;kEACzD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAE,WAAU;kFAAe,QAAQ,QAAQ;;;;;;;;;;;;0EAE9C,6LAAC;;kFACC,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6LAAC;wEAAE,WAAU;kFAAe,QAAQ,QAAQ;;;;;;;;;;;;;;;;;;kEAGhD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAE,WAAU;;oEACV,QAAQ,UAAU,EAAE;oEAAiB;oEAAI,QAAQ,UAAU,EAAE;oEAAiB;;;;;;;;;;;;;kEAGnF,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;gEACpD,SAAQ;gEACR,WAAU;0EACX;;;;;;0EAGD,6LAAC,qIAAA,CAAA,SAAM;gEACL,SAAS,IAAM,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,QAAQ,EAAE,CAAC,WAAW,CAAC;gEAC/D,WAAU;0EACX;;;;;;;;;;;;;;;;;;;;;;;;mCAlCE,QAAQ,EAAE;;;;;;;;;;;;;;;;gBAgD9B,CAAC,cAAc,oBAAoB,cAAc,SAAS,mBACzD,6LAAC,mIAAA,CAAA,OAAI;8BACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3C;GAhjBwB;;QACqB,gIAAA,CAAA,gBAAa;QACzC,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;KAHd"}}, {"offset": {"line": 2717, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}