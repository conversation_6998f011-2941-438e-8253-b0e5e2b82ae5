'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import DateDisplay from '../ui/DateDisplay';

interface CompletedProject {
  id: number;
  title: string;
  description: string;
  category: string;
  completedAt: string;
  duration: number; // بالأيام
  budget: number;
  beforeImages: string[];
  afterImages: string[];
  skills: string[];
  clientRating: number;
  clientReview: string;
  client: {
    name: string;
    location: string;
  };
}

interface CompletedProjectProps {
  project: CompletedProject;
  showClientInfo?: boolean;
}

const CompletedProject: React.FC<CompletedProjectProps> = ({
  project,
  showClientInfo = true
}) => {
  const [activeTab, setActiveTab] = useState<'before' | 'after'>('before');
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  const currentImages = activeTab === 'before' ? project.beforeImages : project.afterImages;

  return (
    <Card className="border-0 bg-white/90 backdrop-blur-sm hover:shadow-xl transition-all duration-300">
      <CardHeader>
        <div className="flex justify-between items-start mb-2">
          <CardTitle className="text-lg leading-tight">{project.title}</CardTitle>
          <Badge variant="success">مكتمل</Badge>
        </div>
        <div className="flex items-center text-sm text-gray-500 space-x-4 space-x-reverse">
          <span className="flex items-center">
            <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            اكتمل في <DateDisplay date={project.completedAt} />
          </span>
          <span className="flex items-center">
            <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0V6a2 2 0 012-2h2a2 2 0 012 2v1m-6 0h6m-6 0l-.5 3.5M18 7l-.5 3.5M8 7l-.5 3.5m10 0L17 14H7l-.5-3.5M17 14v4a2 2 0 01-2 2H9a2 2 0 01-2-2v-4m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v5.02" />
            </svg>
            {project.duration} يوم
          </span>
        </div>
      </CardHeader>

      <CardContent>
        <p className="text-gray-700 mb-4 line-clamp-2">{project.description}</p>

        {/* تبويب قبل وبعد */}
        <div className="mb-4">
          <div className="flex border-b border-gray-200">
            <button
              onClick={() => setActiveTab('before')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'before'
                  ? 'border-teal text-teal'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              قبل ({project.beforeImages.length})
            </button>
            <button
              onClick={() => setActiveTab('after')}
              className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'after'
                  ? 'border-teal text-teal'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              بعد ({project.afterImages.length})
            </button>
          </div>
        </div>

        {/* عرض الصور */}
        {currentImages.length > 0 && (
          <div className="mb-4">
            {/* الصورة الرئيسية */}
            <div className="mb-3">
              <img
                src={currentImages[selectedImageIndex]}
                alt={`${activeTab === 'before' ? 'قبل' : 'بعد'} العمل`}
                className="w-full h-64 object-cover rounded-lg border border-gray-200 hover:scale-105 transition-transform duration-300"
              />
            </div>

            {/* الصور المصغرة */}
            {currentImages.length > 1 && (
              <div className="grid grid-cols-4 gap-2">
                {currentImages.map((image, index) => (
                  <button
                    key={index}
                    onClick={() => setSelectedImageIndex(index)}
                    className={`relative overflow-hidden rounded-md border-2 transition-all ${
                      selectedImageIndex === index
                        ? 'border-teal'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <img
                      src={image}
                      alt={`صورة ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </button>
                ))}
              </div>
            )}
          </div>
        )}

        {/* المهارات */}
        <div className="flex flex-wrap gap-2 mb-4">
          {project.skills.map((skill, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {skill}
            </Badge>
          ))}
        </div>

        {/* معلومات المشروع */}
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="text-sm text-green-600 font-medium">قيمة المشروع</div>
            <div className="text-lg font-bold text-green-700">
              {project.budget.toLocaleString()} ل.س
            </div>
          </div>
          <div className="bg-yellow-50 p-3 rounded-lg">
            <div className="text-sm text-yellow-600 font-medium">تقييم العميل</div>
            <div className="flex items-center">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <svg
                    key={i}
                    className={`w-4 h-4 ${
                      i < project.clientRating ? 'text-yellow-500' : 'text-gray-300'
                    }`}
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                ))}
              </div>
              <span className="mr-1 text-sm font-bold text-yellow-700">
                {project.clientRating}
              </span>
            </div>
          </div>
        </div>

        {/* تقييم العميل */}
        {project.clientReview && (
          <div className="bg-gray-50 p-4 rounded-lg mb-4">
            <h4 className="font-semibold text-gray-700 mb-2">تقييم العميل:</h4>
            <p className="text-gray-600 text-sm italic">"{project.clientReview}"</p>
          </div>
        )}

        {/* معلومات العميل */}
        {showClientInfo && (
          <div className="border-t border-gray-200 pt-4">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>العميل: {project.client.name}</span>
              <span>{project.client.location}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CompletedProject;
