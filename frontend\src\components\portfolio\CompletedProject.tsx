'use client';

import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '../ui/Card';
import { Badge } from '../ui/Badge';
import DateDisplay from '../ui/DateDisplay';

interface CompletedProject {
  id: number;
  title: string;
  description: string;
  category: string;
  completedAt: string;
  duration: number; // بالأيام
  budget: number;
  beforeImages: string[];
  afterImages: string[];
  skills: string[];
  clientRating: number;
  clientReview: string;
  client: {
    name: string;
    location: string;
  };
}

interface CompletedProjectProps {
  project: CompletedProject;
  showClientInfo?: boolean;
}

const CompletedProject: React.FC<CompletedProjectProps> = ({
  project,
  showClientInfo = true
}) => {
  const [showBeforeAfter, setShowBeforeAfter] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  return (
    <div className="group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-teal/30 hover:scale-105">
      {/* مقارنة قبل وبعد */}
      <div className="relative h-64 overflow-hidden">
        {!showBeforeAfter ? (
          // عرض مقارنة قبل وبعد
          <div className="absolute inset-0 grid grid-cols-2">
            {/* صورة قبل */}
            <div className="relative overflow-hidden">
              <img
                src={project.beforeImages[0]}
                alt="قبل"
                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
              />
              <div className="absolute top-4 left-4 bg-red-500/90 text-white px-3 py-1 rounded-full text-sm font-medium">
                قبل
              </div>
            </div>

            {/* صورة بعد */}
            <div className="relative overflow-hidden">
              <img
                src={project.afterImages[0]}
                alt="بعد"
                className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
              />
              <div className="absolute top-4 right-4 bg-green-500/90 text-white px-3 py-1 rounded-full text-sm font-medium">
                بعد
              </div>
            </div>

            {/* خط فاصل */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-1 h-16 bg-white/80 rounded-full shadow-lg"></div>

            {/* أيقونة المقارنة */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-10 h-10 bg-white rounded-full shadow-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
              </svg>
            </div>
          </div>
        ) : (
          // عرض الصور بشكل منفصل
          <div className="relative">
            <img
              src={showBeforeAfter ? project.afterImages[selectedImageIndex] : project.beforeImages[selectedImageIndex]}
              alt={project.title}
              className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
            />

            {/* تأثير التدرج */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
          </div>
        )}

        {/* شارة الفئة */}
        <div className="absolute top-4 right-4">
          <div className="bg-white/90 backdrop-blur-sm text-navy px-3 py-1 rounded-full text-sm font-medium shadow-lg">
            🏆 {project.category}
          </div>
        </div>

        {/* شارة التقييم */}
        <div className="absolute bottom-4 left-4">
          <div className="bg-yellow-500/90 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center">
            <svg className="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
            </svg>
            {project.clientRating}
          </div>
        </div>

        {/* زر التبديل */}
        <button
          onClick={() => setShowBeforeAfter(!showBeforeAfter)}
          className="absolute bottom-4 right-4 bg-white/90 hover:bg-white text-navy px-3 py-1 rounded-full text-sm font-medium shadow-lg transition-all duration-300 hover:scale-105"
        >
          {showBeforeAfter ? 'عرض المقارنة' : 'عرض منفصل'}
        </button>
      </div>

      {/* محتوى البطاقة */}
      <div className="p-6">
        {/* العنوان والوصف */}
        <div className="mb-6">
          <h3 className="text-xl font-bold text-navy mb-2 group-hover:text-teal transition-colors duration-300">
            {project.title}
          </h3>
          <p className="text-gray-600 line-clamp-2 leading-relaxed">
            {project.description}
          </p>
        </div>

        {/* معلومات المشروع */}
        <div className="grid grid-cols-3 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{project.budget.toLocaleString()}</div>
            <div className="text-sm text-gray-500">ل.س</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{project.duration}</div>
            <div className="text-sm text-gray-500">يوم</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{project.clientRating}</div>
            <div className="text-sm text-gray-500">تقييم</div>
          </div>
        </div>

        {/* المهارات */}
        <div className="flex flex-wrap gap-2 mb-6">
          {project.skills.slice(0, 3).map((skill, index) => (
            <Badge key={index} variant="outline" className="text-xs bg-gradient-to-r from-navy/5 to-teal/5 border-navy/20 text-navy">
              {skill}
            </Badge>
          ))}
          {project.skills.length > 3 && (
            <Badge variant="outline" className="text-xs bg-gray-50 border-gray-200 text-gray-600">
              +{project.skills.length - 3}
            </Badge>
          )}
        </div>

        {/* تقييم العميل */}
        {project.clientReview && (
          <div className="bg-gradient-to-r from-gray-50 to-gray-100 p-4 rounded-xl mb-6 border border-gray-200/50">
            <div className="flex items-start">
              <svg className="w-5 h-5 text-gray-400 mt-1 ml-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clipRule="evenodd" />
              </svg>
              <div>
                <p className="text-gray-700 text-sm italic leading-relaxed">"{project.clientReview}"</p>
                {showClientInfo && (
                  <div className="mt-2 text-xs text-gray-500">
                    - {project.client.name}، {project.client.location}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* معلومات إضافية */}
        <div className="flex items-center justify-between pt-4 border-t border-gray-100">
          <div className="flex items-center text-sm text-gray-500">
            <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            اكتمل في <DateDisplay date={project.completedAt} />
          </div>
          <div className="flex items-center text-sm text-teal font-medium">
            <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            مشروع مكتمل
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompletedProject;
