'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';

const CraftsmanProjectsPage = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const projects = [
    {
      id: 1,
      title: 'تركيب خزائن مطبخ حديثة',
      description: 'مطلوب نجار ماهر لتركيب خزائن مطبخ بتصميم عصري مع أجهزة مدمجة',
      budget: '₺6,000 - ₺8,000',
      location: 'دمشق - المزة',
      deadline: '2024-03-20',
      category: 'نجارة',
      postedTime: 'منذ ساعتين',
      offers: 8,
      client: 'أحمد محمد',
      clientRating: 4.8,
      urgency: 'عادي',
      materials: 'متوفرة',
      workType: 'تركيب'
    },
    {
      id: 2,
      title: 'إصلاح نظام كهرباء منزلي',
      description: 'إصلاح وتجديد التمديدات الكهربائية في شقة سكنية مع تركيب لوحة توزيع جديدة',
      budget: '₺2,500 - ₺3,500',
      location: 'حلب - الفرقان',
      deadline: '2024-03-15',
      category: 'كهرباء',
      postedTime: 'منذ 4 ساعات',
      offers: 12,
      client: 'فاطمة أحمد',
      clientRating: 4.9,
      urgency: 'عاجل',
      materials: 'غير متوفرة',
      workType: 'إصلاح'
    },
    {
      id: 3,
      title: 'دهان شقة كاملة',
      description: 'دهان شقة من 3 غرف مع تحضير الجدران والأسقف باستخدام دهانات عالية الجودة',
      budget: '₺3,000 - ₺4,000',
      location: 'دمشق - كفرسوسة',
      deadline: '2024-03-25',
      category: 'دهان',
      postedTime: 'منذ يوم',
      offers: 15,
      client: 'محمد علي',
      clientRating: 4.6,
      urgency: 'عادي',
      materials: 'متوفرة جزئياً',
      workType: 'دهان'
    },
    {
      id: 4,
      title: 'تركيب نظام سباكة حديث',
      description: 'تركيب نظام سباكة متكامل في حمامين مع تركيب أدوات صحية حديثة',
      budget: '₺4,500 - ₺6,000',
      location: 'دمشق - أبو رمانة',
      deadline: '2024-03-18',
      category: 'سباكة',
      postedTime: 'منذ 3 أيام',
      offers: 6,
      client: 'سارة خالد',
      clientRating: 4.7,
      urgency: 'متوسط',
      materials: 'غير متوفرة',
      workType: 'تركيب'
    }
  ];

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'عاجل':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'متوسط':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'عادي':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const filteredProjects = projects.filter(project => {
    const matchesFilter = activeFilter === 'all' || project.category === activeFilter;
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesFilter && matchesSearch;
  });

  const stats = [
    {
      title: 'المشاريع المتاحة',
      value: projects.length.toString(),
      color: 'text-blue-600'
    },
    {
      title: 'مشاريع عاجلة',
      value: projects.filter(p => p.urgency === 'عاجل').length.toString(),
      color: 'text-red-600'
    },
    {
      title: 'متوسط العروض',
      value: Math.round(projects.reduce((sum, p) => sum + p.offers, 0) / projects.length).toString(),
      color: 'text-green-600'
    },
    {
      title: 'متوسط الميزانية',
      value: '₺4,250',
      color: 'text-purple-600'
    }
  ];

  return (
    <ProtectedRoute requiredRoles="craftsman">
      <DashboardLayout 
        title="المشاريع المتاحة"
        subtitle="تصفح المشاريع الجديدة وقدم عروضك"
      >
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className={`text-3xl ${stat.color}`}>
                    🔍
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Filters and Search */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center space-y-4 md:space-y-0">
              {/* Category Filters */}
              <div className="flex flex-wrap gap-2">
                {[
                  { id: 'all', label: 'جميع التخصصات' },
                  { id: 'نجارة', label: 'نجارة' },
                  { id: 'كهرباء', label: 'كهرباء' },
                  { id: 'سباكة', label: 'سباكة' },
                  { id: 'دهان', label: 'دهان' }
                ].map((filter) => (
                  <button
                    key={filter.id}
                    onClick={() => setActiveFilter(filter.id)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                      activeFilter === filter.id
                        ? 'bg-navy text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {filter.label}
                  </button>
                ))}
              </div>

              {/* Search */}
              <div className="flex items-center space-x-4 space-x-reverse">
                <input
                  type="text"
                  placeholder="البحث في المشاريع..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                />
              </div>
            </div>
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {filteredProjects.map((project) => (
              <div key={project.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{project.title}</h3>
                    <p className="text-gray-600 text-sm mb-3 line-clamp-2">{project.description}</p>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getUrgencyColor(project.urgency)}`}>
                    {project.urgency}
                  </span>
                </div>

                {/* Client Info */}
                <div className="flex items-center space-x-3 space-x-reverse mb-4 p-3 bg-gray-50 rounded-lg">
                  <div className="w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white font-bold">
                    {project.client.charAt(0)}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{project.client}</p>
                    <div className="flex items-center space-x-1 space-x-reverse text-sm text-gray-600">
                      <span>⭐ {project.clientRating}</span>
                      <span>•</span>
                      <span>📍 {project.location}</span>
                    </div>
                  </div>
                </div>

                {/* Project Details */}
                <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                  <div>
                    <span className="text-gray-500">الميزانية:</span>
                    <span className="font-semibold text-gray-900 mr-2">{project.budget}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">الموعد النهائي:</span>
                    <span className="font-semibold text-gray-900 mr-2">{project.deadline}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">المواد:</span>
                    <span className="font-semibold text-gray-900 mr-2">{project.materials}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">نوع العمل:</span>
                    <span className="font-semibold text-gray-900 mr-2">{project.workType}</span>
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-500">
                    <span className="bg-gray-100 px-2 py-1 rounded">{project.category}</span>
                    <span>{project.postedTime}</span>
                    <span>{project.offers} عرض</span>
                  </div>
                  <div className="flex space-x-2 space-x-reverse">
                    <Link href={`/projects/${project.id}`}>
                      <Button size="sm" variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                        عرض التفاصيل
                      </Button>
                    </Link>
                    <Button size="sm" className="bg-gradient-to-r from-navy to-teal">
                      تقديم عرض
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredProjects.length === 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد مشاريع</h3>
              <p className="text-gray-600 mb-6">لم يتم العثور على مشاريع بالمعايير المحددة</p>
              <Button 
                onClick={() => {
                  setActiveFilter('all');
                  setSearchTerm('');
                }}
                className="bg-gradient-to-r from-navy to-teal"
              >
                إعادة تعيين الفلاتر
              </Button>
            </div>
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default CraftsmanProjectsPage;
