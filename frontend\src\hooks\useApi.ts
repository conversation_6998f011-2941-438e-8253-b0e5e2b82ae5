import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface ApiResponse<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => void;
}

interface ApiMutationResponse<T> {
  mutate: (data?: any) => Promise<T | null>;
  loading: boolean;
  error: string | null;
}

// Hook لجلب البيانات
export function useApiGet<T>(endpoint: string, dependencies: any[] = []): ApiResponse<T> {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch(endpoint);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ في جلب البيانات');
      }

      setData(result);
    } catch (err: any) {
      setError(err.message);
      setData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, dependencies);

  return { data, loading, error, refetch: fetchData };
}

// Hook للعمليات (POST, PUT, DELETE)
export function useApiMutation<T>(
  endpoint: string, 
  method: 'POST' | 'PUT' | 'DELETE' = 'POST'
): ApiMutationResponse<T> {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const mutate = async (data?: any): Promise<T | null> => {
    try {
      setLoading(true);
      setError(null);

      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (data && method !== 'DELETE') {
        options.body = JSON.stringify(data);
      }

      const response = await fetch(endpoint, options);
      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'حدث خطأ في العملية');
      }

      return result;
    } catch (err: any) {
      setError(err.message);
      return null;
    } finally {
      setLoading(false);
    }
  };

  return { mutate, loading, error };
}

// Hooks مخصصة للكيانات المختلفة

// المشاريع
export function useProjects(filters?: Record<string, string>) {
  const queryString = filters ? 
    '?' + new URLSearchParams(filters).toString() : '';
  
  return useApiGet<{projects: any[], total: number}>(`/api/projects${queryString}`, [filters]);
}

export function useProject(id: string) {
  return useApiGet<{project: any}>(`/api/projects/${id}`, [id]);
}

export function useCreateProject() {
  return useApiMutation<{project: any, message: string}>('/api/projects', 'POST');
}

export function useUpdateProject(id: string) {
  return useApiMutation<{project: any, message: string}>(`/api/projects/${id}`, 'PUT');
}

export function useDeleteProject(id: string) {
  return useApiMutation<{message: string}>(`/api/projects/${id}`, 'DELETE');
}

// العروض
export function useOffers(filters?: Record<string, string>) {
  const queryString = filters ? 
    '?' + new URLSearchParams(filters).toString() : '';
  
  return useApiGet<{offers: any[], total: number}>(`/api/offers${queryString}`, [filters]);
}

export function useCreateOffer() {
  return useApiMutation<{offer: any, message: string}>('/api/offers', 'POST');
}

// المستخدمين
export function useUsers(filters?: Record<string, string>) {
  const queryString = filters ? 
    '?' + new URLSearchParams(filters).toString() : '';
  
  return useApiGet<{users: any[], total: number}>(`/api/users${queryString}`, [filters]);
}

export function useUser(id: string) {
  return useApiGet<{user: any}>(`/api/users/${id}`, [id]);
}

export function useUpdateUser(id: string) {
  return useApiMutation<{user: any, message: string}>(`/api/users/${id}`, 'PUT');
}

// Hook للحصول على بيانات المستخدم الحالي
export function useCurrentUser() {
  const { data: session } = useSession();
  const userId = session?.user?.id;
  
  const { data, loading, error, refetch } = useUser(userId || '');
  
  return {
    user: data?.user || null,
    loading: loading && !!userId,
    error: userId ? error : null,
    refetch
  };
}

// Hook للحصول على مشاريع المستخدم الحالي
export function useMyProjects() {
  const { data: session } = useSession();
  const userId = session?.user?.id;
  
  const filters = userId ? { clientId: userId } : undefined;
  return useProjects(filters);
}

// Hook للحصول على عروض المستخدم الحالي
export function useMyOffers() {
  const { data: session } = useSession();
  const userId = session?.user?.id;
  
  const filters = userId ? { craftsmanId: userId } : undefined;
  return useOffers(filters);
}
