{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// دالة تنسيق التاريخ بشكل ثابت لتجنب مشاكل Hydration\nexport function formatDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n\n    // التحقق من صحة التاريخ\n    if (isNaN(date.getTime())) {\n      return 'تاريخ غير صحيح';\n    }\n\n    // تنسيق ثابت للتاريخ\n    const year = date.getFullYear();\n    const month = date.getMonth() + 1;\n    const day = date.getDate();\n\n    // أسماء الشهور بالعربية\n    const monthNames = [\n      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',\n      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'\n    ];\n\n    return `${day} ${monthNames[month - 1]} ${year}`;\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق التاريخ النسبي (منذ كم يوم)\nexport function formatRelativeDate(dateString: string): string {\n  if (!dateString) return 'غير محدد';\n\n  try {\n    const date = new Date(dateString);\n    const now = new Date();\n    const diffInMs = now.getTime() - date.getTime();\n    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));\n\n    if (diffInDays === 0) {\n      return 'اليوم';\n    } else if (diffInDays === 1) {\n      return 'أمس';\n    } else if (diffInDays < 7) {\n      return `منذ ${diffInDays} أيام`;\n    } else if (diffInDays < 30) {\n      const weeks = Math.floor(diffInDays / 7);\n      return `منذ ${weeks} ${weeks === 1 ? 'أسبوع' : 'أسابيع'}`;\n    } else if (diffInDays < 365) {\n      const months = Math.floor(diffInDays / 30);\n      return `منذ ${months} ${months === 1 ? 'شهر' : 'أشهر'}`;\n    } else {\n      const years = Math.floor(diffInDays / 365);\n      return `منذ ${years} ${years === 1 ? 'سنة' : 'سنوات'}`;\n    }\n  } catch (error) {\n    return 'تاريخ غير صحيح';\n  }\n}\n\n// دالة لتنسيق الأرقام بالفواصل\nexport function formatNumber(num: number): string {\n  return num.toLocaleString('ar-SA');\n}\n\n// دالة للتحقق من صحة البريد الإلكتروني\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n// دالة للتحقق من صحة رقم الهاتف السوري\nexport function isValidSyrianPhone(phone: string): boolean {\n  const phoneRegex = /^(\\+963|0)?[0-9]{9,10}$/;\n  return phoneRegex.test(phone);\n}\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,WAAW,UAAkB;IAC3C,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QAEtB,wBAAwB;QACxB,IAAI,MAAM,KAAK,OAAO,KAAK;YACzB,OAAO;QACT;QAEA,qBAAqB;QACrB,MAAM,OAAO,KAAK,WAAW;QAC7B,MAAM,QAAQ,KAAK,QAAQ,KAAK;QAChC,MAAM,MAAM,KAAK,OAAO;QAExB,wBAAwB;QACxB,MAAM,aAAa;YACjB;YAAS;YAAU;YAAQ;YAAS;YAAQ;YAC5C;YAAS;YAAS;YAAU;YAAU;YAAU;SACjD;QAED,OAAO,GAAG,IAAI,CAAC,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC,EAAE,MAAM;IAClD,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,mBAAmB,UAAkB;IACnD,IAAI,CAAC,YAAY,OAAO;IAExB,IAAI;QACF,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,IAAI,OAAO,KAAK,KAAK,OAAO;QAC7C,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAE7D,IAAI,eAAe,GAAG;YACpB,OAAO;QACT,OAAO,IAAI,eAAe,GAAG;YAC3B,OAAO;QACT,OAAO,IAAI,aAAa,GAAG;YACzB,OAAO,CAAC,IAAI,EAAE,WAAW,KAAK,CAAC;QACjC,OAAO,IAAI,aAAa,IAAI;YAC1B,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,UAAU,UAAU;QAC3D,OAAO,IAAI,aAAa,KAAK;YAC3B,MAAM,SAAS,KAAK,KAAK,CAAC,aAAa;YACvC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,WAAW,IAAI,QAAQ,QAAQ;QACzD,OAAO;YACL,MAAM,QAAQ,KAAK,KAAK,CAAC,aAAa;YACtC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,UAAU,IAAI,QAAQ,SAAS;QACxD;IACF,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,SAAS,aAAa,GAAW;IACtC,OAAO,IAAI,cAAc,CAAC;AAC5B;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,SAAS,mBAAmB,KAAa;IAC9C,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB"}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',\n  {\n    variants: {\n      variant: {\n        default: 'bg-navy text-white hover:bg-navy/90',\n        destructive: 'bg-red-500 text-white hover:bg-red-500/90',\n        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',\n        secondary: 'bg-teal text-white hover:bg-teal/90',\n        ghost: 'hover:bg-accent hover:text-accent-foreground',\n        link: 'underline-offset-4 hover:underline text-primary',\n      },\n      size: {\n        default: 'h-10 py-2 px-4',\n        sm: 'h-9 px-3 rounded-md',\n        lg: 'h-11 px-8 rounded-md',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n      size: 'default',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,wQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;QACN;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { Button } from '../ui/Button';\n\nconst Header = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  return (\n    <header className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex justify-between items-center\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"group flex items-center space-x-3 space-x-reverse\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-navy to-teal\">\n                دوزان\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:block\">\n            <ul className=\"flex space-x-8 space-x-reverse\">\n              <li>\n                <Link href=\"/\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الرئيسية\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  المشاريع\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الحرفيون\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  من نحن\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الأسئلة الشائعة\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  اتصل بنا\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n            </ul>\n          </nav>\n\n          {/* Desktop Buttons */}\n          <div className=\"hidden lg:flex items-center space-x-4 space-x-reverse\">\n            <Link href=\"/login\">\n              <Button variant=\"outline\" size=\"sm\" className=\"border-navy text-navy hover:bg-navy hover:text-white transition-all duration-300\">\n                تسجيل الدخول\n              </Button>\n            </Link>\n            <Link href=\"/register\">\n              <Button size=\"sm\" className=\"bg-gradient-to-r from-navy to-teal hover:from-teal hover:to-navy text-white shadow-lg hover:shadow-xl transition-all duration-300\">\n                إنشاء حساب\n              </Button>\n            </Link>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300\"\n            aria-label=\"فتح القائمة\"\n          >\n            <svg className=\"w-6 h-6 text-navy\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              {isMobileMenuOpen ? (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              ) : (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden mt-4 py-4 border-t border-gray-200\">\n            <nav className=\"space-y-4\">\n              <Link href=\"/\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الرئيسية\n              </Link>\n              <Link href=\"/jobs\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                المشاريع\n              </Link>\n              <Link href=\"/craftsmen\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الحرفيون\n              </Link>\n              <Link href=\"/about\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                من نحن\n              </Link>\n              <Link href=\"/faq\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الأسئلة الشائعة\n              </Link>\n              <Link href=\"/contact\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                اتصل بنا\n              </Link>\n            </nav>\n            <div className=\"mt-6 space-y-3\">\n              <Link href=\"/login\" className=\"block\">\n                <Button variant=\"outline\" className=\"w-full border-navy text-navy hover:bg-navy hover:text-white\">\n                  تسجيل الدخول\n                </Button>\n              </Link>\n              <Link href=\"/register\" className=\"block\">\n                <Button className=\"w-full bg-gradient-to-r from-navy to-teal text-white\">\n                  إنشاء حساب\n                </Button>\n              </Link>\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMA,MAAM,SAAS;;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDAA2L;;;;;;kDAG1M,6LAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;;;;;;sCAO1G,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAA0F;8DAEjH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;gDAA0F;8DAErH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;;gDAA0F;8DAE1H,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;;gDAA0F;8DAEtH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAO,WAAU;;gDAA0F;8DAEpH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;gDAA0F;8DAExH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;wCAAK,WAAU;kDAAmF;;;;;;;;;;;8CAInI,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;kDAAoI;;;;;;;;;;;;;;;;;sCAOpK,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC;gCAAI,WAAU;gCAAoB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC1E,iCACC,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;yDAErE,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAO5E,kCACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAiF;;;;;;8CAG1G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAiF;;;;;;8CAG9G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAiF;;;;;;8CAGnH,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiF;;;;;;8CAG/G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAO,WAAU;8CAAiF;;;;;;8CAG7G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiF;;;;;;;;;;;;sCAInH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAC5B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;kDAA8D;;;;;;;;;;;8CAIpG,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAY,WAAU;8CAC/B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;kDAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzF;GAnIM;KAAA;uCAqIS"}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden\">\n      {/* خلفية هندسية متناسقة */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl\"></div>\n      </div>\n\n      {/* شبكة نقطية ناعمة */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',\n          backgroundSize: '30px 30px'\n        }}></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-16 relative z-10\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Logo and Description */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n              <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center text-white text-2xl font-bold shadow-lg\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-white\">\n                دوزان\n              </span>\n            </div>\n            <p className=\"text-white/90 leading-relaxed mb-6\">\n              منصة تربط بين أصحاب المشاريع والحرفيين المهرة لتنفيذ المشاريع بكفاءة وجودة عالية في جميع أنحاء سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              روابط سريعة\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الرئيسية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  المشاريع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الحرفيون\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  من نحن\n                </Link>\n              </li>\n            </ul>\n          </div>\n          {/* Support */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              الدعم\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/contact\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  اتصل بنا\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  شروط الاستخدام\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              تواصل معنا\n            </h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">البريد الإلكتروني</p>\n                  <a href=\"mailto:<EMAIL>\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">الهاتف</p>\n                  <a href=\"tel:+963112345678\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    +963 11 234 5678\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">العنوان</p>\n                  <p className=\"text-white font-medium\">دمشق، سوريا</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"mt-12 pt-8 border-t border-white/20\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-white/80 text-sm\">\n                &copy; {new Date().getFullYear()} دوزان. جميع الحقوق محفوظة.\n              </p>\n              <p className=\"text-white/60 text-xs mt-1\">\n                صُنع بـ ❤️ في سوريا\n              </p>\n            </div>\n\n            <div className=\"flex items-center space-x-6 space-x-reverse\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">2500+</div>\n                <div className=\"text-white/70 text-xs\">حرفي</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">15000+</div>\n                <div className=\"text-white/70 text-xs\">مشروع</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">98%</div>\n                <div className=\"text-white/70 text-xs\">رضا</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA6H;;;;;;0DAG5I,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAIlD,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAyQ,UAAS;;;;;;;;;;;;;;;;0DAGjT,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA8jD,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO1mD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;;sEACvB,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;;sEAC3B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;;sEAChC,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAO3H,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAO,WAAU;;sEAC1B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3H,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,MAAK;gEAAuB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAM3H,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,MAAK;gEAAoB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAMxH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EACjE,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;8EACrE,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;gDAAwB;gDAC3B,IAAI,OAAO,WAAW;gDAAG;;;;;;;sDAEnC,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;KA5MM;uCA8MS"}}, {"offset": {"line": 1465, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1471, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout = ({ children }: MainLayoutProps) => {\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      <Header />\n      <main className=\"flex-grow\">{children}</main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB;IAC/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAK,WAAU;0BAAa;;;;;;0BAC7B,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;KARM;uCAUS"}}, {"offset": {"line": 1517, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1523, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-white shadow-sm transition-all duration-200 hover:shadow-md',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('font-semibold leading-none tracking-tight text-navy', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-600', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACpE,GAAG,KAAK;kBAER;;;;;;;AAGL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;;AAGL,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG"}}, {"offset": {"line": 1627, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1633, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Badge.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',\n  {\n    variants: {\n      variant: {\n        default: 'border-transparent bg-navy text-white hover:bg-navy/80',\n        secondary: 'border-transparent bg-teal text-white hover:bg-teal/80',\n        destructive: 'border-transparent bg-red-500 text-white hover:bg-red-500/80',\n        outline: 'text-navy border-navy',\n        success: 'border-transparent bg-green-500 text-white hover:bg-green-500/80',\n        warning: 'border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80',\n        info: 'border-transparent bg-skyblue text-navy hover:bg-skyblue/80',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  }\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE;KAJS"}}, {"offset": {"line": 1679, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1685, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/DateDisplay.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { formatDate } from '@/lib/utils';\n\ninterface DateDisplayProps {\n  date: string;\n  className?: string;\n}\n\nconst DateDisplay: React.FC<DateDisplayProps> = ({ date, className = '' }) => {\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  // عرض placeholder أثناء التحميل لتجنب مشاكل hydration\n  if (!mounted) {\n    return <span className={className}>تحميل...</span>;\n  }\n\n  return <span className={className}>{formatDate(date)}</span>;\n};\n\nexport default DateDisplay;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUA,MAAM,cAA0C,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,EAAE;;IACvE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;QACb;gCAAG,EAAE;IAEL,sDAAsD;IACtD,IAAI,CAAC,SAAS;QACZ,qBAAO,6LAAC;YAAK,WAAW;sBAAW;;;;;;IACrC;IAEA,qBAAO,6LAAC;QAAK,WAAW;kBAAY,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;;;;;;AACjD;GAbM;KAAA;uCAeS"}}, {"offset": {"line": 1732, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1738, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/jobs/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport MainLayout from '@/components/layout/MainLayout';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { Badge } from '@/components/ui/Badge';\nimport { Button } from '@/components/ui/Button';\nimport { formatDate } from '@/lib/utils';\nimport DateDisplay from '@/components/ui/DateDisplay';\nimport Dropdown from '@/components/ui/Dropdown';\n\ninterface Job {\n  id: number;\n  title: string;\n  description: string;\n  category: string;\n  location: string;\n  budget: number;\n  deadline: string;\n  postedAt: string;\n  offersCount: number;\n  status: 'open' | 'in_progress' | 'completed';\n  skills: string[];\n  images: string[];\n  client: {\n    name: string;\n    rating: number;\n    reviewsCount: number;\n  };\n}\n\nconst JobsPage = () => {\n  const [selectedCategory, setSelectedCategory] = useState('all');\n  const [sortBy, setSortBy] = useState('newest');\n\n  const categories = [\n    { value: 'all', label: 'جميع الفئات', icon: '🔍' },\n    { value: 'carpentry', label: 'النجارة', icon: '🪚' },\n    { value: 'plumbing', label: 'السباكة', icon: '🔧' },\n    { value: 'electrical', label: 'الكهرباء', icon: '⚡' },\n    { value: 'painting', label: 'الدهان', icon: '🖌️' },\n    { value: 'construction', label: 'البناء', icon: '🧱' },\n    { value: 'hvac', label: 'التكييف', icon: '❄️' },\n    { value: 'metalwork', label: 'الحدادة', icon: '🔨' },\n    { value: 'landscaping', label: 'تنسيق الحدائق', icon: '🌱' },\n  ];\n\n  const sortOptions = [\n    { value: 'newest', label: 'الأحدث', icon: '🕒' },\n    { value: 'budget_high', label: 'الميزانية (الأعلى أولاً)', icon: '💰' },\n    { value: 'budget_low', label: 'الميزانية (الأقل أولاً)', icon: '💵' },\n    { value: 'deadline', label: 'الموعد النهائي', icon: '📅' },\n  ];\n\n  const jobs: Job[] = [\n    {\n      id: 1,\n      title: 'تصميم وتنفيذ خزائن مطبخ خشبية عصرية',\n      description: 'مطلوب نجار محترف لتصميم وتنفيذ خزائن مطبخ خشبية بتصميم عصري وعملي. المطبخ بمساحة 12 متر مربع ويحتاج إلى خزائن علوية وسفلية مع جزيرة وسطية.',\n      category: 'carpentry',\n      location: 'دمشق، المزة',\n      budget: 350000,\n      deadline: '2024-03-15',\n      postedAt: '2024-01-15',\n      offersCount: 8,\n      status: 'open',\n      skills: ['نجارة', 'تصميم', 'تركيب'],\n      images: [\n        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400',\n        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'\n      ],\n      client: {\n        name: 'أحمد محمد',\n        rating: 4.8,\n        reviewsCount: 12\n      }\n    },\n    {\n      id: 2,\n      title: 'إصلاح نظام السباكة الرئيسي للمنزل',\n      description: 'يحتاج المنزل إلى إصلاح شامل لنظام السباكة الرئيسي بما في ذلك تغيير الأنابيب القديمة وإصلاح التسريبات وتركيب صنابير جديدة.',\n      category: 'plumbing',\n      location: 'حلب، الفرقان',\n      budget: 180000,\n      deadline: '2024-02-28',\n      postedAt: '2024-01-20',\n      offersCount: 5,\n      status: 'open',\n      skills: ['سباكة', 'إصلاح', 'تركيب'],\n      images: [\n        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',\n        'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400'\n      ],\n      client: {\n        name: 'فاطمة أحمد',\n        rating: 4.9,\n        reviewsCount: 8\n      }\n    },\n    {\n      id: 3,\n      title: 'تركيب نظام كهربائي كامل لشقة جديدة',\n      description: 'مطلوب كهربائي محترف لتركيب نظام كهربائي كامل لشقة جديدة بمساحة 120 متر مربع. يشمل العمل التمديدات والإضاءة واللوحة الكهربائية.',\n      category: 'electrical',\n      location: 'دمشق، جرمانا',\n      budget: 220000,\n      deadline: '2024-03-10',\n      postedAt: '2024-01-18',\n      offersCount: 12,\n      status: 'open',\n      skills: ['كهرباء', 'تمديدات', 'إضاءة'],\n      images: [\n        'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=400',\n        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'\n      ],\n      client: {\n        name: 'محمد علي',\n        rating: 4.7,\n        reviewsCount: 15\n      }\n    },\n    {\n      id: 4,\n      title: 'دهان شقة سكنية بالكامل',\n      description: 'مطلوب دهان محترف لدهان شقة سكنية بمساحة 100 متر مربع. العمل يشمل تحضير الجدران والدهان بألوان حديثة حسب التصميم المطلوب.',\n      category: 'painting',\n      location: 'حمص، الوعر',\n      budget: 120000,\n      deadline: '2024-02-25',\n      postedAt: '2024-01-22',\n      offersCount: 6,\n      status: 'open',\n      skills: ['دهان', 'تحضير', 'تصميم'],\n      images: [\n        'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400',\n        'https://images.unsplash.com/photo-1581858726788-75bc0f6a952d?w=400'\n      ],\n      client: {\n        name: 'سارة خالد',\n        rating: 4.6,\n        reviewsCount: 9\n      }\n    }\n  ];\n\n  const filteredJobs = jobs.filter(job =>\n    selectedCategory === 'all' || job.category === selectedCategory\n  );\n\n  const sortedJobs = [...filteredJobs].sort((a, b) => {\n    switch (sortBy) {\n      case 'budget_high':\n        return b.budget - a.budget;\n      case 'budget_low':\n        return a.budget - b.budget;\n      case 'deadline':\n        return new Date(a.deadline).getTime() - new Date(b.deadline).getTime();\n      default:\n        return new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime();\n    }\n  });\n\n\n\n  const getStatusBadge = (status: string) => {\n    switch (status) {\n      case 'open':\n        return <Badge variant=\"success\">مفتوح</Badge>;\n      case 'in_progress':\n        return <Badge variant=\"warning\">قيد التنفيذ</Badge>;\n      case 'completed':\n        return <Badge variant=\"info\">مكتمل</Badge>;\n      default:\n        return <Badge variant=\"outline\">{status}</Badge>;\n    }\n  };\n\n  return (\n    <MainLayout>\n      <div className=\"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white relative overflow-hidden\">\n        {/* خلفية هندسية متناسقة */}\n        <div className=\"absolute inset-0 opacity-5\">\n          <div className=\"absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl\"></div>\n          <div className=\"absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl\"></div>\n          <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl\"></div>\n        </div>\n\n        {/* شبكة نقطية ناعمة */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"w-full h-full\" style={{\n            backgroundImage: 'radial-gradient(circle, #567C8D 1px, transparent 1px)',\n            backgroundSize: '50px 50px'\n          }}></div>\n        </div>\n\n        <div className=\"container mx-auto px-4 py-8 relative z-10\">\n          {/* Header */}\n          <div className=\"text-center mb-12\">\n            <div className=\"inline-block bg-gradient-to-r from-navy to-teal text-white px-8 py-3 rounded-full text-sm font-medium mb-6 shadow-lg\">\n              <span className=\"text-lg\">💼</span>\n              <span className=\"mr-2\">فرص عمل متاحة</span>\n            </div>\n            <h1 className=\"text-4xl md:text-5xl lg:text-6xl font-bold text-navy mb-6 leading-tight\">\n              المشاريع\n              <span className=\"block text-transparent bg-clip-text bg-gradient-to-r from-teal via-navy to-teal\">\n                المتاحة\n              </span>\n            </h1>\n            <p className=\"text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed\">\n              اكتشف المشاريع المتاحة وقدم عروضك للحصول على فرص عمل جديدة مع أفضل العملاء\n            </p>\n            <div className=\"mt-8 w-24 h-1 bg-gradient-to-r from-teal to-navy mx-auto rounded-full\"></div>\n          </div>\n\n          {/* Filters */}\n          <div className=\"bg-white rounded-2xl shadow-lg p-6 mb-8\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  فلترة حسب الفئة\n                </label>\n                <select\n                  value={selectedCategory}\n                  onChange={(e) => setSelectedCategory(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent\"\n                >\n                  {categories.map(category => (\n                    <option key={category.id} value={category.id}>\n                      {category.name}\n                    </option>\n                  ))}\n                </select>\n              </div>\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                  ترتيب حسب\n                </label>\n                <select\n                  value={sortBy}\n                  onChange={(e) => setSortBy(e.target.value)}\n                  className=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent\"\n                >\n                  <option value=\"newest\">الأحدث</option>\n                  <option value=\"budget_high\">الميزانية (الأعلى أولاً)</option>\n                  <option value=\"budget_low\">الميزانية (الأقل أولاً)</option>\n                  <option value=\"deadline\">الموعد النهائي</option>\n                </select>\n              </div>\n            </div>\n          </div>\n\n          {/* Jobs Grid */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n            {sortedJobs.map((job) => (\n              <Card key={job.id} className=\"hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm\">\n                <CardHeader>\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <CardTitle className=\"text-xl leading-tight\">{job.title}</CardTitle>\n                    {getStatusBadge(job.status)}\n                  </div>\n                  <div className=\"flex items-center text-sm text-gray-500 space-x-4 space-x-reverse\">\n                    <span className=\"flex items-center\">\n                      <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                      </svg>\n                      {job.location}\n                    </span>\n                    <span className=\"flex items-center\">\n                      <svg className=\"w-4 h-4 ml-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                      <DateDisplay date={job.postedAt} />\n                    </span>\n                  </div>\n                </CardHeader>\n\n                <CardContent>\n                  {/* صور المشروع */}\n                  {job.images && job.images.length > 0 && (\n                    <div className=\"mb-4\">\n                      <div className=\"grid grid-cols-2 gap-2 h-32\">\n                        {job.images.slice(0, 2).map((image, index) => (\n                          <img\n                            key={index}\n                            src={image}\n                            alt={`صورة المشروع ${index + 1}`}\n                            className=\"w-full h-full object-cover rounded-lg border border-gray-200 hover:scale-105 transition-transform duration-300\"\n                          />\n                        ))}\n                        {job.images.length > 2 && (\n                          <div className=\"relative\">\n                            <img\n                              src={job.images[2]}\n                              alt=\"المزيد من الصور\"\n                              className=\"w-full h-full object-cover rounded-lg border border-gray-200\"\n                            />\n                            <div className=\"absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center hover:bg-opacity-40 transition-all duration-300\">\n                              <span className=\"text-white text-sm font-medium\">\n                                +{job.images.length - 2} صور\n                              </span>\n                            </div>\n                          </div>\n                        )}\n                      </div>\n                    </div>\n                  )}\n\n                  <p className=\"text-gray-700 mb-4 line-clamp-3\">{job.description}</p>\n\n                  <div className=\"flex flex-wrap gap-2 mb-4\">\n                    {job.skills.map((skill, index) => (\n                      <Badge key={index} variant=\"outline\" className=\"text-xs\">\n                        {skill}\n                      </Badge>\n                    ))}\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-4 mb-4\">\n                    <div className=\"bg-gradient-to-r from-green-50 to-green-100 p-3 rounded-lg\">\n                      <div className=\"text-sm text-green-600 font-medium\">الميزانية</div>\n                      <div className=\"text-lg font-bold text-green-700\">\n                        {job.budget.toLocaleString()} ل.س\n                      </div>\n                    </div>\n                    <div className=\"bg-gradient-to-r from-blue-50 to-blue-100 p-3 rounded-lg\">\n                      <div className=\"text-sm text-blue-600 font-medium\">الموعد النهائي</div>\n                      <div className=\"text-sm font-bold text-blue-700\">\n                        <DateDisplay date={job.deadline} />\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center text-sm text-gray-600\">\n                      <span className=\"flex items-center\">\n                        <svg className=\"w-4 h-4 ml-1 text-yellow-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                          <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                        </svg>\n                        {job.client.rating} ({job.client.reviewsCount} تقييم)\n                      </span>\n                      <span className=\"mr-4 text-teal font-medium\">{job.offersCount} عرض</span>\n                    </div>\n                    <Link href={`/jobs/${job.id}`}>\n                      <Button size=\"sm\" className=\"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90\">\n                        عرض التفاصيل\n                      </Button>\n                    </Link>\n                  </div>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          {/* Load More */}\n          <div className=\"text-center mt-12\">\n            <Button variant=\"outline\" size=\"lg\" className=\"px-8\">\n              تحميل المزيد من المشاريع\n            </Button>\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  );\n};\n\nexport default JobsPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;;;AATA;;;;;;;;AAgCA,MAAM,WAAW;;IACf,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,aAAa;QACjB;YAAE,OAAO;YAAO,OAAO;YAAe,MAAM;QAAK;QACjD;YAAE,OAAO;YAAa,OAAO;YAAW,MAAM;QAAK;QACnD;YAAE,OAAO;YAAY,OAAO;YAAW,MAAM;QAAK;QAClD;YAAE,OAAO;YAAc,OAAO;YAAY,MAAM;QAAI;QACpD;YAAE,OAAO;YAAY,OAAO;YAAU,MAAM;QAAM;QAClD;YAAE,OAAO;YAAgB,OAAO;YAAU,MAAM;QAAK;QACrD;YAAE,OAAO;YAAQ,OAAO;YAAW,MAAM;QAAK;QAC9C;YAAE,OAAO;YAAa,OAAO;YAAW,MAAM;QAAK;QACnD;YAAE,OAAO;YAAe,OAAO;YAAiB,MAAM;QAAK;KAC5D;IAED,MAAM,cAAc;QAClB;YAAE,OAAO;YAAU,OAAO;YAAU,MAAM;QAAK;QAC/C;YAAE,OAAO;YAAe,OAAO;YAA4B,MAAM;QAAK;QACtE;YAAE,OAAO;YAAc,OAAO;YAA2B,MAAM;QAAK;QACpE;YAAE,OAAO;YAAY,OAAO;YAAkB,MAAM;QAAK;KAC1D;IAED,MAAM,OAAc;QAClB;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,QAAQ;YACR,UAAU;YACV,UAAU;YACV,aAAa;YACb,QAAQ;YACR,QAAQ;gBAAC;gBAAS;gBAAS;aAAQ;YACnC,QAAQ;gBACN;gBACA;aACD;YACD,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,cAAc;YAChB;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,QAAQ;YACR,UAAU;YACV,UAAU;YACV,aAAa;YACb,QAAQ;YACR,QAAQ;gBAAC;gBAAS;gBAAS;aAAQ;YACnC,QAAQ;gBACN;gBACA;aACD;YACD,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,cAAc;YAChB;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,QAAQ;YACR,UAAU;YACV,UAAU;YACV,aAAa;YACb,QAAQ;YACR,QAAQ;gBAAC;gBAAU;gBAAW;aAAQ;YACtC,QAAQ;gBACN;gBACA;aACD;YACD,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,cAAc;YAChB;QACF;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,UAAU;YACV,QAAQ;YACR,UAAU;YACV,UAAU;YACV,aAAa;YACb,QAAQ;YACR,QAAQ;gBAAC;gBAAQ;gBAAS;aAAQ;YAClC,QAAQ;gBACN;gBACA;aACD;YACD,QAAQ;gBACN,MAAM;gBACN,QAAQ;gBACR,cAAc;YAChB;QACF;KACD;IAED,MAAM,eAAe,KAAK,MAAM,CAAC,CAAA,MAC/B,qBAAqB,SAAS,IAAI,QAAQ,KAAK;IAGjD,MAAM,aAAa;WAAI;KAAa,CAAC,IAAI,CAAC,CAAC,GAAG;QAC5C,OAAQ;YACN,KAAK;gBACH,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;YAC5B,KAAK;gBACH,OAAO,EAAE,MAAM,GAAG,EAAE,MAAM;YAC5B,KAAK;gBACH,OAAO,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO;YACtE;gBACE,OAAO,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,QAAQ,EAAE,OAAO;QACxE;IACF;IAIA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAO;;;;;;YAC/B;gBACE,qBAAO,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BAAW;;;;;;QACrC;IACF;IAEA,qBACE,6LAAC,6IAAA,CAAA,UAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAgB,OAAO;4BACpC,iBAAiB;4BACjB,gBAAgB;wBAClB;;;;;;;;;;;8BAGF,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAO;;;;;;;;;;;;8CAEzB,6LAAC;oCAAG,WAAU;;wCAA0E;sDAEtF,6LAAC;4CAAK,WAAU;sDAAkF;;;;;;;;;;;;8CAIpG,6LAAC;oCAAE,WAAU;8CAA0D;;;;;;8CAGvE,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAIjB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,WAAU;0DAET,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC;wDAAyB,OAAO,SAAS,EAAE;kEACzC,SAAS,IAAI;uDADH,SAAS,EAAE;;;;;;;;;;;;;;;;kDAM9B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,UAAU,EAAE,MAAM,CAAC,KAAK;gDACzC,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAc;;;;;;kEAC5B,6LAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,6LAAC;wDAAO,OAAM;kEAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOjC,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,oBACf,6LAAC,mIAAA,CAAA,OAAI;oCAAc,WAAU;;sDAC3B,6LAAC,mIAAA,CAAA,aAAU;;8DACT,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mIAAA,CAAA,YAAS;4DAAC,WAAU;sEAAyB,IAAI,KAAK;;;;;;wDACtD,eAAe,IAAI,MAAM;;;;;;;8DAE5B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;8EACd,6LAAC;oEAAI,WAAU;oEAAe,MAAK;oEAAO,QAAO;oEAAe,SAAQ;;sFACtE,6LAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;sFACrE,6LAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;;gEAEtE,IAAI,QAAQ;;;;;;;sEAEf,6LAAC;4DAAK,WAAU;;8EACd,6LAAC;oEAAI,WAAU;oEAAe,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACtE,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;8EAEvE,6LAAC,0IAAA,CAAA,UAAW;oEAAC,MAAM,IAAI,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;sDAKrC,6LAAC,mIAAA,CAAA,cAAW;;gDAET,IAAI,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,GAAG,mBACjC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;4DACZ,IAAI,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAClC,6LAAC;oEAEC,KAAK;oEACL,KAAK,CAAC,aAAa,EAAE,QAAQ,GAAG;oEAChC,WAAU;mEAHL;;;;;4DAMR,IAAI,MAAM,CAAC,MAAM,GAAG,mBACnB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,KAAK,IAAI,MAAM,CAAC,EAAE;wEAClB,KAAI;wEACJ,WAAU;;;;;;kFAEZ,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAK,WAAU;;gFAAiC;gFAC7C,IAAI,MAAM,CAAC,MAAM,GAAG;gFAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAStC,6LAAC;oDAAE,WAAU;8DAAmC,IAAI,WAAW;;;;;;8DAE/D,6LAAC;oDAAI,WAAU;8DACZ,IAAI,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACtB,6LAAC,oIAAA,CAAA,QAAK;4DAAa,SAAQ;4DAAU,WAAU;sEAC5C;2DADS;;;;;;;;;;8DAMhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqC;;;;;;8EACpD,6LAAC;oEAAI,WAAU;;wEACZ,IAAI,MAAM,CAAC,cAAc;wEAAG;;;;;;;;;;;;;sEAGjC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;8EACnD,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC,0IAAA,CAAA,UAAW;wEAAC,MAAM,IAAI,QAAQ;;;;;;;;;;;;;;;;;;;;;;;8DAKrC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;sFACd,6LAAC;4EAAI,WAAU;4EAA+B,MAAK;4EAAe,SAAQ;sFACxE,cAAA,6LAAC;gFAAK,GAAE;;;;;;;;;;;wEAET,IAAI,MAAM,CAAC,MAAM;wEAAC;wEAAG,IAAI,MAAM,CAAC,YAAY;wEAAC;;;;;;;8EAEhD,6LAAC;oEAAK,WAAU;;wEAA8B,IAAI,WAAW;wEAAC;;;;;;;;;;;;;sEAEhE,6LAAC,+JAAA,CAAA,UAAI;4DAAC,MAAM,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE;sEAC3B,cAAA,6LAAC,qIAAA,CAAA,SAAM;gEAAC,MAAK;gEAAK,WAAU;0EAAyE;;;;;;;;;;;;;;;;;;;;;;;;mCA1FlG,IAAI,EAAE;;;;;;;;;;sCAqGrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;0CAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjE;GA7UM;KAAA;uCA+US"}}, {"offset": {"line": 2631, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}