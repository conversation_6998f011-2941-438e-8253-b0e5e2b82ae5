'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import MainLayout from '@/components/layout/MainLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useMyProjects, useOffers } from '@/hooks/useApi';

const ClientDashboard = () => {
  const { data: session } = useSession();
  const [activeFilter, setActiveFilter] = useState('all');

  // جلب البيانات من API
  const { data: projectsData, loading: projectsLoading, error: projectsError } = useMyProjects();
  const { data: offersData, loading: offersLoading } = useOffers();

  const projects = projectsData?.projects || [];
  const offers = offersData?.offers || [];

  // حساب الإحصائيات من البيانات الحقيقية
  const activeProjects = projects.filter(p => p.status === 'OPEN' || p.status === 'IN_PROGRESS');
  const completedProjects = projects.filter(p => p.status === 'COMPLETED');
  const totalOffers = offers.length;

  const stats = [
    {
      title: 'مشاريعي النشطة',
      value: activeProjects.length.toString(),
      icon: '📋',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: `من ${projects.length} إجمالي`
    },
    {
      title: 'العروض المستلمة',
      value: totalOffers.toString(),
      icon: '📥',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: 'إجمالي العروض'
    },
    {
      title: 'المشاريع المكتملة',
      value: completedProjects.length.toString(),
      icon: '✅',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: 'مشاريع منجزة'
    },
    {
      title: 'إجمالي المشاريع',
      value: projects.length.toString(),
      icon: '💰',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      change: 'جميع المشاريع'
    }
  ];

  // تحويل بيانات المشاريع من API إلى تنسيق الواجهة
  const formattedProjects = projects.map(project => ({
    id: project.id,
    title: project.title,
    description: project.description,
    status: getStatusInArabic(project.status),
    budget: project.budget,
    offers: 0, // سيتم حسابها من العروض
    deadline: new Date(project.deadline).toLocaleDateString('ar-SA'),
    category: project.category,
    priority: getPriorityInArabic(project.priority)
  }));

  // دوال مساعدة للترجمة
  function getStatusInArabic(status: string) {
    switch (status) {
      case 'OPEN': return 'نشط';
      case 'IN_PROGRESS': return 'جاري التنفيذ';
      case 'COMPLETED': return 'مكتمل';
      case 'CANCELLED': return 'ملغي';
      default: return 'في الانتظار';
    }
  }

  function getPriorityInArabic(priority: string) {
    switch (priority) {
      case 'HIGH': return 'عالية';
      case 'MEDIUM': return 'متوسطة';
      case 'LOW': return 'منخفضة';
      default: return 'متوسطة';
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'نشط':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'في الانتظار':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'مكتمل':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'جاري التنفيذ':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'عالية':
        return 'text-red-600';
      case 'متوسطة':
        return 'text-yellow-600';
      case 'منخفضة':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  const filteredProjects = formattedProjects.filter(project => {
    if (activeFilter === 'all') return true;
    return project.status === activeFilter;
  });

  // معالجة حالة التحميل
  if (projectsLoading) {
    return (
      <MainLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal mx-auto mb-4"></div>
            <p className="text-gray-600">جاري تحميل لوحة التحكم...</p>
          </div>
        </div>
      </MainLayout>
    );
  }

  const quickActions = [
    {
      title: 'إنشاء مشروع جديد',
      description: 'ابدأ مشروعك الجديد واحصل على عروض من الحرفيين',
      icon: '➕',
      href: '/projects/create',
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: 'تصفح الحرفيين',
      description: 'ابحث عن حرفيين مؤهلين في منطقتك',
      icon: '👥',
      href: '/craftsmen',
      color: 'from-green-500 to-green-600'
    },
    {
      title: 'العروض المستلمة',
      description: 'راجع العروض الجديدة على مشاريعك',
      icon: '📥',
      href: '/offers/received',
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: 'الرسائل',
      description: 'تواصل مع الحرفيين ومتابعة المحادثات',
      icon: '💬',
      href: '/messages',
      color: 'from-orange-500 to-orange-600'
    }
  ];

  return (
    <ProtectedRoute requiredRoles="client">
      <MainLayout>
        <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white relative overflow-hidden">
          {/* خلفية هندسية متناسقة */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl"></div>
            <div className="absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl"></div>
          </div>

          {/* شبكة نقطية ناعمة */}
          <div className="absolute inset-0 opacity-10">
            <div className="w-full h-full" style={{
              backgroundImage: 'radial-gradient(circle, #567C8D 1px, transparent 1px)',
              backgroundSize: '50px 50px'
            }}></div>
          </div>

          <div className="container mx-auto px-4 py-8 relative z-10">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-navy mb-2">
                    لوحة تحكم العميل
                  </h1>
                  <p className="text-gray-600 text-lg">
                    مرحباً {user?.name}، إدارة مشاريعك والعروض المستلمة
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-gray-500 text-sm">آخر تحديث</p>
                  <p className="text-navy font-medium">منذ 5 دقائق</p>
                </div>
              </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {stats.map((stat, index) => (
                <Card key={index} className="border-0 bg-white/90 backdrop-blur-md shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-600 text-sm mb-1">{stat.title}</p>
                        <p className="text-2xl font-bold text-navy">{stat.value}</p>
                        <p className="text-xs text-gray-500 mt-1">{stat.change}</p>
                      </div>
                      <div className={`text-3xl ${stat.color}`}>
                        {stat.icon}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Quick Actions */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-navy mb-6">الإجراءات السريعة</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {quickActions.map((action, index) => (
                  <Link key={index} href={action.href}>
                    <Card className="border-0 bg-white/90 backdrop-blur-md shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer group">
                      <CardContent className="p-6 text-center">
                        <div className={`w-16 h-16 bg-gradient-to-r ${action.color} rounded-full flex items-center justify-center text-white text-2xl mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                          {action.icon}
                        </div>
                        <h3 className="text-lg font-semibold text-navy mb-2">{action.title}</h3>
                        <p className="text-gray-600 text-sm">{action.description}</p>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </div>

            {/* Projects Section */}
            <div className="mb-8">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-2xl font-bold text-navy">مشاريعي</h2>
                <div className="flex space-x-2 space-x-reverse">
                  {['all', 'نشط', 'في الانتظار', 'جاري التنفيذ', 'مكتمل'].map((filter) => (
                    <button
                      key={filter}
                      onClick={() => setActiveFilter(filter)}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                        activeFilter === filter
                          ? 'bg-navy text-white'
                          : 'bg-white text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      {filter === 'all' ? 'الكل' : filter}
                    </button>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {filteredProjects.map((project) => (
                  <Card key={project.id} className="border-0 bg-white/90 backdrop-blur-md shadow-lg hover:shadow-xl transition-all duration-300">
                    <CardContent className="p-6">
                      <div className="flex justify-between items-start mb-4">
                        <div className="flex-1">
                          <h3 className="text-lg font-semibold text-navy mb-2">{project.title}</h3>
                          <p className="text-gray-600 text-sm mb-3">{project.description}</p>
                        </div>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(project.status)}`}>
                          {project.status}
                        </span>
                      </div>

                      <div className="grid grid-cols-2 gap-4 mb-4">
                        <div>
                          <p className="text-gray-500 text-xs">الميزانية</p>
                          <p className="font-semibold text-navy">{project.budget}</p>
                        </div>
                        <div>
                          <p className="text-gray-500 text-xs">العروض</p>
                          <p className="font-semibold text-navy">{project.offers} عرض</p>
                        </div>
                        <div>
                          <p className="text-gray-500 text-xs">الموعد النهائي</p>
                          <p className="font-semibold text-navy">{project.deadline}</p>
                        </div>
                        <div>
                          <p className="text-gray-500 text-xs">الأولوية</p>
                          <p className={`font-semibold ${getPriorityColor(project.priority)}`}>{project.priority}</p>
                        </div>
                      </div>

                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {project.category}
                        </span>
                        <div className="flex space-x-2 space-x-reverse">
                          <Link href={`/projects/${project.id}`}>
                            <Button size="sm" variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                              عرض التفاصيل
                            </Button>
                          </Link>
                          {project.status === 'نشط' && (
                            <Link href={`/projects/${project.id}/offers`}>
                              <Button size="sm" className="bg-gradient-to-r from-navy to-teal">
                                العروض ({project.offers})
                              </Button>
                            </Link>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredProjects.length === 0 && (
                <Card className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                  <CardContent className="p-12 text-center">
                    <div className="text-6xl mb-4">📋</div>
                    <h3 className="text-xl font-semibold text-navy mb-2">لا توجد مشاريع</h3>
                    <p className="text-gray-600 mb-6">لم يتم العثور على مشاريع بالفلتر المحدد</p>
                    <Link href="/projects/create">
                      <Button className="bg-gradient-to-r from-navy to-teal">
                        إنشاء مشروع جديد
                      </Button>
                    </Link>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default ClientDashboard;
