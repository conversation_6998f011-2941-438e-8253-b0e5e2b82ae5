'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

export default function TestApiPage() {
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const testEndpoint = async (endpoint: string, method: string = 'GET', body?: any) => {
    setLoading(true);
    setError('');
    setResults(null);

    try {
      const options: RequestInit = {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      if (body && method !== 'GET') {
        options.body = JSON.stringify(body);
      }

      const response = await fetch(endpoint, options);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'حدث خطأ');
      }

      setResults(data);
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testCases = [
    {
      name: 'جلب جميع المشاريع',
      endpoint: '/api/projects',
      method: 'GET'
    },
    {
      name: 'جلب مشروع محدد',
      endpoint: '/api/projects/project1',
      method: 'GET'
    },
    {
      name: 'جلب جميع العروض',
      endpoint: '/api/offers',
      method: 'GET'
    },
    {
      name: 'جلب عروض مشروع محدد',
      endpoint: '/api/offers?projectId=project1',
      method: 'GET'
    },
    {
      name: 'جلب جميع المستخدمين',
      endpoint: '/api/users',
      method: 'GET'
    },
    {
      name: 'جلب الحرفيين فقط',
      endpoint: '/api/users?role=CRAFTSMAN',
      method: 'GET'
    },
    {
      name: 'جلب مستخدم محدد',
      endpoint: '/api/users/craftsman1',
      method: 'GET'
    },
    {
      name: 'إنشاء مشروع جديد',
      endpoint: '/api/projects',
      method: 'POST',
      body: {
        title: 'مشروع اختبار',
        description: 'وصف المشروع التجريبي',
        category: 'اختبار',
        budget: '₺1,000 - ₺2,000',
        deadline: '2024-04-01',
        location: 'دمشق',
        priority: 'MEDIUM',
        clientId: 'client',
        materials: 'متوفرة',
        workType: 'جديد',
        requirements: 'لا توجد متطلبات خاصة'
      }
    },
    {
      name: 'تقديم عرض جديد',
      endpoint: '/api/offers',
      method: 'POST',
      body: {
        projectId: 'project1',
        craftsmanId: 'craftsman2',
        amount: 6500,
        currency: 'TRY',
        description: 'عرض تجريبي للاختبار',
        estimatedDuration: 10
      }
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-6xl mx-auto">
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="text-center text-2xl font-bold text-navy">
              اختبار API Routes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
              {testCases.map((testCase, index) => (
                <Button
                  key={index}
                  onClick={() => testEndpoint(testCase.endpoint, testCase.method, testCase.body)}
                  disabled={loading}
                  variant={testCase.method === 'GET' ? 'default' : 'outline'}
                  className="h-auto p-4 text-right"
                >
                  <div>
                    <div className="font-semibold">{testCase.name}</div>
                    <div className="text-xs opacity-75">
                      {testCase.method} {testCase.endpoint}
                    </div>
                  </div>
                </Button>
              ))}
            </div>

            {loading && (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal mx-auto mb-4"></div>
                <p>جاري التحميل...</p>
              </div>
            )}

            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <strong>خطأ:</strong> {error}
              </div>
            )}

            {results && (
              <div className="bg-gray-100 rounded-lg p-4">
                <h3 className="font-semibold mb-2">النتائج:</h3>
                <pre className="text-sm overflow-auto max-h-96 bg-white p-4 rounded border">
                  {JSON.stringify(results, null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>حالة الخدمات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span>NextAuth</span>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                  ✅ يعمل
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span>API Routes</span>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                  ✅ يعمل
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span>Mock Data</span>
                <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">
                  ✅ يعمل
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span>Database (PostgreSQL)</span>
                <span className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-sm">
                  ⚠️ معطل (استخدام Mock Data)
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
