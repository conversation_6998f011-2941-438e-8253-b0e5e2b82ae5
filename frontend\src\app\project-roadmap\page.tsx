'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

const ProjectRoadmapPage = () => {
  const [activeTab, setActiveTab] = useState('roadmap');

  const roadmapPhases = [
    {
      phase: 'المرحلة 1: الأساسيات والأمان',
      status: 'مكتملة 95%',
      color: 'green',
      tasks: [
        { task: 'إعداد Next.js + TypeScript', status: 'مكتمل', priority: 'عالية' },
        { task: 'تصميم قاعدة البيانات Prisma', status: 'مكتمل', priority: 'عالية' },
        { task: 'نظام الأمان المتقدم', status: 'مكتمل', priority: 'عالية' },
        { task: 'نظام المصادقة NextAuth', status: 'مكتمل', priority: 'عالية' },
        { task: 'نظام البريد الإلكتروني', status: 'مكتمل', priority: 'عالية' },
        { task: 'Middleware الأمني', status: 'مكتمل', priority: 'عالية' },
        { task: 'واجهات التسجيل المحسنة', status: 'مكتمل', priority: 'متوسطة' },
        { task: 'ربط قاعدة البيانات الحقيقية', status: 'قيد التطوير', priority: 'عالية' }
      ]
    },
    {
      phase: 'المرحلة 2: الواجهات الأساسية',
      status: 'مكتملة 80%',
      color: 'blue',
      tasks: [
        { task: 'الصفحة الرئيسية', status: 'مكتمل', priority: 'عالية' },
        { task: 'صفحة المشاريع', status: 'مكتمل', priority: 'عالية' },
        { task: 'صفحة إنشاء مشروع', status: 'مكتمل', priority: 'عالية' },
        { task: 'صفحة الحرفيين', status: 'مكتمل', priority: 'متوسطة' },
        { task: 'نظام التقييمات', status: 'مكتمل', priority: 'متوسطة' },
        { task: 'نظام رفع الملفات', status: 'مكتمل', priority: 'متوسطة' },
        { task: 'تحسين التصميم المتجاوب', status: 'يحتاج تحسين', priority: 'متوسطة' },
        { task: 'تحسين الأداء', status: 'يحتاج تحسين', priority: 'متوسطة' }
      ]
    },
    {
      phase: 'المرحلة 3: لوحات التحكم',
      status: 'مكتملة 70%',
      color: 'yellow',
      tasks: [
        { task: 'لوحة تحكم العميل', status: 'مكتمل', priority: 'عالية' },
        { task: 'لوحة تحكم الحرفي', status: 'مكتمل', priority: 'عالية' },
        { task: 'لوحة تحكم الإدارة', status: 'مكتمل', priority: 'متوسطة' },
        { task: 'نظام إدارة المشاريع', status: 'مكتمل', priority: 'عالية' },
        { task: 'نظام إدارة العروض', status: 'مكتمل', priority: 'عالية' },
        { task: 'إحصائيات ومؤشرات الأداء', status: 'يحتاج تحسين', priority: 'متوسطة' },
        { task: 'نظام الإشعارات', status: 'أساسي', priority: 'متوسطة' },
        { task: 'إعدادات الملف الشخصي', status: 'يحتاج تحسين', priority: 'متوسطة' }
      ]
    },
    {
      phase: 'المرحلة 4: الميزات المتقدمة',
      status: 'مكتملة 40%',
      color: 'orange',
      tasks: [
        { task: 'نظام الرسائل المباشرة', status: 'أساسي', priority: 'عالية' },
        { task: 'نظام الدفع الآمن', status: 'لم يبدأ', priority: 'عالية' },
        { task: 'نظام التتبع والتقارير', status: 'لم يبدأ', priority: 'متوسطة' },
        { task: 'نظام الضمانات', status: 'لم يبدأ', priority: 'متوسطة' },
        { task: 'تطبيق الهاتف المحمول', status: 'لم يبدأ', priority: 'منخفضة' },
        { task: 'نظام الذكاء الاصطناعي', status: 'لم يبدأ', priority: 'منخفضة' },
        { task: 'تحليلات متقدمة', status: 'لم يبدأ', priority: 'منخفضة' },
        { task: 'دعم متعدد اللغات', status: 'لم يبدأ', priority: 'منخفضة' }
      ]
    },
    {
      phase: 'المرحلة 5: النشر والتحسين',
      status: 'مكتملة 20%',
      color: 'red',
      tasks: [
        { task: 'إعداد بيئة الإنتاج', status: 'قيد التطوير', priority: 'عالية' },
        { task: 'اختبارات شاملة', status: 'جزئي', priority: 'عالية' },
        { task: 'تحسين الأداء للإنتاج', status: 'لم يبدأ', priority: 'عالية' },
        { task: 'إعداد المراقبة والتنبيهات', status: 'لم يبدأ', priority: 'متوسطة' },
        { task: 'دليل المستخدم', status: 'لم يبدأ', priority: 'متوسطة' },
        { task: 'دعم فني', status: 'لم يبدأ', priority: 'متوسطة' },
        { task: 'خطة التسويق', status: 'لم يبدأ', priority: 'منخفضة' },
        { task: 'تحليل المنافسين', status: 'لم يبدأ', priority: 'منخفضة' }
      ]
    }
  ];

  const criticalIssues = [
    {
      issue: 'قاعدة البيانات الحقيقية',
      severity: 'عالية',
      description: 'المشروع يستخدم بيانات وهمية، يحتاج ربط PostgreSQL حقيقي',
      impact: 'لا يمكن النشر للعامة بدونها',
      solution: 'ربط Railway/Supabase PostgreSQL وتطبيق migrations',
      timeline: '1-2 يوم'
    },
    {
      issue: 'تسجيل الدخول بـ Google',
      severity: 'متوسطة',
      description: 'يحتاج إعداد Google OAuth credentials',
      impact: 'ميزة مهمة لتسهيل التسجيل',
      solution: 'إنشاء مشروع Google Cloud وتكوين OAuth',
      timeline: '2-3 ساعات'
    },
    {
      issue: 'نظام البريد الإلكتروني',
      severity: 'متوسطة',
      description: 'يحتاج إعداد SMTP حقيقي لإرسال الرسائل',
      impact: 'لا يمكن تأكيد الحسابات أو إرسال إشعارات',
      solution: 'إعداد Gmail SMTP أو SendGrid',
      timeline: '1-2 ساعة'
    },
    {
      issue: 'تحسين الأداء',
      severity: 'متوسطة',
      description: 'بعض الصفحات بطيئة التحميل',
      impact: 'تجربة مستخدم سيئة خاصة مع الإنترنت البطيء',
      solution: 'تحسين الصور، lazy loading، code splitting',
      timeline: '1-2 يوم'
    },
    {
      issue: 'اختبارات شاملة',
      severity: 'متوسطة',
      description: 'لا توجد اختبارات آلية للكود',
      impact: 'صعوبة في اكتشاف الأخطاء قبل النشر',
      solution: 'كتابة unit tests و integration tests',
      timeline: '2-3 أيام'
    }
  ];

  const syrianOptimizations = [
    {
      optimization: 'دعم الليرة السورية',
      description: 'إضافة SYP كعملة أساسية مع تحويل للدولار',
      priority: 'عالية',
      effort: 'متوسط'
    },
    {
      optimization: 'تحسين للإنترنت البطيء',
      description: 'ضغط الصور، تقليل حجم الملفات، lazy loading',
      priority: 'عالية',
      effort: 'متوسط'
    },
    {
      optimization: 'مناطق سورية',
      description: 'قائمة شاملة بالمحافظات والمناطق السورية',
      priority: 'عالية',
      effort: 'منخفض'
    },
    {
      optimization: 'أرقام الهواتف السورية',
      description: 'التحقق من صحة أرقام الهواتف السورية',
      priority: 'متوسطة',
      effort: 'منخفض'
    },
    {
      optimization: 'مهن وحرف سورية',
      description: 'قائمة بالمهن والحرف الشائعة في سوريا',
      priority: 'متوسطة',
      effort: 'منخفض'
    },
    {
      optimization: 'دعم فني بالعربية',
      description: 'صفحات مساعدة وأسئلة شائعة باللغة العربية',
      priority: 'متوسطة',
      effort: 'متوسط'
    }
  ];

  const nextSteps = [
    {
      step: 'إكمال قاعدة البيانات',
      description: 'ربط PostgreSQL حقيقي وتطبيق جميع migrations',
      timeline: '1-2 يوم',
      dependencies: []
    },
    {
      step: 'إعداد الخدمات الخارجية',
      description: 'Google OAuth, SMTP, استضافة',
      timeline: '1 يوم',
      dependencies: ['قاعدة البيانات']
    },
    {
      step: 'اختبارات شاملة',
      description: 'اختبار جميع الميزات والسيناريوهات',
      timeline: '2-3 أيام',
      dependencies: ['الخدمات الخارجية']
    },
    {
      step: 'تحسينات الأداء',
      description: 'تحسين السرعة والاستجابة',
      timeline: '1-2 يوم',
      dependencies: ['اختبارات']
    },
    {
      step: 'النشر التجريبي',
      description: 'نشر نسخة تجريبية للاختبار',
      timeline: '1 يوم',
      dependencies: ['تحسينات الأداء']
    },
    {
      step: 'النشر النهائي',
      description: 'إطلاق المشروع للعامة',
      timeline: '1 يوم',
      dependencies: ['النشر التجريبي']
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'مكتمل': return 'text-green-600 bg-green-100';
      case 'قيد التطوير': return 'text-blue-600 bg-blue-100';
      case 'يحتاج تحسين': return 'text-yellow-600 bg-yellow-100';
      case 'أساسي': return 'text-orange-600 bg-orange-100';
      case 'لم يبدأ': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'عالية': return 'text-red-600 bg-red-100';
      case 'متوسطة': return 'text-yellow-600 bg-yellow-100';
      case 'منخفضة': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-navy mb-4">
                🗺️ خارطة طريق مشروع دوزان
              </h1>
              <p className="text-xl text-gray-600">
                خطة البناء الشاملة وقائمة المهام والأخطاء
              </p>
            </div>

            {/* Tabs */}
            <div className="flex flex-wrap gap-2 mb-8">
              {[
                { id: 'roadmap', label: 'خارطة الطريق', icon: '🗺️' },
                { id: 'issues', label: 'المشاكل الحرجة', icon: '🚨' },
                { id: 'syria', label: 'تحسينات سورية', icon: '🇸🇾' },
                { id: 'next', label: 'الخطوات التالية', icon: '⏭️' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-teal text-white'
                      : 'bg-white text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  {tab.icon} {tab.label}
                </button>
              ))}
            </div>

            {/* Roadmap Tab */}
            {activeTab === 'roadmap' && (
              <div className="space-y-6">
                {roadmapPhases.map((phase, index) => (
                  <Card key={index} className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span>{phase.phase}</span>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                          phase.color === 'green' ? 'bg-green-100 text-green-800' :
                          phase.color === 'blue' ? 'bg-blue-100 text-blue-800' :
                          phase.color === 'yellow' ? 'bg-yellow-100 text-yellow-800' :
                          phase.color === 'orange' ? 'bg-orange-100 text-orange-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {phase.status}
                        </span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {phase.tasks.map((task, taskIndex) => (
                          <div key={taskIndex} className="bg-gray-50 p-4 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <span className="font-medium text-navy">{task.task}</span>
                              <span className={`px-2 py-1 rounded text-xs ${
                                task.priority === 'عالية' ? 'bg-red-100 text-red-800' :
                                task.priority === 'متوسطة' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-green-100 text-green-800'
                              }`}>
                                {task.priority}
                              </span>
                            </div>
                            <span className={`px-2 py-1 rounded text-xs ${getStatusColor(task.status)}`}>
                              {task.status}
                            </span>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Issues Tab */}
            {activeTab === 'issues' && (
              <div className="space-y-6">
                {criticalIssues.map((issue, index) => (
                  <Card key={index} className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center justify-between">
                        <span>{issue.issue}</span>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getSeverityColor(issue.severity)}`}>
                          {issue.severity}
                        </span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-semibold text-navy mb-2">الوصف:</h4>
                          <p className="text-gray-700 mb-4">{issue.description}</p>
                          
                          <h4 className="font-semibold text-navy mb-2">التأثير:</h4>
                          <p className="text-red-700">{issue.impact}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-navy mb-2">الحل المقترح:</h4>
                          <p className="text-gray-700 mb-4">{issue.solution}</p>
                          
                          <h4 className="font-semibold text-navy mb-2">الوقت المطلوب:</h4>
                          <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                            {issue.timeline}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Syria Optimizations Tab */}
            {activeTab === 'syria' && (
              <div className="space-y-6">
                <Card className="border-0 bg-blue-50 border-blue-200">
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-navy mb-3">🇸🇾 تحسينات خاصة بالسوق السوري</h3>
                    <p className="text-gray-700">
                      هذه التحسينات ستجعل المنصة أكثر ملاءمة للمستخدمين السوريين وظروفهم الخاصة
                    </p>
                  </CardContent>
                </Card>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {syrianOptimizations.map((opt, index) => (
                    <Card key={index} className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                      <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                          <span>{opt.optimization}</span>
                          <div className="flex space-x-2 space-x-reverse">
                            <span className={`px-2 py-1 rounded text-xs ${
                              opt.priority === 'عالية' ? 'bg-red-100 text-red-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {opt.priority}
                            </span>
                            <span className={`px-2 py-1 rounded text-xs ${
                              opt.effort === 'منخفض' ? 'bg-green-100 text-green-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {opt.effort}
                            </span>
                          </div>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-700">{opt.description}</p>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Next Steps Tab */}
            {activeTab === 'next' && (
              <div className="space-y-6">
                <Card className="border-0 bg-green-50 border-green-200">
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-navy mb-3">⏭️ الخطوات التالية للنشر</h3>
                    <p className="text-gray-700 mb-4">
                      خطة مرحلية لإكمال المشروع ونشره للعامة خلال 7-10 أيام
                    </p>
                    <div className="bg-white p-4 rounded-lg">
                      <h4 className="font-semibold text-navy mb-2">الوقت الإجمالي المتوقع:</h4>
                      <span className="text-2xl font-bold text-green-600">7-10 أيام</span>
                    </div>
                  </CardContent>
                </Card>

                {nextSteps.map((step, index) => (
                  <Card key={index} className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <span className="bg-teal text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">
                          {index + 1}
                        </span>
                        {step.step}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                        <div className="lg:col-span-2">
                          <h4 className="font-semibold text-navy mb-2">الوصف:</h4>
                          <p className="text-gray-700">{step.description}</p>
                        </div>
                        <div>
                          <h4 className="font-semibold text-navy mb-2">الوقت المطلوب:</h4>
                          <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm mb-3 inline-block">
                            {step.timeline}
                          </span>
                          
                          {step.dependencies.length > 0 && (
                            <>
                              <h4 className="font-semibold text-navy mb-2">يعتمد على:</h4>
                              <ul className="space-y-1">
                                {step.dependencies.map((dep, depIndex) => (
                                  <li key={depIndex} className="text-sm text-gray-600">• {dep}</li>
                                ))}
                              </ul>
                            </>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Action Buttons */}
            <div className="mt-12 text-center">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Button 
                  onClick={() => window.open('/test-dashboard', '_blank')}
                  className="bg-gradient-to-r from-blue-500 to-blue-600"
                >
                  🧪 لوحة الاختبار
                </Button>
                <Button 
                  onClick={() => window.open('/syria-simulation', '_blank')}
                  className="bg-gradient-to-r from-green-500 to-green-600"
                >
                  🇸🇾 محاكاة المستخدم
                </Button>
                <Button 
                  onClick={() => window.open('/deployment-guide', '_blank')}
                  className="bg-gradient-to-r from-purple-500 to-purple-600"
                >
                  🚀 دليل النشر
                </Button>
                <Button 
                  onClick={() => window.open('/project-summary', '_blank')}
                  className="bg-gradient-to-r from-orange-500 to-orange-600"
                >
                  📊 ملخص المشروع
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default ProjectRoadmapPage;
