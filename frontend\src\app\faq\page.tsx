'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

interface FAQ {
  id: number;
  question: string;
  answer: string;
  category: string;
}

const FAQPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const categories = [
    { id: 'all', name: 'جميع الأسئلة' },
    { id: 'general', name: 'عام' },
    { id: 'clients', name: 'أصحاب المشاريع' },
    { id: 'craftsmen', name: 'الحرفيون' },
    { id: 'payments', name: 'المدفوعات' },
    { id: 'technical', name: 'تقني' },
  ];

  const faqs: FAQ[] = [
    {
      id: 1,
      question: 'ما هي منصة دوزان؟',
      answer: 'منصة دوزان هي منصة إلكترونية تربط بين أصحاب المشاريع والحرفيين المحترفين في سوريا. تهدف المنصة إلى تسهيل عملية العثور على الحرفيين المناسبين لتنفيذ المشاريع المختلفة بجودة عالية وأسعار منافسة.',
      category: 'general'
    },
    {
      id: 2,
      question: 'كيف يمكنني إنشاء حساب على المنصة؟',
      answer: 'يمكنك إنشاء حساب بسهولة من خلال النقر على زر "إنشاء حساب" في أعلى الصفحة، ثم ملء البيانات المطلوبة مثل الاسم والبريد الإلكتروني وكلمة المرور. يمكنك أيضاً التسجيل باستخدام حساب Google أو Facebook.',
      category: 'general'
    },
    {
      id: 3,
      question: 'هل هناك رسوم على استخدام المنصة؟',
      answer: 'التسجيل وتصفح المشاريع والحرفيين مجاني تماماً. نحصل على عمولة صغيرة (5%) فقط عند إتمام المشروع بنجاح، وهذا يضمن لنا تقديم أفضل خدمة ممكنة.',
      category: 'general'
    },
    {
      id: 4,
      question: 'كيف يمكنني نشر مشروع جديد؟',
      answer: 'بعد تسجيل الدخول، انقر على "أنشئ مشروعاً" واتبع الخطوات الأربع: 1) أدخل التفاصيل الأساسية للمشروع، 2) حدد المتطلبات والمهارات المطلوبة، 3) حدد الميزانية والموعد النهائي، 4) راجع التفاصيل وانشر المشروع.',
      category: 'clients'
    },
    {
      id: 5,
      question: 'كيف أختار الحرفي المناسب لمشروعي؟',
      answer: 'يمكنك مراجعة العروض المقدمة من الحرفيين، والاطلاع على تقييماتهم وأعمالهم السابقة. ننصح بمراجعة الملف الشخصي للحرفي، والتواصل معه لمناقشة تفاصيل المشروع قبل اتخاذ القرار.',
      category: 'clients'
    },
    {
      id: 6,
      question: 'كيف يمكنني ضمان جودة العمل؟',
      answer: 'نوفر نظام تقييمات شامل يساعدك في اختيار الحرفيين المناسبين. كما نضمن حقوقك من خلال نظام الدفع الآمن حيث يتم حجز المبلغ حتى إتمام العمل بنجاح. يمكنك أيضاً طلب التعديلات أو الاستعانة بفريق الدعم في حالة وجود مشاكل.',
      category: 'clients'
    },
    {
      id: 7,
      question: 'كيف يمكنني الانضمام كحرفي؟',
      answer: 'قم بإنشاء حساب واختر "حرفي" كنوع الحساب. ثم أكمل ملفك الشخصي بإضافة خبراتك ومهاراتك وأعمالك السابقة. بعد مراجعة الملف من فريقنا، ستتمكن من تقديم عروض على المشاريع المتاحة.',
      category: 'craftsmen'
    },
    {
      id: 8,
      question: 'كيف أقدم عرضاً على مشروع؟',
      answer: 'تصفح المشاريع المتاحة في تخصصك، واختر المشروع المناسب. انقر على "تقديم عرض" وأدخل السعر المطلوب ومدة التنفيذ ووصف مفصل لكيفية تنفيذ المشروع. تأكد من قراءة متطلبات المشروع بعناية.',
      category: 'craftsmen'
    },
    {
      id: 9,
      question: 'كيف أحصل على أموالي بعد إتمام المشروع؟',
      answer: 'بعد إتمام المشروع وموافقة العميل، سيتم تحويل المبلغ إلى حسابك خلال 3-5 أيام عمل. يمكنك سحب الأموال عبر التحويل البنكي أو المحافظ الإلكترونية المتاحة.',
      category: 'craftsmen'
    },
    {
      id: 10,
      question: 'ما هي طرق الدفع المتاحة؟',
      answer: 'نقبل الدفع عبر البطاقات الائتمانية، التحويل البنكي، والمحافظ الإلكترونية مثل سيريتل كاش وMTN موني. جميع المعاملات آمنة ومشفرة.',
      category: 'payments'
    },
    {
      id: 11,
      question: 'هل يمكنني استرداد أموالي؟',
      answer: 'نعم، يمكنك طلب استرداد الأموال في حالة عدم بدء العمل أو عدم الالتزام بالشروط المتفق عليها. سيتم مراجعة الطلب من فريق الدعم واتخاذ الإجراء المناسب خلال 7 أيام عمل.',
      category: 'payments'
    },
    {
      id: 12,
      question: 'لا أستطيع تسجيل الدخول إلى حسابي',
      answer: 'تأكد من إدخال البريد الإلكتروني وكلمة المرور بشكل صحيح. إذا نسيت كلمة المرور، انقر على "نسيت كلمة المرور" واتبع التعليمات. إذا استمرت المشكلة، تواصل مع فريق الدعم.',
      category: 'technical'
    },
    {
      id: 13,
      question: 'كيف يمكنني تغيير معلومات حسابي؟',
      answer: 'بعد تسجيل الدخول، انتقل إلى "الإعدادات" أو "الملف الشخصي" حيث يمكنك تعديل جميع معلوماتك الشخصية، تغيير كلمة المرور، وإدارة إعدادات الإشعارات.',
      category: 'technical'
    },
    {
      id: 14,
      question: 'ماذا لو لم أكن راضياً عن العمل المنجز؟',
      answer: 'يمكنك التواصل مع الحرفي أولاً لطلب التعديلات اللازمة. إذا لم يتم حل المشكلة، يمكنك رفع شكوى لفريق الدعم الذي سيتدخل لحل النزاع بشكل عادل وقد يشمل ذلك استرداد جزئي أو كامل للمبلغ.',
      category: 'general'
    },
    {
      id: 15,
      question: 'كيف يمكنني التواصل مع فريق الدعم؟',
      answer: 'يمكنك التواصل معنا عبر صفحة "اتصل بنا" أو إرسال بريد إلكتروني إلى <EMAIL> أو الاتصال على +963 11 234 5678. فريق الدعم متاح من الأحد إلى الخميس من 9:00 صباحاً إلى 6:00 مساءً.',
      category: 'general'
    }
  ];

  const filteredFAQs = faqs.filter(faq => 
    selectedCategory === 'all' || faq.category === selectedCategory
  );

  const toggleFAQ = (id: number) => {
    setOpenFAQ(openFAQ === id ? null : id);
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-beige via-white to-skyblue/20 py-16">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-2 rounded-full text-sm font-medium mb-4">
              ❓ الأسئلة الشائعة
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-navy mb-6">
              كل ما تريد معرفته عن 
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-teal to-navy">دوزان</span>
            </h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              إجابات شاملة على جميع الأسئلة التي قد تخطر ببالك حول استخدام منصة دوزان
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Categories Sidebar */}
            <div className="lg:col-span-1">
              <Card className="border-0 bg-white/90 backdrop-blur-sm sticky top-8">
                <CardHeader>
                  <CardTitle>فئات الأسئلة</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {categories.map(category => (
                      <button
                        key={category.id}
                        onClick={() => setSelectedCategory(category.id)}
                        className={`w-full text-right px-4 py-3 rounded-lg transition-all duration-200 ${
                          selectedCategory === category.id
                            ? 'bg-gradient-to-r from-navy to-teal text-white'
                            : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                        }`}
                      >
                        {category.name}
                      </button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* FAQ Content */}
            <div className="lg:col-span-3">
              <div className="space-y-4">
                {filteredFAQs.map((faq) => (
                  <Card key={faq.id} className="border-0 bg-white/90 backdrop-blur-sm">
                    <CardContent className="p-0">
                      <button
                        onClick={() => toggleFAQ(faq.id)}
                        className="w-full text-right p-6 flex justify-between items-center hover:bg-gray-50 transition-colors duration-200"
                      >
                        <h3 className="text-lg font-semibold text-navy">{faq.question}</h3>
                        <svg
                          className={`w-6 h-6 text-gray-500 transition-transform duration-200 ${
                            openFAQ === faq.id ? 'transform rotate-180' : ''
                          }`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>
                      {openFAQ === faq.id && (
                        <div className="px-6 pb-6">
                          <div className="border-t border-gray-200 pt-4">
                            <p className="text-gray-700 leading-relaxed">{faq.answer}</p>
                          </div>
                        </div>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Contact Support */}
              <Card className="border-0 bg-gradient-to-r from-navy to-teal text-white mt-12">
                <CardContent className="p-8 text-center">
                  <h3 className="text-2xl font-bold mb-4">لم تجد إجابة لسؤالك؟</h3>
                  <p className="text-white/90 mb-6">
                    فريق الدعم متاح لمساعدتك في أي وقت. تواصل معنا وسنكون سعداء للإجابة على استفساراتك
                  </p>
                  <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
                    <a 
                      href="/contact" 
                      className="bg-white text-navy px-6 py-3 rounded-lg hover:bg-white/90 transition-colors font-semibold"
                    >
                      تواصل معنا
                    </a>
                    <a 
                      href="mailto:<EMAIL>" 
                      className="border border-white text-white px-6 py-3 rounded-lg hover:bg-white hover:text-navy transition-colors font-semibold"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default FAQPage;
