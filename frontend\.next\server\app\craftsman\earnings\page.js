(()=>{var e={};e.id=6594,e.ids=[6594],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},27421:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=s(70260),a=s(28203),n=s(25155),l=s.n(n),i=s(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let o=["",{children:["craftsman",{children:["earnings",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,98621)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\craftsman\\earnings\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\craftsman\\earnings\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/craftsman/earnings/page",pathname:"/craftsman/earnings",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},60649:(e,t,s)=>{Promise.resolve().then(s.bind(s,98621))},25801:(e,t,s)=>{Promise.resolve().then(s.bind(s,57905))},57905:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(45512),a=s(58009),n=s(60297),l=s(42847),i=s(87272);let d=()=>{let[e,t]=(0,a.useState)("all"),[s,d]=(0,a.useState)("month"),o=[{id:1,projectTitle:"تجديد المطبخ الرئيسي",client:"أحمد محمد",amount:"₺7,500",commission:"₺375",netAmount:"₺7,125",status:"مدفوع",paymentDate:"2024-02-15",paymentMethod:"تحويل بنكي",projectCompletedDate:"2024-02-10"},{id:2,projectTitle:"إصلاح نظام السباكة",client:"فاطمة أحمد",amount:"₺2,200",commission:"₺110",netAmount:"₺2,090",status:"مدفوع",paymentDate:"2024-02-12",paymentMethod:"محفظة إلكترونية",projectCompletedDate:"2024-02-08"},{id:3,projectTitle:"دهان الشقة الكاملة",client:"محمد علي",amount:"₺3,200",commission:"₺160",netAmount:"₺3,040",status:"في الانتظار",paymentDate:null,paymentMethod:null,projectCompletedDate:"2024-02-14"},{id:4,projectTitle:"تركيب خزائن مطبخ",client:"سارة خالد",amount:"₺5,800",commission:"₺290",netAmount:"₺5,510",status:"قيد المعالجة",paymentDate:null,paymentMethod:null,projectCompletedDate:"2024-02-16"}],c=e=>{switch(e){case"مدفوع":return"bg-green-100 text-green-800 border-green-200";case"في الانتظار":return"bg-yellow-100 text-yellow-800 border-yellow-200";case"قيد المعالجة":return"bg-blue-100 text-blue-800 border-blue-200";case"مرفوض":return"bg-red-100 text-red-800 border-red-200";default:return"bg-gray-100 text-gray-800 border-gray-200"}},x=e=>{if(!e)return"⏳";switch(e){case"تحويل بنكي":return"\uD83C\uDFE6";case"محفظة إلكترونية":return"\uD83D\uDCF1";case"بطاقة ائتمان":return"\uD83D\uDCB3";default:return"\uD83D\uDCB0"}},m=o.filter(t=>"all"===e||t.status===e),p=o.reduce((e,t)=>"مدفوع"===t.status?e+parseFloat(t.netAmount.replace("₺","").replace(",","")):e,0),u=o.reduce((e,t)=>"في الانتظار"===t.status||"قيد المعالجة"===t.status?e+parseFloat(t.netAmount.replace("₺","").replace(",","")):e,0),h=[{title:"إجمالي الأرباح",value:`₺${p.toLocaleString()}`,color:"text-green-600",icon:"\uD83D\uDCB0"},{title:"أرباح معلقة",value:`₺${u.toLocaleString()}`,color:"text-yellow-600",icon:"⏳"},{title:"عدد المشاريع",value:o.length.toString(),color:"text-blue-600",icon:"\uD83D\uDCCA"},{title:"متوسط الربح",value:`₺${Math.round(p/o.filter(e=>"مدفوع"===e.status).length||0).toLocaleString()}`,color:"text-purple-600",icon:"\uD83D\uDCC8"}];return(0,r.jsx)(n.A,{requiredRoles:"craftsman",children:(0,r.jsx)(l.A,{title:"الأرباح والمدفوعات",subtitle:"متابعة أرباحك ومدفوعاتك من المشاريع",actions:(0,r.jsxs)("div",{className:"flex space-x-2 space-x-reverse",children:[(0,r.jsxs)("select",{value:s,onChange:e=>d(e.target.value),className:"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy",children:[(0,r.jsx)("option",{value:"week",children:"هذا الأسبوع"}),(0,r.jsx)("option",{value:"month",children:"هذا الشهر"}),(0,r.jsx)("option",{value:"quarter",children:"هذا الربع"}),(0,r.jsx)("option",{value:"year",children:"هذا العام"})]}),(0,r.jsx)(i.Button,{className:"bg-gradient-to-r from-navy to-teal",children:"تصدير التقرير"})]}),children:(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:h.map((e,t)=>(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-1",children:e.title}),(0,r.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:e.value})]}),(0,r.jsx)("div",{className:`text-3xl ${e.color}`,children:e.icon})]})},t))}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"تطور الأرباح"}),(0,r.jsx)("div",{className:"h-64 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDCC8"}),(0,r.jsx)("p",{className:"text-gray-600",children:"سيتم إضافة مخطط الأرباح هنا"})]})})]}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:[{id:"all",label:"جميع المدفوعات"},{id:"مدفوع",label:"مدفوعة"},{id:"في الانتظار",label:"في الانتظار"},{id:"قيد المعالجة",label:"قيد المعالجة"}].map(s=>(0,r.jsx)("button",{onClick:()=>t(s.id),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${e===s.id?"bg-navy text-white":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:s.label},s.id))})}),(0,r.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-50",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"المشروع"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"العميل"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"المبلغ الإجمالي"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"العمولة"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"صافي الربح"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"الحالة"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"تاريخ الدفع"}),(0,r.jsx)("th",{className:"text-right py-4 px-6 font-medium text-gray-700",children:"الإجراءات"})]})}),(0,r.jsx)("tbody",{children:m.map(e=>(0,r.jsxs)("tr",{className:"border-b border-gray-100 hover:bg-gray-50",children:[(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.projectTitle}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["اكتمل في ",e.projectCompletedDate]})]})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold",children:e.client.charAt(0)}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:e.client})]})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsx)("span",{className:"font-bold text-gray-900",children:e.amount})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("span",{className:"text-red-600 font-medium",children:["-",e.commission]})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsx)("span",{className:"font-bold text-green-600",children:e.netAmount})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsx)("span",{className:`px-3 py-1 rounded-full text-xs font-medium border ${c(e.status)}`,children:e.status})}),(0,r.jsx)("td",{className:"py-4 px-6",children:e.paymentDate?(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.paymentDate}),(0,r.jsxs)("div",{className:"flex items-center space-x-1 space-x-reverse text-sm text-gray-500",children:[(0,r.jsx)("span",{children:x(e.paymentMethod)}),(0,r.jsx)("span",{children:e.paymentMethod})]})]}):(0,r.jsx)("span",{className:"text-gray-500 text-sm",children:"في الانتظار"})}),(0,r.jsx)("td",{className:"py-4 px-6",children:(0,r.jsxs)("div",{className:"flex space-x-2 space-x-reverse",children:[(0,r.jsx)(i.Button,{size:"sm",variant:"outline",className:"border-navy text-navy hover:bg-navy hover:text-white",children:"عرض التفاصيل"}),"مدفوع"===e.status&&(0,r.jsx)(i.Button,{size:"sm",variant:"outline",className:"border-gray-300 text-gray-700 hover:bg-gray-100",children:"تحميل الإيصال"})]})})]},e.id))})]})})}),0===m.length&&(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83D\uDCB0"}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-2",children:"لا توجد أرباح"}),(0,r.jsx)("p",{className:"text-gray-600 mb-6",children:"لم يتم العثور على أرباح بالمعايير المحددة"}),(0,r.jsx)(i.Button,{onClick:()=>t("all"),className:"bg-gradient-to-r from-navy to-teal",children:"عرض جميع الأرباح"})]}),(0,r.jsxs)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"طرق استلام الأرباح"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83C\uDFE6"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"حساب بنكي"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"البنك التجاري السوري"})]})]}),(0,r.jsx)("span",{className:"px-2 py-1 bg-green-100 text-green-800 rounded text-xs",children:"افتراضي"})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,r.jsx)("p",{children:"رقم الحساب: ****5678"}),(0,r.jsx)("p",{children:"اسم الحساب: أحمد النجار"})]})]}),(0,r.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,r.jsx)("div",{className:"flex items-center justify-between mb-3",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,r.jsx)("span",{className:"text-2xl",children:"\uD83D\uDCF1"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:"محفظة إلكترونية"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"سيريتل كاش"})]})]})}),(0,r.jsx)("div",{className:"text-sm text-gray-600",children:(0,r.jsx)("p",{children:"رقم المحفظة: +963 123 456 789"})})]})]}),(0,r.jsx)("div",{className:"mt-6 pt-6 border-t border-gray-200",children:(0,r.jsx)(i.Button,{variant:"outline",className:"border-navy text-navy hover:bg-navy hover:text-white",children:"إضافة طريقة دفع جديدة"})})]})]})})})}},98621:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\g\\\\Duzan Website\\\\frontend\\\\src\\\\app\\\\craftsman\\\\earnings\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\craftsman\\earnings\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,3351,7180,1057,1799],()=>s(27421));module.exports=r})();