(()=>{var e={};e.id=5807,e.ids=[5807],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},23265:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(70260),a=r(28203),n=r(25155),l=r.n(n),i=r(67292),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);r.d(t,o);let d=["",{children:["post-job",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,34300)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\post-job\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\post-job\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/post-job/page",pathname:"/post-job",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79574:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,13219,23)),Promise.resolve().then(r.t.bind(r,34863,23)),Promise.resolve().then(r.t.bind(r,25155,23)),Promise.resolve().then(r.t.bind(r,40802,23)),Promise.resolve().then(r.t.bind(r,9350,23)),Promise.resolve().then(r.t.bind(r,48530,23)),Promise.resolve().then(r.t.bind(r,88921,23))},74846:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,66959,23)),Promise.resolve().then(r.t.bind(r,33875,23)),Promise.resolve().then(r.t.bind(r,88903,23)),Promise.resolve().then(r.t.bind(r,57174,23)),Promise.resolve().then(r.t.bind(r,84178,23)),Promise.resolve().then(r.t.bind(r,87190,23)),Promise.resolve().then(r.t.bind(r,61365,23))},31907:(e,t,r)=>{Promise.resolve().then(r.bind(r,95652)),Promise.resolve().then(r.bind(r,99305))},97563:(e,t,r)=>{Promise.resolve().then(r.bind(r,52080)),Promise.resolve().then(r.bind(r,7405))},26597:(e,t,r)=>{Promise.resolve().then(r.bind(r,34300))},20517:(e,t,r)=>{Promise.resolve().then(r.bind(r,15029))},15029:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>C});var s=r(45512),a=r(58009),n=r.n(a),l=r(79334);let i={title:"",description:"",category:"",location:"",requirements:"",skills:[],attachments:[],images:[],budget:0,deadline:"",status:"draft",tags:[]},o=(0,a.createContext)(void 0),d=({children:e})=>{let[t,r]=(0,a.useState)(i),[n,l]=(0,a.useState)(1),[d,c]=(0,a.useState)(!1);return(0,s.jsx)(o.Provider,{value:{formData:t,updateFormData:e=>{r(t=>({...t,...e}))},resetForm:()=>{r(i),l(1)},currentStep:n,setCurrentStep:l,isSubmitting:d,setIsSubmitting:c},children:e})},c=()=>{let e=(0,a.useContext)(o);if(void 0===e)throw Error("useJobForm must be used within a JobFormProvider");return e};var m=r(4209),u=r(28531),x=r.n(u);let h=({currentStep:e,totalSteps:t})=>(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsx)("div",{className:"flex justify-between items-center",children:Array.from({length:t}).map((r,a)=>(0,s.jsxs)(n().Fragment,{children:[(0,s.jsxs)("div",{className:"flex flex-col items-center",children:[(0,s.jsx)("div",{className:`w-10 h-10 rounded-full flex items-center justify-center ${a+1<=e?"bg-navy text-white":"bg-gray-200 text-gray-500"}`,children:a+1}),(0,s.jsxs)("span",{className:`mt-2 text-sm ${a+1<=e?"text-navy font-medium":"text-gray-500"}`,children:[a+1===1&&"التفاصيل الأساسية",a+1===2&&"المتطلبات والمواصفات",a+1===3&&"الميزانية والمدة",a+1===4&&"المراجعة والنشر"]})]}),a<t-1&&(0,s.jsx)("div",{className:`h-1 flex-1 mx-2 ${a+1<e?"bg-navy":"bg-gray-200"}`})]},a))})}),p=({children:e,currentStep:t,totalSteps:r,onNext:a,onPrevious:n,isLastStep:l=!1,isSubmitting:i=!1})=>(0,s.jsx)(m.default,{children:(0,s.jsx)("div",{className:"bg-beige py-12",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"max-w-3xl mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-navy mb-6 text-center",children:"إنشاء مشروع جديد"}),(0,s.jsx)(h,{currentStep:t,totalSteps:r}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-8",children:[e,(0,s.jsxs)("div",{className:"flex justify-between mt-8",children:[t>1?(0,s.jsx)("button",{type:"button",onClick:n,className:"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"السابق"}):(0,s.jsx)(x(),{href:"/",children:(0,s.jsx)("button",{type:"button",className:"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50",children:"إلغاء"})}),(0,s.jsx)("button",{type:"button",onClick:a,disabled:i,className:`px-6 py-2 bg-navy text-white rounded-md hover:bg-navy/90 ${i?"opacity-70 cursor-not-allowed":""}`,children:i?(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"جاري المعالجة..."]}):l?"نشر المشروع":"التالي"})]})]})]})})})});var g=r(70030),b=r(16239);let v=[{value:"carpentry",label:"النجارة",icon:"\uD83E\uDE9A"},{value:"plumbing",label:"السباكة",icon:"\uD83D\uDD27"},{value:"electrical",label:"الكهرباء",icon:"⚡"},{value:"painting",label:"الدهان",icon:"\uD83D\uDD8C️"},{value:"construction",label:"البناء",icon:"\uD83E\uDDF1"},{value:"hvac",label:"التكييف",icon:"❄️"},{value:"metalwork",label:"الحدادة",icon:"\uD83D\uDD28"},{value:"landscaping",label:"تنسيق الحدائق",icon:"\uD83C\uDF31"},{value:"other",label:"أخرى",icon:"\uD83D\uDD27"}],f=()=>{let{formData:e,updateFormData:t}=c(),[r,n]=(0,a.useState)({title:"",description:"",category:""}),l=()=>{let t=!0,r={title:"",description:"",category:""};return e.title&&e.title.trim()?e.title.length<5&&(r.title="عنوان المشروع يجب أن يكون 5 أحرف على الأقل",t=!1):(r.title="عنوان المشروع مطلوب",t=!1),e.description&&e.description.trim()?e.description.length<20&&(r.description="وصف المشروع يجب أن يكون 20 حرف على الأقل",t=!1):(r.description="وصف المشروع مطلوب",t=!1),e.category||(r.category="فئة المشروع مطلوبة",t=!1),n(r),t};return(0,a.useEffect)(()=>{let e=document.getElementById("job-form");if(e){let t=e.onsubmit;e.onsubmit=r=>l()?t?t.call(e,r):void 0:(r.preventDefault(),!1)}},[e]),(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-navy mb-6",children:"التفاصيل الأساسية للمشروع"}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"title",className:"block text-gray-700 font-medium mb-2",children:["عنوان المشروع ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"text",id:"title",value:e.title,onChange:e=>t({title:e.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent",placeholder:"مثال: تصميم وتنفيذ خزائن مطبخ خشبية"}),r.title&&(0,s.jsx)("p",{className:"mt-1 text-red-500 text-sm",children:r.title})]}),(0,s.jsx)("div",{children:(0,s.jsx)(b.A,{label:"فئة المشروع",options:v,value:e.category,onChange:e=>t({category:e}),placeholder:"اختر فئة المشروع",required:!0,error:r.category})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"location",className:"block text-gray-700 font-medium mb-2",children:"الموقع"}),(0,s.jsx)("input",{type:"text",id:"location",value:e.location,onChange:e=>t({location:e.target.value}),className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent",placeholder:"مثال: دمشق، المزة"})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"description",className:"block text-gray-700 font-medium mb-2",children:["وصف المشروع ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("textarea",{id:"description",value:e.description,onChange:e=>t({description:e.target.value}),rows:5,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent",placeholder:"اشرح تفاصيل مشروعك بوضوح..."}),r.description&&(0,s.jsx)("p",{className:"mt-1 text-red-500 text-sm",children:r.description})]}),(0,s.jsx)("div",{children:(0,s.jsx)(g.A,{onImagesChange:e=>t({images:e}),maxImages:5,label:"صور المشروع",description:"أضف صور واضحة تظهر المشكلة أو المنطقة التي تحتاج للعمل عليها. هذا سيساعد الحرفيين على فهم المطلوب بشكل أفضل.",required:!1})})]})]})},y=()=>{let{formData:e,updateFormData:t}=c(),[r,n]=(0,a.useState)(""),[l,i]=(0,a.useState)({requirements:"",skills:""}),o=()=>{r.trim()&&(t({skills:[...e.skills,r.trim()]}),n(""))},d=r=>{t({skills:e.skills.filter((e,t)=>t!==r)})},m=r=>{t({attachments:e.attachments.filter((e,t)=>t!==r)})};return(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-navy mb-6",children:"متطلبات ومواصفات المشروع"}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"requirements",className:"block text-gray-700 font-medium mb-2",children:["متطلبات المشروع ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("textarea",{id:"requirements",value:e.requirements,onChange:e=>t({requirements:e.target.value}),rows:5,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent",placeholder:"اذكر المتطلبات والمواصفات التفصيلية للمشروع..."}),l.requirements&&(0,s.jsx)("p",{className:"mt-1 text-red-500 text-sm",children:l.requirements})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-gray-700 font-medium mb-2",children:["المهارات المطلوبة ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"flex",children:[(0,s.jsx)("input",{type:"text",value:r,onChange:e=>n(e.target.value),onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),o())},className:"flex-grow px-4 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent",placeholder:"أضف مهارة..."}),(0,s.jsx)("button",{type:"button",onClick:o,className:"px-4 py-2 bg-teal text-white rounded-l-md hover:bg-teal/90",children:"إضافة"})]}),l.skills&&(0,s.jsx)("p",{className:"mt-1 text-red-500 text-sm",children:l.skills}),e.skills.length>0&&(0,s.jsx)("div",{className:"mt-3 flex flex-wrap gap-2",children:e.skills.map((e,t)=>(0,s.jsxs)("div",{className:"bg-skyblue px-3 py-1 rounded-full flex items-center",children:[(0,s.jsx)("span",{className:"text-navy",children:e}),(0,s.jsx)("button",{type:"button",onClick:()=>d(t),className:"ml-2 text-navy hover:text-red-500",children:"\xd7"})]},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"المرفقات (اختياري)"}),(0,s.jsxs)("div",{className:"border-2 border-dashed border-gray-300 rounded-md p-6 text-center",children:[(0,s.jsx)("input",{type:"file",id:"file-upload",multiple:!0,onChange:r=>{let s=r.target.files;if(s&&s.length>0){let r=Array.from(s).map(e=>e.name);t({attachments:[...e.attachments,...r]})}},className:"hidden"}),(0,s.jsxs)("label",{htmlFor:"file-upload",className:"cursor-pointer inline-flex items-center justify-center px-4 py-2 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50",children:[(0,s.jsx)("svg",{className:"ml-2 -mr-1 h-5 w-5 text-gray-400",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z",clipRule:"evenodd"})}),"اختر ملفات"]}),(0,s.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:"يمكنك رفع صور، مستندات PDF، أو أي ملفات أخرى ذات صلة بالمشروع"})]}),e.attachments.length>0&&(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"الملفات المرفقة:"}),(0,s.jsx)("ul",{className:"space-y-2",children:e.attachments.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-center justify-between bg-gray-50 p-2 rounded",children:[(0,s.jsx)("span",{className:"text-sm text-gray-700",children:e}),(0,s.jsx)("button",{type:"button",onClick:()=>m(t),className:"text-red-500 hover:text-red-700",children:"حذف"})]},t))})]})]})]})]})},j=()=>{let{formData:e,updateFormData:t}=c(),[r,n]=(0,a.useState)({budget:"",deadline:""});return(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-navy mb-6",children:"الميزانية والجدول الزمني"}),(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"budget",className:"block text-gray-700 font-medium mb-2",children:["ميزانية المشروع (بالليرة السورية) ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none",children:(0,s.jsx)("span",{className:"text-gray-500",children:"ل.س"})}),(0,s.jsx)("input",{type:"number",id:"budget",value:e.budget||"",onChange:e=>t({budget:parseFloat(e.target.value)||0}),min:"0",step:"1000",className:"w-full px-4 py-2 pr-12 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent",placeholder:"أدخل الميزانية المتوقعة"})]}),r.budget&&(0,s.jsx)("p",{className:"mt-1 text-red-500 text-sm",children:r.budget}),(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,s.jsx)("button",{type:"button",onClick:()=>t({budget:5e4}),className:`px-3 py-1 rounded-full text-sm ${5e4===e.budget?"bg-navy text-white":"bg-gray-100 text-gray-700"}`,children:"50,000 ل.س"}),(0,s.jsx)("button",{type:"button",onClick:()=>t({budget:1e5}),className:`px-3 py-1 rounded-full text-sm ${1e5===e.budget?"bg-navy text-white":"bg-gray-100 text-gray-700"}`,children:"100,000 ل.س"}),(0,s.jsx)("button",{type:"button",onClick:()=>t({budget:25e4}),className:`px-3 py-1 rounded-full text-sm ${25e4===e.budget?"bg-navy text-white":"bg-gray-100 text-gray-700"}`,children:"250,000 ل.س"}),(0,s.jsx)("button",{type:"button",onClick:()=>t({budget:5e5}),className:`px-3 py-1 rounded-full text-sm ${5e5===e.budget?"bg-navy text-white":"bg-gray-100 text-gray-700"}`,children:"500,000 ل.س"})]})})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{htmlFor:"deadline",className:"block text-gray-700 font-medium mb-2",children:["الموعد النهائي للمشروع ",(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("input",{type:"date",id:"deadline",value:e.deadline,onChange:e=>t({deadline:e.target.value}),min:new Date().toISOString().split("T")[0],className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"}),r.deadline&&(0,s.jsx)("p",{className:"mt-1 text-red-500 text-sm",children:r.deadline})]}),(0,s.jsxs)("div",{className:"bg-beige bg-opacity-50 p-4 rounded-md",children:[(0,s.jsx)("h3",{className:"text-navy font-medium mb-2",children:"نصائح لتحديد الميزانية والموعد النهائي:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside text-gray-700 space-y-1 text-sm",children:[(0,s.jsx)("li",{children:"حدد ميزانية واقعية تتناسب مع حجم وتعقيد المشروع"}),(0,s.jsx)("li",{children:"ضع في اعتبارك تكلفة المواد والعمالة والوقت المطلوب"}),(0,s.jsx)("li",{children:"امنح وقتاً كافياً لإنجاز المشروع بجودة عالية"}),(0,s.jsx)("li",{children:"ضع في الحسبان الوقت اللازم للتعديلات والمراجعات"})]})]})]})]})};var N=r(44195);let w=()=>{let{formData:e}=c();return(0,s.jsxs)("div",{children:[(0,s.jsx)("h2",{className:"text-xl font-bold text-navy mb-6",children:"مراجعة تفاصيل المشروع"}),(0,s.jsxs)("div",{className:"space-y-8",children:[(0,s.jsx)("div",{className:"bg-beige bg-opacity-50 p-4 rounded-md",children:(0,s.jsx)("p",{className:"text-gray-700 mb-4",children:"يرجى مراجعة تفاصيل مشروعك بعناية قبل النشر. بعد النشر، سيتمكن الحرفيون من الاطلاع على مشروعك وتقديم عروضهم."})}),(0,s.jsxs)("div",{className:"border border-gray-200 rounded-md overflow-hidden",children:[(0,s.jsx)("div",{className:"bg-gray-50 px-4 py-3 border-b border-gray-200",children:(0,s.jsx)("h3",{className:"text-lg font-medium text-navy",children:"التفاصيل الأساسية"})}),(0,s.jsxs)("div",{className:"p-4 space-y-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"عنوان المشروع"}),(0,s.jsx)("p",{className:"text-gray-900",children:e.title||"غير محدد"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"فئة المشروع"}),(0,s.jsx)("p",{className:"text-gray-900",children:(e=>({carpentry:"النجارة",plumbing:"السباكة",electrical:"الكهرباء",painting:"الدهان",construction:"البناء",hvac:"التكييف",metalwork:"الحدادة",landscaping:"تنسيق الحدائق",other:"أخرى"})[e]||e)(e.category)||"غير محدد"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"الموقع"}),(0,s.jsx)("p",{className:"text-gray-900",children:e.location||"غير محدد"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"وصف المشروع"}),(0,s.jsx)("p",{className:"text-gray-900 whitespace-pre-line",children:e.description||"غير محدد"})]})]})]}),(0,s.jsxs)("div",{className:"border border-gray-200 rounded-md overflow-hidden",children:[(0,s.jsx)("div",{className:"bg-gray-50 px-4 py-3 border-b border-gray-200",children:(0,s.jsx)("h3",{className:"text-lg font-medium text-navy",children:"المتطلبات والمواصفات"})}),(0,s.jsxs)("div",{className:"p-4 space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"متطلبات المشروع"}),(0,s.jsx)("p",{className:"text-gray-900 whitespace-pre-line",children:e.requirements||"غير محدد"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"المهارات المطلوبة"}),e.skills.length>0?(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:e.skills.map((e,t)=>(0,s.jsx)("span",{className:"bg-skyblue px-3 py-1 rounded-full text-navy text-sm",children:e},t))}):(0,s.jsx)("p",{className:"text-gray-900",children:"غير محدد"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"المرفقات"}),e.attachments.length>0?(0,s.jsx)("ul",{className:"list-disc list-inside text-gray-900",children:e.attachments.map((e,t)=>(0,s.jsx)("li",{children:e},t))}):(0,s.jsx)("p",{className:"text-gray-900",children:"لا توجد مرفقات"})]})]})]}),(0,s.jsxs)("div",{className:"border border-gray-200 rounded-md overflow-hidden",children:[(0,s.jsx)("div",{className:"bg-gray-50 px-4 py-3 border-b border-gray-200",children:(0,s.jsx)("h3",{className:"text-lg font-medium text-navy",children:"الميزانية والجدول الزمني"})}),(0,s.jsx)("div",{className:"p-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"ميزانية المشروع"}),(0,s.jsx)("p",{className:"text-gray-900",children:e.budget?`${e.budget.toLocaleString()} ل.س`:"غير محدد"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-500",children:"الموعد النهائي"}),(0,s.jsx)("p",{className:"text-gray-900",children:(0,N.Yq)(e.deadline)})]})]})})]}),(0,s.jsx)("div",{className:"bg-navy bg-opacity-5 p-4 rounded-md",children:(0,s.jsxs)("div",{className:"flex items-start",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(0,s.jsx)("svg",{className:"h-5 w-5 text-navy",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor",children:(0,s.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,s.jsx)("div",{className:"mr-3",children:(0,s.jsx)("p",{className:"text-sm text-navy",children:'بالضغط على "نشر المشروع"، أنت توافق على شروط وأحكام منصة دوزان وسياسة الخصوصية.'})})]})})]})]})},k=()=>{let e=(0,l.useRouter)(),{formData:t,currentStep:r,setCurrentStep:a,isSubmitting:n,setIsSubmitting:i}=c(),o=e=>{switch(e){case 1:return t.title&&t.title.trim().length>=5&&t.description&&t.description.trim().length>=20&&t.category&&""!==t.category.trim();case 2:return t.requirements&&""!==t.requirements.trim()&&t.skills&&t.skills.length>0;case 3:let r=new Date;r.setHours(0,0,0,0);let s=t.deadline?new Date(t.deadline):null;return t.budget&&t.budget>0&&t.deadline&&""!==t.deadline.trim()&&s&&s>=r;default:return!0}},d=async()=>{i(!0);try{console.log("Submitting job:",t),await new Promise(e=>setTimeout(e,2e3)),e.push("/job-posted-success")}catch(e){console.error("Error submitting job:",e),alert("حدث خطأ أثناء نشر المشروع. يرجى المحاولة مرة أخرى.")}finally{i(!1)}};return(0,s.jsxs)(p,{currentStep:r,totalSteps:4,onNext:()=>{if(r<4){if(o(r))a(r+1),window.scrollTo(0,0);else{let e="يرجى إكمال جميع الحقول المطلوبة:\n";switch(r){case 1:(!t.title||t.title.trim().length<5)&&(e+="- عنوان المشروع (5 أحرف على الأقل)\n"),(!t.description||t.description.trim().length<20)&&(e+="- وصف المشروع (20 حرف على الأقل)\n"),t.category||(e+="- فئة المشروع\n");break;case 2:t.requirements&&""!==t.requirements.trim()||(e+="- متطلبات المشروع\n"),t.skills&&0!==t.skills.length||(e+="- المهارات المطلوبة (مهارة واحدة على الأقل)\n");break;case 3:(!t.budget||t.budget<=0)&&(e+="- ميزانية المشروع\n"),t.deadline||(e+="- الموعد النهائي للمشروع\n")}alert(e)}}else d()},onPrevious:()=>{r>1&&(a(r-1),window.scrollTo(0,0))},isLastStep:4===r,isSubmitting:n,children:[1===r&&(0,s.jsx)(f,{}),2===r&&(0,s.jsx)(y,{}),3===r&&(0,s.jsx)(j,{}),4===r&&(0,s.jsx)(w,{})]})},C=()=>(0,s.jsx)(d,{children:(0,s.jsx)(k,{})})},52080:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(45512);r(58009);var a=r(90993),n=r(87272);let l=()=>{let{data:e,update:t}=(0,a.useSession)(),r=async t=>{if(e?.user)try{let r=e.user.email;await (0,a.signOut)({redirect:!1}),await new Promise(e=>setTimeout(e,100));let s=await (0,a.signIn)("credentials",{email:r,password:"123456",role:t,redirect:!1});s?.ok&&window.location.reload()}catch(e){console.error("Error switching role:",e)}};return e?.user?(0,s.jsxs)("div",{className:"fixed bottom-4 left-4 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4",children:[(0,s.jsx)("h3",{className:"text-sm font-semibold text-gray-700 mb-3",children:"تبديل الدور (للتطوير)"}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(n.Button,{size:"sm",variant:"client"===e.user.role?"default":"outline",onClick:()=>r("client"),className:"w-full text-xs",children:"\uD83D\uDC64 عميل"}),(0,s.jsx)(n.Button,{size:"sm",variant:"craftsman"===e.user.role?"default":"outline",onClick:()=>r("craftsman"),className:"w-full text-xs",children:"\uD83D\uDC68‍\uD83D\uDD27 حرفي"}),(0,s.jsx)(n.Button,{size:"sm",variant:"admin"===e.user.role?"default":"outline",onClick:()=>r("admin"),className:"w-full text-xs",children:"\uD83D\uDC51 مدير"})]}),(0,s.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["الدور الحالي: ",(0,s.jsx)("span",{className:"font-medium",children:e.user.role})]})})]}):null}},7405:(e,t,r)=>{"use strict";r.d(t,{default:()=>n});var s=r(45512),a=r(90993);function n({children:e}){return(0,s.jsx)(a.SessionProvider,{children:e})}},87272:(e,t,r)=>{"use strict";r.d(t,{Button:()=>d});var s=r(45512),a=r(58009),n=r.n(a),l=r(21643),i=r(44195);let o=(0,l.F)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-navy text-white hover:bg-navy/90",destructive:"bg-red-500 text-white hover:bg-red-500/90",outline:"border border-input hover:bg-accent hover:text-accent-foreground",secondary:"bg-teal text-white hover:bg-teal/90",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md"}},defaultVariants:{variant:"default",size:"default"}}),d=n().forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...n},l)=>(0,s.jsx)("button",{className:(0,i.cn)(o({variant:t,size:r,className:e})),ref:l,...n}));d.displayName="Button"},16239:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(45512),a=r(58009);let n=({options:e,value:t,onChange:r,placeholder:n="اختر خيار",className:l="",disabled:i=!1,error:o,label:d,required:c=!1})=>{let[m,u]=(0,a.useState)(!1),[x,h]=(0,a.useState)(""),p=(0,a.useRef)(null),g=(0,a.useRef)(null),b=e.find(e=>e.value===t),v=e.filter(e=>e.label.toLowerCase().includes(x.toLowerCase()));(0,a.useEffect)(()=>{let e=e=>{p.current&&!p.current.contains(e.target)&&(u(!1),h(""))};return document.addEventListener("mousedown",e),()=>{document.removeEventListener("mousedown",e)}},[]);let f=e=>{r(e),u(!1),h("")};return(0,s.jsxs)("div",{className:"relative",children:[d&&(0,s.jsxs)("label",{className:"block text-gray-700 font-medium mb-2",children:[d,c&&(0,s.jsx)("span",{className:"text-red-500 mr-1",children:"*"})]}),(0,s.jsxs)("div",{ref:p,className:"relative",children:[(0,s.jsx)("div",{onClick:()=>{i||(u(!m),m||setTimeout(()=>{g.current?.focus()},100))},className:`
            relative w-full px-4 py-3 bg-white border rounded-xl cursor-pointer transition-all duration-300
            ${m?"border-teal ring-2 ring-teal/20 shadow-lg":o?"border-red-300 hover:border-red-400":"border-gray-300 hover:border-gray-400"}
            ${i?"bg-gray-50 cursor-not-allowed opacity-60":"hover:shadow-md"}
            ${l}
          `,children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse flex-1",children:[b?.icon&&(0,s.jsx)("span",{className:"text-lg flex-shrink-0",children:b.icon}),(0,s.jsx)("span",{className:`block truncate ${b?"text-gray-900":"text-gray-500"}`,children:b?b.label:n})]}),(0,s.jsx)("div",{className:"flex-shrink-0 mr-3",children:(0,s.jsx)("svg",{className:`w-5 h-5 text-gray-400 transition-transform duration-300 ${m?"rotate-180":"rotate-0"}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]})}),m&&(0,s.jsxs)("div",{className:"absolute z-50 w-full mt-2 bg-white border border-gray-200 rounded-xl shadow-xl max-h-60 overflow-hidden",children:[(0,s.jsx)("div",{className:"p-3 border-b border-gray-100",children:(0,s.jsx)("input",{ref:g,type:"text",value:x,onChange:e=>h(e.target.value),onKeyDown:e=>{"Enter"===e.key?(e.preventDefault(),v.length>0&&f(v[0].value)):"Escape"===e.key&&(u(!1),h(""))},placeholder:"ابحث...",className:"w-full px-3 py-2 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal"})}),(0,s.jsx)("div",{className:"max-h-48 overflow-y-auto",children:v.length>0?v.map(e=>(0,s.jsxs)("div",{onClick:()=>f(e.value),className:`
                      flex items-center space-x-3 space-x-reverse px-4 py-3 cursor-pointer transition-colors duration-200
                      ${t===e.value?"bg-gradient-to-r from-navy/10 to-teal/10 text-navy border-r-4 border-teal":"hover:bg-gray-50"}
                    `,children:[e.icon&&(0,s.jsx)("span",{className:"text-lg flex-shrink-0",children:e.icon}),(0,s.jsx)("span",{className:"block truncate",children:e.label}),t===e.value&&(0,s.jsx)("div",{className:"flex-shrink-0 mr-auto",children:(0,s.jsx)("svg",{className:"w-5 h-5 text-teal",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})})]},e.value)):(0,s.jsx)("div",{className:"px-4 py-3 text-gray-500 text-center",children:"لا توجد نتائج"})})]})]}),o&&(0,s.jsxs)("p",{className:"mt-2 text-red-500 text-sm flex items-center",children:[(0,s.jsx)("svg",{className:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),o]})]})}},70030:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(45512),a=r(58009),n=r(87272);let l=({onImagesChange:e,maxImages:t=5,existingImages:r=[],label:l="إضافة صور",description:i="اختر صور واضحة تظهر التفاصيل المطلوبة",required:o=!1})=>{let[d,c]=(0,a.useState)([]),[m,u]=(0,a.useState)(r),[x,h]=(0,a.useState)(!1),p=(0,a.useRef)(null),g=r=>{if(!r)return;let s=Array.from(r);if(d.length+m.length+s.length>t){alert(`يمكنك رفع ${t} صور كحد أقصى`);return}let a=s.filter(e=>e.type.startsWith("image/")?!(e.size>5242880)||(alert(`${e.name} حجم الملف كبير جداً (الحد الأقصى 5MB)`),!1):(alert(`${e.name} ليس ملف صورة صحيح`),!1));if(0===a.length)return;let n=[...d,...a];c(n),e(n),a.forEach(e=>{let t=new FileReader;t.onload=e=>{u(t=>[...t,e.target?.result])},t.readAsDataURL(e)})},b=t=>{if(t<r.length)u(e=>e.filter((e,r)=>r!==t));else{let s=t-r.length,a=d.filter((e,t)=>t!==s);c(a),e(a),u(e=>e.filter((e,r)=>r!==t))}},v=e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?h(!0):"dragleave"===e.type&&h(!1)};return(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[l," ",o&&(0,s.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:i})]}),(0,s.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${x?"border-teal bg-teal/5":"border-gray-300 hover:border-teal hover:bg-gray-50"}`,onDragEnter:v,onDragLeave:v,onDragOver:v,onDrop:e=>{e.preventDefault(),e.stopPropagation(),h(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&g(e.dataTransfer.files)},children:[(0,s.jsx)("input",{ref:p,type:"file",multiple:!0,accept:"image/*",onChange:e=>g(e.target.files),className:"hidden"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("p",{className:"text-gray-600 mb-2",children:"اسحب الصور هنا أو"}),(0,s.jsx)(n.Button,{type:"button",variant:"outline",onClick:()=>p.current?.click(),children:"اختر الصور"})]}),(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["PNG, JPG, GIF حتى ",t," صور (حد أقصى 5MB لكل صورة)"]})]})]}),m.length>0&&(0,s.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:m.map((e,t)=>(0,s.jsxs)("div",{className:"relative group",children:[(0,s.jsx)("img",{src:e,alt:`صورة ${t+1}`,className:"w-full h-32 object-cover rounded-lg border border-gray-200"}),(0,s.jsx)("button",{type:"button",onClick:()=>b(t),className:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-sm hover:bg-red-600 transition-colors",children:"\xd7"}),(0,s.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center",children:(0,s.jsx)("button",{type:"button",onClick:()=>window.open(e,"_blank"),className:"opacity-0 group-hover:opacity-100 bg-white text-gray-700 px-3 py-1 rounded-md text-sm transition-opacity",children:"عرض"})})]},t))}),(0,s.jsxs)("div",{className:"text-sm text-gray-500 text-center",children:[m.length," من ",t," صور"]})]})}},60636:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var s=r(90993),a=r(79334),n=r(58009);let l=()=>{let{data:e,status:t}=(0,s.useSession)(),r=(0,a.useRouter)(),[l,i]=(0,n.useState)(!1),[o,d]=(0,n.useState)(null),c=async e=>{i(!0),d(null);try{let t=await (0,s.signIn)("credentials",{email:e.email,password:e.password,redirect:!1});if(t?.error)return d("البريد الإلكتروني أو كلمة المرور غير صحيحة"),!1;if(t?.ok)return r.push("/dashboard"),!0;return!1}catch(e){return d("حدث خطأ أثناء تسجيل الدخول"),!1}finally{i(!1)}},m=async e=>{i(!0),d(null);try{let t=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!t.ok){let e=await t.json();return d(e.message||"حدث خطأ أثناء إنشاء الحساب"),!1}let a=await (0,s.signIn)("credentials",{email:e.email,password:e.password,redirect:!1});if(a?.ok)return r.push("/dashboard"),!0;return!1}catch(e){return d("حدث خطأ أثناء إنشاء الحساب"),!1}finally{i(!1)}},u=async()=>{i(!0);try{await (0,s.signOut)({redirect:!1}),r.push("/")}catch(e){console.error("Logout error:",e)}finally{i(!1)}},x=async()=>{i(!0);try{await (0,s.signIn)("google",{callbackUrl:"/dashboard"})}catch(e){d("حدث خطأ أثناء تسجيل الدخول بـ Google")}finally{i(!1)}},h=async()=>{i(!0);try{await (0,s.signIn)("facebook",{callbackUrl:"/dashboard"})}catch(e){d("حدث خطأ أثناء تسجيل الدخول بـ Facebook")}finally{i(!1)}},p="authenticated"===t,g=e?.user,b=g?.role==="client",v=g?.role==="craftsman",f=g?.role==="admin",y=e=>!!g&&(Array.isArray(e)?e.includes(g.role):g.role===e);return{user:g,session:e,isAuthenticated:p,isLoading:l||"loading"===t,error:o,isClient:b,isCraftsman:v,isAdmin:f,hasRole:y,canAccess:e=>p&&y(e),login:c,register:m,logout:u,loginWithGoogle:x,loginWithFacebook:h,setError:d}}},44195:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>l,cn:()=>n});var s=r(82281),a=r(94805);function n(...e){return(0,a.QP)((0,s.$)(e))}function l(e){if(!e)return"غير محدد";try{let t=new Date(e);if(isNaN(t.getTime()))return"تاريخ غير صحيح";let r=t.getFullYear(),s=t.getMonth()+1,a=t.getDate();return`${a} ${["يناير","فبراير","مارس","أبريل","مايو","يونيو","يوليو","أغسطس","سبتمبر","أكتوبر","نوفمبر","ديسمبر"][s-1]} ${r}`}catch(e){return"تاريخ غير صحيح"}}},71354:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,metadata:()=>i});var s=r(62740),a=r(23915),n=r.n(a);r(61135);var l=r(99305);r(95652);let i={title:"Dozan - Connect with Skilled Craftsmen",description:"Find skilled craftsmen for your projects or offer your services as a craftsman"};function o({children:e}){return(0,s.jsx)("html",{lang:"ar",dir:"rtl",children:(0,s.jsx)("body",{className:`${n().variable} font-cairo antialiased`,children:(0,s.jsxs)(l.default,{children:[e,!1]})})})}},34300:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\g\\\\Duzan Website\\\\frontend\\\\src\\\\app\\\\post-job\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\post-job\\page.tsx","default")},95652:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\g\\\\Duzan Website\\\\frontend\\\\src\\\\components\\\\dev\\\\RoleSwitcher.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\components\\dev\\RoleSwitcher.tsx","default")},99305:(e,t,r)=>{"use strict";r.d(t,{default:()=>s});let s=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\g\\\\Duzan Website\\\\frontend\\\\src\\\\components\\\\providers\\\\SessionProvider.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\components\\providers\\SessionProvider.tsx","default")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(88077);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},61135:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,3351,7180,4209],()=>r(23265));module.exports=s})();