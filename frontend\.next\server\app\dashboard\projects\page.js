(()=>{var e={};e.id=7636,e.ids=[7636],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},84953:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var r=s(70260),a=s(28203),n=s(25155),l=s.n(n),i=s(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let o=["",{children:["dashboard",{children:["projects",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,27195)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\dashboard\\projects\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,71354)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\dashboard\\projects\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/dashboard/projects/page",pathname:"/dashboard/projects",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},24525:(e,t,s)=>{Promise.resolve().then(s.bind(s,27195))},5965:(e,t,s)=>{Promise.resolve().then(s.bind(s,72993))},72993:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>m});var r=s(45512),a=s(58009),n=s(4209),l=s(18542),i=s(76719),d=s(87272),o=s(70030);let c=({onBeforeImagesChange:e,onAfterImagesChange:t,projectTitle:s,isCompleted:n=!1})=>{let[l,i]=(0,a.useState)([]),[c,x]=(0,a.useState)([]),[m,h]=(0,a.useState)("before"),g=l.length>0,u=l.length>0&&c.length>0;return(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-lg p-6",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("h3",{className:"text-xl font-bold text-navy mb-2",children:["توثيق المشروع: ",s]}),(0,r.jsx)("p",{className:"text-gray-600",children:"قم برفع صور قبل وبعد العمل لتوثيق جودة عملك وبناء معرض أعمالك"})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:`flex items-center ${"before"===m?"text-teal":l.length>0?"text-green-600":"text-gray-400"}`,children:[(0,r.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${"before"===m?"bg-teal text-white":l.length>0?"bg-green-600 text-white":"bg-gray-200 text-gray-600"}`,children:l.length>0?"✓":"1"}),(0,r.jsx)("span",{className:"mr-2 font-medium",children:"صور قبل العمل"})]}),(0,r.jsx)("div",{className:`flex-1 h-1 mx-4 rounded ${l.length>0?"bg-green-600":"bg-gray-200"}`}),(0,r.jsxs)("div",{className:`flex items-center ${"after"===m?"text-teal":c.length>0?"text-green-600":"text-gray-400"}`,children:[(0,r.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${"after"===m?"bg-teal text-white":c.length>0?"bg-green-600 text-white":"bg-gray-200 text-gray-600"}`,children:c.length>0?"✓":"2"}),(0,r.jsx)("span",{className:"mr-2 font-medium",children:"صور بعد العمل"})]})]})}),"before"===m&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-blue-600 mt-0.5 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-blue-800 mb-1",children:"صور قبل البدء بالعمل"}),(0,r.jsx)("p",{className:"text-blue-700 text-sm",children:"التقط صور واضحة تظهر حالة المكان أو الشيء قبل البدء بالعمل. هذا سيساعد في إظهار الفرق والتطور في عملك."})]})]})}),(0,r.jsx)(o.A,{onImagesChange:t=>{i(t),e(t)},maxImages:5,label:"صور قبل العمل",description:"التقط صور من زوايا مختلفة تظهر الحالة الأولية",required:!0}),(0,r.jsx)("div",{className:"flex justify-end",children:(0,r.jsx)(d.Button,{onClick:()=>h("after"),disabled:!g,className:"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90",children:"التالي: صور بعد العمل"})})]}),"after"===m&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start",children:[(0,r.jsx)("svg",{className:"w-5 h-5 text-green-600 mt-0.5 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-semibold text-green-800 mb-1",children:"صور بعد إنجاز العمل"}),(0,r.jsx)("p",{className:"text-green-700 text-sm",children:"التقط صور تظهر النتيجة النهائية لعملك. تأكد من أن الصور واضحة وتظهر جودة العمل المنجز."})]})]})}),(0,r.jsx)(o.A,{onImagesChange:e=>{x(e),t(e)},maxImages:5,label:"صور بعد العمل",description:"التقط صور من نفس الزوايا لإظهار الفرق بوضوح",required:!0}),(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)(d.Button,{variant:"outline",onClick:()=>h("before"),children:"العودة للخطوة السابقة"}),(0,r.jsx)(d.Button,{disabled:!u,className:"bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800",children:n?"حفظ التوثيق":"إنهاء المشروع"})]})]}),(l.length>0||c.length>0)&&(0,r.jsxs)("div",{className:"mt-8 pt-6 border-t border-gray-200",children:[(0,r.jsx)("h4",{className:"font-semibold text-gray-700 mb-4",children:"ملخص الصور المرفوعة:"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,r.jsx)("div",{className:"font-medium text-blue-800",children:"صور قبل العمل"}),(0,r.jsxs)("div",{className:"text-blue-600",children:[l.length," صور"]})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-3 rounded-lg",children:[(0,r.jsx)("div",{className:"font-medium text-green-800",children:"صور بعد العمل"}),(0,r.jsxs)("div",{className:"text-green-600",children:[c.length," صور"]})]})]})]})]})};var x=s(76616);let m=()=>{let[e,t]=(0,a.useState)(null),[s,o]=(0,a.useState)(!1),[m,h]=(0,a.useState)("all"),g=[{id:1,title:"تصميم وتنفيذ خزائن مطبخ",description:"تصميم وتنفيذ خزائن مطبخ خشبية عصرية مع جزيرة وسطية",status:"in_progress",budget:35e4,deadline:"2024-03-15",startDate:"2024-02-01",client:{name:"أحمد محمد",avatar:"https://randomuser.me/api/portraits/men/1.jpg",rating:4.8},hasBeforeImages:!0,hasAfterImages:!1},{id:2,title:"ترميم غرفة نوم",description:"ترميم وتجديد غرفة نوم كاملة مع تركيب أرضية جديدة",status:"completed",budget:28e4,deadline:"2024-01-30",startDate:"2024-01-15",completedDate:"2024-01-28",client:{name:"فاطمة أحمد",avatar:"https://randomuser.me/api/portraits/women/1.jpg",rating:4.9},hasBeforeImages:!0,hasAfterImages:!0},{id:3,title:"تركيب خزائن مكتب",description:"تصميم وتركيب خزائن مكتب مخصصة مع وحدات تخزين",status:"pending",budget:15e4,deadline:"2024-04-10",client:{name:"محمد علي",avatar:"https://randomuser.me/api/portraits/men/2.jpg",rating:4.7},hasBeforeImages:!1,hasAfterImages:!1}],u=g.filter(e=>"all"===m||e.status===m),p=e=>{switch(e){case"pending":return(0,r.jsx)(i.E,{variant:"warning",children:"في الانتظار"});case"in_progress":return(0,r.jsx)(i.E,{variant:"info",children:"قيد التنفيذ"});case"completed":return(0,r.jsx)(i.E,{variant:"success",children:"مكتمل"});case"cancelled":return(0,r.jsx)(i.E,{variant:"destructive",children:"ملغي"});default:return(0,r.jsx)(i.E,{variant:"outline",children:e})}},b=e=>{t(e),o(!0)},v=e=>{t(e),o(!0)};return(0,r.jsxs)(n.default,{children:[(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-beige via-white to-skyblue/20 py-8",children:(0,r.jsxs)("div",{className:"container mx-auto px-4",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-navy mb-2",children:"إدارة المشاريع"}),(0,r.jsx)("p",{className:"text-gray-600",children:"تابع مشاريعك الحالية ووثق أعمالك بصور قبل وبعد"})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("div",{className:"flex flex-wrap gap-2",children:[{id:"all",label:"جميع المشاريع",count:g.length},{id:"pending",label:"في الانتظار",count:g.filter(e=>"pending"===e.status).length},{id:"in_progress",label:"قيد التنفيذ",count:g.filter(e=>"in_progress"===e.status).length},{id:"completed",label:"مكتملة",count:g.filter(e=>"completed"===e.status).length}].map(e=>(0,r.jsxs)("button",{onClick:()=>h(e.id),className:`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${m===e.id?"bg-navy text-white":"bg-white text-gray-700 hover:bg-gray-50 border border-gray-200"}`,children:[e.label," (",e.count,")"]},e.id))})}),(0,r.jsx)("div",{className:"grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6",children:u.map(e=>(0,r.jsxs)(l.Zp,{className:"border-0 bg-white/90 backdrop-blur-sm hover:shadow-xl transition-all duration-300",children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,r.jsx)(l.ZB,{className:"text-lg leading-tight",children:e.title}),p(e.status)]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,r.jsx)("img",{src:e.client.avatar,alt:e.client.name,className:"w-8 h-8 rounded-full"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-sm",children:e.client.name}),(0,r.jsxs)("div",{className:"flex items-center text-xs text-gray-500",children:[(0,r.jsx)("svg",{className:"w-3 h-3 text-yellow-500 ml-1",fill:"currentColor",viewBox:"0 0 20 20",children:(0,r.jsx)("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"})}),e.client.rating]})]})]})]}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsx)("p",{className:"text-gray-700 text-sm mb-4 line-clamp-2",children:e.description}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3 mb-4",children:[(0,r.jsxs)("div",{className:"bg-green-50 p-2 rounded-lg",children:[(0,r.jsx)("div",{className:"text-green-600 text-xs font-medium",children:"الميزانية"}),(0,r.jsxs)("div",{className:"font-bold text-green-700 text-sm",children:[e.budget.toLocaleString()," ل.س"]})]}),(0,r.jsxs)("div",{className:"bg-blue-50 p-2 rounded-lg",children:[(0,r.jsx)("div",{className:"text-blue-600 text-xs font-medium",children:"الموعد النهائي"}),(0,r.jsx)("div",{className:"font-bold text-blue-700 text-xs",children:(0,r.jsx)(x.A,{date:e.deadline})})]})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("div",{className:"text-xs font-medium text-gray-600 mb-2",children:"حالة التوثيق:"}),(0,r.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:`w-3 h-3 rounded-full ml-1 ${e.hasBeforeImages?"bg-green-500":"bg-gray-300"}`}),(0,r.jsx)("span",{className:"text-xs text-gray-600",children:"صور قبل"})]}),(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:`w-3 h-3 rounded-full ml-1 ${e.hasAfterImages?"bg-green-500":"bg-gray-300"}`}),(0,r.jsx)("span",{className:"text-xs text-gray-600",children:"صور بعد"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:["pending"===e.status&&(0,r.jsx)(d.Button,{size:"sm",className:"w-full bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90",onClick:()=>b(e),children:"بدء العمل وتوثيق الحالة الأولية"}),"in_progress"===e.status&&(0,r.jsxs)("div",{className:"space-y-2",children:[!e.hasBeforeImages&&(0,r.jsx)(d.Button,{size:"sm",variant:"outline",className:"w-full",onClick:()=>b(e),children:"إضافة صور قبل العمل"}),(0,r.jsx)(d.Button,{size:"sm",className:"w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800",onClick:()=>v(e),children:"إنهاء المشروع وتوثيق النتيجة"})]}),"completed"===e.status&&(0,r.jsxs)("div",{className:"text-center py-2",children:[(0,r.jsx)("div",{className:"text-green-600 text-sm font-medium",children:"✓ تم إنجاز المشروع بنجاح"}),e.completedDate&&(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["اكتمل في ",(0,r.jsx)(x.A,{date:e.completedDate})]})]})]})]})]},e.id))}),0===u.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-12 h-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})})}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"لا توجد مشاريع"}),(0,r.jsx)("p",{className:"text-gray-500",children:"لا توجد مشاريع في هذه الفئة حالياً"})]})]})}),s&&e&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,r.jsx)("div",{className:"bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-navy",children:"توثيق المشروع"}),(0,r.jsx)("button",{onClick:()=>o(!1),className:"text-gray-400 hover:text-gray-600",children:(0,r.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,r.jsx)(c,{projectTitle:e.title,onBeforeImagesChange:e=>{console.log("Before images:",e)},onAfterImagesChange:e=>{console.log("After images:",e)},isCompleted:"completed"===e.status})]})})})]})}},76719:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(45512);s(58009);var a=s(21643),n=s(44195);let l=(0,a.F)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-navy text-white hover:bg-navy/80",secondary:"border-transparent bg-teal text-white hover:bg-teal/80",destructive:"border-transparent bg-red-500 text-white hover:bg-red-500/80",outline:"text-navy border-navy",success:"border-transparent bg-green-500 text-white hover:bg-green-500/80",warning:"border-transparent bg-yellow-500 text-white hover:bg-yellow-500/80",info:"border-transparent bg-skyblue text-navy hover:bg-skyblue/80"}},defaultVariants:{variant:"default"}});function i({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,n.cn)(l({variant:t}),e),...s})}},76616:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(45512),a=s(58009),n=s(44195);let l=({date:e,className:t=""})=>{let[s,l]=(0,a.useState)(!1);return((0,a.useEffect)(()=>{l(!0)},[]),s)?(0,r.jsx)("span",{className:t,children:(0,n.Yq)(e)}):(0,r.jsx)("span",{className:t,children:"تحميل..."})}},70030:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(45512),a=s(58009),n=s(87272);let l=({onImagesChange:e,maxImages:t=5,existingImages:s=[],label:l="إضافة صور",description:i="اختر صور واضحة تظهر التفاصيل المطلوبة",required:d=!1})=>{let[o,c]=(0,a.useState)([]),[x,m]=(0,a.useState)(s),[h,g]=(0,a.useState)(!1),u=(0,a.useRef)(null),p=s=>{if(!s)return;let r=Array.from(s);if(o.length+x.length+r.length>t){alert(`يمكنك رفع ${t} صور كحد أقصى`);return}let a=r.filter(e=>e.type.startsWith("image/")?!(e.size>5242880)||(alert(`${e.name} حجم الملف كبير جداً (الحد الأقصى 5MB)`),!1):(alert(`${e.name} ليس ملف صورة صحيح`),!1));if(0===a.length)return;let n=[...o,...a];c(n),e(n),a.forEach(e=>{let t=new FileReader;t.onload=e=>{m(t=>[...t,e.target?.result])},t.readAsDataURL(e)})},b=t=>{if(t<s.length)m(e=>e.filter((e,s)=>s!==t));else{let r=t-s.length,a=o.filter((e,t)=>t!==r);c(a),e(a),m(e=>e.filter((e,s)=>s!==t))}},v=e=>{e.preventDefault(),e.stopPropagation(),"dragenter"===e.type||"dragover"===e.type?g(!0):"dragleave"===e.type&&g(!1)};return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[l," ",d&&(0,r.jsx)("span",{className:"text-red-500",children:"*"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-4",children:i})]}),(0,r.jsxs)("div",{className:`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${h?"border-teal bg-teal/5":"border-gray-300 hover:border-teal hover:bg-gray-50"}`,onDragEnter:v,onDragLeave:v,onDragOver:v,onDrop:e=>{e.preventDefault(),e.stopPropagation(),g(!1),e.dataTransfer.files&&e.dataTransfer.files[0]&&p(e.dataTransfer.files)},children:[(0,r.jsx)("input",{ref:u,type:"file",multiple:!0,accept:"image/*",onChange:e=>p(e.target.files),className:"hidden"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-gray-600 mb-2",children:"اسحب الصور هنا أو"}),(0,r.jsx)(n.Button,{type:"button",variant:"outline",onClick:()=>u.current?.click(),children:"اختر الصور"})]}),(0,r.jsxs)("p",{className:"text-xs text-gray-500",children:["PNG, JPG, GIF حتى ",t," صور (حد أقصى 5MB لكل صورة)"]})]})]}),x.length>0&&(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:x.map((e,t)=>(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)("img",{src:e,alt:`صورة ${t+1}`,className:"w-full h-32 object-cover rounded-lg border border-gray-200"}),(0,r.jsx)("button",{type:"button",onClick:()=>b(t),className:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-sm hover:bg-red-600 transition-colors",children:"\xd7"}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 rounded-lg flex items-center justify-center",children:(0,r.jsx)("button",{type:"button",onClick:()=>window.open(e,"_blank"),className:"opacity-0 group-hover:opacity-100 bg-white text-gray-700 px-3 py-1 rounded-md text-sm transition-opacity",children:"عرض"})})]},t))}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 text-center",children:[x.length," من ",t," صور"]})]})}},27195:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\g\\\\Duzan Website\\\\frontend\\\\src\\\\app\\\\dashboard\\\\projects\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\dashboard\\projects\\page.tsx","default")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,3351,7180,1057,4209],()=>s(84953));module.exports=r})();