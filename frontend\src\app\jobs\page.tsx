'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { formatDate } from '@/lib/utils';
import DateDisplay from '@/components/ui/DateDisplay';
import Dropdown from '@/components/ui/Dropdown';

interface Job {
  id: number;
  title: string;
  description: string;
  category: string;
  location: string;
  budget: number;
  deadline: string;
  postedAt: string;
  offersCount: number;
  status: 'open' | 'in_progress' | 'completed';
  skills: string[];
  images: string[];
  client: {
    name: string;
    rating: number;
    reviewsCount: number;
  };
}

const JobsPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  const categories = [
    { value: 'all', label: 'جميع الفئات', icon: '🔍' },
    { value: 'carpentry', label: 'النجارة', icon: '🪚' },
    { value: 'plumbing', label: 'السباكة', icon: '🔧' },
    { value: 'electrical', label: 'الكهرباء', icon: '⚡' },
    { value: 'painting', label: 'الدهان', icon: '🖌️' },
    { value: 'construction', label: 'البناء', icon: '🧱' },
    { value: 'hvac', label: 'التكييف', icon: '❄️' },
    { value: 'metalwork', label: 'الحدادة', icon: '🔨' },
    { value: 'landscaping', label: 'تنسيق الحدائق', icon: '🌱' },
  ];

  const sortOptions = [
    { value: 'newest', label: 'الأحدث', icon: '🕒' },
    { value: 'budget_high', label: 'الميزانية (الأعلى أولاً)', icon: '💰' },
    { value: 'budget_low', label: 'الميزانية (الأقل أولاً)', icon: '💵' },
    { value: 'deadline', label: 'الموعد النهائي', icon: '📅' },
  ];

  const jobs: Job[] = [
    {
      id: 1,
      title: 'تصميم وتنفيذ خزائن مطبخ خشبية عصرية',
      description: 'مطلوب نجار محترف لتصميم وتنفيذ خزائن مطبخ خشبية بتصميم عصري وعملي. المطبخ بمساحة 12 متر مربع ويحتاج إلى خزائن علوية وسفلية مع جزيرة وسطية.',
      category: 'carpentry',
      location: 'دمشق، المزة',
      budget: 350000,
      deadline: '2024-03-15',
      postedAt: '2024-01-15',
      offersCount: 8,
      status: 'open',
      skills: ['نجارة', 'تصميم', 'تركيب'],
      images: [
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400',
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'
      ],
      client: {
        name: 'أحمد محمد',
        rating: 4.8,
        reviewsCount: 12
      }
    },
    {
      id: 2,
      title: 'إصلاح نظام السباكة الرئيسي للمنزل',
      description: 'يحتاج المنزل إلى إصلاح شامل لنظام السباكة الرئيسي بما في ذلك تغيير الأنابيب القديمة وإصلاح التسريبات وتركيب صنابير جديدة.',
      category: 'plumbing',
      location: 'حلب، الفرقان',
      budget: 180000,
      deadline: '2024-02-28',
      postedAt: '2024-01-20',
      offersCount: 5,
      status: 'open',
      skills: ['سباكة', 'إصلاح', 'تركيب'],
      images: [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
        'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400'
      ],
      client: {
        name: 'فاطمة أحمد',
        rating: 4.9,
        reviewsCount: 8
      }
    },
    {
      id: 3,
      title: 'تركيب نظام كهربائي كامل لشقة جديدة',
      description: 'مطلوب كهربائي محترف لتركيب نظام كهربائي كامل لشقة جديدة بمساحة 120 متر مربع. يشمل العمل التمديدات والإضاءة واللوحة الكهربائية.',
      category: 'electrical',
      location: 'دمشق، جرمانا',
      budget: 220000,
      deadline: '2024-03-10',
      postedAt: '2024-01-18',
      offersCount: 12,
      status: 'open',
      skills: ['كهرباء', 'تمديدات', 'إضاءة'],
      images: [
        'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=400',
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'
      ],
      client: {
        name: 'محمد علي',
        rating: 4.7,
        reviewsCount: 15
      }
    },
    {
      id: 4,
      title: 'دهان شقة سكنية بالكامل',
      description: 'مطلوب دهان محترف لدهان شقة سكنية بمساحة 100 متر مربع. العمل يشمل تحضير الجدران والدهان بألوان حديثة حسب التصميم المطلوب.',
      category: 'painting',
      location: 'حمص، الوعر',
      budget: 120000,
      deadline: '2024-02-25',
      postedAt: '2024-01-22',
      offersCount: 6,
      status: 'open',
      skills: ['دهان', 'تحضير', 'تصميم'],
      images: [
        'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400',
        'https://images.unsplash.com/photo-1581858726788-75bc0f6a952d?w=400'
      ],
      client: {
        name: 'سارة خالد',
        rating: 4.6,
        reviewsCount: 9
      }
    }
  ];

  const filteredJobs = jobs.filter(job =>
    selectedCategory === 'all' || job.category === selectedCategory
  );

  const sortedJobs = [...filteredJobs].sort((a, b) => {
    switch (sortBy) {
      case 'budget_high':
        return b.budget - a.budget;
      case 'budget_low':
        return a.budget - b.budget;
      case 'deadline':
        return new Date(a.deadline).getTime() - new Date(b.deadline).getTime();
      default:
        return new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime();
    }
  });



  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return <Badge variant="success">مفتوح</Badge>;
      case 'in_progress':
        return <Badge variant="warning">قيد التنفيذ</Badge>;
      case 'completed':
        return <Badge variant="info">مكتمل</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white relative overflow-hidden">
        {/* خلفية هندسية متناسقة */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl"></div>
          <div className="absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl"></div>
        </div>

        {/* شبكة نقطية ناعمة */}
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full" style={{
            backgroundImage: 'radial-gradient(circle, #567C8D 1px, transparent 1px)',
            backgroundSize: '50px 50px'
          }}></div>
        </div>

        <div className="container mx-auto px-4 py-8 relative z-10">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="inline-block bg-gradient-to-r from-navy to-teal text-white px-8 py-3 rounded-full text-sm font-medium mb-6 shadow-lg">
              <span className="text-lg">💼</span>
              <span className="mr-2">فرص عمل متاحة</span>
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-navy mb-6 leading-tight">
              المشاريع
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-teal via-navy to-teal">
                المتاحة
              </span>
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              اكتشف المشاريع المتاحة وقدم عروضك للحصول على فرص عمل جديدة مع أفضل العملاء
            </p>
            <div className="mt-8 w-24 h-1 bg-gradient-to-r from-teal to-navy mx-auto rounded-full"></div>
          </div>

          {/* Filters */}
          <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20 p-8 mb-12">
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-navy mb-2">فلترة وترتيب المشاريع</h3>
              <p className="text-gray-600">اختر الفئة والترتيب المناسب لك</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <div>
                <Dropdown
                  label="فلترة حسب الفئة"
                  options={categories}
                  value={selectedCategory}
                  onChange={setSelectedCategory}
                  placeholder="اختر فئة المشروع"
                />
              </div>
              <div>
                <Dropdown
                  label="ترتيب حسب"
                  options={sortOptions}
                  value={sortBy}
                  onChange={setSortBy}
                  placeholder="اختر طريقة الترتيب"
                />
              </div>
            </div>

            {/* إحصائيات سريعة */}
            <div className="mt-8 pt-6 border-t border-gray-200">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div className="bg-gradient-to-r from-navy/5 to-teal/5 rounded-xl p-4">
                  <div className="text-2xl font-bold text-navy">{jobs.length}</div>
                  <div className="text-sm text-gray-600">إجمالي المشاريع</div>
                </div>
                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4">
                  <div className="text-2xl font-bold text-green-600">{filteredJobs.length}</div>
                  <div className="text-sm text-gray-600">مشاريع متاحة</div>
                </div>
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {Math.round(jobs.reduce((sum, job) => sum + job.budget, 0) / jobs.length / 1000)}K
                  </div>
                  <div className="text-sm text-gray-600">متوسط الميزانية</div>
                </div>
                <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {jobs.reduce((sum, job) => sum + job.offersCount, 0)}
                  </div>
                  <div className="text-sm text-gray-600">إجمالي العروض</div>
                </div>
              </div>
            </div>
          </div>

          {/* Jobs Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
            {sortedJobs.map((job, index) => (
              <Card
                key={job.id}
                className="group hover:shadow-2xl transition-all duration-500 border-0 bg-white/90 backdrop-blur-md hover:scale-105 hover:-translate-y-2 overflow-hidden"
                style={{
                  animation: `fadeInUp 0.6s ease-out ${index * 0.1}s both`
                }}
              >
                <CardHeader className="pb-4">
                  <div className="flex justify-between items-start mb-3">
                    <CardTitle className="text-xl leading-tight text-navy group-hover:text-teal transition-colors duration-300 line-clamp-2">
                      {job.title}
                    </CardTitle>
                    {getStatusBadge(job.status)}
                  </div>
                  <div className="flex items-center text-sm text-gray-500 space-x-4 space-x-reverse">
                    <span className="flex items-center">
                      <svg className="w-4 h-4 ml-1 text-teal" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      {job.location}
                    </span>
                    <span className="flex items-center">
                      <svg className="w-4 h-4 ml-1 text-navy" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <DateDisplay date={job.postedAt} />
                    </span>
                  </div>
                </CardHeader>

                <CardContent>
                  {/* صورة المشروع الرئيسية */}
                  {job.images && job.images.length > 0 && (
                    <div className="mb-6 relative overflow-hidden rounded-xl">
                      <img
                        src={job.images[0]}
                        alt={job.title}
                        className="w-full h-48 object-cover transition-transform duration-700 group-hover:scale-110"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

                      {/* شارة الفئة */}
                      <div className="absolute top-3 right-3">
                        <div className="bg-white/90 backdrop-blur-sm text-navy px-3 py-1 rounded-full text-sm font-medium shadow-lg">
                          {categories.find(cat => cat.value === job.category)?.icon || '🏗️'}
                          {categories.find(cat => cat.value === job.category)?.label || 'مشروع'}
                        </div>
                      </div>

                      {/* عدد الصور */}
                      {job.images.length > 1 && (
                        <div className="absolute bottom-3 left-3">
                          <div className="bg-black/50 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm flex items-center">
                            <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            {job.images.length} صور
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  <p className="text-gray-700 mb-6 line-clamp-3 leading-relaxed">{job.description}</p>

                  <div className="flex flex-wrap gap-2 mb-6">
                    {job.skills.slice(0, 3).map((skill, index) => (
                      <Badge key={index} variant="outline" className="text-xs bg-gradient-to-r from-navy/5 to-teal/5 border-navy/20 text-navy">
                        {skill}
                      </Badge>
                    ))}
                    {job.skills.length > 3 && (
                      <Badge variant="outline" className="text-xs bg-gray-50 border-gray-200 text-gray-600">
                        +{job.skills.length - 3}
                      </Badge>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="bg-gradient-to-r from-green-50 to-green-100 p-4 rounded-xl border border-green-200/50">
                      <div className="text-sm text-green-600 font-medium flex items-center">
                        <span className="text-green-500 ml-1">💰</span>
                        الميزانية
                      </div>
                      <div className="text-lg font-bold text-green-700">
                        {job.budget.toLocaleString()} ل.س
                      </div>
                    </div>
                    <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200/50">
                      <div className="text-sm text-blue-600 font-medium flex items-center">
                        <span className="text-blue-500 ml-1">📅</span>
                        الموعد النهائي
                      </div>
                      <div className="text-sm font-bold text-blue-700">
                        <DateDisplay date={job.deadline} />
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-600">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 ml-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <span className="font-medium">{job.client.rating}</span>
                      </div>
                      <div className="flex items-center text-teal font-medium">
                        <span className="text-teal ml-1">📝</span>
                        {job.offersCount} عرض
                      </div>
                    </div>
                    <Link href={`/jobs/${job.id}`}>
                      <Button size="sm" className="bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90 shadow-lg hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                        <span className="flex items-center">
                          عرض التفاصيل
                          <svg className="w-4 h-4 mr-1 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12l-3-3m0 0l-3 3m3-3v12" />
                          </svg>
                        </span>
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-16">
            <div className="bg-white/90 backdrop-blur-md rounded-2xl p-8 shadow-lg border border-white/20">
              <h3 className="text-2xl font-bold text-navy mb-4">هل تبحث عن المزيد؟</h3>
              <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
                اكتشف المزيد من المشاريع المتاحة أو قم بإنشاء مشروع جديد
              </p>
              <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
                <Button variant="outline" size="lg" className="px-8 border-navy text-navy hover:bg-navy hover:text-white">
                  تحميل المزيد من المشاريع
                </Button>
                <Link href="/jobs/create">
                  <Button size="lg" className="px-8 bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90">
                    إنشاء مشروع جديد
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* CSS Animations */}
        <style jsx>{`
          @keyframes fadeInUp {
            from {
              opacity: 0;
              transform: translateY(30px);
            }
            to {
              opacity: 1;
              transform: translateY(0);
            }
          }
        `}</style>
      </div>
    </MainLayout>
  );
};

export default JobsPage;
