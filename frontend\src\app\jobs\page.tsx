'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { formatDate } from '@/lib/utils';
import DateDisplay from '@/components/ui/DateDisplay';

interface Job {
  id: number;
  title: string;
  description: string;
  category: string;
  location: string;
  budget: number;
  deadline: string;
  postedAt: string;
  offersCount: number;
  status: 'open' | 'in_progress' | 'completed';
  skills: string[];
  images: string[];
  client: {
    name: string;
    rating: number;
    reviewsCount: number;
  };
}

const JobsPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('newest');

  const categories = [
    { id: 'all', name: 'جميع الفئات' },
    { id: 'carpentry', name: 'النجارة' },
    { id: 'plumbing', name: 'السباكة' },
    { id: 'electrical', name: 'الكهرباء' },
    { id: 'painting', name: 'الدهان' },
    { id: 'construction', name: 'البناء' },
    { id: 'hvac', name: 'التكييف' },
    { id: 'metalwork', name: 'الحدادة' },
    { id: 'landscaping', name: 'تنسيق الحدائق' },
  ];

  const jobs: Job[] = [
    {
      id: 1,
      title: 'تصميم وتنفيذ خزائن مطبخ خشبية عصرية',
      description: 'مطلوب نجار محترف لتصميم وتنفيذ خزائن مطبخ خشبية بتصميم عصري وعملي. المطبخ بمساحة 12 متر مربع ويحتاج إلى خزائن علوية وسفلية مع جزيرة وسطية.',
      category: 'carpentry',
      location: 'دمشق، المزة',
      budget: 350000,
      deadline: '2024-03-15',
      postedAt: '2024-01-15',
      offersCount: 8,
      status: 'open',
      skills: ['نجارة', 'تصميم', 'تركيب'],
      images: [
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400',
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=300&fit=crop'
      ],
      client: {
        name: 'أحمد محمد',
        rating: 4.8,
        reviewsCount: 12
      }
    },
    {
      id: 2,
      title: 'إصلاح نظام السباكة الرئيسي للمنزل',
      description: 'يحتاج المنزل إلى إصلاح شامل لنظام السباكة الرئيسي بما في ذلك تغيير الأنابيب القديمة وإصلاح التسريبات وتركيب صنابير جديدة.',
      category: 'plumbing',
      location: 'حلب، الفرقان',
      budget: 180000,
      deadline: '2024-02-28',
      postedAt: '2024-01-20',
      offersCount: 5,
      status: 'open',
      skills: ['سباكة', 'إصلاح', 'تركيب'],
      images: [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400',
        'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=400'
      ],
      client: {
        name: 'فاطمة أحمد',
        rating: 4.9,
        reviewsCount: 8
      }
    },
    {
      id: 3,
      title: 'تركيب نظام كهربائي كامل لشقة جديدة',
      description: 'مطلوب كهربائي محترف لتركيب نظام كهربائي كامل لشقة جديدة بمساحة 120 متر مربع. يشمل العمل التمديدات والإضاءة واللوحة الكهربائية.',
      category: 'electrical',
      location: 'دمشق، جرمانا',
      budget: 220000,
      deadline: '2024-03-10',
      postedAt: '2024-01-18',
      offersCount: 12,
      status: 'open',
      skills: ['كهرباء', 'تمديدات', 'إضاءة'],
      images: [
        'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=400',
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'
      ],
      client: {
        name: 'محمد علي',
        rating: 4.7,
        reviewsCount: 15
      }
    },
    {
      id: 4,
      title: 'دهان شقة سكنية بالكامل',
      description: 'مطلوب دهان محترف لدهان شقة سكنية بمساحة 100 متر مربع. العمل يشمل تحضير الجدران والدهان بألوان حديثة حسب التصميم المطلوب.',
      category: 'painting',
      location: 'حمص، الوعر',
      budget: 120000,
      deadline: '2024-02-25',
      postedAt: '2024-01-22',
      offersCount: 6,
      status: 'open',
      skills: ['دهان', 'تحضير', 'تصميم'],
      images: [
        'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=400',
        'https://images.unsplash.com/photo-1581858726788-75bc0f6a952d?w=400'
      ],
      client: {
        name: 'سارة خالد',
        rating: 4.6,
        reviewsCount: 9
      }
    }
  ];

  const filteredJobs = jobs.filter(job =>
    selectedCategory === 'all' || job.category === selectedCategory
  );

  const sortedJobs = [...filteredJobs].sort((a, b) => {
    switch (sortBy) {
      case 'budget_high':
        return b.budget - a.budget;
      case 'budget_low':
        return a.budget - b.budget;
      case 'deadline':
        return new Date(a.deadline).getTime() - new Date(b.deadline).getTime();
      default:
        return new Date(b.postedAt).getTime() - new Date(a.postedAt).getTime();
    }
  });



  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'open':
        return <Badge variant="success">مفتوح</Badge>;
      case 'in_progress':
        return <Badge variant="warning">قيد التنفيذ</Badge>;
      case 'completed':
        return <Badge variant="info">مكتمل</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-beige via-white to-skyblue/20 py-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-navy mb-4">المشاريع المتاحة</h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              اكتشف المشاريع المتاحة وقدم عروضك للحصول على فرص عمل جديدة
            </p>
          </div>

          {/* Filters */}
          <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  فلترة حسب الفئة
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ترتيب حسب
                </label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                >
                  <option value="newest">الأحدث</option>
                  <option value="budget_high">الميزانية (الأعلى أولاً)</option>
                  <option value="budget_low">الميزانية (الأقل أولاً)</option>
                  <option value="deadline">الموعد النهائي</option>
                </select>
              </div>
            </div>
          </div>

          {/* Jobs Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {sortedJobs.map((job) => (
              <Card key={job.id} className="hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex justify-between items-start mb-2">
                    <CardTitle className="text-xl leading-tight">{job.title}</CardTitle>
                    {getStatusBadge(job.status)}
                  </div>
                  <div className="flex items-center text-sm text-gray-500 space-x-4 space-x-reverse">
                    <span className="flex items-center">
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      {job.location}
                    </span>
                    <span className="flex items-center">
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      <DateDisplay date={job.postedAt} />
                    </span>
                  </div>
                </CardHeader>

                <CardContent>
                  {/* صور المشروع */}
                  {job.images && job.images.length > 0 && (
                    <div className="mb-4">
                      <div className="grid grid-cols-2 gap-2 h-32">
                        {job.images.slice(0, 2).map((image, index) => (
                          <img
                            key={index}
                            src={image}
                            alt={`صورة المشروع ${index + 1}`}
                            className="w-full h-full object-cover rounded-lg border border-gray-200 hover:scale-105 transition-transform duration-300"
                          />
                        ))}
                        {job.images.length > 2 && (
                          <div className="relative">
                            <img
                              src={job.images[2]}
                              alt="المزيد من الصور"
                              className="w-full h-full object-cover rounded-lg border border-gray-200"
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center hover:bg-opacity-40 transition-all duration-300">
                              <span className="text-white text-sm font-medium">
                                +{job.images.length - 2} صور
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <p className="text-gray-700 mb-4 line-clamp-3">{job.description}</p>

                  <div className="flex flex-wrap gap-2 mb-4">
                    {job.skills.map((skill, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div className="bg-gradient-to-r from-green-50 to-green-100 p-3 rounded-lg">
                      <div className="text-sm text-green-600 font-medium">الميزانية</div>
                      <div className="text-lg font-bold text-green-700">
                        {job.budget.toLocaleString()} ل.س
                      </div>
                    </div>
                    <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-3 rounded-lg">
                      <div className="text-sm text-blue-600 font-medium">الموعد النهائي</div>
                      <div className="text-sm font-bold text-blue-700">
                        <DateDisplay date={job.deadline} />
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-sm text-gray-600">
                      <span className="flex items-center">
                        <svg className="w-4 h-4 ml-1 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        {job.client.rating} ({job.client.reviewsCount} تقييم)
                      </span>
                      <span className="mr-4 text-teal font-medium">{job.offersCount} عرض</span>
                    </div>
                    <Link href={`/jobs/${job.id}`}>
                      <Button size="sm" className="bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90">
                        عرض التفاصيل
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <Button variant="outline" size="lg" className="px-8">
              تحميل المزيد من المشاريع
            </Button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default JobsPage;
