'use client';

import { useState, useEffect } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import supabase from '@/lib/supabase';

export default function ProjectsPage() {
  const [projects, setProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState({
    category: '',
    location: '',
    status: 'OPEN'
  });

  const loadProjects = async () => {
    setLoading(true);
    setError(null);

    try {
      let query = supabase
        .from('projects')
        .select(`
          *,
          client:users!projects_client_id_fkey(name, email, location)
        `)
        .order('created_at', { ascending: false });

      // Apply filters
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.category) {
        query = query.eq('category', filters.category);
      }
      if (filters.location) {
        query = query.eq('location', filters.location);
      }

      const { data, error } = await query;

      if (error) throw error;
      
      setProjects(data || []);
    } catch (err: any) {
      setError(err.message);
    }

    setLoading(false);
  };

  useEffect(() => {
    loadProjects();
  }, [filters]);

  const formatBudget = (min: number, max: number) => {
    if (!min && !max) return 'غير محدد';
    if (!max) return `من ${min.toLocaleString()} ل.س`;
    if (!min) return `حتى ${max.toLocaleString()} ل.س`;
    return `${min.toLocaleString()} - ${max.toLocaleString()} ل.س`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN': return 'bg-green-100 text-green-800';
      case 'IN_PROGRESS': return 'bg-blue-100 text-blue-800';
      case 'COMPLETED': return 'bg-purple-100 text-purple-800';
      case 'CANCELLED': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'OPEN': return 'مفتوح';
      case 'IN_PROGRESS': return 'قيد التنفيذ';
      case 'COMPLETED': return 'مكتمل';
      case 'CANCELLED': return 'ملغي';
      default: return status;
    }
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-navy mb-4">
                🏗️ المشاريع المتاحة
              </h1>
              <p className="text-xl text-gray-600">
                تصفح المشاريع وقدم عروضك
              </p>
            </div>

            {/* Filters */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">فلترة المشاريع</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <select
                    value={filters.status}
                    onChange={(e) => setFilters({...filters, status: e.target.value})}
                    className="p-2 border rounded-lg"
                  >
                    <option value="">جميع الحالات</option>
                    <option value="OPEN">مفتوح</option>
                    <option value="IN_PROGRESS">قيد التنفيذ</option>
                    <option value="COMPLETED">مكتمل</option>
                  </select>

                  <select
                    value={filters.category}
                    onChange={(e) => setFilters({...filters, category: e.target.value})}
                    className="p-2 border rounded-lg"
                  >
                    <option value="">جميع الفئات</option>
                    <option value="نجارة">نجارة</option>
                    <option value="كهرباء">كهرباء</option>
                    <option value="سباكة">سباكة</option>
                    <option value="دهان">دهان</option>
                    <option value="تجريبي">تجريبي</option>
                    <option value="اختبار">اختبار</option>
                  </select>

                  <select
                    value={filters.location}
                    onChange={(e) => setFilters({...filters, location: e.target.value})}
                    className="p-2 border rounded-lg"
                  >
                    <option value="">جميع المواقع</option>
                    <option value="دمشق">دمشق</option>
                    <option value="حلب">حلب</option>
                    <option value="حمص">حمص</option>
                    <option value="حماة">حماة</option>
                  </select>

                  <Button onClick={loadProjects} disabled={loading}>
                    {loading ? 'جاري التحميل...' : '🔄 تحديث'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Error */}
            {error && (
              <Card className="border-0 bg-red-50 border-red-200 mb-8">
                <CardContent className="p-4">
                  <div className="text-red-800">
                    <strong>خطأ:</strong> {error}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Projects Count */}
            <div className="mb-6">
              <p className="text-gray-600">
                {loading ? 'جاري التحميل...' : `عدد المشاريع: ${projects.length}`}
              </p>
            </div>

            {/* Projects Grid */}
            {loading ? (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal mx-auto mb-4"></div>
                <p className="text-gray-600">جاري تحميل المشاريع...</p>
              </div>
            ) : projects.length === 0 ? (
              <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl">
                <CardContent className="text-center py-12">
                  <div className="text-6xl mb-4">📭</div>
                  <h3 className="text-2xl font-bold text-navy mb-2">لا توجد مشاريع</h3>
                  <p className="text-gray-600 mb-6">لا توجد مشاريع تطابق المعايير المحددة</p>
                  <Button onClick={() => setFilters({category: '', location: '', status: 'OPEN'})}>
                    إعادة تعيين الفلاتر
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {projects.map((project) => (
                  <Card key={project.id} className="border-0 bg-white/90 backdrop-blur-md shadow-xl hover:shadow-2xl transition-shadow">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg text-navy">{project.title}</CardTitle>
                        <span className={`px-2 py-1 rounded text-xs ${getStatusColor(project.status)}`}>
                          {getStatusText(project.status)}
                        </span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <p className="text-gray-600 text-sm">{project.description}</p>
                        
                        <div className="space-y-2 text-sm">
                          <div><strong>الفئة:</strong> {project.category}</div>
                          <div><strong>الموقع:</strong> {project.location}</div>
                          <div><strong>العميل:</strong> {project.client?.name || 'غير محدد'}</div>
                          {(project.budget_min || project.budget_max) && (
                            <div><strong>الميزانية:</strong> {formatBudget(project.budget_min, project.budget_max)}</div>
                          )}
                          <div><strong>تاريخ النشر:</strong> {new Date(project.created_at).toLocaleDateString('ar-SA')}</div>
                        </div>

                        <div className="pt-4 border-t">
                          <Button 
                            className="w-full bg-gradient-to-r from-teal to-navy"
                            onClick={() => window.open(`/projects/${project.id}`, '_blank')}
                          >
                            عرض التفاصيل
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Create Project Button */}
            <div className="text-center mt-12">
              <a 
                href="/projects/create"
                className="inline-block bg-gradient-to-r from-green-500 to-green-600 text-white px-8 py-4 rounded-lg hover:shadow-lg transition-all text-lg font-semibold"
              >
                ➕ إنشاء مشروع جديد
              </a>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
