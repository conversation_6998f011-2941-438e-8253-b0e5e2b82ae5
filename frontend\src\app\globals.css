@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: #ffffff;
  --foreground: #171717;
  --navy: #2F4156;
  --teal: #567C8D;
  --skyblue: #C8D9E6;
  --beige: #F5EFEB;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  direction: rtl;
  text-align: right;
}

/* RTL Support */
.rtl {
  direction: rtl;
  text-align: right;
}

.ltr {
  direction: ltr;
  text-align: left;
}

@layer components {
  .btn-primary {
    @apply bg-navy text-white py-2 px-4 rounded-md hover:bg-opacity-90 transition-all;
  }

  .btn-secondary {
    @apply bg-teal text-white py-2 px-4 rounded-md hover:bg-opacity-90 transition-all;
  }

  .btn-outline {
    @apply border border-navy text-navy py-2 px-4 rounded-md hover:bg-navy hover:text-white transition-all;
  }

  .card {
    @apply bg-white rounded-lg shadow-md p-6;
  }

  .input-field {
    @apply w-full rounded-md border-gray-300 shadow-sm focus:border-teal focus:ring focus:ring-teal focus:ring-opacity-50;
  }
}
