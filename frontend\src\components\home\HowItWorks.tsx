import React from 'react';
import Link from 'next/link';
import { Button } from '../ui/Button';

const steps = [
  {
    id: 1,
    title: 'أنشئ مشروعك',
    description: 'حدد تفاصيل مشروعك، والميزانية، والمدة الزمنية المطلوبة.',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-navy" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
      </svg>
    ),
  },
  {
    id: 2,
    title: 'احصل على عروض',
    description: 'سيقدم الحرفيون المؤهلون عروضهم بناءً على متطلبات مشروعك.',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-navy" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  },
  {
    id: 3,
    title: 'اختر الحرفي المناسب',
    description: 'راجع العروض واختر الحرفي الذي يناسب احتياجاتك وميزانيتك.',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-navy" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
    ),
  },
  {
    id: 4,
    title: 'تابع تقدم المشروع',
    description: 'تواصل مع الحرفي وتابع تقدم المشروع حتى الانتهاء.',
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-navy" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
      </svg>
    ),
  },
];

const HowItWorks = () => {
  return (
    <section className="py-20 bg-gradient-to-b from-white via-beige/30 to-white relative overflow-hidden">
      {/* خلفية هندسية محسنة ومتناسقة */}
      <div className="absolute top-0 left-0 w-full h-full opacity-5">
        <div className="absolute top-20 left-10 w-32 h-32 bg-navy rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 bg-teal rounded-full blur-xl"></div>
        <div className="absolute top-32 right-20 w-28 h-28 bg-navy rounded-full blur-xl"></div>
        <div className="absolute bottom-32 left-32 w-36 h-36 bg-teal rounded-full blur-xl"></div>
      </div>

      {/* شبكة نقطية ناعمة */}
      <div className="absolute inset-0 opacity-10">
        <div className="w-full h-full" style={{
          backgroundImage: 'radial-gradient(circle, #2F4156 1px, transparent 2px)',
          backgroundSize: '23px 23px'
        }}></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <div className="inline-block bg-gradient-to-r from-navy to-teal text-white px-8 py-3 rounded-full text-sm font-medium mb-6 shadow-lg hover:shadow-xl transition-shadow duration-300">
            <span className="text-lg">⚡</span>
            <span className="mr-2">عملية بسيطة وسريعة</span>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-navy mb-6 leading-tight">
            كيف تعمل منصة
            <span className="block pb-2 text-transparent bg-clip-text bg-gradient-to-r from-teal via-navy to-teal drop-shadow-sm">
              دوزان
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            عملية بسيطة وفعالة تضمن لك الحصول على أفضل الحرفيين لمشروعك في 4 خطوات سهلة
          </p>
          <div className="mt-8 w-24 h-1 bg-gradient-to-r from-teal to-navy mx-auto rounded-full"></div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={step.id} className="relative">
              {/* Connection Line */}
              {index < steps.length + 1 && (
                <div className="
        hidden lg:block
        absolute
        top-1/2 left-[90%]
        w-16
        h-px
        bg-gradient-to-r from-navy to-teal
        transform translate-x-4 -translate-y-1/2
        z-[-1]
      "></div>
              )}

              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 relative group">
                {/* Step Number */}
                <div className="absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-r from-navy to-teal rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {step.id}
                </div>

                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-gradient-to-r from-navy/10 to-teal/10 rounded-2xl flex items-center justify-center text-3xl group-hover:scale-110 transition-transform duration-300">
                    {step.icon}
                  </div>
                </div>

                <h3 className="text-xl font-bold text-navy mb-4 text-center">{step.title}</h3>
                <p className="text-gray-600 text-center leading-relaxed">{step.description}</p>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-navy/5 to-teal/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-navy to-teal rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">جاهز للبدء؟</h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto">
              انضم إلى آلاف المستخدمين الذين يثقون بمنصة دوزان لتنفيذ مشاريعهم
            </p>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
              <Link href="/post-job">
                <Button size="lg" className="bg-white text-navy hover:bg-white/90 font-semibold">
                  ابدأ مشروعك الآن
                </Button>
              </Link>
              <Link href="/register">
                <Button size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-navy font-semibold">
                  انضم كحرفي
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HowItWorks;
