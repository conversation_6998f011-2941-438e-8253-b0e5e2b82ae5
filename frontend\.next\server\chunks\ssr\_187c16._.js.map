{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/MainLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/MainLayout.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/MainLayout.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwS,GACrU,sEACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/MainLayout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/MainLayout.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/MainLayout.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoR,GACjT,kDACA"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Card = registerClientReference(\n    function() { throw new Error(\"Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Card.tsx <module evaluation>\",\n    \"Card\",\n);\nexport const CardContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardContent() from the server but CardContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Card.tsx <module evaluation>\",\n    \"CardContent\",\n);\nexport const CardDescription = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardDescription() from the server but CardDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Card.tsx <module evaluation>\",\n    \"CardDescription\",\n);\nexport const CardFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardFooter() from the server but CardFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Card.tsx <module evaluation>\",\n    \"CardFooter\",\n);\nexport const CardHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardHeader() from the server but CardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Card.tsx <module evaluation>\",\n    \"CardHeader\",\n);\nexport const CardTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardTitle() from the server but CardTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Card.tsx <module evaluation>\",\n    \"CardTitle\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,4DACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,4DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,4DACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,4DACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,4DACA"}}, {"offset": {"line": 73, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Card = registerClientReference(\n    function() { throw new Error(\"Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Card.tsx\",\n    \"Card\",\n);\nexport const CardContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardContent() from the server but CardContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Card.tsx\",\n    \"CardContent\",\n);\nexport const CardDescription = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardDescription() from the server but CardDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Card.tsx\",\n    \"CardDescription\",\n);\nexport const CardFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardFooter() from the server but CardFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Card.tsx\",\n    \"CardFooter\",\n);\nexport const CardHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardHeader() from the server but CardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Card.tsx\",\n    \"CardHeader\",\n);\nexport const CardTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardTitle() from the server but CardTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Card.tsx\",\n    \"CardTitle\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,wCACA;AAEG,MAAM,cAAc,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wCACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,wCACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,wCACA;AAEG,MAAM,aAAa,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,wCACA;AAEG,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,wCACA"}}, {"offset": {"line": 107, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Button = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON><PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Button.tsx <module evaluation>\",\n    \"Button\",\n);\nexport const buttonVariants = registerClientReference(\n    function() { throw new Error(\"Attempted to call buttonVariants() from the server but buttonVariants is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Button.tsx <module evaluation>\",\n    \"buttonVariants\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8DACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,8DACA"}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Button = registerClientReference(\n    function() { throw new Error(\"Attempted to call <PERSON><PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Button.tsx\",\n    \"Button\",\n);\nexport const buttonVariants = registerClientReference(\n    function() { throw new Error(\"Attempted to call buttonVariants() from the server but buttonVariants is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/Button.tsx\",\n    \"buttonVariants\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,0CACA;AAEG,MAAM,iBAAiB,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAChD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,0CACA"}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 163, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 169, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/database-instructions/page.tsx"], "sourcesContent": ["import MainLayout from '@/components/layout/MainLayout';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\n\nexport default function DatabaseInstructions() {\n  const sqlCode = `-- Dozan Database Schema for Supabase\n-- Copy and paste this code in Supabase Dashboard > SQL Editor\n\n-- Enable UUID extension\nCREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";\n\n-- <PERSON><PERSON> <PERSON><PERSON><PERSON> types\nCREATE TYPE user_role AS ENUM ('CLIENT', 'CRAFTSMAN', 'ADMIN');\nCREATE TYPE project_status AS ENUM ('OPEN', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'DISPUTED');\nCREATE TYPE project_priority AS ENUM ('LOW', 'MEDIUM', 'HIGH', 'URGENT');\n\n-- Users table\nCREATE TABLE users (\n    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),\n    email VARCHAR(255) UNIQUE NOT NULL,\n    password_hash VARCHAR(255) NOT NULL,\n    name VARCHAR(255) NOT NULL,\n    phone VARCHAR(20),\n    avatar VARCHAR(500),\n    role user_role NOT NULL DEFAULT 'CLIENT',\n    location VARCHAR(255),\n    is_verified BOOLEAN DEFAULT FALSE,\n    is_active BOOLEAN DEFAULT TRUE,\n    email_verified_at TIMESTAMP,\n    last_login TIMESTAMP,\n    created_at TIMESTAMP DEFAULT NOW(),\n    updated_at TIMESTAMP DEFAULT NOW()\n);\n\n-- Projects table\nCREATE TABLE projects (\n    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),\n    title VARCHAR(255) NOT NULL,\n    description TEXT NOT NULL,\n    category VARCHAR(100) NOT NULL,\n    budget_min DECIMAL(10,2),\n    budget_max DECIMAL(10,2),\n    location VARCHAR(255) NOT NULL,\n    priority project_priority DEFAULT 'MEDIUM',\n    materials VARCHAR(50) DEFAULT 'NOT_SPECIFIED',\n    work_type VARCHAR(255),\n    requirements TEXT,\n    images TEXT[], -- Array of image URLs\n    status project_status DEFAULT 'OPEN',\n    client_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,\n    assigned_craftsman_id UUID REFERENCES users(id),\n    deadline TIMESTAMP,\n    created_at TIMESTAMP DEFAULT NOW(),\n    updated_at TIMESTAMP DEFAULT NOW()\n);\n\n-- Insert sample data\nINSERT INTO users (email, password_hash, name, phone, role, location) VALUES\n('<EMAIL>', '$2b$10$example', 'أحمد محمد', '+************', 'CLIENT', 'دمشق'),\n('<EMAIL>', '$2b$10$example', 'محمد أحمد', '+************', 'CRAFTSMAN', 'حلب'),\n('<EMAIL>', '$2b$10$example', 'مدير النظام', '+************', 'ADMIN', 'دمشق');\n\n-- Insert sample project\nINSERT INTO projects (title, description, category, budget_min, budget_max, location, client_id) \nSELECT 'إصلاح باب خشبي', 'يحتاج إصلاح باب خشبي في المنزل', 'نجارة', 50000, 100000, 'دمشق', id \nFROM users WHERE email = '<EMAIL>';\n\nCOMMIT;`;\n\n  const copyToClipboard = () => {\n    navigator.clipboard.writeText(sqlCode);\n    alert('تم نسخ الكود! الصقه في Supabase SQL Editor');\n  };\n\n  return (\n    <MainLayout>\n      <div className=\"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            {/* Header */}\n            <div className=\"text-center mb-8\">\n              <h1 className=\"text-4xl font-bold text-navy mb-4\">\n                📋 تعليمات إعداد قاعدة البيانات\n              </h1>\n              <p className=\"text-xl text-gray-600\">\n                خطوات بسيطة لإعداد قاعدة البيانات في Supabase\n              </p>\n            </div>\n\n            {/* Step 1 */}\n            <Card className=\"border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl text-navy\">الخطوة 1: افتح Supabase Dashboard</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <p className=\"text-gray-700\">\n                    اذهب إلى Supabase Dashboard وافتح مشروعك:\n                  </p>\n                  <div className=\"bg-blue-50 p-4 rounded-lg\">\n                    <p className=\"font-mono text-sm\">\n                      🔗 https://supabase.com/dashboard/projects\n                    </p>\n                    <p className=\"text-sm text-gray-600 mt-2\">\n                      اختر مشروع: <strong>lyjelanmcbzymgauwamc</strong>\n                    </p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Step 2 */}\n            <Card className=\"border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl text-navy\">الخطوة 2: افتح SQL Editor</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <p className=\"text-gray-700\">\n                    في الشريط الجانبي، انقر على:\n                  </p>\n                  <div className=\"bg-green-50 p-4 rounded-lg\">\n                    <p className=\"font-semibold\">📝 SQL Editor</p>\n                    <p className=\"text-sm text-gray-600 mt-1\">\n                      ثم انقر \"New Query\" لإنشاء استعلام جديد\n                    </p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Step 3 */}\n            <Card className=\"border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl text-navy\">الخطوة 3: انسخ والصق الكود</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <div className=\"flex justify-between items-center\">\n                    <p className=\"text-gray-700\">\n                      انسخ الكود التالي والصقه في SQL Editor:\n                    </p>\n                    <Button onClick={copyToClipboard} className=\"bg-gradient-to-r from-teal to-navy\">\n                      📋 نسخ الكود\n                    </Button>\n                  </div>\n                  \n                  <div className=\"bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto max-h-96\">\n                    <pre className=\"text-xs font-mono whitespace-pre-wrap\">\n                      {sqlCode}\n                    </pre>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Step 4 */}\n            <Card className=\"border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl text-navy\">الخطوة 4: تشغيل الكود</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <p className=\"text-gray-700\">\n                    بعد لصق الكود، انقر على:\n                  </p>\n                  <div className=\"bg-purple-50 p-4 rounded-lg\">\n                    <p className=\"font-semibold text-purple-800\">▶️ Run</p>\n                    <p className=\"text-sm text-gray-600 mt-1\">\n                      سيتم إنشاء جميع الجداول والبيانات التجريبية\n                    </p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Step 5 */}\n            <Card className=\"border-0 bg-gradient-to-r from-green-500 to-green-600 text-white mb-8\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl\">الخطوة 5: اختبار النتيجة</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <p>\n                    بعد تشغيل الكود بنجاح، اختبر قاعدة البيانات:\n                  </p>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <a \n                      href=\"/setup-database\"\n                      className=\"block bg-white/20 p-4 rounded-lg hover:bg-white/30 transition-all\"\n                    >\n                      <h3 className=\"font-semibold\">🧪 اختبار قاعدة البيانات</h3>\n                      <p className=\"text-sm opacity-90\">تحقق من الجداول والبيانات</p>\n                    </a>\n                    <a \n                      href=\"/test-dashboard\"\n                      className=\"block bg-white/20 p-4 rounded-lg hover:bg-white/30 transition-all\"\n                    >\n                      <h3 className=\"font-semibold\">🎛️ لوحة الاختبار</h3>\n                      <p className=\"text-sm opacity-90\">اختبار جميع الميزات</p>\n                    </a>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Troubleshooting */}\n            <Card className=\"border-0 bg-yellow-50 border-yellow-200\">\n              <CardHeader>\n                <CardTitle className=\"text-2xl text-navy\">🔧 حل المشاكل</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  <div>\n                    <h3 className=\"font-semibold text-navy mb-2\">إذا ظهر خطأ:</h3>\n                    <ul className=\"space-y-2 text-gray-700\">\n                      <li>• تأكد أن المشروع نشط في Supabase</li>\n                      <li>• تحقق من صحة الكود المنسوخ</li>\n                      <li>• جرب تشغيل الكود على أجزاء صغيرة</li>\n                      <li>• تأكد من وجود صلاحيات الكتابة</li>\n                    </ul>\n                  </div>\n                  \n                  <div className=\"bg-blue-100 border border-blue-400 text-blue-800 px-4 py-3 rounded\">\n                    <strong>💡 نصيحة:</strong> إذا كانت الجداول موجودة مسبقاً، سيتم تجاهل أوامر CREATE TABLE.\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            {/* Navigation */}\n            <div className=\"text-center mt-8 space-x-4 space-x-reverse\">\n              <a \n                href=\"/test-dashboard\"\n                className=\"inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all\"\n              >\n                العودة للوحة الاختبار\n              </a>\n              <a \n                href=\"/setup-database\"\n                className=\"inline-block bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all\"\n              >\n                اختبار قاعدة البيانات\n              </a>\n            </div>\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACtB,MAAM,UAAU,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8DZ,CAAC;IAEN,MAAM,kBAAkB;QACtB,UAAU,SAAS,CAAC,SAAS,CAAC;QAC9B,MAAM;IACR;IAEA,qBACE,8OAAC,0IAAA,CAAA,UAAU;kBACT,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAGlD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAMvC,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAqB;;;;;;;;;;;8CAE5C,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAG7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAoB;;;;;;kEAGjC,8OAAC;wDAAE,WAAU;;4DAA6B;0EAC5B,8OAAC;0EAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ9B,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAqB;;;;;;;;;;;8CAE5C,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAG7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;kEAC7B,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASlD,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAqB;;;;;;;;;;;8CAE5C,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAgB;;;;;;kEAG7B,8OAAC,kIAAA,CAAA,SAAM;wDAAC,SAAS;wDAAiB,WAAU;kEAAqC;;;;;;;;;;;;0DAKnF,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQX,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAqB;;;;;;;;;;;8CAE5C,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAgB;;;;;;0DAG7B,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAgC;;;;;;kEAC7C,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASlD,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW;;;;;;;;;;;8CAElC,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAE;;;;;;0DAGH,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,WAAU;;0EAEV,8OAAC;gEAAG,WAAU;0EAAgB;;;;;;0EAC9B,8OAAC;gEAAE,WAAU;0EAAqB;;;;;;;;;;;;kEAEpC,8OAAC;wDACC,MAAK;wDACL,WAAU;;0EAEV,8OAAC;gEAAG,WAAU;0EAAgB;;;;;;0EAC9B,8OAAC;gEAAE,WAAU;0EAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ5C,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAqB;;;;;;;;;;;8CAE5C,8OAAC,gIAAA,CAAA,cAAW;8CACV,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;0EACJ,8OAAC;0EAAG;;;;;;;;;;;;;;;;;;0DAIR,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;kEAAO;;;;;;oDAAkB;;;;;;;;;;;;;;;;;;;;;;;;sCAOlC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf"}}, {"offset": {"line": 845, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 856, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/node_modules/next/dist/src/build/templates/app-page.ts"], "sourcesContent": ["import type { LoaderTree } from '../../server/lib/app-dir-module'\nimport { AppPageRouteModule } from '../../server/route-modules/app-page/module.compiled' with { 'turbopack-transition': 'next-ssr' }\nimport { RouteKind } from '../../server/route-kind'\n\n// These are injected by the loader afterwards.\n\n/**\n * The tree created in next-app-loader that holds component segments and modules\n * and I've updated it.\n */\ndeclare const tree: LoaderTree\ndeclare const pages: any\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\n// INJECT:tree\n// INJECT:pages\n\nexport { tree, pages }\n\nexport { default as GlobalError } from 'VAR_MODULE_GLOBAL_ERROR'\n\n// These are injected by the loader afterwards.\ndeclare const __next_app_require__: any\ndeclare const __next_app_load_chunk__: any\n\n// INJECT:__next_app_require__\n// INJECT:__next_app_load_chunk__\n\nexport const __next_app__ = {\n  require: __next_app_require__,\n  loadChunk: __next_app_load_chunk__,\n}\n\nexport * from '../../server/app-render/entry-base'\n\n// Create and export the route module that will be consumed.\nexport const routeModule = new AppPageRouteModule({\n  definition: {\n    kind: RouteKind.APP_PAGE,\n    page: 'VAR_DEFINITION_PAGE',\n    pathname: 'VAR_DEFINITION_PATHNAME',\n    // The following aren't used in production.\n    bundlePath: '',\n    filename: '',\n    appPaths: [],\n  },\n  userland: {\n    loaderTree: tree,\n  },\n})\n"], "names": ["AppPageRouteModule", "RouteKind", "tree", "pages", "default", "GlobalError", "__next_app__", "require", "__next_app_require__", "loadChunk", "__next_app_load_chunk__", "routeModule", "definition", "kind", "APP_PAGE", "page", "pathname", "bundlePath", "filename", "appPaths", "userland", "loaderTree"], "mappings": ";;;;;;AA0BA,8BAA8B;AAzB9B,SAASA,kBAAkB,QAAQ,2DAA2D;IAAE,wBAAwB;AAAW,EAAC;AAYpI,yEAAyE;AAEzE,cAAc;AAGd,SAASE,IAAI,EAAEC,KAAK,GAAE;AAWtB,OAAO,MAAMG,eAAe;AAG5B,EAAC;;;;;;;;;;AAED,cAAc,qCAAoC,sBAAA;AAElD,UAAA,kDAA4D;AAC5D,MAAA,CAAO,MAAMK;IAAAA;IAAAA,SAAc,IAAIX,mBAAmB;YAChDY,QAAAA;YAAAA,GAAY;YAAA;wBACVC,IAAAA;oBAAAA,CAAMZ,UAAUa;oBAAAA,OAAQ;yBACxBC,MAAM;8BACNC,IAAAA,CAAAA,GAAU;wBAAA,QAAA;4BAAA,IAAA;4BAAA;yBAAA;;uBACV,2CAA2C;;iBAC3CC,YAAY;sBACZC,IAAAA,CAAAA;YAAAA,CAAU;SAAA;;SACVC,UAAU,EAAE;UACd,QAAA;YAAA,MAAA;gBACAC,OAAU,QAAA;wBAAA;4BACRC,KAAAA,CAAAA,GAAAA,IAAYnB,uKAAZmB,CAAAA,sBAAYnB,EAAAA,MAAAA,MAAAA,MAAAA,EAAAA,iBAAAA,CAAAA,CAAAA,EAAAA,iTAAAA,CAAAA,UAAAA,CAAAA,GAAAA,CAAAA,KAAAA,CAAAA,KAAAA,MAAAA,CAAAA,CAAAA,EAAAA,CAAAA,EAAAA,EAAAA;4BACd,OAAA,GAAA,iTAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA,EAAA,iTAAA,CAAA,UAAA,CAAA,MAAA,EAAA;4BACA,MAAA,CAAA,YAAA,CAAA", "ignoreList": [0]}}, {"offset": {"line": 967, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}