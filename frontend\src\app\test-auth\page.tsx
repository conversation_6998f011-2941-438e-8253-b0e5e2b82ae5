'use client';

import { useState } from 'react';
import { signIn, signOut, useSession } from 'next-auth/react';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';

export default function TestAuthPage() {
  const { data: session, status } = useSession();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const result = await signIn('credentials', {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        setError('خطأ في تسجيل الدخول');
      }
    } catch (err) {
      setError('حدث خطأ غير متوقع');
    } finally {
      setLoading(false);
    }
  };

  const testAccounts = [
    { email: '<EMAIL>', password: 'Test123!@#', role: 'مدير النظام' },
    { email: '<EMAIL>', password: 'Test123!@#', role: 'عميل' },
    { email: '<EMAIL>', password: 'Test123!@#', role: 'حرفي' },
    { email: '<EMAIL>', password: 'Test123!@#', role: 'حرفي' },
  ];

  const quickLogin = (testEmail: string, testPassword: string) => {
    setEmail(testEmail);
    setPassword(testPassword);
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal mx-auto mb-4"></div>
          <p>جاري التحميل...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4">
      <div className="max-w-md mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="text-center text-2xl font-bold text-navy">
              اختبار المصادقة
            </CardTitle>
          </CardHeader>
          <CardContent>
            {session ? (
              <div className="space-y-4">
                <div className="text-center">
                  <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    ✅ تم تسجيل الدخول بنجاح!
                  </div>
                  <p className="text-lg font-semibold">{session.user?.name}</p>
                  <p className="text-gray-600">{session.user?.email}</p>
                  <p className="text-sm text-teal font-medium">
                    الدور: {session.user?.role}
                  </p>
                </div>
                <Button 
                  onClick={() => signOut()} 
                  variant="outline" 
                  className="w-full"
                >
                  تسجيل الخروج
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {error && (
                  <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    {error}
                  </div>
                )}

                <form onSubmit={handleLogin} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      البريد الإلكتروني
                    </label>
                    <input
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      كلمة المرور
                    </label>
                    <input
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                      required
                    />
                  </div>

                  <Button 
                    type="submit" 
                    disabled={loading}
                    className="w-full"
                  >
                    {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}
                  </Button>
                </form>

                <div className="mt-6">
                  <h3 className="text-sm font-medium text-gray-700 mb-3">
                    حسابات تجريبية:
                  </h3>
                  <div className="space-y-2">
                    {testAccounts.map((account, index) => (
                      <button
                        key={index}
                        onClick={() => quickLogin(account.email, account.password)}
                        className="w-full text-left p-2 bg-gray-100 hover:bg-gray-200 rounded text-sm"
                      >
                        <div className="font-medium">{account.role}</div>
                        <div className="text-gray-600 text-xs">{account.email}</div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
