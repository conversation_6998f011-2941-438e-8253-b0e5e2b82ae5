'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import Dropdown from '@/components/ui/Dropdown';

interface Craftsman {
  id: number;
  name: string;
  profession: string;
  bio: string;
  location: string;
  rating: number;
  reviewsCount: number;
  completedJobs: number;
  hourlyRate: number;
  skills: string[];
  avatar: string;
  isOnline: boolean;
  responseTime: string;
  joinedDate: string;
  verified: boolean;
}

const CraftsmenPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLocation, setSelectedLocation] = useState('all');
  const [sortBy, setSortBy] = useState('rating');
  const [searchQuery, setSearchQuery] = useState('');

  const categories = [
    { value: 'all', label: 'جميع التخصصات', icon: '🔍' },
    { value: 'carpentry', label: 'النجارة', icon: '🪚' },
    { value: 'plumbing', label: 'السباكة', icon: '🔧' },
    { value: 'electrical', label: 'الكهرباء', icon: '⚡' },
    { value: 'painting', label: 'الدهان', icon: '🖌️' },
    { value: 'construction', label: 'البناء', icon: '🧱' },
    { value: 'hvac', label: 'التكييف', icon: '❄️' },
    { value: 'metalwork', label: 'الحدادة', icon: '🔨' },
    { value: 'landscaping', label: 'تنسيق الحدائق', icon: '🌱' },
  ];

  const locations = [
    { value: 'all', label: 'جميع المحافظات', icon: '🌍' },
    { value: 'damascus', label: 'دمشق', icon: '🏛️' },
    { value: 'aleppo', label: 'حلب', icon: '🏰' },
    { value: 'homs', label: 'حمص', icon: '🏘️' },
    { value: 'hama', label: 'حماة', icon: '🌾' },
    { value: 'lattakia', label: 'اللاذقية', icon: '🌊' },
    { value: 'tartous', label: 'طرطوس', icon: '⛵' },
    { value: 'daraa', label: 'درعا', icon: '🏜️' },
  ];

  const sortOptions = [
    { value: 'rating', label: 'الأعلى تقييماً', icon: '⭐' },
    { value: 'experience', label: 'الأكثر خبرة', icon: '📊' },
    { value: 'price_low', label: 'السعر (الأقل أولاً)', icon: '💵' },
    { value: 'price_high', label: 'السعر (الأعلى أولاً)', icon: '💰' },
  ];

  const craftsmen: Craftsman[] = [
    {
      id: 1,
      name: 'محمد النجار',
      profession: 'نجار محترف',
      bio: 'نجار محترف مع خبرة 10 سنوات في تصميم وتنفيذ الأثاث المنزلي والمكتبي. متخصص في المطابخ والخزائن العصرية.',
      location: 'دمشق، المزة',
      rating: 4.9,
      reviewsCount: 47,
      completedJobs: 89,
      hourlyRate: 2500,
      skills: ['نجارة', 'تصميم أثاث', 'تركيب', 'ترميم'],
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
      isOnline: true,
      responseTime: 'خلال ساعة',
      joinedDate: '2022-03-15',
      verified: true
    },
    {
      id: 2,
      name: 'أحمد السباك',
      profession: 'سباك معتمد',
      bio: 'سباك معتمد مع خبرة 8 سنوات في جميع أعمال السباكة والصرف الصحي. أعمل بأحدث التقنيات وأضمن الجودة.',
      location: 'حلب، الفرقان',
      rating: 4.8,
      reviewsCount: 32,
      completedJobs: 67,
      hourlyRate: 2000,
      skills: ['سباكة', 'صرف صحي', 'تسليك', 'تركيب'],
      avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
      isOnline: false,
      responseTime: 'خلال 3 ساعات',
      joinedDate: '2022-07-20',
      verified: true
    },
    {
      id: 3,
      name: 'علي الكهربائي',
      profession: 'كهربائي محترف',
      bio: 'كهربائي محترف متخصص في التمديدات الكهربائية والإضاءة الذكية. أعمل وفق أعلى معايير السلامة.',
      location: 'دمشق، جرمانا',
      rating: 4.7,
      reviewsCount: 28,
      completedJobs: 54,
      hourlyRate: 2200,
      skills: ['كهرباء', 'تمديدات', 'إضاءة', 'صيانة'],
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
      isOnline: true,
      responseTime: 'خلال 30 دقيقة',
      joinedDate: '2023-01-10',
      verified: false
    },
    {
      id: 4,
      name: 'خالد الدهان',
      profession: 'دهان وديكور',
      bio: 'دهان محترف متخصص في الدهانات الحديثة والديكورات الداخلية. أستخدم أفضل أنواع الدهانات العالمية.',
      location: 'حمص، الوعر',
      rating: 4.6,
      reviewsCount: 19,
      completedJobs: 41,
      hourlyRate: 1800,
      skills: ['دهان', 'ديكور', 'تصميم', 'تشطيب'],
      avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
      isOnline: true,
      responseTime: 'خلال ساعتين',
      joinedDate: '2023-05-22',
      verified: true
    },
    {
      id: 5,
      name: 'سامر البناء',
      profession: 'مقاول بناء',
      bio: 'مقاول بناء مع فريق عمل متكامل. متخصص في أعمال البناء والترميم والتشطيبات بجودة عالية.',
      location: 'حماة، المدينة',
      rating: 4.8,
      reviewsCount: 35,
      completedJobs: 23,
      hourlyRate: 3000,
      skills: ['بناء', 'ترميم', 'تشطيب', 'إدارة مشاريع'],
      avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
      isOnline: false,
      responseTime: 'خلال 4 ساعات',
      joinedDate: '2022-11-08',
      verified: true
    },
    {
      id: 6,
      name: 'يوسف التكييف',
      profession: 'فني تكييف',
      bio: 'فني تكييف معتمد متخصص في تركيب وصيانة جميع أنواع أجهزة التكييف والتبريد.',
      location: 'اللاذقية، الرمل الشمالي',
      rating: 4.5,
      reviewsCount: 22,
      completedJobs: 38,
      hourlyRate: 2300,
      skills: ['تكييف', 'تبريد', 'صيانة', 'تركيب'],
      avatar: 'https://randomuser.me/api/portraits/men/6.jpg',
      isOnline: true,
      responseTime: 'خلال ساعة',
      joinedDate: '2023-02-14',
      verified: false
    }
  ];

  const filteredCraftsmen = craftsmen.filter(craftsman => {
    const matchesCategory = selectedCategory === 'all' || craftsman.skills.some(skill =>
      skill.toLowerCase().includes(selectedCategory.toLowerCase())
    );
    const matchesLocation = selectedLocation === 'all' ||
      craftsman.location.toLowerCase().includes(selectedLocation.toLowerCase());
    const matchesSearch = searchQuery === '' ||
      craftsman.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      craftsman.profession.toLowerCase().includes(searchQuery.toLowerCase()) ||
      craftsman.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()));

    return matchesCategory && matchesLocation && matchesSearch;
  });

  const sortedCraftsmen = [...filteredCraftsmen].sort((a, b) => {
    switch (sortBy) {
      case 'rating':
        return b.rating - a.rating;
      case 'price_low':
        return a.hourlyRate - b.hourlyRate;
      case 'price_high':
        return b.hourlyRate - a.hourlyRate;
      case 'experience':
        return b.completedJobs - a.completedJobs;
      default:
        return b.rating - a.rating;
    }
  });

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white relative overflow-hidden">
        {/* خلفية هندسية متناسقة */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl"></div>
          <div className="absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl"></div>
        </div>

        {/* شبكة نقطية ناعمة */}
        <div className="absolute inset-0 opacity-10">
          <div className="w-full h-full" style={{
            backgroundImage: 'radial-gradient(circle, #567C8D 1px, transparent 1px)',
            backgroundSize: '50px 50px'
          }}></div>
        </div>

        <div className="container mx-auto px-4 py-8 relative z-10">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="inline-block bg-gradient-to-r from-navy to-teal text-white px-8 py-3 rounded-full text-sm font-medium mb-6 shadow-lg">
              <span className="text-lg">👨‍🔧</span>
              <span className="mr-2">حرفيون محترفون</span>
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-navy mb-6 leading-tight">
              أفضل
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-teal via-navy to-teal">
                الحرفيين
              </span>
            </h1>
            <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              اعثر على أفضل الحرفيين المحترفين في سوريا لتنفيذ مشاريعك بجودة عالية وأسعار منافسة
            </p>
            <div className="mt-8 w-24 h-1 bg-gradient-to-r from-teal to-navy mx-auto rounded-full"></div>
          </div>

          {/* Search and Filters */}
          <div className="bg-white/90 backdrop-blur-md rounded-2xl shadow-lg border border-white/20 p-8 mb-12">
            <div className="text-center mb-6">
              <h3 className="text-xl font-bold text-navy mb-2">البحث والفلترة</h3>
              <p className="text-gray-600">ابحث عن الحرفي المناسب لمشروعك</p>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
              <div>
                <label className="block text-gray-700 font-medium mb-2">
                  البحث
                </label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="ابحث عن حرفي..."
                  className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal transition-all duration-300"
                />
              </div>
              <div>
                <Dropdown
                  label="التخصص"
                  options={categories}
                  value={selectedCategory}
                  onChange={setSelectedCategory}
                  placeholder="اختر التخصص"
                />
              </div>
              <div>
                <Dropdown
                  label="المحافظة"
                  options={locations}
                  value={selectedLocation}
                  onChange={setSelectedLocation}
                  placeholder="اختر المحافظة"
                />
              </div>
              <div>
                <Dropdown
                  label="ترتيب حسب"
                  options={sortOptions}
                  value={sortBy}
                  onChange={setSortBy}
                  placeholder="اختر طريقة الترتيب"
                />
              </div>
            </div>

            {/* إحصائيات سريعة */}
            <div className="pt-6 border-t border-gray-200">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
                <div className="bg-gradient-to-r from-navy/5 to-teal/5 rounded-xl p-4">
                  <div className="text-2xl font-bold text-navy">{craftsmen.length}</div>
                  <div className="text-sm text-gray-600">حرفي محترف</div>
                </div>
                <div className="bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-4">
                  <div className="text-2xl font-bold text-green-600">{sortedCraftsmen.length}</div>
                  <div className="text-sm text-gray-600">نتيجة البحث</div>
                </div>
                <div className="bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {Math.round(craftsmen.reduce((sum, c) => sum + c.rating, 0) / craftsmen.length * 10) / 10}
                  </div>
                  <div className="text-sm text-gray-600">متوسط التقييم</div>
                </div>
                <div className="bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {craftsmen.reduce((sum, c) => sum + c.completedJobs, 0)}
                  </div>
                  <div className="text-sm text-gray-600">مشروع مكتمل</div>
                </div>
              </div>
            </div>
          </div>

          {/* Craftsmen Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sortedCraftsmen.map((craftsman) => (
              <Card key={craftsman.id} className="hover:shadow-xl transition-all duration-300 border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center">
                      <div className="relative">
                        <img
                          src={craftsman.avatar}
                          alt={craftsman.name}
                          className="w-16 h-16 rounded-full object-cover"
                        />
                        {craftsman.isOnline && (
                          <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 border-2 border-white rounded-full"></div>
                        )}
                      </div>
                      <div className="mr-4">
                        <div className="flex items-center">
                          <h3 className="font-bold text-navy">{craftsman.name}</h3>
                          {craftsman.verified && (
                            <svg className="w-5 h-5 text-blue-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">{craftsman.profession}</p>
                        <div className="flex items-center text-sm text-gray-500">
                          <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          </svg>
                          {craftsman.location}
                        </div>
                      </div>
                    </div>
                    <div className="text-left">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 text-yellow-500 ml-1" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <span className="font-semibold">{craftsman.rating}</span>
                        <span className="text-sm text-gray-500 mr-1">({craftsman.reviewsCount})</span>
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent>
                  <p className="text-gray-700 text-sm mb-4 line-clamp-2">{craftsman.bio}</p>

                  <div className="flex flex-wrap gap-1 mb-4">
                    {craftsman.skills.slice(0, 3).map((skill, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                    {craftsman.skills.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{craftsman.skills.length - 3}
                      </Badge>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div className="bg-green-50 p-2 rounded-lg">
                      <div className="text-green-600 font-medium">السعر/ساعة</div>
                      <div className="font-bold text-green-700">{craftsman.hourlyRate.toLocaleString()} ل.س</div>
                    </div>
                    <div className="bg-blue-50 p-2 rounded-lg">
                      <div className="text-blue-600 font-medium">المشاريع</div>
                      <div className="font-bold text-blue-700">{craftsman.completedJobs}</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <span className="flex items-center">
                      <div className={`w-2 h-2 rounded-full ml-1 ${craftsman.isOnline ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                      {craftsman.isOnline ? 'متصل الآن' : 'غير متصل'}
                    </span>
                    <span>يرد {craftsman.responseTime}</span>
                  </div>

                  <div className="flex space-x-2 space-x-reverse">
                    <Link href={`/craftsmen/${craftsman.id}`} className="flex-1">
                      <Button size="sm" className="w-full bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90">
                        عرض الملف الشخصي
                      </Button>
                    </Link>
                    <Button size="sm" variant="outline" className="px-3">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <Button variant="outline" size="lg" className="px-8">
              تحميل المزيد من الحرفيين
            </Button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default CraftsmenPage;
