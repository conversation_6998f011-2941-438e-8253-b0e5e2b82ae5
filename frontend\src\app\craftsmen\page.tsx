'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';

interface Craftsman {
  id: number;
  name: string;
  profession: string;
  bio: string;
  location: string;
  rating: number;
  reviewsCount: number;
  completedJobs: number;
  hourlyRate: number;
  skills: string[];
  avatar: string;
  isOnline: boolean;
  responseTime: string;
  joinedDate: string;
  verified: boolean;
}

const CraftsmenPage = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLocation, setSelectedLocation] = useState('all');
  const [sortBy, setSortBy] = useState('rating');
  const [searchQuery, setSearchQuery] = useState('');

  const categories = [
    { id: 'all', name: 'جميع التخصصات' },
    { id: 'carpentry', name: 'النجارة' },
    { id: 'plumbing', name: 'السباكة' },
    { id: 'electrical', name: 'الكهرباء' },
    { id: 'painting', name: 'الدهان' },
    { id: 'construction', name: 'البناء' },
    { id: 'hvac', name: 'التكييف' },
    { id: 'metalwork', name: 'الحدادة' },
    { id: 'landscaping', name: 'تنسيق الحدائق' },
  ];

  const locations = [
    { id: 'all', name: 'جميع المحافظات' },
    { id: 'damascus', name: 'دمشق' },
    { id: 'aleppo', name: 'حلب' },
    { id: 'homs', name: 'حمص' },
    { id: 'hama', name: 'حماة' },
    { id: 'lattakia', name: 'اللاذقية' },
    { id: 'tartous', name: 'طرطوس' },
    { id: 'daraa', name: 'درعا' },
  ];

  const craftsmen: Craftsman[] = [
    {
      id: 1,
      name: 'محمد النجار',
      profession: 'نجار محترف',
      bio: 'نجار محترف مع خبرة 10 سنوات في تصميم وتنفيذ الأثاث المنزلي والمكتبي. متخصص في المطابخ والخزائن العصرية.',
      location: 'دمشق، المزة',
      rating: 4.9,
      reviewsCount: 47,
      completedJobs: 89,
      hourlyRate: 2500,
      skills: ['نجارة', 'تصميم أثاث', 'تركيب', 'ترميم'],
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
      isOnline: true,
      responseTime: 'خلال ساعة',
      joinedDate: '2022-03-15',
      verified: true
    },
    {
      id: 2,
      name: 'أحمد السباك',
      profession: 'سباك معتمد',
      bio: 'سباك معتمد مع خبرة 8 سنوات في جميع أعمال السباكة والصرف الصحي. أعمل بأحدث التقنيات وأضمن الجودة.',
      location: 'حلب، الفرقان',
      rating: 4.8,
      reviewsCount: 32,
      completedJobs: 67,
      hourlyRate: 2000,
      skills: ['سباكة', 'صرف صحي', 'تسليك', 'تركيب'],
      avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
      isOnline: false,
      responseTime: 'خلال 3 ساعات',
      joinedDate: '2022-07-20',
      verified: true
    },
    {
      id: 3,
      name: 'علي الكهربائي',
      profession: 'كهربائي محترف',
      bio: 'كهربائي محترف متخصص في التمديدات الكهربائية والإضاءة الذكية. أعمل وفق أعلى معايير السلامة.',
      location: 'دمشق، جرمانا',
      rating: 4.7,
      reviewsCount: 28,
      completedJobs: 54,
      hourlyRate: 2200,
      skills: ['كهرباء', 'تمديدات', 'إضاءة', 'صيانة'],
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
      isOnline: true,
      responseTime: 'خلال 30 دقيقة',
      joinedDate: '2023-01-10',
      verified: false
    },
    {
      id: 4,
      name: 'خالد الدهان',
      profession: 'دهان وديكور',
      bio: 'دهان محترف متخصص في الدهانات الحديثة والديكورات الداخلية. أستخدم أفضل أنواع الدهانات العالمية.',
      location: 'حمص، الوعر',
      rating: 4.6,
      reviewsCount: 19,
      completedJobs: 41,
      hourlyRate: 1800,
      skills: ['دهان', 'ديكور', 'تصميم', 'تشطيب'],
      avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
      isOnline: true,
      responseTime: 'خلال ساعتين',
      joinedDate: '2023-05-22',
      verified: true
    },
    {
      id: 5,
      name: 'سامر البناء',
      profession: 'مقاول بناء',
      bio: 'مقاول بناء مع فريق عمل متكامل. متخصص في أعمال البناء والترميم والتشطيبات بجودة عالية.',
      location: 'حماة، المدينة',
      rating: 4.8,
      reviewsCount: 35,
      completedJobs: 23,
      hourlyRate: 3000,
      skills: ['بناء', 'ترميم', 'تشطيب', 'إدارة مشاريع'],
      avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
      isOnline: false,
      responseTime: 'خلال 4 ساعات',
      joinedDate: '2022-11-08',
      verified: true
    },
    {
      id: 6,
      name: 'يوسف التكييف',
      profession: 'فني تكييف',
      bio: 'فني تكييف معتمد متخصص في تركيب وصيانة جميع أنواع أجهزة التكييف والتبريد.',
      location: 'اللاذقية، الرمل الشمالي',
      rating: 4.5,
      reviewsCount: 22,
      completedJobs: 38,
      hourlyRate: 2300,
      skills: ['تكييف', 'تبريد', 'صيانة', 'تركيب'],
      avatar: 'https://randomuser.me/api/portraits/men/6.jpg',
      isOnline: true,
      responseTime: 'خلال ساعة',
      joinedDate: '2023-02-14',
      verified: false
    }
  ];

  const filteredCraftsmen = craftsmen.filter(craftsman => {
    const matchesCategory = selectedCategory === 'all' || craftsman.skills.some(skill => 
      skill.toLowerCase().includes(selectedCategory.toLowerCase())
    );
    const matchesLocation = selectedLocation === 'all' || 
      craftsman.location.toLowerCase().includes(selectedLocation.toLowerCase());
    const matchesSearch = searchQuery === '' || 
      craftsman.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      craftsman.profession.toLowerCase().includes(searchQuery.toLowerCase()) ||
      craftsman.skills.some(skill => skill.toLowerCase().includes(searchQuery.toLowerCase()));
    
    return matchesCategory && matchesLocation && matchesSearch;
  });

  const sortedCraftsmen = [...filteredCraftsmen].sort((a, b) => {
    switch (sortBy) {
      case 'rating':
        return b.rating - a.rating;
      case 'price_low':
        return a.hourlyRate - b.hourlyRate;
      case 'price_high':
        return b.hourlyRate - a.hourlyRate;
      case 'experience':
        return b.completedJobs - a.completedJobs;
      default:
        return b.rating - a.rating;
    }
  });

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-beige via-white to-skyblue/20 py-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-navy mb-4">الحرفيون المحترفون</h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              اعثر على أفضل الحرفيين المحترفين في سوريا لتنفيذ مشاريعك بجودة عالية
            </p>
          </div>

          {/* Search and Filters */}
          <div className="bg-white rounded-2xl shadow-lg p-6 mb-8">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  البحث
                </label>
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="ابحث عن حرفي..."
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التخصص
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                >
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المحافظة
                </label>
                <select
                  value={selectedLocation}
                  onChange={(e) => setSelectedLocation(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                >
                  {locations.map(location => (
                    <option key={location.id} value={location.id}>
                      {location.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  ترتيب حسب
                </label>
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                >
                  <option value="rating">التقييم</option>
                  <option value="price_low">السعر (الأقل أولاً)</option>
                  <option value="price_high">السعر (الأعلى أولاً)</option>
                  <option value="experience">الخبرة</option>
                </select>
              </div>
            </div>
            <div className="text-sm text-gray-600">
              تم العثور على {sortedCraftsmen.length} حرفي
            </div>
          </div>

          {/* Craftsmen Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {sortedCraftsmen.map((craftsman) => (
              <Card key={craftsman.id} className="hover:shadow-xl transition-all duration-300 border-0 bg-white/90 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center">
                      <div className="relative">
                        <img
                          src={craftsman.avatar}
                          alt={craftsman.name}
                          className="w-16 h-16 rounded-full object-cover"
                        />
                        {craftsman.isOnline && (
                          <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 border-2 border-white rounded-full"></div>
                        )}
                      </div>
                      <div className="mr-4">
                        <div className="flex items-center">
                          <h3 className="font-bold text-navy">{craftsman.name}</h3>
                          {craftsman.verified && (
                            <svg className="w-5 h-5 text-blue-500 mr-1" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">{craftsman.profession}</p>
                        <div className="flex items-center text-sm text-gray-500">
                          <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          </svg>
                          {craftsman.location}
                        </div>
                      </div>
                    </div>
                    <div className="text-left">
                      <div className="flex items-center">
                        <svg className="w-4 h-4 text-yellow-500 ml-1" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                        </svg>
                        <span className="font-semibold">{craftsman.rating}</span>
                        <span className="text-sm text-gray-500 mr-1">({craftsman.reviewsCount})</span>
                      </div>
                    </div>
                  </div>
                </CardHeader>

                <CardContent>
                  <p className="text-gray-700 text-sm mb-4 line-clamp-2">{craftsman.bio}</p>

                  <div className="flex flex-wrap gap-1 mb-4">
                    {craftsman.skills.slice(0, 3).map((skill, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                    {craftsman.skills.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{craftsman.skills.length - 3}
                      </Badge>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div className="bg-green-50 p-2 rounded-lg">
                      <div className="text-green-600 font-medium">السعر/ساعة</div>
                      <div className="font-bold text-green-700">{craftsman.hourlyRate.toLocaleString()} ل.س</div>
                    </div>
                    <div className="bg-blue-50 p-2 rounded-lg">
                      <div className="text-blue-600 font-medium">المشاريع</div>
                      <div className="font-bold text-blue-700">{craftsman.completedJobs}</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm text-gray-600 mb-4">
                    <span className="flex items-center">
                      <div className={`w-2 h-2 rounded-full ml-1 ${craftsman.isOnline ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                      {craftsman.isOnline ? 'متصل الآن' : 'غير متصل'}
                    </span>
                    <span>يرد {craftsman.responseTime}</span>
                  </div>

                  <div className="flex space-x-2 space-x-reverse">
                    <Link href={`/craftsmen/${craftsman.id}`} className="flex-1">
                      <Button size="sm" className="w-full bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90">
                        عرض الملف الشخصي
                      </Button>
                    </Link>
                    <Button size="sm" variant="outline" className="px-3">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Load More */}
          <div className="text-center mt-12">
            <Button variant="outline" size="lg" className="px-8">
              تحميل المزيد من الحرفيين
            </Button>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default CraftsmenPage;
