import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/users/[id] - جلب مستخدم واحد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = await prisma.user.findUnique({
      where: { id: params.id },
      include: {
        craftsmanProfile: {
          include: {
            portfolioItems: {
              orderBy: { createdAt: 'desc' },
              take: 5,
            },
          },
        },
        _count: {
          select: {
            clientProjects: true,
            craftsmanOffers: true,
            clientReviews: true,
            craftsmanReviews: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'المستخدم غير موجود' },
        { status: 404 }
      );
    }

    return NextResponse.json(user);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'خطأ في جلب المستخدم' },
      { status: 500 }
    );
  }
}

// PUT /api/users/[id] - تحديث مستخدم
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { name, phone, location, bio, avatar } = body;

    const user = await prisma.user.update({
      where: { id: params.id },
      data: {
        name,
        phone,
        location,
        bio,
        avatar,
      },
      include: {
        craftsmanProfile: true,
      },
    });

    return NextResponse.json(user);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'خطأ في تحديث المستخدم' },
      { status: 500 }
    );
  }
}

// DELETE /api/users/[id] - حذف مستخدم
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.user.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: 'تم حذف المستخدم بنجاح' });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'خطأ في حذف المستخدم' },
      { status: 500 }
    );
  }
}
