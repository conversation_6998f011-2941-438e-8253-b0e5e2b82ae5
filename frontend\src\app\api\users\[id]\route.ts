import { NextRequest, NextResponse } from 'next/server';
import { mockUsers, getProjectsByUserId, getOffersByCraftsmanId } from '@/lib/mock-data';

// GET /api/users/[id] - جلب مستخدم محدد
export async function GET(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const { id } = context.params;
    
    const user = mockUsers.find(u => u.id === id);
    if (!user) {
      return NextResponse.json(
        { error: 'المستخدم غير موجود' },
        { status: 404 }
      );
    }

    // إضافة إحصائيات المستخدم
    const userProjects = getProjectsByUserId(id);
    const userOffers = getOffersByCraftsmanId(id);

    const userWithStats = {
      ...user,
      stats: {
        totalProjects: userProjects.length,
        completedProjects: userProjects.filter(p => p.status === 'COMPLETED').length,
        totalOffers: userOffers.length,
        acceptedOffers: userOffers.filter(o => o.status === 'ACCEPTED').length,
      }
    };

    return NextResponse.json({ user: userWithStats });
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'خطأ في جلب المستخدم' },
      { status: 500 }
    );
  }
}

// PUT /api/users/[id] - تحديث مستخدم
export async function PUT(
  request: NextRequest,
  context: { params: { id: string } }
) {
  try {
    const { id } = context.params;
    const body = await request.json();

    const userIndex = mockUsers.findIndex(u => u.id === id);
    if (userIndex === -1) {
      return NextResponse.json(
        { error: 'المستخدم غير موجود' },
        { status: 404 }
      );
    }

    // تحديث المستخدم (منع تغيير بعض الحقول الحساسة)
    const { id: _, email: __, role: ___, ...updateData } = body;
    
    const updatedUser = {
      ...mockUsers[userIndex],
      ...updateData,
    };

    mockUsers[userIndex] = updatedUser;

    return NextResponse.json({
      user: updatedUser,
      message: 'تم تحديث الملف الشخصي بنجاح'
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'خطأ في تحديث المستخدم' },
      { status: 500 }
    );
  }
}
