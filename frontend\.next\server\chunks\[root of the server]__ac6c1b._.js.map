{"version": 3, "sources": [], "sections": [{"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/<PERSON>zan%20Website/frontend/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\nimport { NextAuthOptions } from 'next-auth';\nimport CredentialsProvider from 'next-auth/providers/credentials';\nimport GoogleProvider from 'next-auth/providers/google';\nimport Facebook<PERSON>rovider from 'next-auth/providers/facebook';\n\nconst authOptions: NextAuthOptions = {\n  providers: [\n    CredentialsProvider({\n      name: 'credentials',\n      credentials: {\n        email: { label: 'Email', type: 'email' },\n        password: { label: 'Password', type: 'password' }\n      },\n      async authorize(credentials) {\n        if (!credentials?.email || !credentials?.password) {\n          return null;\n        }\n\n        try {\n          // للتطوير: قبول أي بيانات اعتماد صحيحة\n          // في الإنتاج: استدعاء API حقيقي\n          if (credentials.email && credentials.password.length >= 6) {\n            const userName = credentials.email.split('@')[0];\n            // استخدام الدور المرسل أو افتراضي\n            const role = (credentials as any).role || 'client';\n            return {\n              id: '1',\n              email: credentials.email,\n              name: userName.charAt(0).toUpperCase() + userName.slice(1),\n              role: role,\n            };\n          }\n\n          return null;\n        } catch (error) {\n          console.error('Auth error:', error);\n          return null;\n        }\n      }\n    }),\n    // تعطيل Google و Facebook مؤقتاً للتطوير\n    ...(process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET ? [\n      GoogleProvider({\n        clientId: process.env.GOOGLE_CLIENT_ID,\n        clientSecret: process.env.GOOGLE_CLIENT_SECRET,\n      })\n    ] : []),\n    ...(process.env.FACEBOOK_CLIENT_ID && process.env.FACEBOOK_CLIENT_SECRET ? [\n      FacebookProvider({\n        clientId: process.env.FACEBOOK_CLIENT_ID,\n        clientSecret: process.env.FACEBOOK_CLIENT_SECRET,\n      })\n    ] : []),\n  ],\n  session: {\n    strategy: 'jwt',\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  jwt: {\n    maxAge: 30 * 24 * 60 * 60, // 30 days\n  },\n  callbacks: {\n    async jwt({ token, user, account }) {\n      // إضافة معلومات إضافية للـ token\n      if (user) {\n        token.role = user.role || 'client';\n      }\n\n      // للتسجيل عبر Google أو Facebook\n      if (account && (account.provider === 'google' || account.provider === 'facebook')) {\n        token.role = 'client'; // افتراضي للتطوير\n      }\n\n      return token;\n    },\n    async session({ session, token }) {\n      // إضافة معلومات إضافية للـ session\n      if (token && session.user) {\n        session.user.id = token.sub || '1';\n        session.user.role = (token.role as 'client' | 'craftsman' | 'admin') || 'client';\n        // التأكد من وجود name\n        if (!session.user.name && session.user.email) {\n          const userName = session.user.email.split('@')[0];\n          session.user.name = userName.charAt(0).toUpperCase() + userName.slice(1);\n        }\n      }\n      return session;\n    },\n  },\n  pages: {\n    signIn: '/login',\n    signUp: '/register',\n    error: '/auth/error',\n  },\n  secret: process.env.NEXTAUTH_SECRET,\n};\n\nconst handler = NextAuth(authOptions);\n\nexport { handler as GET, handler as POST };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AACA;;;;;AAEA,MAAM,cAA+B;IACnC,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAW;gBACzB,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,IAAI;oBACF,uCAAuC;oBACvC,gCAAgC;oBAChC,IAAI,YAAY,KAAK,IAAI,YAAY,QAAQ,CAAC,MAAM,IAAI,GAAG;wBACzD,MAAM,WAAW,YAAY,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wBAChD,kCAAkC;wBAClC,MAAM,OAAO,AAAC,YAAoB,IAAI,IAAI;wBAC1C,OAAO;4BACL,IAAI;4BACJ,OAAO,YAAY,KAAK;4BACxB,MAAM,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;4BACxD,MAAM;wBACR;oBACF;oBAEA,OAAO;gBACT,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,eAAe;oBAC7B,OAAO;gBACT;YACF;QACF;QACA,yCAAyC;WACrC,QAAQ,GAAG,CAAC,gBAAgB,IAAI,QAAQ,GAAG,CAAC,oBAAoB,GAAG;YACrE,CAAA,GAAA,qJAAA,CAAA,UAAc,AAAD,EAAE;gBACb,UAAU,QAAQ,GAAG,CAAC,gBAAgB;gBACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;YAChD;SACD,GAAG,EAAE;WACF,QAAQ,GAAG,CAAC,kBAAkB,IAAI,QAAQ,GAAG,CAAC,sBAAsB,GAAG;YACzE,CAAA,GAAA,uJAAA,CAAA,UAAgB,AAAD,EAAE;gBACf,UAAU,QAAQ,GAAG,CAAC,kBAAkB;gBACxC,cAAc,QAAQ,GAAG,CAAC,sBAAsB;YAClD;SACD,GAAG,EAAE;KACP;IACD,SAAS;QACP,UAAU;QACV,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,KAAK;QACH,QAAQ,KAAK,KAAK,KAAK;IACzB;IACA,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE;YAChC,iCAAiC;YACjC,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI,IAAI;YAC5B;YAEA,iCAAiC;YACjC,IAAI,WAAW,CAAC,QAAQ,QAAQ,KAAK,YAAY,QAAQ,QAAQ,KAAK,UAAU,GAAG;gBACjF,MAAM,IAAI,GAAG,UAAU,kBAAkB;YAC3C;YAEA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,mCAAmC;YACnC,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI;gBAC/B,QAAQ,IAAI,CAAC,IAAI,GAAG,AAAC,MAAM,IAAI,IAAyC;gBACxE,sBAAsB;gBACtB,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,IAAI,QAAQ,IAAI,CAAC,KAAK,EAAE;oBAC5C,MAAM,WAAW,QAAQ,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;oBACjD,QAAQ,IAAI,CAAC,IAAI,GAAG,SAAS,MAAM,CAAC,GAAG,WAAW,KAAK,SAAS,KAAK,CAAC;gBACxE;YACF;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE"}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}