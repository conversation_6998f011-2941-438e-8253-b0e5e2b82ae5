{"version": 3, "sources": [], "sections": [{"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/<PERSON>zan%20Website/frontend/src/app/api/auth/%5B...nextauth%5D/route.ts"], "sourcesContent": ["import NextAuth from 'next-auth';\r\nimport CredentialsProvider from 'next-auth/providers/credentials';\r\n\r\nconst authOptions = {\r\n  providers: [\r\n    CredentialsProvider({\r\n      name: 'credentials',\r\n      credentials: {\r\n        email: { label: 'Email', type: 'email' },\r\n        password: { label: 'Password', type: 'password' }\r\n      },\r\n      async authorize(credentials: any) {\r\n        if (!credentials?.email || !credentials?.password) {\r\n          return null;\r\n        }\r\n\r\n        // حسابات تجريبية ثابتة للتطوير\r\n        const testAccounts = [\r\n          { email: '<EMAIL>', password: 'Test123!@#', role: 'ADMIN', name: 'مدير النظام' },\r\n          { email: '<EMAIL>', password: 'Test123!@#', role: 'CLIENT', name: 'أحمد محمد' },\r\n          { email: '<EMAIL>', password: 'Test123!@#', role: 'CRAFTSMAN', name: 'محمد النجار' },\r\n          { email: '<EMAIL>', password: 'Test123!@#', role: 'CRAFTSMAN', name: 'سارة الكهربائية' },\r\n        ];\r\n\r\n        const testUser = testAccounts.find(\r\n          account => account.email === credentials.email && account.password === credentials.password\r\n        );\r\n\r\n        if (testUser) {\r\n          return {\r\n            id: testUser.email.split('@')[0],\r\n            email: testUser.email,\r\n            name: testUser.name,\r\n            role: testUser.role,\r\n            isVerified: true,\r\n          };\r\n        }\r\n\r\n        return null;\r\n      }\r\n    }),\r\n  ],\r\n  callbacks: {\r\n    async jwt({ token, user }: any) {\r\n      if (user) {\r\n        token.role = user.role;\r\n        token.isVerified = user.isVerified;\r\n      }\r\n      return token;\r\n    },\r\n    async session({ session, token }: any) {\r\n      if (token && session.user) {\r\n        session.user.id = token.sub || '1';\r\n        session.user.role = token.role;\r\n        session.user.isVerified = token.isVerified;\r\n      }\r\n      return session;\r\n    },\r\n  },\r\n  pages: {\r\n    signIn: '/auth/signin',\r\n    error: '/auth/error',\r\n  },\r\n  secret: process.env.NEXTAUTH_SECRET,\r\n};\r\n\r\nconst handler = NextAuth(authOptions);\r\n\r\nexport { handler as GET, handler as POST };"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,cAAc;IAClB,WAAW;QACT,CAAA,GAAA,0JAAA,CAAA,UAAmB,AAAD,EAAE;YAClB,MAAM;YACN,aAAa;gBACX,OAAO;oBAAE,OAAO;oBAAS,MAAM;gBAAQ;gBACvC,UAAU;oBAAE,OAAO;oBAAY,MAAM;gBAAW;YAClD;YACA,MAAM,WAAU,WAAgB;gBAC9B,IAAI,CAAC,aAAa,SAAS,CAAC,aAAa,UAAU;oBACjD,OAAO;gBACT;gBAEA,+BAA+B;gBAC/B,MAAM,eAAe;oBACnB;wBAAE,OAAO;wBAAmB,UAAU;wBAAc,MAAM;wBAAS,MAAM;oBAAc;oBACvF;wBAAE,OAAO;wBAAoB,UAAU;wBAAc,MAAM;wBAAU,MAAM;oBAAY;oBACvF;wBAAE,OAAO;wBAAwB,UAAU;wBAAc,MAAM;wBAAa,MAAM;oBAAc;oBAChG;wBAAE,OAAO;wBAAwB,UAAU;wBAAc,MAAM;wBAAa,MAAM;oBAAkB;iBACrG;gBAED,MAAM,WAAW,aAAa,IAAI,CAChC,CAAA,UAAW,QAAQ,KAAK,KAAK,YAAY,KAAK,IAAI,QAAQ,QAAQ,KAAK,YAAY,QAAQ;gBAG7F,IAAI,UAAU;oBACZ,OAAO;wBACL,IAAI,SAAS,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;wBAChC,OAAO,SAAS,KAAK;wBACrB,MAAM,SAAS,IAAI;wBACnB,MAAM,SAAS,IAAI;wBACnB,YAAY;oBACd;gBACF;gBAEA,OAAO;YACT;QACF;KACD;IACD,WAAW;QACT,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAO;YAC5B,IAAI,MAAM;gBACR,MAAM,IAAI,GAAG,KAAK,IAAI;gBACtB,MAAM,UAAU,GAAG,KAAK,UAAU;YACpC;YACA,OAAO;QACT;QACA,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAO;YACnC,IAAI,SAAS,QAAQ,IAAI,EAAE;gBACzB,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG,IAAI;gBAC/B,QAAQ,IAAI,CAAC,IAAI,GAAG,MAAM,IAAI;gBAC9B,QAAQ,IAAI,CAAC,UAAU,GAAG,MAAM,UAAU;YAC5C;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;QACR,OAAO;IACT;IACA,QAAQ,QAAQ,GAAG,CAAC,eAAe;AACrC;AAEA,MAAM,UAAU,CAAA,GAAA,uIAAA,CAAA,UAAQ,AAAD,EAAE"}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}