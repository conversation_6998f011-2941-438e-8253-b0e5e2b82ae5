'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';
import supabase from '@/lib/supabase';

export default function RegisterPage() {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
    role: 'CLIENT',
    location: '',
    specialties: [] as string[]
  });
  const [loading, setLoading] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.password !== formData.confirmPassword) {
      alert('كلمات المرور غير متطابقة');
      return;
    }

    setLoading(true);

    try {
      // إنشاء المستخدم في قاعدة البيانات
      const { data, error } = await supabase
        .from('users')
        .insert([{
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          role: formData.role,
          location: formData.location
        }])
        .select()
        .single();

      if (error) throw error;

      // حفظ بيانات المستخدم محلياً
      if (typeof window !== 'undefined') {
        localStorage.setItem('user', JSON.stringify({
          id: data.id,
          name: data.name,
          email: data.email,
          role: data.role
        }));
      }

      alert('تم إنشاء الحساب بنجاح!');

      // توجيه حسب نوع المستخدم
      if (formData.role === 'CLIENT') {
        router.push('/client/dashboard');
      } else {
        router.push('/craftsman/dashboard');
      }
    } catch (error: any) {
      console.error('Error creating account:', error);
      alert('حدث خطأ أثناء إنشاء الحساب: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const nextStep = () => {
    if (currentStep === 1) {
      if (!formData.name || !formData.email || !formData.phone) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
      }
    }
    setCurrentStep(prev => prev + 1);
  };

  const prevStep = () => {
    setCurrentStep(prev => prev - 1);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-navy via-teal to-skyblue flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Logo */}
        <div className="text-center mb-8">
          <Link href="/" className="inline-flex items-center space-x-2 space-x-reverse">
            <div className="w-12 h-12 bg-white rounded-xl flex items-center justify-center shadow-lg">
              <span className="text-2xl font-bold text-navy">د</span>
            </div>
            <span className="text-3xl font-bold text-white">دوزان</span>
          </Link>
          <p className="text-white/80 mt-2">إنشاء حساب جديد</p>
        </div>

        {/* Progress Steps */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4 space-x-reverse">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
              currentStep >= 1 ? 'bg-white text-navy' : 'bg-white/30 text-white'
            }`}>
              1
            </div>
            <div className={`w-16 h-1 ${currentStep >= 2 ? 'bg-white' : 'bg-white/30'}`}></div>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
              currentStep >= 2 ? 'bg-white text-navy' : 'bg-white/30 text-white'
            }`}>
              2
            </div>
          </div>
        </div>

        {/* Registration Form */}
        <div className="bg-white rounded-2xl shadow-2xl p-8">
          <h1 className="text-2xl font-bold text-navy text-center mb-6">
            {currentStep === 1 ? 'المعلومات الأساسية' : 'تفاصيل الحساب'}
          </h1>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            {currentStep === 1 && (
              <>
                <div>
                  <label className="block text-gray-700 font-medium mb-2">الاسم الكامل *</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                    placeholder="أحمد محمد"
                    required
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2">البريد الإلكتروني *</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2">رقم الهاتف *</label>
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                    placeholder="+963991234567"
                    required
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2">نوع الحساب *</label>
                  <select
                    name="role"
                    value={formData.role}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                  >
                    <option value="CLIENT">عميل (أبحث عن حرفيين)</option>
                    <option value="CRAFTSMAN">حرفي (أقدم خدمات)</option>
                  </select>
                </div>

                <Button
                  type="button"
                  onClick={nextStep}
                  className="w-full bg-gradient-to-r from-teal to-navy py-3"
                >
                  التالي
                </Button>
              </>
            )}

            {currentStep === 2 && (
              <>
                <div>
                  <label className="block text-gray-700 font-medium mb-2">المدينة *</label>
                  <select
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                    required
                  >
                    <option value="">اختر المدينة</option>
                    <option value="دمشق">دمشق</option>
                    <option value="حلب">حلب</option>
                    <option value="حمص">حمص</option>
                    <option value="حماة">حماة</option>
                    <option value="اللاذقية">اللاذقية</option>
                    <option value="طرطوس">طرطوس</option>
                    <option value="درعا">درعا</option>
                    <option value="السويداء">السويداء</option>
                    <option value="القنيطرة">القنيطرة</option>
                    <option value="دير الزور">دير الزور</option>
                    <option value="الرقة">الرقة</option>
                    <option value="الحسكة">الحسكة</option>
                    <option value="إدلب">إدلب</option>
                  </select>
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2">كلمة المرور *</label>
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                    placeholder="••••••••"
                    required
                  />
                </div>

                <div>
                  <label className="block text-gray-700 font-medium mb-2">تأكيد كلمة المرور *</label>
                  <input
                    type="password"
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal focus:border-transparent"
                    placeholder="••••••••"
                    required
                  />
                </div>

                <div className="flex space-x-4 space-x-reverse">
                  <Button
                    type="button"
                    onClick={prevStep}
                    variant="outline"
                    className="flex-1"
                  >
                    السابق
                  </Button>
                  
                  <Button
                    type="submit"
                    className="flex-1 bg-gradient-to-r from-teal to-navy"
                    disabled={loading}
                  >
                    {loading ? 'جاري الإنشاء...' : 'إنشاء الحساب'}
                  </Button>
                </div>
              </>
            )}
          </form>

          <div className="mt-6 text-center">
            <p className="text-gray-600">
              لديك حساب بالفعل؟{' '}
              <Link href="/auth/login" className="text-teal font-medium hover:underline">
                تسجيل الدخول
              </Link>
            </p>
          </div>
        </div>

        <div className="text-center mt-6">
          <Link href="/" className="text-white/80 hover:text-white transition-colors">
            ← العودة للصفحة الرئيسية
          </Link>
        </div>
      </div>
    </div>
  );
}
