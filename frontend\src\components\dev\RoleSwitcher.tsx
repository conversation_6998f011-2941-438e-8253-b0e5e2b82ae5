'use client';

import React from 'react';
import { useSession, signOut, signIn } from 'next-auth/react';
import { Button } from '@/components/ui/Button';

const RoleSwitcher = () => {
  const { data: session, update } = useSession();

  const switchRole = async (newRole: 'client' | 'craftsman' | 'admin') => {
    if (session?.user) {
      try {
        // في التطوير: تسجيل خروج ثم دخول بالدور الجديد
        const email = session.user.email;

        // تسجيل خروج
        await signOut({ redirect: false });

        // انتظار قصير
        await new Promise(resolve => setTimeout(resolve, 100));

        // تسجيل دخول بالدور الجديد
        const result = await signIn('credentials', {
          email: email,
          password: '123456',
          role: newRole, // إرسال الدور الجديد
          redirect: false,
        });

        if (result?.ok) {
          // إعادة تحميل الصفحة
          window.location.reload();
        }
      } catch (error) {
        console.error('Error switching role:', error);
      }
    }
  };

  if (!session?.user) {
    return null;
  }

  return (
    <div className="fixed bottom-4 left-4 z-50 bg-white border border-gray-200 rounded-lg shadow-lg p-4">
      <h3 className="text-sm font-semibold text-gray-700 mb-3">تبديل الدور (للتطوير)</h3>
      <div className="space-y-2">
        <Button
          size="sm"
          variant={session.user.role === 'client' ? 'default' : 'outline'}
          onClick={() => switchRole('client')}
          className="w-full text-xs"
        >
          👤 عميل
        </Button>
        <Button
          size="sm"
          variant={session.user.role === 'craftsman' ? 'default' : 'outline'}
          onClick={() => switchRole('craftsman')}
          className="w-full text-xs"
        >
          👨‍🔧 حرفي
        </Button>
        <Button
          size="sm"
          variant={session.user.role === 'admin' ? 'default' : 'outline'}
          onClick={() => switchRole('admin')}
          className="w-full text-xs"
        >
          👑 مدير
        </Button>
      </div>
      <div className="mt-3 pt-3 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          الدور الحالي: <span className="font-medium">{session.user.role}</span>
        </p>
      </div>
    </div>
  );
};

export default RoleSwitcher;
