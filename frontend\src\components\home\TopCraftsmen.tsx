'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';

const TopCraftsmen = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const craftsmen = [
    {
      id: 1,
      name: 'محمد النجار',
      profession: 'نجار محترف',
      avatar: 'https://randomuser.me/api/portraits/men/1.jpg',
      rating: 4.9,
      reviewsCount: 47,
      completedJobs: 89,
      specialties: ['مطابخ', 'خزائن', 'أثاث مكتبي'],
      location: 'دمشق، المزة',
      hourlyRate: 2500,
      responseTime: 'خلال ساعة',
      verified: true,
      portfolioImages: [
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=200&fit=crop',
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=200&fit=crop&crop=entropy',
        'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=200&fit=crop&crop=faces'
      ]
    },
    {
      id: 2,
      name: 'أحمد السباك',
      profession: 'سباك معتمد',
      avatar: 'https://randomuser.me/api/portraits/men/2.jpg',
      rating: 4.8,
      reviewsCount: 32,
      completedJobs: 67,
      specialties: ['سباكة', 'صرف صحي', 'تسليك'],
      location: 'حلب، الفرقان',
      hourlyRate: 2000,
      responseTime: 'خلال 3 ساعات',
      verified: true,
      portfolioImages: [
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop',
        'https://images.unsplash.com/photo-1607472586893-edb57bdc0e39?w=300&h=200&fit=crop',
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop&crop=entropy'
      ]
    },
    {
      id: 3,
      name: 'علي الكهربائي',
      profession: 'كهربائي محترف',
      avatar: 'https://randomuser.me/api/portraits/men/3.jpg',
      rating: 4.7,
      reviewsCount: 28,
      completedJobs: 54,
      specialties: ['كهرباء', 'تمديدات', 'إضاءة'],
      location: 'دمشق، جرمانا',
      hourlyRate: 2200,
      responseTime: 'خلال 30 دقيقة',
      verified: false,
      portfolioImages: [
        'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=200&fit=crop',
        'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop',
        'https://images.unsplash.com/photo-1621905251189-08b45d6a269e?w=300&h=200&fit=crop&crop=entropy'
      ]
    },
    {
      id: 4,
      name: 'خالد الدهان',
      profession: 'دهان وديكور',
      avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
      rating: 4.6,
      reviewsCount: 19,
      completedJobs: 41,
      specialties: ['دهان', 'ديكور', 'تصميم'],
      location: 'حمص، الوعر',
      hourlyRate: 1800,
      responseTime: 'خلال ساعتين',
      verified: true,
      portfolioImages: [
        'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=200&fit=crop',
        'https://images.unsplash.com/photo-1581858726788-75bc0f6a952d?w=300&h=200&fit=crop',
        'https://images.unsplash.com/photo-1562259949-e8e7689d7828?w=300&h=200&fit=crop&crop=entropy'
      ]
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % craftsmen.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [craftsmen.length]);

  return (
    <section className="py-20 bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden">
      {/* خلفية متحركة */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-full h-full opacity-10">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl animate-pulse"></div>
          <div className="absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl animate-pulse" style={{ animationDelay: '1s' }}></div>
          <div className="absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl animate-pulse" style={{ animationDelay: '2s' }}></div>
          <div className="absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl animate-pulse" style={{ animationDelay: '3s' }}></div>
        </div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* العنوان */}
        <div className="text-center mb-16">
          <div className="inline-block bg-white/20 backdrop-blur-sm rounded-full px-6 py-2 mb-6">
            <span className="text-white text-sm font-medium">⭐ نخبة الحرفيين</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            أفضل الحرفيين في 
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-yellow-300 to-orange-300">سوريا</span>
          </h2>
          <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
            تعرف على نخبة من أمهر الحرفيين المحترفين الذين حققوا أعلى التقييمات وأنجزوا مئات المشاريع بنجاح
          </p>
        </div>

        {/* عرض الحرفيين */}
        <div className="relative">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            {/* بطاقة الحرفي الرئيسية */}
            <div className="order-2 lg:order-1">
              <div className="bg-white/10 backdrop-blur-md rounded-3xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-500">
                <div className="flex items-start gap-6 mb-6">
                  <div className="relative">
                    <img
                      src={craftsmen[currentSlide].avatar}
                      alt={craftsmen[currentSlide].name}
                      className="w-20 h-20 rounded-full object-cover border-4 border-white/30"
                    />
                    {craftsmen[currentSlide].verified && (
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                  </div>

                  <div className="flex-1">
                    <h3 className="text-2xl font-bold text-white mb-1">
                      {craftsmen[currentSlide].name}
                    </h3>
                    <p className="text-white/80 mb-2">{craftsmen[currentSlide].profession}</p>
                    <div className="flex items-center text-white/70 text-sm mb-3">
                      <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      </svg>
                      {craftsmen[currentSlide].location}
                    </div>
                    
                    <div className="flex items-center mb-4">
                      <div className="flex items-center ml-4">
                        {[...Array(5)].map((_, i) => (
                          <svg
                            key={i}
                            className={`w-4 h-4 ${
                              i < Math.floor(craftsmen[currentSlide].rating) ? 'text-yellow-400' : 'text-white/30'
                            }`}
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                        ))}
                      </div>
                      <span className="text-white font-semibold">{craftsmen[currentSlide].rating}</span>
                      <span className="text-white/70 text-sm mr-2">({craftsmen[currentSlide].reviewsCount} تقييم)</span>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-6">
                  <div className="bg-white/10 p-4 rounded-xl">
                    <div className="text-white/70 text-sm mb-1">المشاريع المكتملة</div>
                    <div className="text-2xl font-bold text-white">{craftsmen[currentSlide].completedJobs}</div>
                  </div>
                  <div className="bg-white/10 p-4 rounded-xl">
                    <div className="text-white/70 text-sm mb-1">السعر/ساعة</div>
                    <div className="text-xl font-bold text-white">{craftsmen[currentSlide].hourlyRate.toLocaleString()} ل.س</div>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 mb-6">
                  {craftsmen[currentSlide].specialties.map((specialty, index) => (
                    <Badge key={index} variant="outline" className="border-white/30 text-white">
                      {specialty}
                    </Badge>
                  ))}
                </div>

                <div className="flex items-center justify-between">
                  <div className="text-white/70 text-sm">
                    ⚡ يرد {craftsmen[currentSlide].responseTime}
                  </div>
                  <Link href={`/craftsmen/${craftsmen[currentSlide].id}`}>
                    <Button className="bg-white text-navy hover:bg-white/90">
                      عرض الملف الشخصي
                    </Button>
                  </Link>
                </div>
              </div>
            </div>

            {/* معرض الأعمال */}
            <div className="order-1 lg:order-2">
              <div className="grid grid-cols-2 gap-4">
                {craftsmen[currentSlide].portfolioImages.map((image, index) => (
                  <div
                    key={index}
                    className="relative overflow-hidden rounded-2xl group"
                    style={{
                      animation: `fadeInScale 0.6s ease-out ${index * 0.1}s both`
                    }}
                  >
                    <img
                      src={image}
                      alt={`عمل ${index + 1}`}
                      className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* مؤشرات التنقل */}
          <div className="flex justify-center mt-8 space-x-2 space-x-reverse">
            {craftsmen.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentSlide(index)}
                className={`w-3 h-3 rounded-full transition-all duration-300 ${
                  currentSlide === index ? 'bg-white scale-125' : 'bg-white/50 hover:bg-white/70'
                }`}
              />
            ))}
          </div>
        </div>

        {/* زر عرض جميع الحرفيين */}
        <div className="text-center mt-12">
          <Link href="/craftsmen">
            <Button size="lg" className="bg-white text-navy hover:bg-white/90 px-8 py-4 text-lg">
              استكشف جميع الحرفيين
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Button>
          </Link>
        </div>
      </div>

      <style jsx>{`
        @keyframes fadeInScale {
          from {
            opacity: 0;
            transform: scale(0.8);
          }
          to {
            opacity: 1;
            transform: scale(1);
          }
        }
      `}</style>
    </section>
  );
};

export default TopCraftsmen;
