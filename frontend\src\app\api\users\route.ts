import { NextRequest, NextResponse } from 'next/server';
import { mockUsers } from '@/lib/mock-data';

// GET /api/users - جلب المستخدمين
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role');
    const location = searchParams.get('location');
    const isActive = searchParams.get('isActive');

    let filteredUsers = [...mockUsers];

    // تطبيق الفلاتر
    if (role) {
      filteredUsers = filteredUsers.filter(user => user.role === role);
    }
    if (location) {
      filteredUsers = filteredUsers.filter(user => 
        user.location?.includes(location)
      );
    }
    if (isActive !== null) {
      filteredUsers = filteredUsers.filter(user => 
        user.isActive === (isActive === 'true')
      );
    }

    // إزالة كلمات المرور من النتائج (في التطبيق الحقيقي لن تكون موجودة هنا)
    const safeUsers = filteredUsers.map(user => {
      const { ...safeUser } = user;
      return safeUser;
    });

    return NextResponse.json({
      users: safeUsers,
      total: safeUsers.length
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'خطأ في جلب المستخدمين' },
      { status: 500 }
    );
  }
}
