import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET /api/users - جلب جميع المستخدمين (للمدير فقط)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const role = searchParams.get('role') as UserRole | null;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';

    const skip = (page - 1) * limit;

    const where = {
      ...(role && { role }),
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' as const } },
          { email: { contains: search, mode: 'insensitive' as const } },
        ],
      }),
    };

    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip,
        take: limit,
        include: {
          craftsmanProfile: true,
          _count: {
            select: {
              clientProjects: true,
              craftsmanOffers: true,
              clientReviews: true,
              craftsmanReviews: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      }),
      prisma.user.count({ where }),
    ]);

    return NextResponse.json({
      users,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit),
      },
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'خطأ في جلب المستخدمين' },
      { status: 500 }
    );
  }
}

// POST /api/users - إنشاء مستخدم جديد
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, name, phone, role, location, bio } = body;

    // التحقق من وجود المستخدم
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'المستخدم موجود بالفعل' },
        { status: 400 }
      );
    }

    // إنشاء المستخدم
    const user = await prisma.user.create({
      data: {
        email,
        name,
        phone,
        role: role || UserRole.CLIENT,
        location,
        bio,
      },
      include: {
        craftsmanProfile: true,
      },
    });

    // إنشاء ملف الحرفي إذا كان الدور حرفي
    if (role === UserRole.CRAFTSMAN) {
      await prisma.craftsmanProfile.create({
        data: {
          userId: user.id,
          skills: [],
          experience: 0,
          languages: ['العربية'],
        },
      });
    }

    return NextResponse.json(user, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'خطأ في إنشاء المستخدم' },
      { status: 500 }
    );
  }
}
