(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},79551:e=>{"use strict";e.exports=require("url")},56953:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=r(70260),l=r(28203),t=r(25155),n=r.n(t),i=r(67292),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let c=["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,11891)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,71354)),"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,19937,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,69116,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,41485,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\profile\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:l.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29262:(e,s,r)=>{Promise.resolve().then(r.bind(r,11891))},92310:(e,s,r)=>{Promise.resolve().then(r.bind(r,65215))},65215:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>o});var a=r(45512),l=r(58009),t=r(4209),n=r(60297),i=r(18542),d=r(87272),c=r(60636);let o=()=>{let{user:e,isClient:s,isCraftsman:r,isAdmin:o}=(0,c.A)(),[x,m]=(0,l.useState)("personal"),[u,p]=(0,l.useState)(!1),[h,b]=(0,l.useState)({name:e?.name||"",email:e?.email||"",phone:"",address:"",bio:"",skills:"",experience:"",hourlyRate:""}),g=e=>{let{name:s,value:r}=e.target;b(e=>({...e,[s]:r}))},f=[{id:"personal",label:"المعلومات الشخصية",icon:"\uD83D\uDC64"},{id:"security",label:"الأمان",icon:"\uD83D\uDD12"},{id:"notifications",label:"الإشعارات",icon:"\uD83D\uDD14"},...r?[{id:"portfolio",label:"معرض الأعمال",icon:"\uD83D\uDDBC️"}]:[],...o?[{id:"admin",label:"إعدادات الإدارة",icon:"⚙️"}]:[]];return(0,a.jsx)(n.A,{children:(0,a.jsx)(t.default,{children:(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-5",children:[(0,a.jsx)("div",{className:"absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl"}),(0,a.jsx)("div",{className:"absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl"}),(0,a.jsx)("div",{className:"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl"})]}),(0,a.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,a.jsx)("div",{className:"w-full h-full",style:{backgroundImage:"radial-gradient(circle, #567C8D 1px, transparent 1px)",backgroundSize:"50px 50px"}})}),(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8 relative z-10",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl md:text-4xl font-bold text-navy mb-2",children:"الملف الشخصي"}),(0,a.jsx)("p",{className:"text-gray-600",children:"إدارة معلوماتك الشخصية وإعدادات حسابك"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)(i.Zp,{className:"border-0 bg-white/90 backdrop-blur-md shadow-lg",children:(0,a.jsxs)(i.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("div",{className:"w-24 h-24 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white text-4xl mx-auto mb-4",children:(e=>{switch(e){case"client":default:return"\uD83D\uDC64";case"craftsman":return"\uD83D\uDC68‍\uD83D\uDD27";case"admin":return"\uD83D\uDC51"}})(e?.role||"")}),(0,a.jsx)("h3",{className:"text-xl font-bold text-navy",children:e?.name||"مستخدم"}),(0,a.jsx)("p",{className:"text-gray-600",children:(e=>{switch(e){case"client":return"عميل";case"craftsman":return"حرفي";case"admin":return"مدير";default:return"مستخدم"}})(e?.role||"")}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e?.email})]}),(0,a.jsx)("nav",{className:"space-y-2",children:f.map(e=>(0,a.jsxs)("button",{onClick:()=>m(e.id),className:`w-full flex items-center space-x-3 space-x-reverse px-4 py-3 rounded-lg text-right transition-colors duration-200 ${x===e.id?"bg-navy text-white":"text-gray-700 hover:bg-gray-100"}`,children:[(0,a.jsx)("span",{className:"text-lg",children:e.icon}),(0,a.jsx)("span",{children:e.label})]},e.id))})]})})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsx)(i.Zp,{className:"border-0 bg-white/90 backdrop-blur-md shadow-lg",children:(0,a.jsxs)(i.Wu,{className:"p-8",children:["personal"===x&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-navy",children:"المعلومات الشخصية"}),(0,a.jsx)(d.Button,{onClick:()=>p(!u),variant:u?"outline":"default",className:u?"border-gray-300 text-gray-700":"bg-gradient-to-r from-navy to-teal",children:u?"إلغاء":"تعديل"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"الاسم الكامل"}),u?(0,a.jsx)("input",{type:"text",name:"name",value:h.name,onChange:g,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal"}):(0,a.jsx)("p",{className:"px-4 py-3 bg-gray-50 rounded-xl",children:h.name||"غير محدد"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"البريد الإلكتروني"}),u?(0,a.jsx)("input",{type:"email",name:"email",value:h.email,onChange:g,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal"}):(0,a.jsx)("p",{className:"px-4 py-3 bg-gray-50 rounded-xl",children:h.email||"غير محدد"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"رقم الهاتف"}),u?(0,a.jsx)("input",{type:"tel",name:"phone",value:h.phone,onChange:g,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal",placeholder:"+963 xxx xxx xxx"}):(0,a.jsx)("p",{className:"px-4 py-3 bg-gray-50 rounded-xl",children:h.phone||"غير محدد"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"العنوان"}),u?(0,a.jsx)("input",{type:"text",name:"address",value:h.address,onChange:g,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal",placeholder:"المدينة، الحي"}):(0,a.jsx)("p",{className:"px-4 py-3 bg-gray-50 rounded-xl",children:h.address||"غير محدد"})]}),r&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"نبذة عني"}),u?(0,a.jsx)("textarea",{name:"bio",value:h.bio,onChange:g,rows:4,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal",placeholder:"اكتب نبذة مختصرة عن خبرتك ومهاراتك..."}):(0,a.jsx)("p",{className:"px-4 py-3 bg-gray-50 rounded-xl min-h-[100px]",children:h.bio||"غير محدد"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"المهارات"}),u?(0,a.jsx)("input",{type:"text",name:"skills",value:h.skills,onChange:g,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal",placeholder:"نجارة، سباكة، كهرباء..."}):(0,a.jsx)("p",{className:"px-4 py-3 bg-gray-50 rounded-xl",children:h.skills||"غير محدد"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"سنوات الخبرة"}),u?(0,a.jsx)("input",{type:"text",name:"experience",value:h.experience,onChange:g,className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal",placeholder:"5 سنوات"}):(0,a.jsx)("p",{className:"px-4 py-3 bg-gray-50 rounded-xl",children:h.experience||"غير محدد"})]})]})]}),u&&(0,a.jsxs)("div",{className:"mt-8 flex justify-end space-x-4 space-x-reverse",children:[(0,a.jsx)(d.Button,{onClick:()=>p(!1),variant:"outline",className:"border-gray-300 text-gray-700",children:"إلغاء"}),(0,a.jsx)(d.Button,{onClick:()=>{console.log("Saving profile data:",h),p(!1)},className:"bg-gradient-to-r from-navy to-teal",children:"حفظ التغييرات"})]})]}),"security"===x&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-navy mb-6",children:"إعدادات الأمان"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(i.Zp,{className:"border border-gray-200",children:(0,a.jsxs)(i.Wu,{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-navy mb-4",children:"تغيير كلمة المرور"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"كلمة المرور الحالية"}),(0,a.jsx)("input",{type:"password",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"كلمة المرور الجديدة"}),(0,a.jsx)("input",{type:"password",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-gray-700 font-medium mb-2",children:"تأكيد كلمة المرور الجديدة"}),(0,a.jsx)("input",{type:"password",className:"w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal/20 focus:border-teal"})]}),(0,a.jsx)(d.Button,{className:"bg-gradient-to-r from-navy to-teal",children:"تحديث كلمة المرور"})]})]})}),(0,a.jsx)(i.Zp,{className:"border border-gray-200",children:(0,a.jsxs)(i.Wu,{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-navy mb-4",children:"المصادقة الثنائية"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"أضف طبقة حماية إضافية لحسابك عن طريق تفعيل المصادقة الثنائية"}),(0,a.jsx)(d.Button,{variant:"outline",className:"border-navy text-navy",children:"تفعيل المصادقة الثنائية"})]})})]})]}),"notifications"===x&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-navy mb-6",children:"إعدادات الإشعارات"}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(i.Zp,{className:"border border-gray-200",children:(0,a.jsxs)(i.Wu,{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-navy mb-4",children:"إشعارات البريد الإلكتروني"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)("input",{type:"checkbox",className:"rounded border-gray-300",defaultChecked:!0}),(0,a.jsx)("span",{children:"عروض جديدة على مشاريعي"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)("input",{type:"checkbox",className:"rounded border-gray-300",defaultChecked:!0}),(0,a.jsx)("span",{children:"تحديثات حالة المشروع"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)("input",{type:"checkbox",className:"rounded border-gray-300"}),(0,a.jsx)("span",{children:"رسائل جديدة"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)("input",{type:"checkbox",className:"rounded border-gray-300"}),(0,a.jsx)("span",{children:"تذكيرات المواعيد"})]})]})]})}),(0,a.jsx)(i.Zp,{className:"border border-gray-200",children:(0,a.jsxs)(i.Wu,{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-navy mb-4",children:"إشعارات المتصفح"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)("input",{type:"checkbox",className:"rounded border-gray-300",defaultChecked:!0}),(0,a.jsx)("span",{children:"إشعارات فورية"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-3 space-x-reverse",children:[(0,a.jsx)("input",{type:"checkbox",className:"rounded border-gray-300"}),(0,a.jsx)("span",{children:"أصوات الإشعارات"})]})]})]})}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(d.Button,{className:"bg-gradient-to-r from-navy to-teal",children:"حفظ الإعدادات"})})]})]}),"portfolio"===x&&r&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-navy mb-6",children:"معرض الأعمال"}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsx)(d.Button,{className:"bg-gradient-to-r from-navy to-teal",children:"إضافة عمل جديد"})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:(0,a.jsx)(i.Zp,{className:"border border-gray-200",children:(0,a.jsxs)(i.Wu,{className:"p-4",children:[(0,a.jsx)("div",{className:"aspect-video bg-gray-200 rounded-lg mb-4 flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-gray-500",children:"صورة العمل"})}),(0,a.jsx)("h4",{className:"font-semibold text-navy mb-2",children:"مشروع تجديد مطبخ"}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:"تجديد كامل لمطبخ عائلي مع خزائن حديثة"}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-teal font-medium",children:"مكتمل"}),(0,a.jsx)(d.Button,{size:"sm",variant:"outline",children:"تعديل"})]})]})})})]})]})})})]})]})]})})})}},60297:(e,s,r)=>{"use strict";r.d(s,{A:()=>c});var a=r(45512),l=r(60636),t=r(18542),n=r(87272),i=r(28531),d=r.n(i);function c({children:e,requiredRoles:s,fallback:r}){let{isAuthenticated:i,isLoading:c,canAccess:o,user:x}=(0,l.A)();return c?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white flex items-center justify-center",children:(0,a.jsx)(t.Zp,{className:"border-0 bg-white/90 backdrop-blur-md shadow-lg",children:(0,a.jsxs)(t.Wu,{className:"p-12 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-16 w-16 border-b-2 border-navy mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600 text-lg",children:"جاري التحميل..."})]})})}):i?s&&!o(s)?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white flex items-center justify-center",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsx)(t.Zp,{className:"max-w-md mx-auto border-0 bg-white/90 backdrop-blur-md shadow-lg",children:(0,a.jsxs)(t.Wu,{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"text-6xl mb-6",children:"⛔"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-navy mb-4",children:"غير مصرح لك"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"ليس لديك الصلاحية للوصول إلى هذه الصفحة"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(d(),{href:"/dashboard",children:(0,a.jsx)(n.Button,{className:"w-full bg-gradient-to-r from-navy to-teal",children:"العودة للوحة التحكم"})}),(0,a.jsx)(d(),{href:"/",children:(0,a.jsx)(n.Button,{variant:"outline",className:"w-full border-navy text-navy",children:"العودة للصفحة الرئيسية"})})]}),(0,a.jsxs)("div",{className:"mt-6 text-sm text-gray-500",children:["مسجل كـ: ",x?.role==="client"?"عميل":x?.role==="craftsman"?"حرفي":"مدير"]})]})})})}):(0,a.jsx)(a.Fragment,{children:e}):r||(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white flex items-center justify-center",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsx)(t.Zp,{className:"max-w-md mx-auto border-0 bg-white/90 backdrop-blur-md shadow-lg",children:(0,a.jsxs)(t.Wu,{className:"p-8 text-center",children:[(0,a.jsx)("div",{className:"text-6xl mb-6",children:"\uD83D\uDD12"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-navy mb-4",children:"تسجيل الدخول مطلوب"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"يجب تسجيل الدخول للوصول إلى هذه الصفحة"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)(d(),{href:"/login",children:(0,a.jsx)(n.Button,{className:"w-full bg-gradient-to-r from-navy to-teal",children:"تسجيل الدخول"})}),(0,a.jsx)(d(),{href:"/register",children:(0,a.jsx)(n.Button,{variant:"outline",className:"w-full border-navy text-navy",children:"إنشاء حساب جديد"})})]})]})})})})}},11891:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(46760).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\g\\\\Duzan Website\\\\frontend\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\g\\Duzan Website\\frontend\\src\\app\\profile\\page.tsx","default")}};var s=require("../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[638,3351,7180,1057,4209],()=>r(56953));module.exports=a})();