'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '../ui/Button';

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  return (
    <header className="bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="group flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300">
                🔨
              </div>
              <span className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-navy to-teal">
                دوزان
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:block">
            <ul className="flex space-x-8 space-x-reverse">
              <li>
                <Link href="/" className="relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group">
                  الرئيسية
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300"></span>
                </Link>
              </li>
              <li>
                <Link href="/jobs" className="relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group">
                  المشاريع
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300"></span>
                </Link>
              </li>
              <li>
                <Link href="/craftsmen" className="relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group">
                  الحرفيون
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300"></span>
                </Link>
              </li>
              <li>
                <Link href="/about" className="relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group">
                  من نحن
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300"></span>
                </Link>
              </li>
              <li>
                <Link href="/faq" className="relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group">
                  الأسئلة الشائعة
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300"></span>
                </Link>
              </li>
              <li>
                <Link href="/contact" className="relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group">
                  اتصل بنا
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300"></span>
                </Link>
              </li>
            </ul>
          </nav>

          {/* Desktop Buttons */}
          <div className="hidden lg:flex items-center space-x-4 space-x-reverse">
            <Link href="/login">
              <Button variant="outline" size="sm" className="border-navy text-navy hover:bg-navy hover:text-white transition-all duration-300">
                تسجيل الدخول
              </Button>
            </Link>
            <Link href="/register">
              <Button size="sm" className="bg-gradient-to-r from-navy to-teal hover:from-teal hover:to-navy text-white shadow-lg hover:shadow-xl transition-all duration-300">
                إنشاء حساب
              </Button>
            </Link>
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300"
            aria-label="فتح القائمة"
          >
            <svg className="w-6 h-6 text-navy" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {isMobileMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden mt-4 py-4 border-t border-gray-200">
            <nav className="space-y-4">
              <Link href="/" className="block text-gray-700 hover:text-navy font-medium transition-colors duration-300">
                الرئيسية
              </Link>
              <Link href="/jobs" className="block text-gray-700 hover:text-navy font-medium transition-colors duration-300">
                المشاريع
              </Link>
              <Link href="/craftsmen" className="block text-gray-700 hover:text-navy font-medium transition-colors duration-300">
                الحرفيون
              </Link>
              <Link href="/about" className="block text-gray-700 hover:text-navy font-medium transition-colors duration-300">
                من نحن
              </Link>
              <Link href="/faq" className="block text-gray-700 hover:text-navy font-medium transition-colors duration-300">
                الأسئلة الشائعة
              </Link>
              <Link href="/contact" className="block text-gray-700 hover:text-navy font-medium transition-colors duration-300">
                اتصل بنا
              </Link>
            </nav>
            <div className="mt-6 space-y-3">
              <Link href="/login" className="block">
                <Button variant="outline" className="w-full border-navy text-navy hover:bg-navy hover:text-white">
                  تسجيل الدخول
                </Button>
              </Link>
              <Link href="/register" className="block">
                <Button className="w-full bg-gradient-to-r from-navy to-teal text-white">
                  إنشاء حساب
                </Button>
              </Link>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
