'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { Button } from '../ui/Button';
import AuthButton from '../auth/AuthButton';
import NotificationBell from '../notifications/NotificationBell';

const Header = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { data: session } = useSession();

  // بيانات تجريبية للإشعارات
  const mockNotifications = [
    {
      id: '1',
      title: 'عرض جديد',
      message: 'تم تقديم عرض جديد على مشروع تجديد المطبخ',
      type: 'offer' as const,
      read: false,
      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
      actionUrl: '/client/offers'
    },
    {
      id: '2',
      title: 'رسالة جديدة',
      message: 'رسالة جديدة من محمد النجار',
      type: 'message' as const,
      read: false,
      timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
      actionUrl: '/messages'
    },
    {
      id: '3',
      title: 'تقييم جديد',
      message: 'تم تقييم عملك بـ 5 نجوم',
      type: 'review' as const,
      read: true,
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
      actionUrl: '/craftsman/reviews'
    }
  ];

  const handleNotificationClick = (notification: any) => {
    if (notification.actionUrl) {
      window.location.href = notification.actionUrl;
    }
  };

  const handleMarkAsRead = (notificationId: string) => {
    // TODO: تنفيذ تحديث حالة الإشعار في قاعدة البيانات
    console.log('Mark as read:', notificationId);
  };

  const handleMarkAllAsRead = () => {
    // TODO: تنفيذ تحديد جميع الإشعارات كمقروءة
    console.log('Mark all as read');
  };

  return (
    <header className="bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50">
      <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="group flex items-center space-x-3 space-x-reverse">
              <div className="w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300">
                🔨
              </div>
              <span className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-navy to-teal">
                دوزان
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:block">
            <ul className="flex space-x-8 space-x-reverse">
              <li>
                <Link href="/" className="relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group">
                  الرئيسية
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300"></span>
                </Link>
              </li>
              <li>
                <Link href="/jobs" className="relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group">
                  المشاريع
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300"></span>
                </Link>
              </li>
              <li>
                <Link href="/craftsmen" className="relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group">
                  الحرفيون
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300"></span>
                </Link>
              </li>
              <li>
                <Link href="/about" className="relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group">
                  من نحن
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300"></span>
                </Link>
              </li>
              <li>
                <Link href="/faq" className="relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group">
                  الأسئلة الشائعة
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300"></span>
                </Link>
              </li>
              <li>
                <Link href="/contact" className="relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group">
                  اتصل بنا
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300"></span>
                </Link>
              </li>
            </ul>
          </nav>

          {/* Desktop Auth */}
          <div className="hidden lg:flex items-center space-x-4 space-x-reverse">
            {session && (
              <>
                <Link href="/messages">
                  <Button variant="outline" size="sm" className="relative">
                    💬
                    <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
                      2
                    </span>
                  </Button>
                </Link>
                <NotificationBell
                  notifications={mockNotifications}
                  onMarkAsRead={handleMarkAsRead}
                  onMarkAllAsRead={handleMarkAllAsRead}
                  onNotificationClick={handleNotificationClick}
                />
              </>
            )}
            <AuthButton />
          </div>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300"
            aria-label="فتح القائمة"
          >
            <svg className="w-6 h-6 text-navy" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {isMobileMenuOpen ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              )}
            </svg>
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div className="lg:hidden mt-4 py-4 border-t border-gray-200">
            <nav className="space-y-4">
              <Link href="/" className="block text-gray-700 hover:text-navy font-medium transition-colors duration-300">
                الرئيسية
              </Link>
              <Link href="/jobs" className="block text-gray-700 hover:text-navy font-medium transition-colors duration-300">
                المشاريع
              </Link>
              <Link href="/craftsmen" className="block text-gray-700 hover:text-navy font-medium transition-colors duration-300">
                الحرفيون
              </Link>
              <Link href="/about" className="block text-gray-700 hover:text-navy font-medium transition-colors duration-300">
                من نحن
              </Link>
              <Link href="/faq" className="block text-gray-700 hover:text-navy font-medium transition-colors duration-300">
                الأسئلة الشائعة
              </Link>
              <Link href="/contact" className="block text-gray-700 hover:text-navy font-medium transition-colors duration-300">
                اتصل بنا
              </Link>
            </nav>
            <div className="mt-6">
              <AuthButton />
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
