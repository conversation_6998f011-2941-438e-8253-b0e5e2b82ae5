{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/hooks/useAuth.ts"], "sourcesContent": ["'use client';\n\nimport { useSession, signIn, signOut } from 'next-auth/react';\nimport { useRouter } from 'next/navigation';\nimport { useState } from 'react';\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  name: string;\n  email: string;\n  password: string;\n  role: 'client' | 'craftsman';\n  phone?: string;\n}\n\nexport const useAuth = () => {\n  const { data: session, status } = useSession();\n  const router = useRouter();\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n\n  const login = async (credentials: LoginCredentials) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const result = await signIn('credentials', {\n        email: credentials.email,\n        password: credentials.password,\n        redirect: false,\n      });\n\n      if (result?.error) {\n        setError('البريد الإلكتروني أو كلمة المرور غير صحيحة');\n        return false;\n      }\n\n      if (result?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const register = async (data: RegisterData) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(data),\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json();\n        setError(errorData.message || 'حدث خطأ أثناء إنشاء الحساب');\n        return false;\n      }\n\n      // تسجيل الدخول تلقائياً بعد التسجيل\n      const loginResult = await signIn('credentials', {\n        email: data.email,\n        password: data.password,\n        redirect: false,\n      });\n\n      if (loginResult?.ok) {\n        router.push('/dashboard');\n        return true;\n      }\n\n      return false;\n    } catch (error) {\n      setError('حدث خطأ أثناء إنشاء الحساب');\n      return false;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    setIsLoading(true);\n    try {\n      await signOut({ redirect: false });\n      router.push('/');\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithGoogle = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('google', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Google');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const loginWithFacebook = async () => {\n    setIsLoading(true);\n    try {\n      await signIn('facebook', { callbackUrl: '/dashboard' });\n    } catch (error) {\n      setError('حدث خطأ أثناء تسجيل الدخول بـ Facebook');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const isAuthenticated = status === 'authenticated';\n  const isLoading_session = status === 'loading';\n  const user = session?.user;\n\n  // التحقق من الأدوار مع fallback\n  const isClient = user?.role === 'client';\n  const isCraftsman = user?.role === 'craftsman';\n  const isAdmin = user?.role === 'admin';\n\n  // التحقق من الصلاحيات\n  const hasRole = (role: string | string[]) => {\n    if (!user) return false;\n    if (Array.isArray(role)) {\n      return role.includes(user.role);\n    }\n    return user.role === role;\n  };\n\n  const canAccess = (requiredRoles: string | string[]) => {\n    return isAuthenticated && hasRole(requiredRoles);\n  };\n\n  return {\n    // Session data\n    user,\n    session,\n    isAuthenticated,\n    isLoading: isLoading || isLoading_session,\n    error,\n\n    // Role checks\n    isClient,\n    isCraftsman,\n    isAdmin,\n    hasRole,\n    canAccess,\n\n    // Auth methods\n    login,\n    register,\n    logout,\n    loginWithGoogle,\n    loginWithFacebook,\n\n    // Utility\n    setError,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;AACA;AACA;;AAJA;;;;AAmBO,MAAM,UAAU;;IACrB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,QAAQ,OAAO;QACnB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC,OAAO,YAAY,KAAK;gBACxB,UAAU,YAAY,QAAQ;gBAC9B,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;gBACT,OAAO;YACT;YAEA,IAAI,QAAQ,IAAI;gBACd,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,OAAO,IAAI;gBAC9B,OAAO;YACT;YAEA,oCAAoC;YACpC,MAAM,cAAc,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBAC9C,OAAO,KAAK,KAAK;gBACjB,UAAU,KAAK,QAAQ;gBACvB,UAAU;YACZ;YAEA,IAAI,aAAa,IAAI;gBACnB,OAAO,IAAI,CAAC;gBACZ,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,SAAS;YACT,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE;gBAAE,UAAU;YAAM;YAChC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;gBAAE,aAAa;YAAa;QACrD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,aAAa;QACb,IAAI;YACF,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,YAAY;gBAAE,aAAa;YAAa;QACvD,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,WAAW;IACnC,MAAM,oBAAoB,WAAW;IACrC,MAAM,OAAO,SAAS;IAEtB,gCAAgC;IAChC,MAAM,WAAW,MAAM,SAAS;IAChC,MAAM,cAAc,MAAM,SAAS;IACnC,MAAM,UAAU,MAAM,SAAS;IAE/B,sBAAsB;IACtB,MAAM,UAAU,CAAC;QACf,IAAI,CAAC,MAAM,OAAO;QAClB,IAAI,MAAM,OAAO,CAAC,OAAO;YACvB,OAAO,KAAK,QAAQ,CAAC,KAAK,IAAI;QAChC;QACA,OAAO,KAAK,IAAI,KAAK;IACvB;IAEA,MAAM,YAAY,CAAC;QACjB,OAAO,mBAAmB,QAAQ;IACpC;IAEA,OAAO;QACL,eAAe;QACf;QACA;QACA;QACA,WAAW,aAAa;QACxB;QAEA,cAAc;QACd;QACA;QACA;QACA;QACA;QAEA,eAAe;QACf;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;IACF;AACF;GA7Ja;;QACuB,iJAAA,CAAA,aAAU;QAC7B,qIAAA,CAAA,YAAS"}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 176, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/auth/AuthButton.tsx"], "sourcesContent": ["'use client';\n\nimport { useAuth } from '@/hooks/useAuth';\nimport { Button } from '@/components/ui/Button';\nimport Link from 'next/link';\nimport { useState } from 'react';\n\nexport default function AuthButton() {\n  const { isAuthenticated, user, logout, isLoading } = useAuth();\n  const [isDropdownOpen, setIsDropdownOpen] = useState(false);\n\n  if (isLoading) {\n    return (\n      <div className=\"animate-pulse\">\n        <div className=\"h-10 w-24 bg-gray-200 rounded\"></div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return (\n      <div className=\"flex items-center space-x-3 space-x-reverse\">\n        <Link href=\"/login\">\n          <Button variant=\"outline\" size=\"sm\" className=\"border-navy text-navy hover:bg-navy hover:text-white\">\n            تسجيل الدخول\n          </Button>\n        </Link>\n        <Link href=\"/register\">\n          <Button size=\"sm\" className=\"bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90\">\n            إنشاء حساب\n          </Button>\n        </Link>\n      </div>\n    );\n  }\n\n  const getRoleDisplayName = (role: string) => {\n    switch (role) {\n      case 'client':\n        return 'عميل';\n      case 'craftsman':\n        return 'حرفي';\n      case 'admin':\n        return 'مدير';\n      default:\n        return 'مستخدم';\n    }\n  };\n\n  const getRoleIcon = (role: string) => {\n    switch (role) {\n      case 'client':\n        return '👤';\n      case 'craftsman':\n        return '👨‍🔧';\n      case 'admin':\n        return '👑';\n      default:\n        return '👤';\n    }\n  };\n\n  return (\n    <div className=\"relative\">\n      <button\n        onClick={() => setIsDropdownOpen(!isDropdownOpen)}\n        className=\"flex items-center space-x-2 space-x-reverse bg-white border border-gray-200 rounded-lg px-4 py-2 hover:bg-gray-50 transition-colors duration-200\"\n      >\n        <div className=\"text-lg\">{getRoleIcon(user?.role || '')}</div>\n        <div className=\"text-right\">\n          <div className=\"text-sm font-medium text-navy\">{user?.name || 'مستخدم'}</div>\n          <div className=\"text-xs text-gray-500\">{getRoleDisplayName(user?.role || '')}</div>\n        </div>\n        <svg\n          className={`w-4 h-4 text-gray-500 transition-transform duration-200 ${\n            isDropdownOpen ? 'rotate-180' : ''\n          }`}\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n        </svg>\n      </button>\n\n      {isDropdownOpen && (\n        <>\n          {/* Backdrop */}\n          <div\n            className=\"fixed inset-0 z-10\"\n            onClick={() => setIsDropdownOpen(false)}\n          />\n\n          {/* Dropdown Menu */}\n          <div className=\"absolute left-0 mt-2 w-64 bg-white border border-gray-200 rounded-lg shadow-lg z-20\">\n            <div className=\"p-4 border-b border-gray-100\">\n              <div className=\"flex items-center space-x-3 space-x-reverse\">\n                <div className=\"text-2xl\">{getRoleIcon(user?.role || '')}</div>\n                <div>\n                  <div className=\"font-medium text-navy\">{user?.name || 'مستخدم'}</div>\n                  <div className=\"text-sm text-gray-500\">{user?.email || 'بريد إلكتروني'}</div>\n                  <div className=\"text-xs text-teal font-medium\">\n                    {getRoleDisplayName(user?.role || '')}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"py-2\">\n              <Link\n                href=\"/dashboard\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>📊</span>\n                  <span>لوحة التحكم</span>\n                </div>\n              </Link>\n\n              <Link\n                href=\"/profile\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>⚙️</span>\n                  <span>الملف الشخصي</span>\n                </div>\n              </Link>\n\n              {user?.role === 'client' && (\n                <Link\n                  href=\"/projects/create\"\n                  className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                  onClick={() => setIsDropdownOpen(false)}\n                >\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span>➕</span>\n                    <span>إنشاء مشروع</span>\n                  </div>\n                </Link>\n              )}\n\n              {user?.role === 'craftsman' && (\n                <Link\n                  href=\"/offers\"\n                  className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                  onClick={() => setIsDropdownOpen(false)}\n                >\n                  <div className=\"flex items-center space-x-2 space-x-reverse\">\n                    <span>💼</span>\n                    <span>عروضي</span>\n                  </div>\n                </Link>\n              )}\n\n              <Link\n                href=\"/messages\"\n                className=\"block px-4 py-2 text-gray-700 hover:bg-gray-50 transition-colors duration-200\"\n                onClick={() => setIsDropdownOpen(false)}\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>💬</span>\n                  <span>الرسائل</span>\n                </div>\n              </Link>\n            </div>\n\n            <div className=\"border-t border-gray-100 py-2\">\n              <button\n                onClick={() => {\n                  logout();\n                  setIsDropdownOpen(false);\n                }}\n                className=\"block w-full text-right px-4 py-2 text-red-600 hover:bg-red-50 transition-colors duration-200\"\n              >\n                <div className=\"flex items-center space-x-2 space-x-reverse\">\n                  <span>🚪</span>\n                  <span>تسجيل الخروج</span>\n                </div>\n              </button>\n            </div>\n          </div>\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,0HAAA,CAAA,UAAO,AAAD;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,IAAI,WAAW;QACb,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;wBAAK,WAAU;kCAAuD;;;;;;;;;;;8BAIvG,6LAAC,+JAAA,CAAA,UAAI;oBAAC,MAAK;8BACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,WAAU;kCAAyE;;;;;;;;;;;;;;;;;IAM7G;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBACC,SAAS,IAAM,kBAAkB,CAAC;gBAClC,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCAAW,YAAY,MAAM,QAAQ;;;;;;kCACpD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAiC,MAAM,QAAQ;;;;;;0CAC9D,6LAAC;gCAAI,WAAU;0CAAyB,mBAAmB,MAAM,QAAQ;;;;;;;;;;;;kCAE3E,6LAAC;wBACC,WAAW,CAAC,wDAAwD,EAClE,iBAAiB,eAAe,IAChC;wBACF,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;YAIxE,gCACC;;kCAEE,6LAAC;wBACC,WAAU;wBACV,SAAS,IAAM,kBAAkB;;;;;;kCAInC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAY,YAAY,MAAM,QAAQ;;;;;;sDACrD,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAyB,MAAM,QAAQ;;;;;;8DACtD,6LAAC;oDAAI,WAAU;8DAAyB,MAAM,SAAS;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DACZ,mBAAmB,MAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAM1C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAIV,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;oCAIT,MAAM,SAAS,0BACd,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;oCAKX,MAAM,SAAS,6BACd,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAKZ,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAK;;;;;;8DACN,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;0CAKZ,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,SAAS;wCACP;wCACA,kBAAkB;oCACpB;oCACA,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxB;GArLwB;;QAC+B,0HAAA,CAAA,UAAO;;;KADtC"}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 656, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/notifications/NotificationBell.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Button } from '@/components/ui/Button';\n\ninterface Notification {\n  id: string;\n  title: string;\n  message: string;\n  type: 'message' | 'offer' | 'project' | 'review' | 'system';\n  read: boolean;\n  timestamp: string;\n  actionUrl?: string;\n}\n\ninterface NotificationBellProps {\n  notifications: Notification[];\n  onMarkAsRead: (notificationId: string) => void;\n  onMarkAllAsRead: () => void;\n  onNotificationClick: (notification: Notification) => void;\n}\n\nconst NotificationBell: React.FC<NotificationBellProps> = ({\n  notifications,\n  onMarkAsRead,\n  onMarkAllAsRead,\n  onNotificationClick\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  const unreadCount = notifications.filter(n => !n.read).length;\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'message': return '💬';\n      case 'offer': return '💰';\n      case 'project': return '🏗️';\n      case 'review': return '⭐';\n      case 'system': return '🔔';\n      default: return '📢';\n    }\n  };\n\n  const getNotificationColor = (type: string) => {\n    switch (type) {\n      case 'message': return 'text-blue-600';\n      case 'offer': return 'text-green-600';\n      case 'project': return 'text-purple-600';\n      case 'review': return 'text-yellow-600';\n      case 'system': return 'text-gray-600';\n      default: return 'text-gray-600';\n    }\n  };\n\n  const formatTime = (timestamp: string) => {\n    const now = new Date();\n    const notificationTime = new Date(timestamp);\n    const diffInMinutes = (now.getTime() - notificationTime.getTime()) / (1000 * 60);\n\n    if (diffInMinutes < 1) {\n      return 'الآن';\n    } else if (diffInMinutes < 60) {\n      return `منذ ${Math.floor(diffInMinutes)} دقيقة`;\n    } else if (diffInMinutes < 1440) {\n      return `منذ ${Math.floor(diffInMinutes / 60)} ساعة`;\n    } else {\n      return `منذ ${Math.floor(diffInMinutes / 1440)} يوم`;\n    }\n  };\n\n  const handleNotificationClick = (notification: Notification) => {\n    if (!notification.read) {\n      onMarkAsRead(notification.id);\n    }\n    onNotificationClick(notification);\n    setIsOpen(false);\n  };\n\n  return (\n    <div className=\"relative\" ref={dropdownRef}>\n      {/* Bell Button */}\n      <Button\n        variant=\"outline\"\n        size=\"sm\"\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"relative p-2\"\n      >\n        <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\" />\n        </svg>\n        \n        {unreadCount > 0 && (\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n            {unreadCount > 99 ? '99+' : unreadCount}\n          </span>\n        )}\n      </Button>\n\n      {/* Dropdown */}\n      {isOpen && (\n        <div className=\"absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\">\n          {/* Header */}\n          <div className=\"p-4 border-b border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <h3 className=\"font-semibold text-gray-900\">الإشعارات</h3>\n              {unreadCount > 0 && (\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={onMarkAllAsRead}\n                  className=\"text-xs\"\n                >\n                  تحديد الكل كمقروء\n                </Button>\n              )}\n            </div>\n          </div>\n\n          {/* Notifications List */}\n          <div className=\"max-h-96 overflow-y-auto\">\n            {notifications.length > 0 ? (\n              notifications.slice(0, 10).map((notification) => (\n                <div\n                  key={notification.id}\n                  onClick={() => handleNotificationClick(notification)}\n                  className={`p-4 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors ${\n                    !notification.read ? 'bg-blue-50' : ''\n                  }`}\n                >\n                  <div className=\"flex items-start space-x-3 space-x-reverse\">\n                    <div className={`text-lg ${getNotificationColor(notification.type)}`}>\n                      {getNotificationIcon(notification.type)}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"flex items-center justify-between mb-1\">\n                        <h4 className={`text-sm font-medium ${\n                          !notification.read ? 'text-gray-900' : 'text-gray-700'\n                        }`}>\n                          {notification.title}\n                        </h4>\n                        {!notification.read && (\n                          <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n                        )}\n                      </div>\n                      <p className=\"text-sm text-gray-600 line-clamp-2\">\n                        {notification.message}\n                      </p>\n                      <p className=\"text-xs text-gray-500 mt-1\">\n                        {formatTime(notification.timestamp)}\n                      </p>\n                    </div>\n                  </div>\n                </div>\n              ))\n            ) : (\n              <div className=\"p-8 text-center\">\n                <div className=\"text-4xl mb-2\">🔔</div>\n                <p className=\"text-gray-500\">لا توجد إشعارات</p>\n              </div>\n            )}\n          </div>\n\n          {/* Footer */}\n          {notifications.length > 10 && (\n            <div className=\"p-3 border-t border-gray-200 text-center\">\n              <Button variant=\"outline\" size=\"sm\" className=\"w-full\">\n                عرض جميع الإشعارات\n              </Button>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default NotificationBell;\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAsBA,MAAM,mBAAoD,CAAC,EACzD,aAAa,EACb,YAAY,EACZ,eAAe,EACf,mBAAmB,EACpB;;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,MAAM,cAAc,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,EAAE,MAAM;IAE7D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM;iEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,UAAU;oBACZ;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;8CAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;;QACzD;qCAAG,EAAE;IAEL,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,MAAM,IAAI;QAChB,MAAM,mBAAmB,IAAI,KAAK;QAClC,MAAM,gBAAgB,CAAC,IAAI,OAAO,KAAK,iBAAiB,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE/E,IAAI,gBAAgB,GAAG;YACrB,OAAO;QACT,OAAO,IAAI,gBAAgB,IAAI;YAC7B,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,eAAe,MAAM,CAAC;QACjD,OAAO,IAAI,gBAAgB,MAAM;YAC/B,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;QACrD,OAAO;YACL,OAAO,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,gBAAgB,MAAM,IAAI,CAAC;QACtD;IACF;IAEA,MAAM,0BAA0B,CAAC;QAC/B,IAAI,CAAC,aAAa,IAAI,EAAE;YACtB,aAAa,aAAa,EAAE;QAC9B;QACA,oBAAoB;QACpB,UAAU;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;QAAW,KAAK;;0BAE7B,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAQ;gBACR,MAAK;gBACL,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;;kCAEV,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAGtE,cAAc,mBACb,6LAAC;wBAAK,WAAU;kCACb,cAAc,KAAK,QAAQ;;;;;;;;;;;;YAMjC,wBACC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA8B;;;;;;gCAC3C,cAAc,mBACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;kCAQP,6LAAC;wBAAI,WAAU;kCACZ,cAAc,MAAM,GAAG,IACtB,cAAc,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,6BAC9B,6LAAC;gCAEC,SAAS,IAAM,wBAAwB;gCACvC,WAAW,CAAC,+EAA+E,EACzF,CAAC,aAAa,IAAI,GAAG,eAAe,IACpC;0CAEF,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAW,CAAC,QAAQ,EAAE,qBAAqB,aAAa,IAAI,GAAG;sDACjE,oBAAoB,aAAa,IAAI;;;;;;sDAExC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAW,CAAC,oBAAoB,EAClC,CAAC,aAAa,IAAI,GAAG,kBAAkB,iBACvC;sEACC,aAAa,KAAK;;;;;;wDAEpB,CAAC,aAAa,IAAI,kBACjB,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAGnB,6LAAC;oDAAE,WAAU;8DACV,aAAa,OAAO;;;;;;8DAEvB,6LAAC;oDAAE,WAAU;8DACV,WAAW,aAAa,SAAS;;;;;;;;;;;;;;;;;;+BAzBnC,aAAa,EAAE;;;;sDAgCxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;oBAMlC,cAAc,MAAM,GAAG,oBACtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAK,WAAU;sCAAS;;;;;;;;;;;;;;;;;;;;;;;AASrE;GApKM;KAAA;uCAsKS"}}, {"offset": {"line": 965, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useSession } from 'next-auth/react';\nimport { Button } from '../ui/Button';\nimport AuthButton from '../auth/AuthButton';\nimport NotificationBell from '../notifications/NotificationBell';\n\nconst Header = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const { data: session } = useSession();\n\n  // بيانات تجريبية للإشعارات\n  const mockNotifications = [\n    {\n      id: '1',\n      title: 'عرض جديد',\n      message: 'تم تقديم عرض جديد على مشروع تجديد المطبخ',\n      type: 'offer' as const,\n      read: false,\n      timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(),\n      actionUrl: '/client/offers'\n    },\n    {\n      id: '2',\n      title: 'رسالة جديدة',\n      message: 'رسالة جديدة من محمد النجار',\n      type: 'message' as const,\n      read: false,\n      timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(),\n      actionUrl: '/messages'\n    },\n    {\n      id: '3',\n      title: 'تقييم جديد',\n      message: 'تم تقييم عملك بـ 5 نجوم',\n      type: 'review' as const,\n      read: true,\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),\n      actionUrl: '/craftsman/reviews'\n    }\n  ];\n\n  const handleNotificationClick = (notification: any) => {\n    if (notification.actionUrl) {\n      window.location.href = notification.actionUrl;\n    }\n  };\n\n  const handleMarkAsRead = (notificationId: string) => {\n    // TODO: تنفيذ تحديث حالة الإشعار في قاعدة البيانات\n    console.log('Mark as read:', notificationId);\n  };\n\n  const handleMarkAllAsRead = () => {\n    // TODO: تنفيذ تحديد جميع الإشعارات كمقروءة\n    console.log('Mark all as read');\n  };\n\n  return (\n    <header className=\"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 sticky top-0 z-50\">\n      <div className=\"container mx-auto px-4 py-4\">\n        <div className=\"flex justify-between items-center\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"group flex items-center space-x-3 space-x-reverse\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-navy to-teal rounded-xl flex items-center justify-center text-white text-xl font-bold shadow-lg group-hover:scale-110 transition-transform duration-300\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-navy to-teal\">\n                دوزان\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:block\">\n            <ul className=\"flex space-x-8 space-x-reverse\">\n              <li>\n                <Link href=\"/\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الرئيسية\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  المشاريع\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الحرفيون\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  من نحن\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  الأسئلة الشائعة\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"relative text-gray-700 hover:text-navy font-medium transition-colors duration-300 group\">\n                  اتصل بنا\n                  <span className=\"absolute bottom-0 left-0 w-0 h-0.5 bg-gradient-to-r from-navy to-teal group-hover:w-full transition-all duration-300\"></span>\n                </Link>\n              </li>\n            </ul>\n          </nav>\n\n          {/* Desktop Auth */}\n          <div className=\"hidden lg:flex items-center space-x-4 space-x-reverse\">\n            {session && (\n              <>\n                <Link href=\"/messages\">\n                  <Button variant=\"outline\" size=\"sm\" className=\"relative\">\n                    💬\n                    <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center\">\n                      2\n                    </span>\n                  </Button>\n                </Link>\n                <NotificationBell\n                  notifications={mockNotifications}\n                  onMarkAsRead={handleMarkAsRead}\n                  onMarkAllAsRead={handleMarkAllAsRead}\n                  onNotificationClick={handleNotificationClick}\n                />\n              </>\n            )}\n            <AuthButton />\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n            className=\"lg:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors duration-300\"\n            aria-label=\"فتح القائمة\"\n          >\n            <svg className=\"w-6 h-6 text-navy\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              {isMobileMenuOpen ? (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              ) : (\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              )}\n            </svg>\n          </button>\n        </div>\n\n        {/* Mobile Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"lg:hidden mt-4 py-4 border-t border-gray-200\">\n            <nav className=\"space-y-4\">\n              <Link href=\"/\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الرئيسية\n              </Link>\n              <Link href=\"/jobs\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                المشاريع\n              </Link>\n              <Link href=\"/craftsmen\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الحرفيون\n              </Link>\n              <Link href=\"/about\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                من نحن\n              </Link>\n              <Link href=\"/faq\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                الأسئلة الشائعة\n              </Link>\n              <Link href=\"/contact\" className=\"block text-gray-700 hover:text-navy font-medium transition-colors duration-300\">\n                اتصل بنا\n              </Link>\n            </nav>\n            <div className=\"mt-6\">\n              <AuthButton />\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASA,MAAM,SAAS;;IACb,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAEnC,2BAA2B;IAC3B,MAAM,oBAAoB;QACxB;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC5D,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,MAAM,WAAW;YAC5D,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,SAAS;YACT,MAAM;YACN,MAAM;YACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI,KAAK,KAAK,MAAM,WAAW;YAChE,WAAW;QACb;KACD;IAED,MAAM,0BAA0B,CAAC;QAC/B,IAAI,aAAa,SAAS,EAAE;YAC1B,OAAO,QAAQ,CAAC,IAAI,GAAG,aAAa,SAAS;QAC/C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,mDAAmD;QACnD,QAAQ,GAAG,CAAC,iBAAiB;IAC/B;IAEA,MAAM,sBAAsB;QAC1B,2CAA2C;QAC3C,QAAQ,GAAG,CAAC;IACd;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDAA2L;;;;;;kDAG1M,6LAAC;wCAAK,WAAU;kDAAsF;;;;;;;;;;;;;;;;;sCAO1G,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;;gDAA0F;8DAEjH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAQ,WAAU;;gDAA0F;8DAErH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAa,WAAU;;gDAA0F;8DAE1H,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;;gDAA0F;8DAEtH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAO,WAAU;;gDAA0F;8DAEpH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;kDAGpB,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;;gDAA0F;8DAExH,6LAAC;oDAAK,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAOxB,6LAAC;4BAAI,WAAU;;gCACZ,yBACC;;sDACE,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,MAAK;gDAAK,WAAU;;oDAAW;kEAEvD,6LAAC;wDAAK,WAAU;kEAA+G;;;;;;;;;;;;;;;;;sDAKnI,6LAAC,0JAAA,CAAA,UAAgB;4CACf,eAAe;4CACf,cAAc;4CACd,iBAAiB;4CACjB,qBAAqB;;;;;;;;8CAI3B,6LAAC,2IAAA,CAAA,UAAU;;;;;;;;;;;sCAIb,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;4BACV,cAAW;sCAEX,cAAA,6LAAC;gCAAI,WAAU;gCAAoB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC1E,iCACC,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;yDAErE,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;gBAO5E,kCACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAiF;;;;;;8CAG1G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;8CAAiF;;;;;;8CAG9G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAa,WAAU;8CAAiF;;;;;;8CAGnH,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAAiF;;;;;;8CAG/G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAO,WAAU;8CAAiF;;;;;;8CAG7G,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAAiF;;;;;;;;;;;;sCAInH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2IAAA,CAAA,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzB;GAnLM;;QAEsB,iJAAA,CAAA,aAAU;;;KAFhC;uCAqLS"}}, {"offset": {"line": 1445, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1451, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/Footer.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\n\nconst Footer = () => {\n  return (\n    <footer className=\"bg-gradient-to-br from-navy via-teal to-skyblue relative overflow-hidden\">\n      {/* خلفية هندسية متناسقة */}\n      <div className=\"absolute inset-0 opacity-10\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl\"></div>\n        <div className=\"absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl\"></div>\n      </div>\n\n      {/* شبكة نقطية ناعمة */}\n      <div className=\"absolute inset-0 opacity-20\">\n        <div className=\"w-full h-full\" style={{\n          backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',\n          backgroundSize: '30px 30px'\n        }}></div>\n      </div>\n\n      <div className=\"container mx-auto px-4 py-16 relative z-10\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Logo and Description */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"flex items-center space-x-3 space-x-reverse mb-6\">\n              <div className=\"w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center text-white text-2xl font-bold shadow-lg\">\n                🔨\n              </div>\n              <span className=\"text-3xl font-bold text-white\">\n                دوزان\n              </span>\n            </div>\n            <p className=\"text-white/90 leading-relaxed mb-6\">\n              منصة تربط بين أصحاب المشاريع والحرفيين المهرة لتنفيذ المشاريع بكفاءة وجودة عالية في جميع أنحاء سوريا.\n            </p>\n            <div className=\"flex space-x-4 space-x-reverse\">\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path d=\"M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84\" />\n                </svg>\n              </a>\n              <a href=\"#\" className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white hover:bg-white/30 hover:scale-110 transition-all duration-300\">\n                <svg className=\"h-5 w-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path fillRule=\"evenodd\" d=\"M12.315 2c2.43 0 2.784.013 3.808.06 1.064.049 1.791.218 2.427.465a4.902 4.902 0 011.772 1.153 4.902 4.902 0 011.153 1.772c.247.636.416 1.363.465 2.427.048 1.067.06 1.407.06 4.123v.08c0 2.643-.012 2.987-.06 4.043-.049 1.064-.218 1.791-.465 2.427a4.902 4.902 0 01-1.153 1.772 4.902 4.902 0 01-1.772 1.153c-.636.247-1.363.416-2.427.465-1.067.048-1.407.06-4.123.06h-.08c-2.643 0-2.987-.012-4.043-.06-1.064-.049-1.791-.218-2.427-.465a4.902 4.902 0 01-1.772-1.153 4.902 4.902 0 01-1.153-1.772c-.247-.636-.416-1.363-.465-2.427-.047-1.024-.06-1.379-.06-3.808v-.63c0-2.43.013-2.784.06-3.808.049-1.064.218-1.791.465-2.427a4.902 4.902 0 011.153-1.772A4.902 4.902 0 015.45 2.525c.636-.247 1.363-.416 2.427-.465C8.901 2.013 9.256 2 11.685 2h.63zm-.081 1.802h-.468c-2.456 0-2.784.011-3.807.058-.975.045-1.504.207-1.857.344-.467.182-.8.398-1.15.748-.35.35-.566.683-.748 1.15-.137.353-.3.882-.344 1.857-.047 1.023-.058 1.351-.058 3.807v.468c0 2.456.011 2.784.058 3.807.045.975.207 1.504.344 1.857.182.466.399.8.748 1.15.35.35.683.566 1.15.748.353.137.882.3 1.857.344 1.054.048 1.37.058 4.041.058h.08c2.597 0 2.917-.01 3.96-.058.976-.045 1.505-.207 1.858-.344.466-.182.8-.398 1.15-.748.35-.35.566-.683.748-1.15.137-.353.3-.882.344-1.857.048-1.055.058-1.37.058-4.041v-.08c0-2.597-.01-2.917-.058-3.96-.045-.976-.207-1.505-.344-1.858a3.097 3.097 0 00-.748-1.15 3.098 3.098 0 00-1.15-.748c-.353-.137-.882-.3-1.857-.344-1.023-.047-1.351-.058-3.807-.058zM12 6.865a5.135 5.135 0 110 10.27 5.135 5.135 0 010-10.27zm0 1.802a3.333 3.333 0 100 6.666 3.333 3.333 0 000-6.666zm5.338-3.205a1.2 1.2 0 110 2.4 1.2 1.2 0 010-2.4z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              روابط سريعة\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الرئيسية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/jobs\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  المشاريع\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/craftsmen\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الحرفيون\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/about\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  من نحن\n                </Link>\n              </li>\n            </ul>\n          </div>\n          {/* Support */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              الدعم\n            </h3>\n            <ul className=\"space-y-3\">\n              <li>\n                <Link href=\"/contact\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  اتصل بنا\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/faq\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  الأسئلة الشائعة\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/privacy\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  سياسة الخصوصية\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/terms\" className=\"text-white/80 hover:text-white hover:translate-x-2 transition-all duration-300 flex items-center group\">\n                  <span className=\"w-1 h-1 bg-white/60 rounded-full ml-2 group-hover:bg-white transition-colors duration-300\"></span>\n                  شروط الاستخدام\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"text-xl font-bold text-white mb-6 flex items-center\">\n              <span className=\"w-2 h-6 bg-gradient-to-b from-yellow-400 to-orange-500 rounded-full ml-3\"></span>\n              تواصل معنا\n            </h3>\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">البريد الإلكتروني</p>\n                  <a href=\"mailto:<EMAIL>\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    <EMAIL>\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">الهاتف</p>\n                  <a href=\"tel:+963112345678\" className=\"text-white hover:text-yellow-300 transition-colors duration-300 font-medium\">\n                    +963 11 234 5678\n                  </a>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3 space-x-reverse group\">\n                <div className=\"w-10 h-10 bg-white/20 backdrop-blur-sm rounded-lg flex items-center justify-center text-white group-hover:bg-white/30 transition-colors duration-300\">\n                  <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                  </svg>\n                </div>\n                <div>\n                  <p className=\"text-white/70 text-sm\">العنوان</p>\n                  <p className=\"text-white font-medium\">دمشق، سوريا</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"mt-12 pt-8 border-t border-white/20\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-center md:text-right\">\n              <p className=\"text-white/80 text-sm\">\n                &copy; {new Date().getFullYear()} دوزان. جميع الحقوق محفوظة.\n              </p>\n              <p className=\"text-white/60 text-xs mt-1\">\n                صُنع بـ ❤️ في سوريا\n              </p>\n            </div>\n\n            <div className=\"flex items-center space-x-6 space-x-reverse\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">2500+</div>\n                <div className=\"text-white/70 text-xs\">حرفي</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">15000+</div>\n                <div className=\"text-white/70 text-xs\">مشروع</div>\n              </div>\n              <div className=\"w-px h-8 bg-white/20\"></div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-white\">98%</div>\n                <div className=\"text-white/70 text-xs\">رضا</div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,SAAS;IACb,qBACE,6LAAC;QAAO,WAAU;;0BAEhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;oBAAgB,OAAO;wBACpC,iBAAiB;wBACjB,gBAAgB;oBAClB;;;;;;;;;;;0BAGF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAA6H;;;;;;0DAG5I,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;kDAIlD,6LAAC;wCAAE,WAAU;kDAAqC;;;;;;kDAGlD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAAyQ,UAAS;;;;;;;;;;;;;;;;0DAGjT,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,6LAAC;gDAAE,MAAK;gDAAI,WAAU;0DACpB,cAAA,6LAAC;oDAAI,WAAU;oDAAU,MAAK;oDAAe,SAAQ;8DACnD,cAAA,6LAAC;wDAAK,UAAS;wDAAU,GAAE;wDAA8jD,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO1mD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAI,WAAU;;sEACvB,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAQ,WAAU;;sEAC3B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAa,WAAU;;sEAChC,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAO3H,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAO,WAAU;;sEAC1B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAW,WAAU;;sEAC9B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;0DAIvH,6LAAC;0DACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;oDAAS,WAAU;;sEAC5B,6LAAC;4DAAK,WAAU;;;;;;wDAAmG;;;;;;;;;;;;;;;;;;;;;;;;0CAQ3H,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAK,WAAU;;;;;;4CAAkF;;;;;;;kDAGpG,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,MAAK;gEAAuB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAM3H,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;sEACjE,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,MAAK;gEAAoB,WAAU;0EAA8E;;;;;;;;;;;;;;;;;;0DAMxH,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAAU,MAAK;4DAAO,QAAO;4DAAe,SAAQ;;8EACjE,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;8EACrE,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;0EAAwB;;;;;;0EACrC,6LAAC;gEAAE,WAAU;0EAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;;gDAAwB;gDAC3B,IAAI,OAAO,WAAW;gDAAG;;;;;;;sDAEnC,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAK5C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;KA5MM;uCA8MS"}}, {"offset": {"line": 2308, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2314, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/layout/MainLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Header from './Header';\nimport Footer from './Footer';\n\ninterface MainLayoutProps {\n  children: React.ReactNode;\n}\n\nconst MainLayout = ({ children }: MainLayoutProps) => {\n  return (\n    <div className=\"flex flex-col min-h-screen\">\n      <Header />\n      <main className=\"flex-grow\">{children}</main>\n      <Footer />\n    </div>\n  );\n};\n\nexport default MainLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAJA;;;;AAUA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAmB;IAC/C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,UAAM;;;;;0BACP,6LAAC;gBAAK,WAAU;0BAAa;;;;;;0BAC7B,6LAAC,yIAAA,CAAA,UAAM;;;;;;;;;;;AAGb;KARM;uCAUS"}}, {"offset": {"line": 2360, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2366, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-white shadow-sm transition-all duration-200 hover:shadow-md',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('font-semibold leading-none tracking-tight text-navy', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-600', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACpE,GAAG,KAAK;kBAER;;;;;;;AAGL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;;AAGL,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG"}}, {"offset": {"line": 2470, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2476, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/deployment-guide/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport MainLayout from '@/components/layout/MainLayout';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\nimport { Button } from '@/components/ui/Button';\n\nconst DeploymentGuidePage = () => {\n  const [activeTab, setActiveTab] = useState('services');\n\n  const externalServices = [\n    {\n      name: 'قاعدة البيانات',\n      service: 'PostgreSQL',\n      provider: 'Railway / Supabase',\n      cost: 'مجاني للبداية',\n      required: true,\n      description: 'قاعدة بيانات PostgreSQL للإنتاج',\n      setup: [\n        'إنشاء حساب على Railway.app',\n        'إنشاء مشروع PostgreSQL جديد',\n        'نسخ DATABASE_URL',\n        'تطبيق Prisma migrations'\n      ],\n      alternatives: [\n        'Supabase (مجاني + ميزات إضافية)',\n        'PlanetScale (MySQL)',\n        'MongoDB Atlas'\n      ]\n    },\n    {\n      name: 'استضافة Frontend',\n      service: 'Vercel',\n      provider: 'Vercel',\n      cost: 'مجاني',\n      required: true,\n      description: 'استضافة تطبيق Next.js',\n      setup: [\n        'ربط GitHub repository',\n        'تكوين متغيرات البيئة',\n        'نشر تلقائي عند كل commit'\n      ],\n      alternatives: [\n        'Netlify',\n        'GitHub Pages',\n        'AWS Amplify'\n      ]\n    },\n    {\n      name: 'البريد الإلكتروني',\n      service: 'Gmail SMTP',\n      provider: 'Google',\n      cost: 'مجاني (محدود)',\n      required: true,\n      description: 'إرسال رسائل التأكيد والإشعارات',\n      setup: [\n        'تفعيل 2FA على حساب Gmail',\n        'إنشاء App Password',\n        'تكوين SMTP settings'\n      ],\n      alternatives: [\n        'SendGrid (مجاني حتى 100 رسالة/يوم)',\n        'Mailgun',\n        'Amazon SES'\n      ]\n    },\n    {\n      name: 'تسجيل الدخول بـ Google',\n      service: 'Google OAuth',\n      provider: 'Google Cloud',\n      cost: 'مجاني',\n      required: false,\n      description: 'تسجيل دخول اختياري بحساب Google',\n      setup: [\n        'إنشاء مشروع في Google Cloud Console',\n        'تفعيل Google+ API',\n        'إنشاء OAuth 2.0 credentials',\n        'تكوين authorized domains'\n      ],\n      alternatives: [\n        'Facebook Login',\n        'GitHub OAuth',\n        'تعطيل الميزة مؤقتاً'\n      ]\n    },\n    {\n      name: 'تخزين الملفات',\n      service: 'Local Storage',\n      provider: 'محلي',\n      cost: 'مجاني',\n      required: false,\n      description: 'تخزين الصور محلياً في البداية',\n      setup: [\n        'استخدام مجلد /public/uploads',\n        'تكوين API route للرفع',\n        'إعداد حدود الحجم والنوع'\n      ],\n      alternatives: [\n        'Cloudinary (مجاني حتى 25GB)',\n        'AWS S3',\n        'Google Cloud Storage'\n      ]\n    }\n  ];\n\n  const deploymentSteps = [\n    {\n      phase: 'التحضير',\n      steps: [\n        'تنظيف الكود وإزالة console.log',\n        'تحديث متغيرات البيئة للإنتاج',\n        'اختبار شامل لجميع الميزات',\n        'تحسين الأداء والصور',\n        'إنشاء repository على GitHub'\n      ]\n    },\n    {\n      phase: 'قاعدة البيانات',\n      steps: [\n        'إنشاء حساب Railway/Supabase',\n        'إنشاء قاعدة بيانات PostgreSQL',\n        'نسخ DATABASE_URL',\n        'تطبيق Prisma migrations',\n        'إدخال البيانات الأولية'\n      ]\n    },\n    {\n      phase: 'البريد الإلكتروني',\n      steps: [\n        'إنشاء حساب Gmail مخصص للمشروع',\n        'تفعيل 2FA',\n        'إنشاء App Password',\n        'تكوين SMTP settings',\n        'اختبار إرسال البريد'\n      ]\n    },\n    {\n      phase: 'النشر',\n      steps: [\n        'ربط GitHub مع Vercel',\n        'تكوين متغيرات البيئة في Vercel',\n        'نشر أول إصدار',\n        'اختبار الموقع المنشور',\n        'تكوين domain مخصص (اختياري)'\n      ]\n    },\n    {\n      phase: 'المراقبة',\n      steps: [\n        'إعداد Google Analytics',\n        'مراقبة الأخطاء والأداء',\n        'إعداد النسخ الاحتياطية',\n        'تكوين SSL certificates',\n        'اختبار الأمان'\n      ]\n    }\n  ];\n\n  const localAlternatives = [\n    {\n      service: 'قاعدة البيانات',\n      local: 'SQLite + Prisma',\n      description: 'يمكن استخدام SQLite محلياً للتطوير والاختبار',\n      pros: ['لا يحتاج إنترنت', 'سهل الإعداد', 'مجاني تماماً'],\n      cons: ['محدود للمشاريع الصغيرة', 'لا يدعم المستخدمين المتعددين بكفاءة']\n    },\n    {\n      service: 'البريد الإلكتروني',\n      local: 'محاكاة في Console',\n      description: 'طباعة رسائل البريد في console بدلاً من الإرسال',\n      pros: ['لا يحتاج إعداد خارجي', 'سريع للتطوير'],\n      cons: ['لا يعمل في الإنتاج', 'لا يمكن اختبار التجربة الحقيقية']\n    },\n    {\n      service: 'تسجيل الدخول بـ Google',\n      local: 'تعطيل مؤقت',\n      description: 'إزالة زر Google وإبقاء تسجيل الدخول العادي فقط',\n      pros: ['لا يحتاج إعداد', 'يقلل التعقيد'],\n      cons: ['تجربة مستخدم أقل', 'فقدان ميزة مهمة']\n    }\n  ];\n\n  const costBreakdown = [\n    { service: 'Railway PostgreSQL', cost: '$0/شهر (مجاني)', limit: 'حتى 500MB' },\n    { service: 'Vercel Hosting', cost: '$0/شهر (مجاني)', limit: 'حتى 100GB bandwidth' },\n    { service: 'Gmail SMTP', cost: '$0/شهر (مجاني)', limit: 'حتى 500 رسالة/يوم' },\n    { service: 'Google OAuth', cost: '$0/شهر (مجاني)', limit: 'بلا حدود' },\n    { service: 'Domain (اختياري)', cost: '$10-15/سنة', limit: '.com domain' }\n  ];\n\n  return (\n    <MainLayout>\n      <div className=\"min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            {/* Header */}\n            <div className=\"text-center mb-8\">\n              <h1 className=\"text-4xl font-bold text-navy mb-4\">\n                🚀 دليل النشر والخدمات الخارجية\n              </h1>\n              <p className=\"text-xl text-gray-600\">\n                كل ما تحتاجه لنشر مشروع دوزان على الإنترنت\n              </p>\n            </div>\n\n            {/* Tabs */}\n            <div className=\"flex space-x-4 space-x-reverse mb-8\">\n              {[\n                { id: 'services', label: 'الخدمات المطلوبة', icon: '🛠️' },\n                { id: 'steps', label: 'خطوات النشر', icon: '📋' },\n                { id: 'local', label: 'البدائل المحلية', icon: '💻' },\n                { id: 'costs', label: 'التكاليف', icon: '💰' }\n              ].map((tab) => (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n                    activeTab === tab.id\n                      ? 'bg-teal text-white'\n                      : 'bg-white text-gray-600 hover:bg-gray-50'\n                  }`}\n                >\n                  {tab.icon} {tab.label}\n                </button>\n              ))}\n            </div>\n\n            {/* Services Tab */}\n            {activeTab === 'services' && (\n              <div className=\"space-y-6\">\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n                  {externalServices.map((service, index) => (\n                    <Card key={index} className=\"border-0 bg-white/90 backdrop-blur-md shadow-lg\">\n                      <CardHeader>\n                        <CardTitle className=\"flex items-center justify-between\">\n                          <span>{service.name}</span>\n                          <div className=\"flex items-center space-x-2 space-x-reverse\">\n                            {service.required && (\n                              <span className=\"bg-red-100 text-red-800 text-xs px-2 py-1 rounded\">\n                                مطلوب\n                              </span>\n                            )}\n                            <span className=\"bg-green-100 text-green-800 text-xs px-2 py-1 rounded\">\n                              {service.cost}\n                            </span>\n                          </div>\n                        </CardTitle>\n                      </CardHeader>\n                      <CardContent>\n                        <p className=\"text-gray-600 mb-4\">{service.description}</p>\n                        \n                        <div className=\"mb-4\">\n                          <h4 className=\"font-semibold text-navy mb-2\">خطوات الإعداد:</h4>\n                          <ol className=\"list-decimal list-inside space-y-1 text-sm\">\n                            {service.setup.map((step, stepIndex) => (\n                              <li key={stepIndex} className=\"text-gray-700\">{step}</li>\n                            ))}\n                          </ol>\n                        </div>\n\n                        <div>\n                          <h4 className=\"font-semibold text-navy mb-2\">بدائل أخرى:</h4>\n                          <ul className=\"space-y-1 text-sm\">\n                            {service.alternatives.map((alt, altIndex) => (\n                              <li key={altIndex} className=\"text-gray-600\">• {alt}</li>\n                            ))}\n                          </ul>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Steps Tab */}\n            {activeTab === 'steps' && (\n              <div className=\"space-y-6\">\n                {deploymentSteps.map((phase, index) => (\n                  <Card key={index} className=\"border-0 bg-white/90 backdrop-blur-md shadow-lg\">\n                    <CardHeader>\n                      <CardTitle className=\"flex items-center\">\n                        <span className=\"bg-teal text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3\">\n                          {index + 1}\n                        </span>\n                        {phase.phase}\n                      </CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n                        {phase.steps.map((step, stepIndex) => (\n                          <div key={stepIndex} className=\"bg-gray-50 p-4 rounded-lg\">\n                            <div className=\"flex items-start space-x-2 space-x-reverse\">\n                              <span className=\"text-teal font-bold\">{stepIndex + 1}.</span>\n                              <span className=\"text-gray-700\">{step}</span>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </CardContent>\n                  </Card>\n                ))}\n              </div>\n            )}\n\n            {/* Local Alternatives Tab */}\n            {activeTab === 'local' && (\n              <div className=\"space-y-6\">\n                <Card className=\"border-0 bg-blue-50 border-blue-200\">\n                  <CardContent className=\"p-6\">\n                    <h3 className=\"font-semibold text-navy mb-3\">💡 هل يمكن تشغيل كل شيء محلياً؟</h3>\n                    <p className=\"text-gray-700 mb-4\">\n                      نعم! يمكن تشغيل المشروع محلياً بالكامل للتطوير والاختبار، لكن للنشر العام ستحتاج بعض الخدمات الخارجية.\n                    </p>\n                    <div className=\"bg-white p-4 rounded-lg\">\n                      <h4 className=\"font-semibold text-navy mb-2\">الوضع الحالي:</h4>\n                      <p className=\"text-green-700\">\n                        ✅ المشروع يعمل محلياً بالكامل مع بيانات وهمية ومحاكاة للخدمات\n                      </p>\n                    </div>\n                  </CardContent>\n                </Card>\n\n                {localAlternatives.map((alt, index) => (\n                  <Card key={index} className=\"border-0 bg-white/90 backdrop-blur-md shadow-lg\">\n                    <CardHeader>\n                      <CardTitle>{alt.service}</CardTitle>\n                    </CardHeader>\n                    <CardContent>\n                      <div className=\"mb-4\">\n                        <h4 className=\"font-semibold text-navy mb-2\">البديل المحلي:</h4>\n                        <p className=\"text-gray-700 mb-3\">{alt.local}</p>\n                        <p className=\"text-gray-600\">{alt.description}</p>\n                      </div>\n\n                      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                        <div>\n                          <h4 className=\"font-semibold text-green-600 mb-2\">المزايا:</h4>\n                          <ul className=\"space-y-1\">\n                            {alt.pros.map((pro, proIndex) => (\n                              <li key={proIndex} className=\"text-green-700 text-sm\">✅ {pro}</li>\n                            ))}\n                          </ul>\n                        </div>\n                        <div>\n                          <h4 className=\"font-semibold text-red-600 mb-2\">العيوب:</h4>\n                          <ul className=\"space-y-1\">\n                            {alt.cons.map((con, conIndex) => (\n                              <li key={conIndex} className=\"text-red-700 text-sm\">❌ {con}</li>\n                            ))}\n                          </ul>\n                        </div>\n                      </div>\n                    </CardContent>\n                  </Card>\n                ))}\n              </div>\n            )}\n\n            {/* Costs Tab */}\n            {activeTab === 'costs' && (\n              <div className=\"space-y-6\">\n                <Card className=\"border-0 bg-green-50 border-green-200\">\n                  <CardContent className=\"p-6\">\n                    <h3 className=\"font-semibold text-navy mb-3\">💰 ملخص التكاليف</h3>\n                    <div className=\"text-center\">\n                      <div className=\"text-4xl font-bold text-green-600 mb-2\">$0/شهر</div>\n                      <p className=\"text-gray-700\">التكلفة الإجمالية للبداية (مع الحدود المجانية)</p>\n                    </div>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"border-0 bg-white/90 backdrop-blur-md shadow-lg\">\n                  <CardHeader>\n                    <CardTitle>تفصيل التكاليف</CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"space-y-4\">\n                      {costBreakdown.map((item, index) => (\n                        <div key={index} className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\">\n                          <div>\n                            <h4 className=\"font-semibold text-navy\">{item.service}</h4>\n                            <p className=\"text-sm text-gray-600\">{item.limit}</p>\n                          </div>\n                          <div className=\"text-right\">\n                            <span className=\"font-bold text-green-600\">{item.cost}</span>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  </CardContent>\n                </Card>\n\n                <Card className=\"border-0 bg-yellow-50 border-yellow-200\">\n                  <CardContent className=\"p-6\">\n                    <h3 className=\"font-semibold text-navy mb-3\">📈 عند نمو المشروع</h3>\n                    <p className=\"text-gray-700 mb-4\">\n                      عندما يزداد عدد المستخدمين، قد تحتاج للترقية:\n                    </p>\n                    <ul className=\"space-y-2 text-gray-700\">\n                      <li>• Railway Pro: $5/شهر (قاعدة بيانات أكبر)</li>\n                      <li>• Vercel Pro: $20/شهر (bandwidth أكثر)</li>\n                      <li>• SendGrid: $15/شهر (40,000 رسالة)</li>\n                      <li>• Cloudinary: $89/شهر (تخزين صور احترافي)</li>\n                    </ul>\n                  </CardContent>\n                </Card>\n              </div>\n            )}\n\n            {/* Action Buttons */}\n            <div className=\"mt-12 text-center\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <Button \n                  onClick={() => window.open('/syria-simulation', '_blank')}\n                  className=\"bg-gradient-to-r from-blue-500 to-blue-600\"\n                >\n                  🇸🇾 محاكاة المستخدم السوري\n                </Button>\n                <Button \n                  onClick={() => window.open('/test-dashboard', '_blank')}\n                  className=\"bg-gradient-to-r from-green-500 to-green-600\"\n                >\n                  🧪 لوحة الاختبار الشاملة\n                </Button>\n                <Button \n                  onClick={() => window.open('/project-summary', '_blank')}\n                  className=\"bg-gradient-to-r from-purple-500 to-purple-600\"\n                >\n                  📊 ملخص المشروع\n                </Button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </MainLayout>\n  );\n};\n\nexport default DeploymentGuidePage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOA,MAAM,sBAAsB;;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,mBAAmB;QACvB;YACE,MAAM;YACN,SAAS;YACT,UAAU;YACV,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM;YACN,SAAS;YACT,UAAU;YACV,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;gBACL;gBACA;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM;YACN,SAAS;YACT,UAAU;YACV,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;gBACL;gBACA;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM;YACN,SAAS;YACT,UAAU;YACV,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;gBACL;gBACA;gBACA;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;aACD;QACH;QACA;YACE,MAAM;YACN,SAAS;YACT,UAAU;YACV,MAAM;YACN,UAAU;YACV,aAAa;YACb,OAAO;gBACL;gBACA;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;aACD;QACH;KACD;IAED,MAAM,kBAAkB;QACtB;YACE,OAAO;YACP,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA;YACE,OAAO;YACP,OAAO;gBACL;gBACA;gBACA;gBACA;gBACA;aACD;QACH;KACD;IAED,MAAM,oBAAoB;QACxB;YACE,SAAS;YACT,OAAO;YACP,aAAa;YACb,MAAM;gBAAC;gBAAmB;gBAAe;aAAe;YACxD,MAAM;gBAAC;gBAA0B;aAAsC;QACzE;QACA;YACE,SAAS;YACT,OAAO;YACP,aAAa;YACb,MAAM;gBAAC;gBAAwB;aAAe;YAC9C,MAAM;gBAAC;gBAAsB;aAAkC;QACjE;QACA;YACE,SAAS;YACT,OAAO;YACP,aAAa;YACb,MAAM;gBAAC;gBAAkB;aAAe;YACxC,MAAM;gBAAC;gBAAoB;aAAkB;QAC/C;KACD;IAED,MAAM,gBAAgB;QACpB;YAAE,SAAS;YAAsB,MAAM;YAAkB,OAAO;QAAY;QAC5E;YAAE,SAAS;YAAkB,MAAM;YAAkB,OAAO;QAAsB;QAClF;YAAE,SAAS;YAAc,MAAM;YAAkB,OAAO;QAAoB;QAC5E;YAAE,SAAS;YAAgB,MAAM;YAAkB,OAAO;QAAW;QACrE;YAAE,SAAS;YAAoB,MAAM;YAAc,OAAO;QAAc;KACzE;IAED,qBACE,6LAAC,6IAAA,CAAA,UAAU;kBACT,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAGlD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAMvC,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAY,OAAO;oCAAoB,MAAM;gCAAM;gCACzD;oCAAE,IAAI;oCAAS,OAAO;oCAAe,MAAM;gCAAK;gCAChD;oCAAE,IAAI;oCAAS,OAAO;oCAAmB,MAAM;gCAAK;gCACpD;oCAAE,IAAI;oCAAS,OAAO;oCAAY,MAAM;gCAAK;6BAC9C,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,mDAAmD,EAC7D,cAAc,IAAI,EAAE,GAChB,uBACA,2CACJ;;wCAED,IAAI,IAAI;wCAAC;wCAAE,IAAI,KAAK;;mCARhB,IAAI,EAAE;;;;;;;;;;wBAchB,cAAc,4BACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,SAAS,sBAC9B,6LAAC,mIAAA,CAAA,OAAI;wCAAa,WAAU;;0DAC1B,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,6LAAC;sEAAM,QAAQ,IAAI;;;;;;sEACnB,6LAAC;4DAAI,WAAU;;gEACZ,QAAQ,QAAQ,kBACf,6LAAC;oEAAK,WAAU;8EAAoD;;;;;;8EAItE,6LAAC;oEAAK,WAAU;8EACb,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;0DAKrB,6LAAC,mIAAA,CAAA,cAAW;;kEACV,6LAAC;wDAAE,WAAU;kEAAsB,QAAQ,WAAW;;;;;;kEAEtD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA+B;;;;;;0EAC7C,6LAAC;gEAAG,WAAU;0EACX,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACxB,6LAAC;wEAAmB,WAAU;kFAAiB;uEAAtC;;;;;;;;;;;;;;;;kEAKf,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA+B;;;;;;0EAC7C,6LAAC;gEAAG,WAAU;0EACX,QAAQ,YAAY,CAAC,GAAG,CAAC,CAAC,KAAK,yBAC9B,6LAAC;wEAAkB,WAAU;;4EAAgB;4EAAG;;uEAAvC;;;;;;;;;;;;;;;;;;;;;;;uCAhCR;;;;;;;;;;;;;;;wBA4ClB,cAAc,yBACb,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,OAAO,sBAC3B,6LAAC,mIAAA,CAAA,OAAI;oCAAa,WAAU;;sDAC1B,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,6LAAC;wDAAK,WAAU;kEACb,QAAQ;;;;;;oDAEV,MAAM,KAAK;;;;;;;;;;;;sDAGhB,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;0DACZ,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,0BACtB,6LAAC;wDAAoB,WAAU;kEAC7B,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;wEAAuB,YAAY;wEAAE;;;;;;;8EACrD,6LAAC;oEAAK,WAAU;8EAAiB;;;;;;;;;;;;uDAH3B;;;;;;;;;;;;;;;;mCAZP;;;;;;;;;;wBA2BhB,cAAc,yBACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAA+B;;;;;;kEAC7C,6LAAC;wDAAE,WAAU;kEAAiB;;;;;;;;;;;;;;;;;;;;;;;gCAOnC,kBAAkB,GAAG,CAAC,CAAC,KAAK,sBAC3B,6LAAC,mIAAA,CAAA,OAAI;wCAAa,WAAU;;0DAC1B,6LAAC,mIAAA,CAAA,aAAU;0DACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;8DAAE,IAAI,OAAO;;;;;;;;;;;0DAEzB,6LAAC,mIAAA,CAAA,cAAW;;kEACV,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA+B;;;;;;0EAC7C,6LAAC;gEAAE,WAAU;0EAAsB,IAAI,KAAK;;;;;;0EAC5C,6LAAC;gEAAE,WAAU;0EAAiB,IAAI,WAAW;;;;;;;;;;;;kEAG/C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAoC;;;;;;kFAClD,6LAAC;wEAAG,WAAU;kFACX,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,yBAClB,6LAAC;gFAAkB,WAAU;;oFAAyB;oFAAG;;+EAAhD;;;;;;;;;;;;;;;;0EAIf,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAAkC;;;;;;kFAChD,6LAAC;wEAAG,WAAU;kFACX,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,yBAClB,6LAAC;gFAAkB,WAAU;;oFAAuB;oFAAG;;+EAA9C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAxBV;;;;;;;;;;;wBAoChB,cAAc,yBACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAyC;;;;;;kEACxD,6LAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;8CAKnC,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,6LAAC,mIAAA,CAAA,aAAU;sDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;;;;;;sDAEb,6LAAC,mIAAA,CAAA,cAAW;sDACV,cAAA,6LAAC;gDAAI,WAAU;0DACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAA2B,KAAK,OAAO;;;;;;kFACrD,6LAAC;wEAAE,WAAU;kFAAyB,KAAK,KAAK;;;;;;;;;;;;0EAElD,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EAA4B,KAAK,IAAI;;;;;;;;;;;;uDAN/C;;;;;;;;;;;;;;;;;;;;;8CAclB,6LAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,6LAAC;gDAAG,WAAU;;kEACZ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;kEACJ,6LAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,qBAAqB;wCAChD,WAAU;kDACX;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,mBAAmB;wCAC9C,WAAU;kDACX;;;;;;kDAGD,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAS,IAAM,OAAO,IAAI,CAAC,oBAAoB;wCAC/C,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GA/aM;KAAA;uCAibS"}}, {"offset": {"line": 3512, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}