'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';

const ClientOffersPage = () => {
  const [activeFilter, setActiveFilter] = useState('all');

  const offers = [
    {
      id: 1,
      projectId: 1,
      projectTitle: 'تجديد المطبخ الرئيسي',
      craftsmanName: 'أحمد النجار',
      craftsmanRating: 4.9,
      craftsmanCompletedProjects: 45,
      price: '₺7,500',
      duration: '15 يوم',
      status: 'جديد',
      description: 'أقدم خدمات تجديد المطابخ بأعلى جودة وأفضل الأسعار. لدي خبرة 10 سنوات في هذا المجال.',
      submittedAt: '2024-02-15 10:30',
      materials: 'متضمنة',
      warranty: '6 أشهر'
    },
    {
      id: 2,
      projectId: 1,
      projectTitle: 'تجديد المطبخ الرئيسي',
      craftsmanName: 'محمد الخشب',
      craftsmanRating: 4.7,
      craftsmanCompletedProjects: 32,
      price: '₺8,200',
      duration: '12 يوم',
      status: 'مراجعة',
      description: 'متخصص في تصميم وتنفيذ المطابخ العصرية. أستخدم أجود أنواع الخشب والمواد.',
      submittedAt: '2024-02-14 14:20',
      materials: 'غير متضمنة',
      warranty: '12 شهر'
    },
    {
      id: 3,
      projectId: 2,
      projectTitle: 'إصلاح نظام السباكة',
      craftsmanName: 'علي السباك',
      craftsmanRating: 4.8,
      craftsmanCompletedProjects: 67,
      price: '₺2,200',
      duration: '3 أيام',
      status: 'مقبول',
      description: 'خبرة 15 سنة في أعمال السباكة. أضمن حل جميع مشاكل التسريب نهائياً.',
      submittedAt: '2024-02-10 09:15',
      materials: 'متضمنة',
      warranty: '3 أشهر'
    },
    {
      id: 4,
      projectId: 4,
      projectTitle: 'تركيب نظام كهرباء ذكي',
      craftsmanName: 'سارة الكهربائية',
      craftsmanRating: 4.9,
      craftsmanCompletedProjects: 28,
      price: '₺4,800',
      duration: '7 أيام',
      status: 'مرفوض',
      description: 'متخصصة في الأنظمة الذكية والتحكم الآلي. أقدم حلول متطورة وعملية.',
      submittedAt: '2024-02-08 16:45',
      materials: 'متضمنة',
      warranty: '24 شهر'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'جديد':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'مراجعة':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'مقبول':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'مرفوض':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const filteredOffers = offers.filter(offer => {
    if (activeFilter === 'all') return true;
    return offer.status === activeFilter;
  });

  const stats = [
    {
      title: 'إجمالي العروض',
      value: offers.length.toString(),
      color: 'text-blue-600'
    },
    {
      title: 'عروض جديدة',
      value: offers.filter(o => o.status === 'جديد').length.toString(),
      color: 'text-green-600'
    },
    {
      title: 'عروض مقبولة',
      value: offers.filter(o => o.status === 'مقبول').length.toString(),
      color: 'text-purple-600'
    },
    {
      title: 'متوسط السعر',
      value: '₺5,675',
      color: 'text-orange-600'
    }
  ];

  return (
    <ProtectedRoute requiredRoles="client">
      <DashboardLayout 
        title="العروض المستلمة"
        subtitle="مراجعة وإدارة العروض على مشاريعك"
      >
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className={`text-3xl ${stat.color}`}>
                    📥
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-wrap gap-2">
              {[
                { id: 'all', label: 'جميع العروض' },
                { id: 'جديد', label: 'جديدة' },
                { id: 'مراجعة', label: 'قيد المراجعة' },
                { id: 'مقبول', label: 'مقبولة' },
                { id: 'مرفوض', label: 'مرفوضة' }
              ].map((filter) => (
                <button
                  key={filter.id}
                  onClick={() => setActiveFilter(filter.id)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    activeFilter === filter.id
                      ? 'bg-navy text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {filter.label}
                </button>
              ))}
            </div>
          </div>

          {/* Offers List */}
          <div className="space-y-6">
            {filteredOffers.map((offer) => (
              <div key={offer.id} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-1">{offer.projectTitle}</h3>
                    <p className="text-gray-600 text-sm">عرض من {offer.craftsmanName}</p>
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(offer.status)}`}>
                    {offer.status}
                  </span>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                  {/* Craftsman Info */}
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900">معلومات الحرفي</h4>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="w-12 h-12 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white font-bold">
                        {offer.craftsmanName.charAt(0)}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{offer.craftsmanName}</p>
                        <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
                          <span className="flex items-center">
                            ⭐ {offer.craftsmanRating}
                          </span>
                          <span>•</span>
                          <span>{offer.craftsmanCompletedProjects} مشروع مكتمل</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Offer Details */}
                  <div className="space-y-3">
                    <h4 className="font-medium text-gray-900">تفاصيل العرض</h4>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">السعر:</span>
                        <span className="font-semibold text-gray-900 mr-2">{offer.price}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">المدة:</span>
                        <span className="font-semibold text-gray-900 mr-2">{offer.duration}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">المواد:</span>
                        <span className="font-semibold text-gray-900 mr-2">{offer.materials}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">الضمان:</span>
                        <span className="font-semibold text-gray-900 mr-2">{offer.warranty}</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Description */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 mb-2">وصف العرض</h4>
                  <p className="text-gray-600 text-sm leading-relaxed">{offer.description}</p>
                </div>

                {/* Actions */}
                <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                  <div className="text-xs text-gray-500">
                    تم التقديم في {offer.submittedAt}
                  </div>
                  <div className="flex space-x-2 space-x-reverse">
                    <Link href={`/craftsmen/${offer.craftsmanName}`}>
                      <Button size="sm" variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-100">
                        عرض الملف الشخصي
                      </Button>
                    </Link>
                    <Link href={`/messages?user=${offer.craftsmanName}`}>
                      <Button size="sm" variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                        إرسال رسالة
                      </Button>
                    </Link>
                    {offer.status === 'جديد' && (
                      <>
                        <Button size="sm" variant="outline" className="border-red-300 text-red-700 hover:bg-red-100">
                          رفض
                        </Button>
                        <Button size="sm" className="bg-gradient-to-r from-navy to-teal">
                          قبول العرض
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredOffers.length === 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-6xl mb-4">📥</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد عروض</h3>
              <p className="text-gray-600 mb-6">لم يتم العثور على عروض بالمعايير المحددة</p>
              <Link href="/client/projects">
                <Button className="bg-gradient-to-r from-navy to-teal">
                  عرض مشاريعي
                </Button>
              </Link>
            </div>
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default ClientOffersPage;
