import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { OfferStatus } from '@prisma/client';

// GET /api/offers/[id] - جلب عرض واحد
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const offer = await prisma.offer.findUnique({
      where: { id: params.id },
      include: {
        project: {
          include: {
            client: {
              select: {
                id: true,
                name: true,
                email: true,
                phone: true,
                avatar: true,
                location: true,
              },
            },
          },
        },
        craftsman: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
            avatar: true,
            location: true,
            craftsmanProfile: {
              select: {
                rating: true,
                completedProjects: true,
                skills: true,
                experience: true,
              },
            },
          },
        },
        client: {
          select: {
            id: true,
            name: true,
            email: true,
            avatar: true,
          },
        },
      },
    });

    if (!offer) {
      return NextResponse.json(
        { error: 'العرض غير موجود' },
        { status: 404 }
      );
    }

    return NextResponse.json(offer);
  } catch (error) {
    console.error('Error fetching offer:', error);
    return NextResponse.json(
      { error: 'خطأ في جلب العرض' },
      { status: 500 }
    );
  }
}

// PUT /api/offers/[id] - تحديث عرض
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const { status, price, duration, description, materials, warranty } = body;

    const offer = await prisma.offer.findUnique({
      where: { id: params.id },
      include: {
        project: true,
        craftsman: true,
        client: true,
      },
    });

    if (!offer) {
      return NextResponse.json(
        { error: 'العرض غير موجود' },
        { status: 404 }
      );
    }

    const updatedOffer = await prisma.offer.update({
      where: { id: params.id },
      data: {
        ...(status && { status }),
        ...(price && { price: parseFloat(price) }),
        ...(duration && { duration }),
        ...(description && { description }),
        ...(materials && { materials }),
        ...(warranty && { warranty }),
      },
      include: {
        project: true,
        craftsman: true,
        client: true,
      },
    });

    // إنشاء إشعارات حسب حالة العرض
    if (status === OfferStatus.ACCEPTED) {
      // إشعار للحرفي
      await prisma.notification.create({
        data: {
          userId: offer.craftsmanId,
          type: 'OFFER_ACCEPTED',
          title: 'تم قبول عرضك',
          message: `تم قبول عرضك على مشروع ${offer.project.title}`,
          actionUrl: `/craftsman/offers`,
        },
      });

      // تحديث حالة المشروع
      await prisma.project.update({
        where: { id: offer.projectId },
        data: { status: 'IN_PROGRESS' },
      });

      // رفض باقي العروض
      await prisma.offer.updateMany({
        where: {
          projectId: offer.projectId,
          id: { not: params.id },
          status: OfferStatus.PENDING,
        },
        data: { status: OfferStatus.REJECTED },
      });
    } else if (status === OfferStatus.REJECTED) {
      // إشعار للحرفي
      await prisma.notification.create({
        data: {
          userId: offer.craftsmanId,
          type: 'OFFER_REJECTED',
          title: 'تم رفض عرضك',
          message: `تم رفض عرضك على مشروع ${offer.project.title}`,
          actionUrl: `/craftsman/offers`,
        },
      });
    }

    return NextResponse.json(updatedOffer);
  } catch (error) {
    console.error('Error updating offer:', error);
    return NextResponse.json(
      { error: 'خطأ في تحديث العرض' },
      { status: 500 }
    );
  }
}

// DELETE /api/offers/[id] - حذف عرض
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.offer.delete({
      where: { id: params.id },
    });

    return NextResponse.json({ message: 'تم حذف العرض بنجاح' });
  } catch (error) {
    console.error('Error deleting offer:', error);
    return NextResponse.json(
      { error: 'خطأ في حذف العرض' },
      { status: 500 }
    );
  }
}
