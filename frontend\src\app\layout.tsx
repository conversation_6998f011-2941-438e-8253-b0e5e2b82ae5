import type { <PERSON>ada<PERSON> } from "next";
import { Cairo } from "next/font/google";
import "./globals.css";
import { dir } from 'next-i18next/dist/commonjs/config/defaultConfig';

const cairo = Cairo({
  variable: "--font-cairo",
  subsets: ["arabic", "latin"],
  weight: ["400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "Dozan - Connect with Skilled Craftsmen",
  description: "Find skilled craftsmen for your projects or offer your services as a craftsman",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body
        className={`${cairo.variable} font-cairo antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
