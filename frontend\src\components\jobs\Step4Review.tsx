'use client';

import React from 'react';
import { useJobForm } from '@/context/JobFormContext';
import { formatDate } from '@/lib/utils';

const Step4Review = () => {
  const { formData } = useJobForm();



  // Get category name from ID
  const getCategoryName = (categoryId: string) => {
    const categories: Record<string, string> = {
      'carpentry': 'النجارة',
      'plumbing': 'السباكة',
      'electrical': 'الكهرباء',
      'painting': 'الدهان',
      'construction': 'البناء',
      'hvac': 'التكييف',
      'metalwork': 'الحدادة',
      'landscaping': 'تنسيق الحدائق',
      'other': 'أخرى',
    };

    return categories[categoryId] || categoryId;
  };

  return (
    <div>
      <h2 className="text-xl font-bold text-navy mb-6">مراجعة تفاصيل المشروع</h2>

      <div className="space-y-8">
        <div className="bg-beige bg-opacity-50 p-4 rounded-md">
          <p className="text-gray-700 mb-4">
            يرجى مراجعة تفاصيل مشروعك بعناية قبل النشر. بعد النشر، سيتمكن الحرفيون من الاطلاع على مشروعك وتقديم عروضهم.
          </p>
        </div>

        <div className="border border-gray-200 rounded-md overflow-hidden">
          <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h3 className="text-lg font-medium text-navy">التفاصيل الأساسية</h3>
          </div>
          <div className="p-4 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">عنوان المشروع</h4>
                <p className="text-gray-900">{formData.title || 'غير محدد'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">فئة المشروع</h4>
                <p className="text-gray-900">{getCategoryName(formData.category) || 'غير محدد'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">الموقع</h4>
                <p className="text-gray-900">{formData.location || 'غير محدد'}</p>
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">وصف المشروع</h4>
              <p className="text-gray-900 whitespace-pre-line">{formData.description || 'غير محدد'}</p>
            </div>
          </div>
        </div>

        <div className="border border-gray-200 rounded-md overflow-hidden">
          <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h3 className="text-lg font-medium text-navy">المتطلبات والمواصفات</h3>
          </div>
          <div className="p-4 space-y-4">
            <div>
              <h4 className="text-sm font-medium text-gray-500">متطلبات المشروع</h4>
              <p className="text-gray-900 whitespace-pre-line">{formData.requirements || 'غير محدد'}</p>
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">المهارات المطلوبة</h4>
              {formData.skills.length > 0 ? (
                <div className="flex flex-wrap gap-2 mt-1">
                  {formData.skills.map((skill, index) => (
                    <span key={index} className="bg-skyblue px-3 py-1 rounded-full text-navy text-sm">
                      {skill}
                    </span>
                  ))}
                </div>
              ) : (
                <p className="text-gray-900">غير محدد</p>
              )}
            </div>
            <div>
              <h4 className="text-sm font-medium text-gray-500">المرفقات</h4>
              {formData.attachments.length > 0 ? (
                <ul className="list-disc list-inside text-gray-900">
                  {formData.attachments.map((file, index) => (
                    <li key={index}>{file}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-gray-900">لا توجد مرفقات</p>
              )}
            </div>
          </div>
        </div>

        <div className="border border-gray-200 rounded-md overflow-hidden">
          <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <h3 className="text-lg font-medium text-navy">الميزانية والجدول الزمني</h3>
          </div>
          <div className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-gray-500">ميزانية المشروع</h4>
                <p className="text-gray-900">{formData.budget ? `${formData.budget.toLocaleString()} ل.س` : 'غير محدد'}</p>
              </div>
              <div>
                <h4 className="text-sm font-medium text-gray-500">الموعد النهائي</h4>
                <p className="text-gray-900">{formatDate(formData.deadline)}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-navy bg-opacity-5 p-4 rounded-md">
          <div className="flex items-start">
            <div className="flex-shrink-0 mt-0.5">
              <svg className="h-5 w-5 text-navy" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="mr-3">
              <p className="text-sm text-navy">
                بالضغط على "نشر المشروع"، أنت توافق على شروط وأحكام منصة دوزان وسياسة الخصوصية.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Step4Review;
