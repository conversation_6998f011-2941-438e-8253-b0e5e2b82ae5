import nodemailer from 'nodemailer';

// إعداد خدمة البريد الإلكتروني
const transporter = nodemailer.createTransporter({
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASSWORD,
  },
});

// قوالب البريد الإلكتروني
const emailTemplates = {
  verification: {
    subject: 'تأكيد البريد الإلكتروني - منصة دوزان',
    html: (name: string, verificationLink: string) => `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>تأكيد البريد الإلكتروني</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #1e3a8a, #0891b2); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }
          .button { display: inline-block; background: linear-gradient(135deg, #1e3a8a, #0891b2); color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; color: #6b7280; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔨 دوزان</h1>
            <p>منصة ربط العملاء بالحرفيين</p>
          </div>
          <div class="content">
            <h2>مرحباً ${name}!</h2>
            <p>شكراً لانضمامك إلى منصة دوزان. لإكمال تسجيلك، يرجى تأكيد بريدك الإلكتروني بالنقر على الرابط أدناه:</p>
            <div style="text-align: center;">
              <a href="${verificationLink}" class="button">تأكيد البريد الإلكتروني</a>
            </div>
            <p>إذا لم تقم بإنشاء حساب على منصة دوزان، يرجى تجاهل هذا البريد.</p>
            <p>هذا الرابط صالح لمدة 24 ساعة فقط.</p>
          </div>
          <div class="footer">
            <p>© 2024 دوزان. جميع الحقوق محفوظة.</p>
            <p>إذا كان لديك أي استفسار، تواصل معنا على: <EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `
  },

  passwordReset: {
    subject: 'إعادة تعيين كلمة المرور - منصة دوزان',
    html: (name: string, resetLink: string) => `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>إعادة تعيين كلمة المرور</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #dc2626, #ea580c); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }
          .button { display: inline-block; background: linear-gradient(135deg, #dc2626, #ea580c); color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; color: #6b7280; }
          .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 5px; margin: 15px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔨 دوزان</h1>
            <p>إعادة تعيين كلمة المرور</p>
          </div>
          <div class="content">
            <h2>مرحباً ${name}!</h2>
            <p>تلقينا طلباً لإعادة تعيين كلمة المرور لحسابك على منصة دوزان.</p>
            <div class="warning">
              <strong>⚠️ تنبيه أمني:</strong> إذا لم تطلب إعادة تعيين كلمة المرور، يرجى تجاهل هذا البريد وتأكد من أمان حسابك.
            </div>
            <p>لإعادة تعيين كلمة المرور، انقر على الرابط أدناه:</p>
            <div style="text-align: center;">
              <a href="${resetLink}" class="button">إعادة تعيين كلمة المرور</a>
            </div>
            <p>هذا الرابط صالح لمدة ساعة واحدة فقط لأسباب أمنية.</p>
          </div>
          <div class="footer">
            <p>© 2024 دوزان. جميع الحقوق محفوظة.</p>
            <p>إذا كان لديك أي استفسار، تواصل معنا على: <EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `
  },

  welcome: {
    subject: 'مرحباً بك في منصة دوزان! 🎉',
    html: (name: string, role: string) => `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>مرحباً بك في دوزان</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #059669, #0891b2); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }
          .button { display: inline-block; background: linear-gradient(135deg, #059669, #0891b2); color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; color: #6b7280; }
          .feature { background: #f0f9ff; padding: 15px; margin: 10px 0; border-radius: 5px; border-right: 4px solid #0891b2; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 مرحباً بك في دوزان!</h1>
            <p>منصة ربط العملاء بالحرفيين المؤهلين</p>
          </div>
          <div class="content">
            <h2>أهلاً وسهلاً ${name}!</h2>
            <p>نحن سعداء لانضمامك إلى منصة دوزان كـ${role === 'CLIENT' ? 'عميل' : 'حرفي'}.</p>
            
            ${role === 'CLIENT' ? `
              <h3>كعميل، يمكنك:</h3>
              <div class="feature">🏗️ نشر مشاريعك والحصول على عروض من حرفيين مؤهلين</div>
              <div class="feature">⭐ تقييم الحرفيين ومشاركة تجربتك</div>
              <div class="feature">💬 التواصل المباشر مع الحرفيين</div>
              <div class="feature">🔒 ضمان الجودة والأمان في جميع المعاملات</div>
            ` : `
              <h3>كحرفي، يمكنك:</h3>
              <div class="feature">🔍 تصفح المشاريع المتاحة في مجال تخصصك</div>
              <div class="feature">💰 تقديم عروضك وتحديد أسعارك</div>
              <div class="feature">🎨 عرض أعمالك السابقة في معرض الأعمال</div>
              <div class="feature">📈 بناء سمعتك من خلال التقييمات الإيجابية</div>
            `}
            
            <div style="text-align: center;">
              <a href="${process.env.NEXTAUTH_URL}" class="button">ابدأ الآن</a>
            </div>
          </div>
          <div class="footer">
            <p>© 2024 دوزان. جميع الحقوق محفوظة.</p>
            <p>تحتاج مساعدة؟ تواصل معنا على: <EMAIL></p>
          </div>
        </div>
      </body>
      </html>
    `
  },

  notification: {
    subject: (type: string) => {
      const subjects = {
        'new_offer': 'عرض جديد على مشروعك - دوزان',
        'offer_accepted': 'تم قبول عرضك - دوزان',
        'offer_rejected': 'تم رفض عرضك - دوزان',
        'project_completed': 'تم إكمال المشروع - دوزان',
        'new_message': 'رسالة جديدة - دوزان',
        'new_review': 'تقييم جديد - دوزان'
      };
      return subjects[type as keyof typeof subjects] || 'إشعار من دوزان';
    },
    html: (name: string, title: string, message: string, actionLink?: string, actionText?: string) => `
      <!DOCTYPE html>
      <html dir="rtl" lang="ar">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${title}</title>
        <style>
          body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; direction: rtl; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #1e3a8a, #0891b2); color: white; padding: 30px; text-align: center; border-radius: 10px 10px 0 0; }
          .content { background: white; padding: 30px; border: 1px solid #e5e7eb; }
          .button { display: inline-block; background: linear-gradient(135deg, #1e3a8a, #0891b2); color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; margin: 20px 0; }
          .footer { background: #f9fafb; padding: 20px; text-align: center; border-radius: 0 0 10px 10px; color: #6b7280; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🔔 ${title}</h1>
          </div>
          <div class="content">
            <h2>مرحباً ${name}!</h2>
            <p>${message}</p>
            ${actionLink && actionText ? `
              <div style="text-align: center;">
                <a href="${actionLink}" class="button">${actionText}</a>
              </div>
            ` : ''}
          </div>
          <div class="footer">
            <p>© 2024 دوزان. جميع الحقوق محفوظة.</p>
          </div>
        </div>
      </body>
      </html>
    `
  }
};

// إرسال بريد تأكيد البريد الإلكتروني
export async function sendVerificationEmail(email: string, name: string, token: string) {
  const verificationLink = `${process.env.NEXTAUTH_URL}/auth/verify-email?token=${token}`;
  
  const mailOptions = {
    from: `"دوزان" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
    to: email,
    subject: emailTemplates.verification.subject,
    html: emailTemplates.verification.html(name, verificationLink)
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log('تم إرسال بريد التأكيد إلى:', email);
    return true;
  } catch (error) {
    console.error('خطأ في إرسال بريد التأكيد:', error);
    return false;
  }
}

// إرسال بريد إعادة تعيين كلمة المرور
export async function sendPasswordResetEmail(email: string, name: string, token: string) {
  const resetLink = `${process.env.NEXTAUTH_URL}/auth/reset-password?token=${token}`;
  
  const mailOptions = {
    from: `"دوزان" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
    to: email,
    subject: emailTemplates.passwordReset.subject,
    html: emailTemplates.passwordReset.html(name, resetLink)
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log('تم إرسال بريد إعادة تعيين كلمة المرور إلى:', email);
    return true;
  } catch (error) {
    console.error('خطأ في إرسال بريد إعادة تعيين كلمة المرور:', error);
    return false;
  }
}

// إرسال بريد الترحيب
export async function sendWelcomeEmail(email: string, name: string, role: string) {
  const mailOptions = {
    from: `"دوزان" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
    to: email,
    subject: emailTemplates.welcome.subject,
    html: emailTemplates.welcome.html(name, role)
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log('تم إرسال بريد الترحيب إلى:', email);
    return true;
  } catch (error) {
    console.error('خطأ في إرسال بريد الترحيب:', error);
    return false;
  }
}

// إرسال إشعار بالبريد الإلكتروني
export async function sendNotificationEmail(
  email: string, 
  name: string, 
  type: string, 
  title: string, 
  message: string, 
  actionLink?: string, 
  actionText?: string
) {
  const mailOptions = {
    from: `"دوزان" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
    to: email,
    subject: emailTemplates.notification.subject(type),
    html: emailTemplates.notification.html(name, title, message, actionLink, actionText)
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log('تم إرسال إشعار بالبريد الإلكتروني إلى:', email);
    return true;
  } catch (error) {
    console.error('خطأ في إرسال الإشعار بالبريد الإلكتروني:', error);
    return false;
  }
}
