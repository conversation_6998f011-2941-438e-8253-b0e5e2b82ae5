'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/Button';
import supabase from '@/lib/supabase';

export default function TestDBPage() {
  const [users, setUsers] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [bids, setBids] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testConnection = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('Testing Supabase connection...');

      // اختبار الاتصال بجدول المستخدمين
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('*')
        .limit(5);

      if (usersError) {
        console.error('Users error:', usersError);
        throw new Error(`Users table error: ${usersError.message}`);
      }

      console.log('Users data:', usersData);
      setUsers(usersData || []);

      // اختبار الاتصال بجدول المشاريع
      const { data: projectsData, error: projectsError } = await supabase
        .from('projects')
        .select('*')
        .limit(5);

      if (projectsError) {
        console.error('Projects error:', projectsError);
        throw new Error(`Projects table error: ${projectsError.message}`);
      }

      console.log('Projects data:', projectsData);
      setProjects(projectsData || []);

      // اختبار الاتصال بجدول العروض
      const { data: bidsData, error: bidsError } = await supabase
        .from('bids')
        .select('*')
        .limit(5);

      if (bidsError) {
        console.error('Bids error:', bidsError);
        throw new Error(`Bids table error: ${bidsError.message}`);
      }

      console.log('Bids data:', bidsData);
      setBids(bidsData || []);

    } catch (err: any) {
      console.error('Database test error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const seedDatabase = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('Seeding database...');

      // إضافة مستخدمين تجريبيين
      const { data: userData, error: userError } = await supabase
        .from('users')
        .upsert([
          {
            id: '11111111-1111-1111-1111-111111111111',
            name: 'أحمد محمد',
            email: '<EMAIL>',
            phone: '+963991234567',
            password_hash: 'hashed_password_1',
            role: 'CLIENT',
            location: 'دمشق'
          },
          {
            id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
            name: 'محمد النجار',
            email: '<EMAIL>',
            phone: '+963996789012',
            password_hash: 'hashed_password_2',
            role: 'CRAFTSMAN',
            location: 'دمشق'
          }
        ])
        .select();

      if (userError) throw userError;
      console.log('Users created:', userData);

      // إضافة مشروع تجريبي
      const { data: projectData, error: projectError } = await supabase
        .from('projects')
        .upsert([
          {
            id: 'p1111111-1111-1111-1111-111111111111',
            title: 'تجديد مطبخ عصري',
            description: 'تجديد مطبخ كامل بتصميم عصري',
            category: 'نجارة',
            location: 'دمشق',
            budget_min: 200000,
            budget_max: 350000,
            deadline: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            priority: 'HIGH',
            client_id: '11111111-1111-1111-1111-111111111111',
            status: 'OPEN'
          }
        ])
        .select();

      if (projectError) throw projectError;
      console.log('Project created:', projectData);

      // إضافة عرض تجريبي
      const { data: bidData, error: bidError } = await supabase
        .from('bids')
        .upsert([
          {
            id: 'b1111111-1111-1111-1111-111111111111',
            project_id: 'p1111111-1111-1111-1111-111111111111',
            craftsman_id: 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
            amount: 280000,
            description: 'سأقوم بتجديد المطبخ بالكامل باستخدام أفضل المواد',
            estimated_duration: '12-15 يوم',
            materials_included: true,
            warranty_period: '24 شهر',
            status: 'PENDING'
          }
        ])
        .select();

      if (bidError) throw bidError;
      console.log('Bid created:', bidData);

      alert('تم إنشاء البيانات التجريبية بنجاح!');
      await testConnection();

    } catch (err: any) {
      console.error('Seeding error:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-navy mb-8">اختبار قاعدة البيانات</h1>
        
        <div className="space-y-6">
          <div className="flex space-x-4 space-x-reverse">
            <Button 
              onClick={testConnection}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {loading ? 'جاري الاختبار...' : 'اختبار الاتصال'}
            </Button>
            
            <Button 
              onClick={seedDatabase}
              disabled={loading}
              className="bg-green-600 hover:bg-green-700"
            >
              {loading ? 'جاري الإنشاء...' : 'إنشاء بيانات تجريبية'}
            </Button>
          </div>

          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
              <strong>خطأ:</strong> {error}
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-bold mb-4">المستخدمين ({users.length})</h3>
              <div className="space-y-2">
                {users.map((user) => (
                  <div key={user.id} className="text-sm">
                    <strong>{user.name}</strong> - {user.role}
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-bold mb-4">المشاريع ({projects.length})</h3>
              <div className="space-y-2">
                {projects.map((project) => (
                  <div key={project.id} className="text-sm">
                    <strong>{project.title}</strong> - {project.status}
                  </div>
                ))}
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-bold mb-4">العروض ({bids.length})</h3>
              <div className="space-y-2">
                {bids.map((bid) => (
                  <div key={bid.id} className="text-sm">
                    <strong>{bid.amount?.toLocaleString()} ل.س</strong> - {bid.status}
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="bg-white p-6 rounded-lg shadow">
            <h3 className="text-lg font-bold mb-4">معلومات الاتصال</h3>
            <div className="space-y-2 text-sm">
              <div><strong>Supabase URL:</strong> {process.env.NEXT_PUBLIC_SUPABASE_URL || 'غير محدد'}</div>
              <div><strong>API Key:</strong> {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'موجود' : 'غير موجود'}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
