module.exports = {

"[externals]/next/dist/compiled/next-server/app-route.runtime.dev.js [external] (next/dist/compiled/next-server/app-route.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/next-server/app-route.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page.runtime.dev.js [external] (next/dist/compiled/next-server/app-page.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/compiled/next-server/app-page.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, m: module, e: exports, t: __turbopack_require_real__ } = __turbopack_context__;
{
const mod = __turbopack_external_require__("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/lib/mock-data.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
// بيانات تجريبية للتطوير بدون قاعدة بيانات
__turbopack_esm__({
    "getNotificationsByUserId": (()=>getNotificationsByUserId),
    "getOffersByCraftsmanId": (()=>getOffersByCraftsmanId),
    "getOffersByProjectId": (()=>getOffersByProjectId),
    "getProjectsByUserId": (()=>getProjectsByUserId),
    "getUserByEmail": (()=>getUserByEmail),
    "getUserById": (()=>getUserById),
    "mockNotifications": (()=>mockNotifications),
    "mockOffers": (()=>mockOffers),
    "mockProjects": (()=>mockProjects),
    "mockUsers": (()=>mockUsers)
});
const mockUsers = [
    {
        id: 'admin',
        email: '<EMAIL>',
        name: 'مدير النظام',
        role: 'ADMIN',
        phone: '+963 11 123 4567',
        location: 'دمشق، سوريا',
        bio: 'مدير منصة دوزان',
        isActive: true,
        isVerified: true,
        createdAt: new Date('2024-01-01')
    },
    {
        id: 'client',
        email: '<EMAIL>',
        name: 'أحمد محمد',
        role: 'CLIENT',
        phone: '+963 123 456 789',
        location: 'دمشق - المزة',
        bio: 'عميل يبحث عن حرفيين مؤهلين',
        isActive: true,
        isVerified: true,
        createdAt: new Date('2024-01-15')
    },
    {
        id: 'craftsman1',
        email: '<EMAIL>',
        name: 'محمد النجار',
        role: 'CRAFTSMAN',
        phone: '+963 987 654 321',
        location: 'دمشق - كفرسوسة',
        bio: 'نجار محترف مع خبرة 10 سنوات',
        isActive: true,
        isVerified: true,
        createdAt: new Date('2024-01-10')
    },
    {
        id: 'craftsman2',
        email: '<EMAIL>',
        name: 'سارة الكهربائية',
        role: 'CRAFTSMAN',
        phone: '+963 555 123 456',
        location: 'حلب - الفرقان',
        bio: 'كهربائية متخصصة في الأنظمة الذكية',
        isActive: true,
        isVerified: true,
        createdAt: new Date('2024-01-20')
    }
];
const mockProjects = [
    {
        id: 'project1',
        title: 'تجديد مطبخ منزلي',
        description: 'أحتاج إلى تجديد مطبخ منزلي بالكامل مع تغيير الخزائن والأرضية',
        category: 'نجارة',
        budget: '₺6,000 - ₺8,000',
        deadline: new Date('2024-03-15'),
        location: 'دمشق - المزة',
        priority: 'HIGH',
        status: 'OPEN',
        clientId: 'client',
        images: [],
        materials: 'متوفرة جزئياً',
        workType: 'تجديد',
        requirements: 'يفضل استخدام خشب عالي الجودة',
        views: 15,
        featured: true,
        createdAt: new Date('2024-02-01')
    },
    {
        id: 'project2',
        title: 'تركيب نظام إضاءة ذكي',
        description: 'تركيب نظام إضاءة ذكي في المنزل مع إمكانية التحكم عبر الهاتف',
        category: 'كهرباء',
        budget: '₺4,000 - ₺6,000',
        deadline: new Date('2024-03-10'),
        location: 'دمشق - المالكي',
        priority: 'MEDIUM',
        status: 'OPEN',
        clientId: 'client',
        images: [],
        materials: 'غير متوفرة',
        workType: 'تركيب',
        requirements: 'نظام متوافق مع الهواتف الذكية',
        views: 8,
        featured: false,
        createdAt: new Date('2024-02-05')
    }
];
const mockOffers = [
    {
        id: 'offer1',
        projectId: 'project1',
        craftsmanId: 'craftsman1',
        amount: 7000,
        currency: 'TRY',
        description: 'يمكنني تنفيذ هذا المشروع بجودة عالية وفي الوقت المحدد',
        estimatedDuration: 14,
        status: 'PENDING',
        createdAt: new Date('2024-02-02')
    },
    {
        id: 'offer2',
        projectId: 'project2',
        craftsmanId: 'craftsman2',
        amount: 5000,
        currency: 'TRY',
        description: 'متخصصة في أنظمة الإضاءة الذكية مع ضمان سنة كاملة',
        estimatedDuration: 7,
        status: 'PENDING',
        createdAt: new Date('2024-02-06')
    }
];
const mockNotifications = [
    {
        id: 'notif1',
        userId: 'client',
        title: 'عرض جديد على مشروعك',
        message: 'تلقيت عرضاً جديداً من محمد النجار على مشروع تجديد المطبخ',
        type: 'INFO',
        isRead: false,
        createdAt: new Date('2024-02-02')
    },
    {
        id: 'notif2',
        userId: 'craftsman1',
        title: 'مشروع جديد متاح',
        message: 'يوجد مشروع نجارة جديد في منطقتك',
        type: 'INFO',
        isRead: true,
        createdAt: new Date('2024-02-01')
    }
];
const getUserById = (id)=>{
    return mockUsers.find((user)=>user.id === id);
};
const getUserByEmail = (email)=>{
    return mockUsers.find((user)=>user.email === email);
};
const getProjectsByUserId = (userId)=>{
    return mockProjects.filter((project)=>project.clientId === userId || project.craftsmanId === userId);
};
const getOffersByProjectId = (projectId)=>{
    return mockOffers.filter((offer)=>offer.projectId === projectId);
};
const getOffersByCraftsmanId = (craftsmanId)=>{
    return mockOffers.filter((offer)=>offer.craftsmanId === craftsmanId);
};
const getNotificationsByUserId = (userId)=>{
    return mockNotifications.filter((notification)=>notification.userId === userId);
};
}}),
"[project]/src/app/api/projects/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, x: __turbopack_external_require__, y: __turbopack_external_import__, z: __turbopack_require_stub__ } = __turbopack_context__;
{
__turbopack_esm__({
    "GET": (()=>GET),
    "POST": (()=>POST)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_import__("[project]/src/lib/mock-data.ts [app-route] (ecmascript)");
;
;
async function GET(request) {
    try {
        const { searchParams } = new URL(request.url);
        const category = searchParams.get('category');
        const location = searchParams.get('location');
        const status = searchParams.get('status');
        const clientId = searchParams.get('clientId');
        const craftsmanId = searchParams.get('craftsmanId');
        let filteredProjects = [
            ...__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockProjects"]
        ];
        // تطبيق الفلاتر
        if (category) {
            filteredProjects = filteredProjects.filter((p)=>p.category === category);
        }
        if (location) {
            filteredProjects = filteredProjects.filter((p)=>p.location.includes(location));
        }
        if (status) {
            filteredProjects = filteredProjects.filter((p)=>p.status === status);
        }
        if (clientId) {
            filteredProjects = filteredProjects.filter((p)=>p.clientId === clientId);
        }
        if (craftsmanId) {
            filteredProjects = filteredProjects.filter((p)=>p.craftsmanId === craftsmanId);
        }
        // إضافة بيانات العميل لكل مشروع
        const projectsWithClient = filteredProjects.map((project)=>{
            const client = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockUsers"].find((user)=>user.id === project.clientId);
            const craftsman = project.craftsmanId ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockUsers"].find((user)=>user.id === project.craftsmanId) : null;
            return {
                ...project,
                client: client ? {
                    id: client.id,
                    name: client.name,
                    location: client.location,
                    avatar: client.avatar
                } : null,
                craftsman: craftsman ? {
                    id: craftsman.id,
                    name: craftsman.name,
                    location: craftsman.location,
                    avatar: craftsman.avatar
                } : null
            };
        });
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            projects: projectsWithClient,
            total: projectsWithClient.length
        });
    } catch (error) {
        console.error('Error fetching projects:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'خطأ في جلب المشاريع'
        }, {
            status: 500
        });
    }
}
async function POST(request) {
    try {
        const body = await request.json();
        const { title, description, category, budget, deadline, location, priority = 'MEDIUM', clientId, materials, workType, requirements, images = [] } = body;
        // التحقق من البيانات المطلوبة
        if (!title || !description || !category || !budget || !deadline || !location || !clientId) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'جميع الحقول المطلوبة يجب ملؤها'
            }, {
                status: 400
            });
        }
        // التحقق من وجود العميل
        const client = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockUsers"].find((user)=>user.id === clientId);
        if (!client || client.role !== 'CLIENT') {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'العميل غير موجود'
            }, {
                status: 404
            });
        }
        // إنشاء مشروع جديد
        const newProject = {
            id: `project_${Date.now()}`,
            title,
            description,
            category,
            budget,
            deadline: new Date(deadline),
            location,
            priority,
            status: 'OPEN',
            clientId,
            images,
            materials,
            workType,
            requirements,
            views: 0,
            featured: false,
            createdAt: new Date()
        };
        // إضافة المشروع للبيانات التجريبية (في التطبيق الحقيقي سيتم حفظه في قاعدة البيانات)
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$mock$2d$data$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["mockProjects"].push(newProject);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            project: newProject,
            message: 'تم إنشاء المشروع بنجاح'
        }, {
            status: 201
        });
    } catch (error) {
        console.error('Error creating project:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'خطأ في إنشاء المشروع'
        }, {
            status: 500
        });
    }
}
}}),
"[project]/ (server-utils)": ((__turbopack_context__) => {

var { r: __turbopack_require__, f: __turbopack_module_context__, i: __turbopack_import__, s: __turbopack_esm__, v: __turbopack_export_value__, n: __turbopack_export_namespace__, c: __turbopack_cache__, M: __turbopack_modules__, l: __turbopack_load__, j: __turbopack_dynamic__, P: __turbopack_resolve_absolute_path__, U: __turbopack_relative_url__, R: __turbopack_resolve_module_id_path__, b: __turbopack_worker_blob_url__, g: global, __dirname, t: __turbopack_require_real__ } = __turbopack_context__;
{
}}),

};

//# sourceMappingURL=%5Broot%20of%20the%20server%5D__fb1461._.js.map