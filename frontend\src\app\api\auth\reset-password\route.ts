import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { 
  hashPassword,
  validatePasswordStrength,
  getClientIP,
  getUserAgent,
  checkRateLimit
} from '@/lib/security';

export async function POST(request: NextRequest) {
  try {
    // التحقق من معدل الطلبات
    const clientIP = getClientIP(request);
    const rateLimit = checkRateLimit(`reset_password_${clientIP}`, 5, 900000); // 5 طلبات كل 15 دقيقة
    
    if (!rateLimit.allowed) {
      return NextResponse.json(
        { 
          error: 'تم تجاوز الحد المسموح من المحاولات. يرجى المحاولة بعد 15 دقيقة',
          resetTime: rateLimit.resetTime
        },
        { status: 429 }
      );
    }

    const { token, password } = await request.json();

    if (!token || !password) {
      return NextResponse.json(
        { error: 'الرمز وكلمة المرور الجديدة مطلوبان' },
        { status: 400 }
      );
    }

    // التحقق من قوة كلمة المرور
    const passwordValidation = validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        { error: 'كلمة المرور ضعيفة', details: passwordValidation.errors },
        { status: 400 }
      );
    }

    // البحث عن المستخدم بالرمز
    const user = await prisma.user.findFirst({
      where: {
        passwordResetToken: token,
        passwordResetExpiry: {
          gt: new Date() // التأكد من أن الرمز لم ينته
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'رمز إعادة التعيين غير صحيح أو منتهي الصلاحية' },
        { status: 400 }
      );
    }

    // التحقق من حالة الحساب
    if (!user.isActive) {
      return NextResponse.json(
        { error: 'الحساب معطل. يرجى التواصل مع الدعم' },
        { status: 403 }
      );
    }

    // تشفير كلمة المرور الجديدة
    const hashedPassword = await hashPassword(password);

    // تحديث كلمة المرور وإزالة رمز إعادة التعيين
    await prisma.user.update({
      where: { id: user.id },
      data: {
        password: hashedPassword,
        passwordResetToken: null,
        passwordResetExpiry: null,
        passwordChangedAt: new Date(),
        loginAttempts: 0 // إعادة تعيين محاولات تسجيل الدخول الفاشلة
      }
    });

    // تسجيل النشاط
    await prisma.auditLog.create({
      data: {
        userId: user.id,
        action: 'PASSWORD_RESET_COMPLETED',
        entity: 'User',
        entityId: user.id,
        ipAddress: clientIP,
        userAgent: getUserAgent(request)
      }
    });

    // إبطال جميع الجلسات الحالية (اختياري)
    // يمكن تنفيذ هذا عن طريق تحديث tokenVersion أو إضافة blacklist للـ tokens

    return NextResponse.json({
      message: 'تم تغيير كلمة المرور بنجاح. يمكنك الآن تسجيل الدخول بكلمة المرور الجديدة'
    });

  } catch (error) {
    console.error('خطأ في إعادة تعيين كلمة المرور:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}

// GET endpoint للتحقق من صحة رمز إعادة التعيين
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { error: 'رمز إعادة التعيين مطلوب' },
        { status: 400 }
      );
    }

    // التحقق من صحة الرمز دون إعادة تعيين كلمة المرور
    const user = await prisma.user.findFirst({
      where: {
        passwordResetToken: token,
        passwordResetExpiry: {
          gt: new Date()
        }
      },
      select: {
        id: true,
        name: true,
        email: true,
        isActive: true
      }
    });

    if (!user) {
      return NextResponse.json(
        { error: 'رمز إعادة التعيين غير صحيح أو منتهي الصلاحية' },
        { status: 400 }
      );
    }

    if (!user.isActive) {
      return NextResponse.json(
        { error: 'الحساب معطل' },
        { status: 403 }
      );
    }

    return NextResponse.json({
      valid: true,
      user: {
        name: user.name,
        email: user.email
      }
    });

  } catch (error) {
    console.error('خطأ في التحقق من رمز إعادة التعيين:', error);
    return NextResponse.json(
      { error: 'حدث خطأ في الخادم' },
      { status: 500 }
    );
  }
}
