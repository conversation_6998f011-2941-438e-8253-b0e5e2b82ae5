import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// دالة تنسيق التاريخ بشكل ثابت لتجنب مشاكل Hydration
export function formatDate(dateString: string): string {
  if (!dateString) return 'غير محدد';

  try {
    const date = new Date(dateString);

    // التحقق من صحة التاريخ
    if (isNaN(date.getTime())) {
      return 'تاريخ غير صحيح';
    }

    // تنسيق ثابت للتاريخ
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    // أسماء الشهور بالعربية
    const monthNames = [
      'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
      'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    return `${day} ${monthNames[month - 1]} ${year}`;
  } catch (error) {
    return 'تاريخ غير صحيح';
  }
}

// دالة لتنسيق التاريخ النسبي (منذ كم يوم)
export function formatRelativeDate(dateString: string): string {
  if (!dateString) return 'غير محدد';

  try {
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now.getTime() - date.getTime();
    const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

    if (diffInDays === 0) {
      return 'اليوم';
    } else if (diffInDays === 1) {
      return 'أمس';
    } else if (diffInDays < 7) {
      return `منذ ${diffInDays} أيام`;
    } else if (diffInDays < 30) {
      const weeks = Math.floor(diffInDays / 7);
      return `منذ ${weeks} ${weeks === 1 ? 'أسبوع' : 'أسابيع'}`;
    } else if (diffInDays < 365) {
      const months = Math.floor(diffInDays / 30);
      return `منذ ${months} ${months === 1 ? 'شهر' : 'أشهر'}`;
    } else {
      const years = Math.floor(diffInDays / 365);
      return `منذ ${years} ${years === 1 ? 'سنة' : 'سنوات'}`;
    }
  } catch (error) {
    return 'تاريخ غير صحيح';
  }
}

// دالة لتنسيق الأرقام بالفواصل
export function formatNumber(num: number): string {
  return num.toLocaleString('ar-SA');
}

// دالة للتحقق من صحة البريد الإلكتروني
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

// دالة للتحقق من صحة رقم الهاتف السوري
export function isValidSyrianPhone(phone: string): boolean {
  const phoneRegex = /^(\+963|0)?[0-9]{9,10}$/;
  return phoneRegex.test(phone);
}
