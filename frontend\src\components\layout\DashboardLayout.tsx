'use client';

import { useState } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/Button';

interface DashboardLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
}

export default function DashboardLayout({ children, title, subtitle }: DashboardLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  
  // Mock user for development - will be replaced with real auth
  const mockUser = { name: 'مستخدم تجريبي', role: 'CLIENT' };
  
  const isClient = mockUser?.role === 'CLIENT';
  const isCraftsman = mockUser?.role === 'CRAFTSMAN';
  const isAdmin = mockUser?.role === 'ADMIN';

  const clientMenuItems = [
    { href: '/dashboard/client', label: 'لوحة التحكم', icon: '🏠' },
    { href: '/dashboard/client/projects', label: 'مشاريعي', icon: '📋' },
    { href: '/projects/create', label: 'إنشاء مشروع', icon: '➕' },
    { href: '/messages', label: 'الرسائل', icon: '💬' },
    { href: '/dashboard/client/profile', label: 'الملف الشخصي', icon: '👤' },
  ];

  const craftsmanMenuItems = [
    { href: '/dashboard/craftsman', label: 'لوحة التحكم', icon: '🏠' },
    { href: '/dashboard/craftsman/bids', label: 'عروضي', icon: '📋' },
    { href: '/dashboard/craftsman/projects', label: 'مشاريعي', icon: '🔨' },
    { href: '/dashboard/craftsman/portfolio', label: 'معرض الأعمال', icon: '🎨' },
    { href: '/messages', label: 'الرسائل', icon: '💬' },
    { href: '/dashboard/craftsman/profile', label: 'الملف الشخصي', icon: '👤' },
  ];

  const adminMenuItems = [
    { href: '/dashboard/admin', label: 'لوحة التحكم', icon: '🏠' },
    { href: '/dashboard/admin/users', label: 'المستخدمين', icon: '👥' },
    { href: '/dashboard/admin/projects', label: 'المشاريع', icon: '📋' },
    { href: '/dashboard/admin/reports', label: 'التقارير', icon: '📊' },
    { href: '/dashboard/admin/settings', label: 'الإعدادات', icon: '⚙️' },
  ];

  const menuItems = isClient ? clientMenuItems : isCraftsman ? craftsmanMenuItems : adminMenuItems;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="flex items-center justify-between px-6 py-4">
          <div className="flex items-center space-x-4 space-x-reverse">
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="lg:hidden p-2 rounded-md text-gray-600 hover:bg-gray-100"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            
            <Link href="/" className="flex items-center space-x-2 space-x-reverse">
              <div className="w-8 h-8 bg-gradient-to-r from-teal to-navy rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">د</span>
              </div>
              <span className="text-xl font-bold text-navy">دوزان</span>
            </Link>
          </div>

          <div className="flex items-center space-x-4 space-x-reverse">
            <div className="hidden md:block text-right">
              <p className="text-sm font-medium text-gray-900">{mockUser?.name}</p>
              <p className="text-xs text-gray-500">
                {isClient ? 'عميل' : isCraftsman ? 'حرفي' : 'مدير'}
              </p>
            </div>
            
            <div className="w-10 h-10 bg-gradient-to-r from-teal to-navy rounded-full flex items-center justify-center">
              <span className="text-white font-bold">
                {mockUser?.name?.charAt(0) || 'م'}
              </span>
            </div>

            <Button
              onClick={() => window.location.href = '/'}
              variant="outline"
              size="sm"
              className="text-red-600 border-red-200 hover:bg-red-50"
            >
              تسجيل الخروج
            </Button>
          </div>
        </div>
      </header>

      <div className="flex">
        {/* Sidebar */}
        <aside className={`
          fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out
          lg:translate-x-0 lg:static lg:inset-0
          ${sidebarOpen ? 'translate-x-0' : 'translate-x-full'}
        `}>
          <div className="flex flex-col h-full pt-20 lg:pt-0">
            <nav className="flex-1 px-4 py-6 space-y-2">
              {menuItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
                  onClick={() => setSidebarOpen(false)}
                >
                  <span className="text-xl">{item.icon}</span>
                  <span className="font-medium">{item.label}</span>
                </Link>
              ))}
            </nav>

            <div className="p-4 border-t">
              <Link
                href="/"
                className="flex items-center space-x-3 space-x-reverse px-4 py-3 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <span className="text-xl">🏠</span>
                <span className="font-medium">العودة للموقع</span>
              </Link>
            </div>
          </div>
        </aside>

        {/* Overlay for mobile */}
        {sidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setSidebarOpen(false)}
          />
        )}

        {/* Main Content */}
        <main className="flex-1 lg:mr-64">
          <div className="p-6">
            {/* Page Header */}
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-navy">{title}</h1>
              {subtitle && (
                <p className="text-gray-600 mt-2">{subtitle}</p>
              )}
            </div>

            {/* Page Content */}
            {children}
          </div>
        </main>
      </div>
    </div>
  );
}
