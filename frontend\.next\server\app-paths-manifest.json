{"/test-auth/page": "app/test-auth/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/test-api/page": "app/test-api/page.js", "/api/projects/[id]/route": "app/api/projects/[id]/route.js", "/api/offers/route": "app/api/offers/route.js", "/api/users/[id]/route": "app/api/users/[id]/route.js", "/api/projects/route": "app/api/projects/route.js", "/api/users/route": "app/api/users/route.js", "/test-dashboard/page": "app/test-dashboard/page.js", "/_not-found/page": "app/_not-found/page.js", "/test-upload/page": "app/test-upload/page.js", "/test-reviews/page": "app/test-reviews/page.js", "/project-summary/page": "app/project-summary/page.js", "/messages/page": "app/messages/page.js", "/page": "app/page.js", "/client/offers/page": "app/client/offers/page.js", "/dashboard/page": "app/dashboard/page.js", "/login/page": "app/login/page.js", "/notifications/page": "app/notifications/page.js", "/dashboard/profile/page": "app/dashboard/profile/page.js", "/auth/verify-email/page": "app/auth/verify-email/page.js", "/syria-simulation/page": "app/syria-simulation/page.js", "/deployment-guide/page": "app/deployment-guide/page.js"}