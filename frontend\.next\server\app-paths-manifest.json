{"/_not-found/page": "app/_not-found/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/auth/register/route": "app/api/auth/register/route.js", "/api/health/database/route": "app/api/health/database/route.js", "/api/notifications/[id]/route": "app/api/notifications/[id]/route.js", "/api/notifications/route": "app/api/notifications/route.js", "/api/offers/[id]/route": "app/api/offers/[id]/route.js", "/api/projects/[id]/route": "app/api/projects/[id]/route.js", "/api/offers/route": "app/api/offers/route.js", "/api/projects/route": "app/api/projects/route.js", "/api/seed/route": "app/api/seed/route.js", "/api/users/route": "app/api/users/route.js", "/api/users/[id]/route": "app/api/users/[id]/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/admin/projects/page": "app/admin/projects/page.js", "/about/page": "app/about/page.js", "/admin/dashboard/page": "app/admin/dashboard/page.js", "/admin/users/page": "app/admin/users/page.js", "/auth/error/page": "app/auth/error/page.js", "/client/dashboard/page": "app/client/dashboard/page.js", "/client/offers/page": "app/client/offers/page.js", "/client/payments/page": "app/client/payments/page.js", "/client/projects/page": "app/client/projects/page.js", "/craftsman/current-projects/page": "app/craftsman/current-projects/page.js", "/contact/page": "app/contact/page.js", "/craftsman/dashboard/page": "app/craftsman/dashboard/page.js", "/craftsman/offers/page": "app/craftsman/offers/page.js", "/craftsman/earnings/page": "app/craftsman/earnings/page.js", "/craftsman/projects/page": "app/craftsman/projects/page.js", "/craftsman/portfolio/page": "app/craftsman/portfolio/page.js", "/craftsmen/page": "app/craftsmen/page.js", "/craftsmen/[id]/page": "app/craftsmen/[id]/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/profile/page": "app/dashboard/profile/page.js", "/dashboard/projects/page": "app/dashboard/projects/page.js", "/faq/page": "app/faq/page.js", "/job-posted-success/page": "app/job-posted-success/page.js", "/jobs/page": "app/jobs/page.js", "/jobs/[id]/page": "app/jobs/[id]/page.js", "/login/page": "app/login/page.js", "/notifications/page": "app/notifications/page.js", "/messages/page": "app/messages/page.js", "/page": "app/page.js", "/privacy/page": "app/privacy/page.js", "/post-job/page": "app/post-job/page.js", "/profile/page": "app/profile/page.js", "/projects/[id]/page": "app/projects/[id]/page.js", "/projects/create/page": "app/projects/create/page.js", "/register/page": "app/register/page.js", "/terms/page": "app/terms/page.js", "/test-auth/page": "app/test-auth/page.js", "/unauthorized/page": "app/unauthorized/page.js"}