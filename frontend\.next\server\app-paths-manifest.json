{"/test-supabase/page": "app/test-supabase/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/messages/page": "app/messages/page.js", "/_not-found/page": "app/_not-found/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/test-simple-supabase/page": "app/test-simple-supabase/page.js", "/setup-database/page": "app/setup-database/page.js", "/test-dashboard/page": "app/test-dashboard/page.js", "/api/offers/route": "app/api/offers/route.js", "/api/users/route": "app/api/users/route.js", "/api/projects/route": "app/api/projects/route.js", "/project-roadmap/page": "app/project-roadmap/page.js", "/database-instructions/page": "app/database-instructions/page.js", "/dashboard/page": "app/dashboard/page.js", "/test-api/page": "app/test-api/page.js", "/api/projects/[id]/route": "app/api/projects/[id]/route.js", "/api/users/[id]/route": "app/api/users/[id]/route.js", "/test-upload/page": "app/test-upload/page.js", "/api/upload/route": "app/api/upload/route.js", "/syria-simulation/page": "app/syria-simulation/page.js", "/view-database/page": "app/view-database/page.js", "/test-reviews/page": "app/test-reviews/page.js", "/client/offers/page": "app/client/offers/page.js", "/test-app-integration/page": "app/test-app-integration/page.js", "/projects/page": "app/projects/page.js", "/projects/[id]/page": "app/projects/[id]/page.js", "/page": "app/page.js", "/jobs/page": "app/jobs/page.js", "/jobs/[id]/page": "app/jobs/[id]/page.js", "/craftsmen/page": "app/craftsmen/page.js", "/craftsmen/[id]/page": "app/craftsmen/[id]/page.js", "/dashboard/client/page": "app/dashboard/client/page.js", "/dashboard/craftsman/page": "app/dashboard/craftsman/page.js"}