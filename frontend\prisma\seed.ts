import { PrismaClient, UserRole, ProjectStatus, ProjectPriority } from '@prisma/client';
import { hashPassword } from '../src/lib/security';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 بدء تهيئة قاعدة البيانات...');

  // كلمة مرور افتراضية للحسابات التجريبية
  const defaultPassword = await hashPassword('Test123!@#');

  // إنشاء مستخدمين تجريبيين
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'مدير النظام',
      password: defaultPassword,
      role: UserRole.ADMIN,
      location: 'دمشق، سوريا',
      bio: 'مدير منصة دوزان',
      isActive: true,
      isVerified: true,
      emailVerifiedAt: new Date(),
    },
  });

  const clientUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'أحمد محمد',
      password: defaultPassword,
      phone: '+963 123 456 789',
      role: UserRole.CLIENT,
      location: 'دمشق - المزة',
      bio: 'عميل يبحث عن حرفيين مؤهلين',
      isActive: true,
      isVerified: true,
      emailVerifiedAt: new Date(),
    },
  });

  const craftsmanUser1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'محمد النجار',
      password: defaultPassword,
      phone: '+963 987 654 321',
      role: UserRole.CRAFTSMAN,
      location: 'دمشق - كفرسوسة',
      bio: 'نجار محترف مع خبرة 10 سنوات',
      isActive: true,
      isVerified: true,
      emailVerifiedAt: new Date(),
    },
  });

  const craftsmanUser2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'سارة الكهربائية',
      password: defaultPassword,
      phone: '+963 555 123 456',
      role: UserRole.CRAFTSMAN,
      location: 'حلب - الفرقان',
      bio: 'كهربائية متخصصة في الأنظمة الذكية',
      isActive: true,
      isVerified: true,
      emailVerifiedAt: new Date(),
    },
  });

  // إنشاء ملفات الحرفيين
  const craftsmanProfile1 = await prisma.craftsmanProfile.upsert({
    where: { userId: craftsmanUser1.id },
    update: {},
    create: {
      userId: craftsmanUser1.id,
      skills: ['نجارة', 'تركيب خزائن', 'أثاث مخصص'],
      experience: 10,
      hourlyRate: 50,
      availability: 'AVAILABLE',
      workingHours: '8:00 ص - 6:00 م',
      languages: ['العربية', 'الإنجليزية'],
      rating: 4.8,
      totalProjects: 45,
      completedProjects: 42,
    },
  });

  const craftsmanProfile2 = await prisma.craftsmanProfile.upsert({
    where: { userId: craftsmanUser2.id },
    update: {},
    create: {
      userId: craftsmanUser2.id,
      skills: ['كهرباء', 'أنظمة ذكية', 'إضاءة'],
      experience: 7,
      hourlyRate: 45,
      availability: 'AVAILABLE',
      workingHours: '9:00 ص - 5:00 م',
      languages: ['العربية'],
      rating: 4.9,
      totalProjects: 28,
      completedProjects: 26,
    },
  });

  // إنشاء مشاريع تجريبية
  const project1 = await prisma.project.create({
    data: {
      title: 'تجديد المطبخ الرئيسي',
      description: 'تجديد كامل للمطبخ مع تركيب خزائن جديدة وأجهزة حديثة',
      category: 'نجارة',
      budget: '₺6,000 - ₺8,000',
      deadline: new Date('2024-03-15'),
      location: 'دمشق - المزة',
      priority: ProjectPriority.HIGH,
      materials: 'متوفرة جزئياً',
      workType: 'تجديد',
      requirements: 'يفضل استخدام خشب عالي الجودة',
      images: [],
      status: ProjectStatus.OPEN,
      clientId: clientUser.id,
    },
  });

  const project2 = await prisma.project.create({
    data: {
      title: 'تركيب نظام كهرباء ذكي',
      description: 'تركيب نظام إضاءة ذكي ومفاتيح تحكم عن بعد',
      category: 'كهرباء',
      budget: '₺4,000 - ₺6,000',
      deadline: new Date('2024-03-10'),
      location: 'دمشق - المالكي',
      priority: ProjectPriority.MEDIUM,
      materials: 'غير متوفرة',
      workType: 'تركيب',
      requirements: 'نظام متوافق مع الهواتف الذكية',
      images: [],
      status: ProjectStatus.OPEN,
      clientId: clientUser.id,
    },
  });

  // إنشاء عروض تجريبية
  const offer1 = await prisma.offer.create({
    data: {
      projectId: project1.id,
      craftsmanId: craftsmanUser1.id,
      clientId: clientUser.id,
      price: 7200,
      duration: '14 يوم',
      description: 'أقدم خدمات تجديد المطابخ بأعلى جودة مع ضمان سنة كاملة',
      materials: 'متضمنة',
      warranty: '12 شهر',
      status: 'PENDING',
    },
  });

  const offer2 = await prisma.offer.create({
    data: {
      projectId: project2.id,
      craftsmanId: craftsmanUser2.id,
      clientId: clientUser.id,
      price: 4800,
      duration: '7 أيام',
      description: 'متخصصة في الأنظمة الذكية والتحكم الآلي',
      materials: 'متضمنة',
      warranty: '24 شهر',
      status: 'PENDING',
    },
  });

  // إنشاء عناصر معرض الأعمال
  await prisma.portfolioItem.create({
    data: {
      craftsmanId: craftsmanProfile1.id,
      title: 'تجديد مطبخ عصري',
      description: 'تجديد كامل للمطبخ مع تركيب خزائن حديثة وأسطح عمل من الجرانيت',
      category: 'نجارة',
      beforeImages: ['/images/portfolio/kitchen-before-1.jpg'],
      afterImages: ['/images/portfolio/kitchen-after-1.jpg'],
      tags: ['مطبخ', 'خزائن', 'تجديد'],
      featured: true,
      duration: '12 يوم',
      budget: 7500,
      clientName: 'فاطمة أحمد',
      completedDate: new Date('2024-01-15'),
    },
  });

  await prisma.portfolioItem.create({
    data: {
      craftsmanId: craftsmanProfile2.id,
      title: 'نظام إضاءة ذكي',
      description: 'تركيب نظام إضاءة ذكي مع تحكم عن بعد',
      category: 'كهرباء',
      beforeImages: ['/images/portfolio/lighting-before-1.jpg'],
      afterImages: ['/images/portfolio/lighting-after-1.jpg'],
      tags: ['إضاءة', 'ذكي', 'تحكم عن بعد'],
      featured: true,
      duration: '5 أيام',
      budget: 3200,
      clientName: 'خالد محمد',
      completedDate: new Date('2024-02-01'),
    },
  });

  // إنشاء إشعارات تجريبية
  await prisma.notification.create({
    data: {
      userId: clientUser.id,
      type: 'OFFER_RECEIVED',
      title: 'عرض جديد على مشروعك',
      message: 'تم استلام عرض جديد من محمد النجار على مشروع تجديد المطبخ',
      actionUrl: '/client/offers',
    },
  });

  await prisma.notification.create({
    data: {
      userId: craftsmanUser1.id,
      type: 'NEW_PROJECT',
      title: 'مشروع جديد متاح',
      message: 'مشروع جديد في تخصصك: تركيب خزائن مطبخ في دمشق',
      actionUrl: '/craftsman/projects',
    },
  });

  console.log('✅ تم إنشاء البيانات التجريبية بنجاح!');
  console.log('👤 المستخدمين:');
  console.log(`   - مدير: <EMAIL>`);
  console.log(`   - عميل: <EMAIL>`);
  console.log(`   - حرفي 1: <EMAIL>`);
  console.log(`   - حرفي 2: <EMAIL>`);
  console.log('📋 المشاريع:', project1.title, '،', project2.title);
  console.log('💼 العروض:', offer1.id, '،', offer2.id);
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
