import { PrismaClient, UserRole, ProjectStatus, ProjectPriority } from '@prisma/client';
import { hashPassword } from '../src/lib/security';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 بدء تهيئة قاعدة البيانات...');

  // إنشاء المواقع السورية
  console.log('📍 إنشاء المواقع السورية...');

  const syrianCities = [
    { name: 'Damascus', nameAr: 'دمشق', type: 'city' },
    { name: 'Aleppo', nameAr: 'حلب', type: 'city' },
    { name: 'Homs', nameAr: 'حمص', type: 'city' },
    { name: 'Latakia', nameAr: 'اللاذقية', type: 'city' },
    { name: 'Hama', nameAr: 'حماة', type: 'city' },
    { name: '<PERSON>ir <PERSON>-Zor', nameAr: 'دير الزور', type: 'city' },
    { name: '<PERSON><PERSON><PERSON>', nameAr: 'الرقة', type: 'city' },
    { name: 'Daraa', nameAr: 'درعا', type: 'city' },
    { name: 'Tartus', nameAr: 'طرطوس', type: 'city' },
    { name: 'Qamishli', nameAr: 'القامشلي', type: 'city' },
    { name: 'Idlib', nameAr: 'إدلب', type: 'city' },
    { name: 'Sweida', nameAr: 'السويداء', type: 'city' }
  ];

  for (const city of syrianCities) {
    await prisma.location.upsert({
      where: { nameAr: city.nameAr },
      update: {},
      create: city
    });
  }

  // إنشاء فئات العمل
  console.log('🔧 إنشاء فئات العمل...');

  const categories = [
    { name: 'Carpentry', nameAr: 'نجارة', description: 'أعمال النجارة والأثاث', icon: '🪚', color: '#8B4513' },
    { name: 'Electrical', nameAr: 'كهرباء', description: 'التمديدات والصيانة الكهربائية', icon: '⚡', color: '#FFD700' },
    { name: 'Plumbing', nameAr: 'سباكة', description: 'أعمال السباكة والصرف الصحي', icon: '🔧', color: '#4169E1' },
    { name: 'Painting', nameAr: 'دهان', description: 'أعمال الدهان والديكور', icon: '🎨', color: '#FF6347' },
    { name: 'Tiling', nameAr: 'بلاط', description: 'تركيب البلاط والسيراميك', icon: '🏠', color: '#CD853F' },
    { name: 'Masonry', nameAr: 'بناء', description: 'أعمال البناء والإنشاءات', icon: '🧱', color: '#A0522D' },
    { name: 'HVAC', nameAr: 'تكييف', description: 'تركيب وصيانة أجهزة التكييف', icon: '❄️', color: '#00CED1' },
    { name: 'Gardening', nameAr: 'حدائق', description: 'تنسيق وصيانة الحدائق', icon: '🌱', color: '#32CD32' },
    { name: 'Cleaning', nameAr: 'تنظيف', description: 'خدمات التنظيف والصيانة', icon: '🧽', color: '#87CEEB' },
    { name: 'Welding', nameAr: 'لحام', description: 'أعمال اللحام والحدادة', icon: '🔥', color: '#FF4500' }
  ];

  for (const category of categories) {
    await prisma.category.upsert({
      where: { nameAr: category.nameAr },
      update: {},
      create: category
    });
  }

  // كلمة مرور افتراضية للحسابات التجريبية
  const defaultPassword = await hashPassword('Test123!@#');

  // إنشاء المستخدمين
  console.log('👥 إنشاء المستخدمين...');

  // مدير النظام
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'مدير النظام',
      password: defaultPassword,
      role: 'ADMIN',
      location: 'دمشق، سوريا',
      bio: 'مدير منصة دوزان للحرفيين',
      isVerified: true,
      emailVerifiedAt: new Date(),
    },
  });

  // عميل تجريبي
  const client1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'أحمد محمد',
      password: defaultPassword,
      role: 'CLIENT',
      location: 'دمشق، المزة',
      bio: 'عميل يبحث عن حرفيين مؤهلين لتنفيذ مشاريع منزلية',
      isVerified: true,
      emailVerifiedAt: new Date(),
    },
  });

  // حرفي نجار
  const craftsman1 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'محمد النجار',
      password: defaultPassword,
      role: 'CRAFTSMAN',
      location: 'دمشق، الميدان',
      bio: 'نجار محترف مع خبرة 10 سنوات في تصميم وتنفيذ الأثاث المنزلي والمكتبي',
      isVerified: true,
      emailVerifiedAt: new Date(),
    },
  });

  // إنشاء ملف الحرفي للنجار
  await prisma.craftsmanProfile.upsert({
    where: { userId: craftsman1.id },
    update: {},
    create: {
      userId: craftsman1.id,
      skills: ['نجارة الأثاث', 'تصميم المطابخ', 'تركيب الأبواب', 'الديكور الخشبي'],
      experience: 10,
      hourlyRate: 50,
      availability: 'AVAILABLE',
      workingHours: 'الأحد - الخميس: 8:00 - 17:00',
      languages: ['العربية', 'الإنجليزية'],
      rating: 4.9,
      totalProjects: 45,
      completedProjects: 42,
    },
  });

  // حرفي كهربائي
  const craftsman2 = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'علي الكهربائي',
      password: defaultPassword,
      role: 'CRAFTSMAN',
      location: 'حلب، الفرقان',
      bio: 'كهربائي معتمد مع خبرة 8 سنوات في التمديدات والصيانة الكهربائية',
      isVerified: true,
      emailVerifiedAt: new Date(),
    },
  });

  // إنشاء ملف الحرفي للكهربائي
  await prisma.craftsmanProfile.upsert({
    where: { userId: craftsman2.id },
    update: {},
    create: {
      userId: craftsman2.id,
      skills: ['التمديدات الكهربائية', 'صيانة الأجهزة', 'تركيب الإضاءة', 'أنظمة الأمان'],
      experience: 8,
      hourlyRate: 40,
      availability: 'AVAILABLE',
      workingHours: 'السبت - الخميس: 9:00 - 18:00',
      languages: ['العربية'],
      rating: 4.7,
      totalProjects: 38,
      completedProjects: 35,
    },
  });

  console.log('✅ تم إنشاء المستخدمين والملفات الشخصية');

  // إنشاء مشاريع تجريبية
  const project1 = await prisma.project.create({
    data: {
      title: 'تجديد المطبخ الرئيسي',
      description: 'تجديد كامل للمطبخ مع تركيب خزائن جديدة وأجهزة حديثة',
      category: 'نجارة',
      budget: '₺6,000 - ₺8,000',
      deadline: new Date('2024-03-15'),
      location: 'دمشق - المزة',
      priority: ProjectPriority.HIGH,
      materials: 'متوفرة جزئياً',
      workType: 'تجديد',
      requirements: 'يفضل استخدام خشب عالي الجودة',
      images: [],
      status: ProjectStatus.OPEN,
      clientId: client1.id,
    },
  });

  const project2 = await prisma.project.create({
    data: {
      title: 'تركيب نظام كهرباء ذكي',
      description: 'تركيب نظام إضاءة ذكي ومفاتيح تحكم عن بعد',
      category: 'كهرباء',
      budget: '₺4,000 - ₺6,000',
      deadline: new Date('2024-03-10'),
      location: 'دمشق - المالكي',
      priority: ProjectPriority.MEDIUM,
      materials: 'غير متوفرة',
      workType: 'تركيب',
      requirements: 'نظام متوافق مع الهواتف الذكية',
      images: [],
      status: ProjectStatus.OPEN,
      clientId: client1.id,
    },
  });

  // إنشاء عروض تجريبية
  const offer1 = await prisma.offer.create({
    data: {
      projectId: project1.id,
      craftsmanId: craftsman1.id,
      clientId: client1.id,
      price: 7200,
      duration: '14 يوم',
      description: 'أقدم خدمات تجديد المطابخ بأعلى جودة مع ضمان سنة كاملة',
      materials: 'متضمنة',
      warranty: '12 شهر',
      status: 'PENDING',
    },
  });

  const offer2 = await prisma.offer.create({
    data: {
      projectId: project2.id,
      craftsmanId: craftsman2.id,
      clientId: client1.id,
      price: 4800,
      duration: '7 أيام',
      description: 'متخصصة في الأنظمة الذكية والتحكم الآلي',
      materials: 'متضمنة',
      warranty: '24 شهر',
      status: 'PENDING',
    },
  });

  // إنشاء عناصر معرض الأعمال
  const craftsmanProfile1 = await prisma.craftsmanProfile.findUnique({
    where: { userId: craftsman1.id }
  });

  const craftsmanProfile2 = await prisma.craftsmanProfile.findUnique({
    where: { userId: craftsman2.id }
  });

  if (craftsmanProfile1) {
    await prisma.portfolioItem.create({
      data: {
        craftsmanId: craftsmanProfile1.id,
      title: 'تجديد مطبخ عصري',
      description: 'تجديد كامل للمطبخ مع تركيب خزائن حديثة وأسطح عمل من الجرانيت',
      category: 'نجارة',
      beforeImages: ['/images/portfolio/kitchen-before-1.jpg'],
      afterImages: ['/images/portfolio/kitchen-after-1.jpg'],
      tags: ['مطبخ', 'خزائن', 'تجديد'],
      featured: true,
      duration: '12 يوم',
      budget: 7500,
      clientName: 'فاطمة أحمد',
        completedDate: new Date('2024-01-15'),
      },
    });
  }

  if (craftsmanProfile2) {
    await prisma.portfolioItem.create({
      data: {
        craftsmanId: craftsmanProfile2.id,
      title: 'نظام إضاءة ذكي',
      description: 'تركيب نظام إضاءة ذكي مع تحكم عن بعد',
      category: 'كهرباء',
      beforeImages: ['/images/portfolio/lighting-before-1.jpg'],
      afterImages: ['/images/portfolio/lighting-after-1.jpg'],
      tags: ['إضاءة', 'ذكي', 'تحكم عن بعد'],
      featured: true,
      duration: '5 أيام',
      budget: 3200,
      clientName: 'خالد محمد',
        completedDate: new Date('2024-02-01'),
      },
    });
  }

  // إنشاء إشعارات تجريبية
  await prisma.notification.create({
    data: {
      userId: client1.id,
      type: 'OFFER_RECEIVED',
      title: 'عرض جديد على مشروعك',
      message: 'تم استلام عرض جديد من محمد النجار على مشروع تجديد المطبخ',
      actionUrl: '/client/offers',
    },
  });

  await prisma.notification.create({
    data: {
      userId: craftsman1.id,
      type: 'NEW_PROJECT',
      title: 'مشروع جديد متاح',
      message: 'مشروع جديد في تخصصك: تركيب خزائن مطبخ في دمشق',
      actionUrl: '/craftsman/projects',
    },
  });

  console.log('✅ تم إنشاء البيانات التجريبية بنجاح!');
  console.log('👤 المستخدمين:');
  console.log(`   - مدير: <EMAIL>`);
  console.log(`   - عميل: <EMAIL>`);
  console.log(`   - حرفي 1: <EMAIL>`);
  console.log(`   - حرفي 2: <EMAIL>`);
  console.log('📋 المشاريع:', project1.title, '،', project2.title);
  console.log('💼 العروض:', offer1.id, '،', offer2.id);
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async (e) => {
    console.error('❌ خطأ في تهيئة قاعدة البيانات:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
