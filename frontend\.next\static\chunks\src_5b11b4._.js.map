{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\ninterface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn(\n        'rounded-xl border bg-white shadow-sm transition-all duration-200 hover:shadow-md',\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex flex-col space-y-1.5 p-6', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, children, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn('font-semibold leading-none tracking-tight text-navy', className)}\n    {...props}\n  >\n    {children}\n  </h3>\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-gray-600', className)}\n    {...props}\n  >\n    {children}\n  </p>\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => (\n    <div ref={ref} className={cn('p-6 pt-0', className)} {...props}>\n      {children}\n    </div>\n  )\n);\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => (\n    <div\n      ref={ref}\n      className={cn('flex items-center p-6 pt-0', className)}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n);\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAqBA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oFACA;QAED,GAAG,KAAK;kBAER;;;;;;;AAIP,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uDAAuD;QACpE,GAAG,KAAK;kBAER;;;;;;;AAGL,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;;AAGL,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;kBAC3D;;;;;;;AAIP,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAClC,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;;AAIP,WAAW,WAAW,GAAG"}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/test-auth/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { signIn, signOut, useSession } from 'next-auth/react';\nimport { Button } from '@/components/ui/Button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';\n\nexport default function TestAuthPage() {\n  const { data: session, status } = useSession();\n  const [email, setEmail] = useState('');\n  const [password, setPassword] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  const handleLogin = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n    setError('');\n\n    try {\n      const result = await signIn('credentials', {\n        email,\n        password,\n        redirect: false,\n      });\n\n      if (result?.error) {\n        setError('خطأ في تسجيل الدخول');\n      }\n    } catch (err) {\n      setError('حدث خطأ غير متوقع');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testAccounts = [\n    { email: '<EMAIL>', password: 'Test123!@#', role: 'مدير النظام' },\n    { email: '<EMAIL>', password: 'Test123!@#', role: 'عميل' },\n    { email: '<EMAIL>', password: 'Test123!@#', role: 'حرفي' },\n    { email: '<EMAIL>', password: 'Test123!@#', role: 'حرفي' },\n  ];\n\n  const quickLogin = (testEmail: string, testPassword: string) => {\n    setEmail(testEmail);\n    setPassword(testPassword);\n  };\n\n  if (status === 'loading') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-teal mx-auto mb-4\"></div>\n          <p>جاري التحميل...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-12 px-4\">\n      <div className=\"max-w-md mx-auto\">\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-center text-2xl font-bold text-navy\">\n              اختبار المصادقة\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            {session ? (\n              <div className=\"space-y-4\">\n                <div className=\"text-center\">\n                  <div className=\"bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4\">\n                    ✅ تم تسجيل الدخول بنجاح!\n                  </div>\n                  <p className=\"text-lg font-semibold\">{session.user?.name}</p>\n                  <p className=\"text-gray-600\">{session.user?.email}</p>\n                  <p className=\"text-sm text-teal font-medium\">\n                    الدور: {session.user?.role}\n                  </p>\n                </div>\n                <Button \n                  onClick={() => signOut()} \n                  variant=\"outline\" \n                  className=\"w-full\"\n                >\n                  تسجيل الخروج\n                </Button>\n              </div>\n            ) : (\n              <div className=\"space-y-4\">\n                {error && (\n                  <div className=\"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded\">\n                    {error}\n                  </div>\n                )}\n\n                <form onSubmit={handleLogin} className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      البريد الإلكتروني\n                    </label>\n                    <input\n                      type=\"email\"\n                      value={email}\n                      onChange={(e) => setEmail(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal\"\n                      required\n                    />\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      كلمة المرور\n                    </label>\n                    <input\n                      type=\"password\"\n                      value={password}\n                      onChange={(e) => setPassword(e.target.value)}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal\"\n                      required\n                    />\n                  </div>\n\n                  <Button \n                    type=\"submit\" \n                    disabled={loading}\n                    className=\"w-full\"\n                  >\n                    {loading ? 'جاري تسجيل الدخول...' : 'تسجيل الدخول'}\n                  </Button>\n                </form>\n\n                <div className=\"mt-6\">\n                  <h3 className=\"text-sm font-medium text-gray-700 mb-3\">\n                    حسابات تجريبية:\n                  </h3>\n                  <div className=\"space-y-2\">\n                    {testAccounts.map((account, index) => (\n                      <button\n                        key={index}\n                        onClick={() => quickLogin(account.email, account.password)}\n                        className=\"w-full text-left p-2 bg-gray-100 hover:bg-gray-200 rounded text-sm\"\n                      >\n                        <div className=\"font-medium\">{account.role}</div>\n                        <div className=\"text-gray-600 text-xs\">{account.email}</div>\n                      </button>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            )}\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,MAAM,cAAc,OAAO;QACzB,EAAE,cAAc;QAChB,WAAW;QACX,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC;gBACA;gBACA,UAAU;YACZ;YAEA,IAAI,QAAQ,OAAO;gBACjB,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,eAAe;QACnB;YAAE,OAAO;YAAmB,UAAU;YAAc,MAAM;QAAc;QACxE;YAAE,OAAO;YAAoB,UAAU;YAAc,MAAM;QAAO;QAClE;YAAE,OAAO;YAAwB,UAAU;YAAc,MAAM;QAAO;QACtE;YAAE,OAAO;YAAwB,UAAU;YAAc,MAAM;QAAO;KACvE;IAED,MAAM,aAAa,CAAC,WAAmB;QACrC,SAAS;QACT,YAAY;IACd;IAEA,IAAI,WAAW,WAAW;QACxB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;sCAA2C;;;;;;;;;;;kCAIlE,6LAAC,mIAAA,CAAA,cAAW;kCACT,wBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA6E;;;;;;sDAG5F,6LAAC;4CAAE,WAAU;sDAAyB,QAAQ,IAAI,EAAE;;;;;;sDACpD,6LAAC;4CAAE,WAAU;sDAAiB,QAAQ,IAAI,EAAE;;;;;;sDAC5C,6LAAC;4CAAE,WAAU;;gDAAgC;gDACnC,QAAQ,IAAI,EAAE;;;;;;;;;;;;;8CAG1B,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD;oCACrB,SAAQ;oCACR,WAAU;8CACX;;;;;;;;;;;iDAKH,6LAAC;4BAAI,WAAU;;gCACZ,uBACC,6LAAC;oCAAI,WAAU;8CACZ;;;;;;8CAIL,6LAAC;oCAAK,UAAU;oCAAa,WAAU;;sDACrC,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAA+C;;;;;;8DAGhE,6LAAC;oDACC,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;oDACV,QAAQ;;;;;;;;;;;;sDAIZ,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU;4CACV,WAAU;sDAET,UAAU,yBAAyB;;;;;;;;;;;;8CAIxC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC;;;;;;sDAGvD,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,6LAAC;oDAEC,SAAS,IAAM,WAAW,QAAQ,KAAK,EAAE,QAAQ,QAAQ;oDACzD,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEAAe,QAAQ,IAAI;;;;;;sEAC1C,6LAAC;4DAAI,WAAU;sEAAyB,QAAQ,KAAK;;;;;;;mDALhD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiB7B;GAtJwB;;QACY,iJAAA,CAAA,aAAU;;;KADtB"}}, {"offset": {"line": 473, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}