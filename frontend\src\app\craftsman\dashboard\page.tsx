'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import MainLayout from '@/components/layout/MainLayout';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useProjects, useMyOffers } from '@/hooks/useApi';

const CraftsmanDashboard = () => {
  const { data: session } = useSession();
  const [activeFilter, setActiveFilter] = useState('all');

  // استخدام بيانات المستخدم من الجلسة أو بيانات افتراضية
  const user = session?.user || { name: 'حرفي' };

  // جلب البيانات من API
  const { data: projectsData, loading: projectsLoading } = useProjects({ status: 'OPEN' });
  const { data: offersData, loading: offersLoading } = useMyOffers();

  const availableProjectsFromApi = projectsData?.projects || [];
  const myOffersFromApi = offersData?.offers || [];

  // حساب الإحصائيات من البيانات الحقيقية
  const inProgressProjects = 0; // سيتم حسابها من مشاريع الحرفي
  const sentOffers = myOffersFromApi.length;
  const completedProjects = 0; // سيتم حسابها من مشاريع الحرفي
  const rating = 4.9; // قيمة افتراضية

  const stats = [
    {
      title: 'المشاريع الجارية',
      value: inProgressProjects.toString(),
      icon: '🔨',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: 'مشاريع نشطة'
    },
    {
      title: 'العروض المرسلة',
      value: sentOffers.toString(),
      icon: '📤',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: 'عروض مقدمة'
    },
    {
      title: 'المشاريع المكتملة',
      value: completedProjects.toString(),
      icon: '✅',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: 'مشاريع منجزة'
    },
    {
      title: 'التقييم',
      value: rating.toString(),
      icon: '⭐',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      change: 'من 5 نجوم'
    }
  ];

  // تحويل بيانات المشاريع المتاحة من API
  const availableProjects = availableProjectsFromApi.slice(0, 3).map(project => ({
    id: parseInt(project.id.replace('project', '') || '0'),
    title: project.title,
    description: project.description,
    budget: project.budget,
    location: project.location,
    deadline: new Date(project.deadline).toLocaleDateString('ar-SA'),
    category: project.category,
    postedTime: 'منذ ساعات', // يمكن حسابها من تاريخ الإنشاء
    offers: 0, // سيتم حسابها من العروض
    client: project.client?.name || 'عميل'
  }));

  const myProjects = [
    {
      id: 1,
      title: 'تجديد حمام رئيسي',
      client: 'سارة خالد',
      status: 'جاري التنفيذ',
      progress: 75,
      budget: '₺4,500',
      deadline: '2024-03-10',
      startDate: '2024-02-15'
    },
    {
      id: 2,
      title: 'تركيب أرضيات خشبية',
      client: 'عمر حسن',
      status: 'جاري التنفيذ',
      progress: 40,
      budget: '₺5,200',
      deadline: '2024-03-18',
      startDate: '2024-02-20'
    },
    {
      id: 3,
      title: 'إصلاح سباكة المطبخ',
      client: 'ليلى أحمد',
      status: 'مكتمل',
      progress: 100,
      budget: '₺1,800',
      deadline: '2024-02-25',
      startDate: '2024-02-10'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'جاري التنفيذ':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'مكتمل':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'معلق':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const quickActions = [
    {
      title: 'تصفح المشاريع المتاحة',
      description: 'ابحث عن مشاريع جديدة تناسب مهاراتك',
      icon: '🔍',
      href: '/projects',
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: 'إدارة عروضي',
      description: 'تابع حالة العروض التي قدمتها',
      icon: '📋',
      href: '/offers/sent',
      color: 'from-green-500 to-green-600'
    },
    {
      title: 'معرض أعمالي',
      description: 'أضف وأدر معرض أعمالك المكتملة',
      icon: '🖼️',
      href: '/portfolio',
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: 'الرسائل',
      description: 'تواصل مع العملاء ومتابعة المحادثات',
      icon: '💬',
      href: '/messages',
      color: 'from-orange-500 to-orange-600'
    }
  ];

  return (
    <ProtectedRoute requiredRoles="craftsman">
      <MainLayout>
        <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white relative overflow-hidden">
          {/* خلفية هندسية متناسقة */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute top-20 left-20 w-32 h-32 bg-navy rounded-full blur-xl"></div>
            <div className="absolute bottom-20 right-20 w-28 h-28 bg-teal rounded-full blur-xl"></div>
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-40 h-40 bg-skyblue rounded-full blur-2xl"></div>
          </div>

          {/* شبكة نقطية ناعمة */}
          <div className="absolute inset-0 opacity-10">
            <div className="w-full h-full" style={{
              backgroundImage: 'radial-gradient(circle, #567C8D 1px, transparent 1px)',
              backgroundSize: '50px 50px'
            }}></div>
          </div>

          <div className="container mx-auto px-4 py-8 relative z-10">
            {/* Header */}
            <div className="mb-8">
              <div className="flex items-center justify-between">
                <div>
                  <h1 className="text-3xl md:text-4xl font-bold text-navy mb-2">
                    لوحة تحكم الحرفي
                  </h1>
                  <p className="text-gray-600 text-lg">
                    مرحباً {user?.name}، إدارة مشاريعك وعروضك
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-gray-500 text-sm">آخر تحديث</p>
                  <p className="text-navy font-medium">منذ 3 دقائق</p>
                </div>
              </div>
            </div>

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              {stats.map((stat, index) => (
                <Card key={index} className="border-0 bg-white/90 backdrop-blur-md shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-gray-600 text-sm mb-1">{stat.title}</p>
                        <p className="text-2xl font-bold text-navy">{stat.value}</p>
                        <p className="text-xs text-gray-500 mt-1">{stat.change}</p>
                      </div>
                      <div className={`text-3xl ${stat.color}`}>
                        {stat.icon}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Quick Actions */}
            <div className="mb-8">
              <h2 className="text-2xl font-bold text-navy mb-6">الإجراءات السريعة</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {quickActions.map((action, index) => (
                  <Link key={index} href={action.href}>
                    <Card className="border-0 bg-white/90 backdrop-blur-md shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer group">
                      <CardContent className="p-6 text-center">
                        <div className={`w-16 h-16 bg-gradient-to-r ${action.color} rounded-full flex items-center justify-center text-white text-2xl mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                          {action.icon}
                        </div>
                        <h3 className="text-lg font-semibold text-navy mb-2">{action.title}</h3>
                        <p className="text-gray-600 text-sm">{action.description}</p>
                      </CardContent>
                    </Card>
                  </Link>
                ))}
              </div>
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Available Projects */}
              <Card className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                <CardContent className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h3 className="text-xl font-bold text-navy">المشاريع المتاحة</h3>
                    <Link href="/projects" className="text-teal hover:text-teal/80 text-sm font-medium">
                      عرض الكل
                    </Link>
                  </div>

                  <div className="space-y-4">
                    {availableProjects.map((project) => (
                      <div key={project.id} className="border border-gray-200 rounded-lg p-4 hover:border-teal transition-colors duration-200">
                        <div className="flex justify-between items-start mb-3">
                          <h4 className="font-semibold text-navy">{project.title}</h4>
                          <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                            {project.category}
                          </span>
                        </div>

                        <p className="text-gray-600 text-sm mb-3">{project.description}</p>

                        <div className="grid grid-cols-2 gap-2 text-xs text-gray-600 mb-3">
                          <div>💰 {project.budget}</div>
                          <div>📍 {project.location}</div>
                          <div>⏰ {project.deadline}</div>
                          <div>📤 {project.offers} عرض</div>
                        </div>

                        <div className="flex justify-between items-center">
                          <span className="text-xs text-gray-500">{project.postedTime}</span>
                          <div className="flex space-x-2 space-x-reverse">
                            <Link href={`/projects/${project.id}`}>
                              <Button size="sm" variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                                عرض
                              </Button>
                            </Link>
                            <Button size="sm" className="bg-gradient-to-r from-navy to-teal">
                              تقديم عرض
                            </Button>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* My Projects */}
              <Card className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                <CardContent className="p-6">
                  <div className="flex justify-between items-center mb-6">
                    <h3 className="text-xl font-bold text-navy">مشاريعي الحالية</h3>
                    <Link href="/projects/my-projects" className="text-teal hover:text-teal/80 text-sm font-medium">
                      عرض الكل
                    </Link>
                  </div>

                  <div className="space-y-4">
                    {myProjects.map((project) => (
                      <div key={project.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex justify-between items-start mb-3">
                          <div>
                            <h4 className="font-semibold text-navy">{project.title}</h4>
                            <p className="text-sm text-gray-600">العميل: {project.client}</p>
                          </div>
                          <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(project.status)}`}>
                            {project.status}
                          </span>
                        </div>

                        {project.status === 'جاري التنفيذ' && (
                          <div className="mb-3">
                            <div className="flex justify-between text-xs text-gray-600 mb-1">
                              <span>التقدم</span>
                              <span>{project.progress}%</span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-gradient-to-r from-navy to-teal h-2 rounded-full transition-all duration-300"
                                style={{ width: `${project.progress}%` }}
                              ></div>
                            </div>
                          </div>
                        )}

                        <div className="grid grid-cols-2 gap-2 text-xs text-gray-600 mb-3">
                          <div>💰 {project.budget}</div>
                          <div>⏰ {project.deadline}</div>
                        </div>

                        <div className="flex justify-end">
                          <Link href={`/projects/${project.id}`}>
                            <Button size="sm" variant="outline" className="border-navy text-navy hover:bg-navy hover:text-white">
                              إدارة المشروع
                            </Button>
                          </Link>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <div className="mt-8">
              <Card className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                <CardContent className="p-6">
                  <h3 className="text-xl font-bold text-navy mb-4">النشاط الأخير</h3>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 space-x-reverse p-3 bg-blue-50 rounded-lg border-r-4 border-blue-500">
                      <div className="text-2xl">🎉</div>
                      <div className="flex-1">
                        <p className="font-medium text-navy">تم قبول عرضك</p>
                        <p className="text-gray-600 text-sm">مشروع تجديد الحمام - سارة خالد</p>
                      </div>
                      <span className="text-xs text-gray-500">منذ ساعة</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse p-3 bg-green-50 rounded-lg border-r-4 border-green-500">
                      <div className="text-2xl">✅</div>
                      <div className="flex-1">
                        <p className="font-medium text-navy">تم إكمال مشروع</p>
                        <p className="text-gray-600 text-sm">إصلاح سباكة المطبخ - ليلى أحمد</p>
                      </div>
                      <span className="text-xs text-gray-500">منذ يومين</span>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse p-3 bg-yellow-50 rounded-lg border-r-4 border-yellow-500">
                      <div className="text-2xl">⭐</div>
                      <div className="flex-1">
                        <p className="font-medium text-navy">تقييم جديد</p>
                        <p className="text-gray-600 text-sm">5 نجوم من عمر حسن</p>
                      </div>
                      <span className="text-xs text-gray-500">منذ 3 أيام</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </MainLayout>
    </ProtectedRoute>
  );
};

export default CraftsmanDashboard;
