'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';

const FeaturedProjectsSimple = () => {
  const projects = [
    {
      id: 1,
      title: 'تجديد مطبخ عصري فاخر',
      beforeImage: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop',
      afterImage: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop&sat=2&brightness=1.1',
      craftsman: 'محمد النجار',
      location: 'دمشق، المزة',
      duration: '14 يوم',
      budget: '350,000 ل.س',
      rating: 5,
      description: 'تحويل مطبخ تقليدي إلى مطبخ عصري بخزائن خشبية فاخرة وجزيرة وسطية'
    },
    {
      id: 2,
      title: 'تصميم حمام مودرن',
      beforeImage: 'https://images.unsplash.com/photo-1620626011761-996317b8d101?w=600&h=400&fit=crop',
      afterImage: 'https://images.unsplash.com/photo-1620626011761-996317b8d101?w=600&h=400&fit=crop&sat=2&brightness=1.1',
      craftsman: 'أحمد السباك',
      location: 'حلب، الفرقان',
      duration: '10 أيام',
      budget: '180,000 ل.س',
      rating: 5,
      description: 'تجديد حمام كامل بتصميم عصري وتركيبات حديثة'
    },
    {
      id: 3,
      title: 'غرفة نوم رئيسية فاخرة',
      beforeImage: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop',
      afterImage: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop&sat=2&brightness=1.1',
      craftsman: 'علي النجار',
      location: 'دمشق، جرمانا',
      duration: '12 يوم',
      budget: '280,000 ل.س',
      rating: 5,
      description: 'تصميم غرفة نوم رئيسية مع خزائن مدمجة وإضاءة مخفية'
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-white via-beige/30 to-white relative overflow-hidden">
      {/* عناصر تصميمية في الخلفية */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-64 h-64 bg-navy rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 bg-teal rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* العنوان */}
        <div className="text-center mb-16">
          <div className="inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-2 rounded-full text-sm font-medium mb-4">
            🏆 مشاريع مميزة
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-navy mb-6">
            أعمال تحكي قصص 
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-teal to-navy">النجاح</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            اكتشف مجموعة من أفضل المشاريع التي تم إنجازها على منصة دوزان بجودة استثنائية
          </p>
        </div>

        {/* شبكة المشاريع */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {projects.map((project, index) => (
            <div
              key={project.id}
              className="group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-teal/30 hover:scale-105"
            >
              {/* مقارنة قبل وبعد */}
              <div className="relative h-48 overflow-hidden">
                <div className="absolute inset-0 grid grid-cols-2">
                  {/* صورة قبل */}
                  <div className="relative overflow-hidden">
                    <img
                      src={project.beforeImage}
                      alt="قبل"
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                    />
                    <div className="absolute top-4 left-4 bg-red-500/90 text-white px-3 py-1 rounded-full text-sm font-medium">
                      قبل
                    </div>
                  </div>
                  
                  {/* صورة بعد */}
                  <div className="relative overflow-hidden">
                    <img
                      src={project.afterImage}
                      alt="بعد"
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                    />
                    <div className="absolute top-4 right-4 bg-green-500/90 text-white px-3 py-1 rounded-full text-sm font-medium">
                      بعد
                    </div>
                  </div>
                </div>

                {/* خط الفاصل */}
                <div className="absolute inset-y-0 left-1/2 w-1 bg-white shadow-lg transform -translate-x-1/2 z-10"></div>
                <div className="absolute top-1/2 left-1/2 w-8 h-8 bg-white rounded-full shadow-lg transform -translate-x-1/2 -translate-y-1/2 z-20 flex items-center justify-center">
                  <div className="w-4 h-4 bg-gradient-to-r from-navy to-teal rounded-full"></div>
                </div>

                {/* تأثير التدرج */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </div>

              {/* محتوى البطاقة */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h3 className="text-xl font-bold text-navy mb-2 group-hover:text-teal transition-colors">
                      {project.title}
                    </h3>
                    <p className="text-gray-600 text-sm line-clamp-2">
                      {project.description}
                    </p>
                  </div>
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <svg
                        key={i}
                        className={`w-4 h-4 ${
                          i < project.rating ? 'text-yellow-500' : 'text-gray-300'
                        }`}
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                      </svg>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="text-xs text-gray-500 mb-1">الحرفي</div>
                    <div className="font-semibold text-navy text-sm">{project.craftsman}</div>
                  </div>
                  <div className="bg-gray-50 p-3 rounded-lg">
                    <div className="text-xs text-gray-500 mb-1">المدة</div>
                    <div className="font-semibold text-navy text-sm">{project.duration}</div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-xs text-gray-500">الميزانية</div>
                    <div className="font-bold text-green-600">{project.budget}</div>
                  </div>
                  <div className="text-xs text-gray-500">
                    📍 {project.location}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* زر عرض المزيد */}
        <div className="text-center">
          <Link href="/jobs">
            <Button size="lg" className="bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90 px-8 py-4 text-lg">
              استكشف جميع المشاريع
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProjectsSimple;
