'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';

const CreateProjectPage = () => {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: '',
    budget: '',
    deadline: '',
    location: '',
    priority: 'متوسطة',
    materials: 'غير محدد',
    workType: '',
    requirements: '',
    images: [] as File[]
  });

  const categories = [
    { id: 'نجارة', label: 'نجارة', icon: '🪚' },
    { id: 'كهرباء', label: 'كهرباء', icon: '⚡' },
    { id: 'سباكة', label: 'سباكة', icon: '🔧' },
    { id: 'دهان', label: 'دهان', icon: '🎨' },
    { id: 'بناء', label: 'بناء وإنشاءات', icon: '🏗️' },
    { id: 'تكييف', label: 'تكييف وتبريد', icon: '❄️' },
    { id: 'تنظيف', label: 'تنظيف', icon: '🧹' },
    { id: 'أخرى', label: 'أخرى', icon: '🔨' }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const files = Array.from(e.target.files);
      setFormData(prev => ({ ...prev, images: [...prev.images, ...files] }));
    }
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // هنا سيتم إرسال البيانات إلى الخادم
    console.log('Project data:', formData);
    // إعادة توجيه إلى صفحة المشاريع
    router.push('/client/projects');
  };

  const nextStep = () => {
    if (currentStep < 3) setCurrentStep(currentStep + 1);
  };

  const prevStep = () => {
    if (currentStep > 1) setCurrentStep(currentStep - 1);
  };

  const steps = [
    { number: 1, title: 'المعلومات الأساسية', description: 'عنوان ووصف المشروع' },
    { number: 2, title: 'التفاصيل', description: 'الميزانية والموعد والمتطلبات' },
    { number: 3, title: 'الصور والمراجعة', description: 'إضافة صور ومراجعة البيانات' }
  ];

  return (
    <ProtectedRoute requiredRoles="client">
      <DashboardLayout 
        title="إنشاء مشروع جديد"
        subtitle="أضف مشروعك واحصل على عروض من الحرفيين المؤهلين"
      >
        <div className="max-w-4xl mx-auto">
          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => (
                <div key={step.number} className="flex items-center">
                  <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                    currentStep >= step.number 
                      ? 'bg-navy border-navy text-white' 
                      : 'border-gray-300 text-gray-500'
                  }`}>
                    {currentStep > step.number ? '✓' : step.number}
                  </div>
                  <div className="mr-4">
                    <p className={`text-sm font-medium ${
                      currentStep >= step.number ? 'text-navy' : 'text-gray-500'
                    }`}>
                      {step.title}
                    </p>
                    <p className="text-xs text-gray-500">{step.description}</p>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-16 h-0.5 mx-4 ${
                      currentStep > step.number ? 'bg-navy' : 'bg-gray-300'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>

          <form onSubmit={handleSubmit} className="space-y-8">
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">المعلومات الأساسية</h3>
                
                <div className="space-y-6">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">عنوان المشروع *</label>
                    <input
                      type="text"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                      placeholder="مثال: تجديد المطبخ الرئيسي"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 font-medium mb-2">وصف المشروع *</label>
                    <textarea
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      rows={4}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                      placeholder="اكتب وصفاً مفصلاً عن المشروع المطلوب..."
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 font-medium mb-2">التخصص المطلوب *</label>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {categories.map((category) => (
                        <button
                          key={category.id}
                          type="button"
                          onClick={() => setFormData(prev => ({ ...prev, category: category.id }))}
                          className={`p-4 border-2 rounded-xl text-center transition-colors duration-200 ${
                            formData.category === category.id
                              ? 'border-navy bg-navy/5 text-navy'
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <div className="text-2xl mb-2">{category.icon}</div>
                          <div className="text-sm font-medium">{category.label}</div>
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <label className="block text-gray-700 font-medium mb-2">الموقع *</label>
                    <input
                      type="text"
                      name="location"
                      value={formData.location}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                      placeholder="مثال: دمشق - المزة"
                      required
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Details */}
            {currentStep === 2 && (
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                <h3 className="text-xl font-semibold text-gray-900 mb-6">تفاصيل المشروع</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-gray-700 font-medium mb-2">الميزانية المتوقعة *</label>
                    <input
                      type="text"
                      name="budget"
                      value={formData.budget}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                      placeholder="مثال: ₺5,000 - ₺8,000"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 font-medium mb-2">الموعد النهائي *</label>
                    <input
                      type="date"
                      name="deadline"
                      value={formData.deadline}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-gray-700 font-medium mb-2">الأولوية</label>
                    <select
                      name="priority"
                      value={formData.priority}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                    >
                      <option value="منخفضة">منخفضة</option>
                      <option value="متوسطة">متوسطة</option>
                      <option value="عالية">عالية</option>
                      <option value="عاجلة">عاجلة</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-gray-700 font-medium mb-2">المواد</label>
                    <select
                      name="materials"
                      value={formData.materials}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                    >
                      <option value="غير محدد">غير محدد</option>
                      <option value="متوفرة">متوفرة من العميل</option>
                      <option value="مطلوبة من الحرفي">مطلوبة من الحرفي</option>
                      <option value="متوفرة جزئياً">متوفرة جزئياً</option>
                    </select>
                  </div>
                </div>

                <div className="mt-6">
                  <label className="block text-gray-700 font-medium mb-2">نوع العمل</label>
                  <input
                    type="text"
                    name="workType"
                    value={formData.workType}
                    onChange={handleInputChange}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                    placeholder="مثال: تركيب، إصلاح، تجديد، صيانة"
                  />
                </div>

                <div className="mt-6">
                  <label className="block text-gray-700 font-medium mb-2">متطلبات إضافية</label>
                  <textarea
                    name="requirements"
                    value={formData.requirements}
                    onChange={handleInputChange}
                    rows={3}
                    className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-navy/20 focus:border-navy"
                    placeholder="أي متطلبات أو ملاحظات إضافية..."
                  />
                </div>
              </div>
            )}

            {/* Step 3: Images and Review */}
            {currentStep === 3 && (
              <div className="space-y-8">
                {/* Image Upload */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">إضافة صور (اختياري)</h3>
                  
                  <div className="border-2 border-dashed border-gray-300 rounded-xl p-8 text-center">
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                      id="image-upload"
                    />
                    <label htmlFor="image-upload" className="cursor-pointer">
                      <div className="text-4xl text-gray-400 mb-4">📷</div>
                      <p className="text-gray-600 mb-2">اضغط لإضافة صور أو اسحب الصور هنا</p>
                      <p className="text-sm text-gray-500">PNG, JPG, GIF حتى 10MB</p>
                    </label>
                  </div>

                  {formData.images.length > 0 && (
                    <div className="mt-6">
                      <h4 className="font-medium text-gray-900 mb-4">الصور المرفوعة ({formData.images.length})</h4>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                        {formData.images.map((image, index) => (
                          <div key={index} className="relative">
                            <img
                              src={URL.createObjectURL(image)}
                              alt={`صورة ${index + 1}`}
                              className="w-full h-24 object-cover rounded-lg"
                            />
                            <button
                              type="button"
                              onClick={() => removeImage(index)}
                              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Project Review */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-8">
                  <h3 className="text-xl font-semibold text-gray-900 mb-6">مراجعة المشروع</h3>
                  
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <span className="text-gray-500">العنوان:</span>
                        <span className="font-medium text-gray-900 mr-2">{formData.title}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">التخصص:</span>
                        <span className="font-medium text-gray-900 mr-2">{formData.category}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">الميزانية:</span>
                        <span className="font-medium text-gray-900 mr-2">{formData.budget}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">الموعد النهائي:</span>
                        <span className="font-medium text-gray-900 mr-2">{formData.deadline}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">الموقع:</span>
                        <span className="font-medium text-gray-900 mr-2">{formData.location}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">الأولوية:</span>
                        <span className="font-medium text-gray-900 mr-2">{formData.priority}</span>
                      </div>
                    </div>
                    
                    <div>
                      <span className="text-gray-500">الوصف:</span>
                      <p className="text-gray-900 mt-1">{formData.description}</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <div>
                {currentStep > 1 && (
                  <Button
                    type="button"
                    onClick={prevStep}
                    variant="outline"
                    className="border-gray-300 text-gray-700"
                  >
                    السابق
                  </Button>
                )}
              </div>
              
              <div className="flex space-x-4 space-x-reverse">
                <Button
                  type="button"
                  variant="outline"
                  className="border-gray-300 text-gray-700"
                  onClick={() => router.push('/client/projects')}
                >
                  إلغاء
                </Button>
                
                {currentStep < 3 ? (
                  <Button
                    type="button"
                    onClick={nextStep}
                    className="bg-gradient-to-r from-navy to-teal"
                    disabled={
                      (currentStep === 1 && (!formData.title || !formData.description || !formData.category || !formData.location)) ||
                      (currentStep === 2 && (!formData.budget || !formData.deadline))
                    }
                  >
                    التالي
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    className="bg-gradient-to-r from-navy to-teal"
                  >
                    نشر المشروع
                  </Button>
                )}
              </div>
            </div>
          </form>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default CreateProjectPage;
