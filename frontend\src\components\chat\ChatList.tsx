'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/Card';

interface ChatItem {
  id: string;
  recipientId: string;
  recipientName: string;
  recipientAvatar?: string;
  lastMessage: string;
  lastMessageTime: string;
  unreadCount: number;
  projectTitle?: string;
  online?: boolean;
}

interface ChatListProps {
  chats: ChatItem[];
  selectedChatId?: string;
  onSelectChat: (chatId: string) => void;
  loading?: boolean;
}

const ChatList: React.FC<ChatListProps> = ({
  chats,
  selectedChatId,
  onSelectChat,
  loading = false
}) => {
  const formatTime = (timestamp: string) => {
    const now = new Date();
    const messageTime = new Date(timestamp);
    const diffInHours = (now.getTime() - messageTime.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 1) {
      return messageTime.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else if (diffInHours < 24) {
      return messageTime.toLocaleTimeString('ar-SA', {
        hour: '2-digit',
        minute: '2-digit'
      });
    } else {
      return messageTime.toLocaleDateString('ar-SA', {
        month: 'short',
        day: 'numeric'
      });
    }
  };

  const truncateMessage = (message: string, maxLength: number = 50) => {
    if (message.length <= maxLength) return message;
    return message.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <Card className="h-full">
        <CardHeader>
          <CardTitle>المحادثات</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>المحادثات</span>
          {chats.filter(chat => chat.unreadCount > 0).length > 0 && (
            <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full">
              {chats.reduce((sum, chat) => sum + chat.unreadCount, 0)}
            </span>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="p-0">
        {chats.length > 0 ? (
          <div className="space-y-1">
            {chats.map((chat) => (
              <div
                key={chat.id}
                onClick={() => onSelectChat(chat.id)}
                className={`p-4 cursor-pointer transition-colors hover:bg-gray-50 border-b border-gray-100 ${
                  selectedChatId === chat.id ? 'bg-blue-50 border-r-4 border-r-teal' : ''
                }`}
              >
                <div className="flex items-center space-x-3 space-x-reverse">
                  {/* Avatar */}
                  <div className="relative">
                    {chat.recipientAvatar ? (
                      <img
                        src={chat.recipientAvatar}
                        alt={chat.recipientName}
                        className="w-12 h-12 rounded-full object-cover"
                      />
                    ) : (
                      <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                        <span className="text-gray-600 font-medium">
                          {chat.recipientName.charAt(0)}
                        </span>
                      </div>
                    )}
                    {chat.online && (
                      <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                    )}
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h4 className="font-semibold text-gray-900 truncate">
                        {chat.recipientName}
                      </h4>
                      <span className="text-xs text-gray-500">
                        {formatTime(chat.lastMessageTime)}
                      </span>
                    </div>
                    
                    {chat.projectTitle && (
                      <p className="text-xs text-blue-600 mb-1 truncate">
                        مشروع: {chat.projectTitle}
                      </p>
                    )}
                    
                    <div className="flex items-center justify-between">
                      <p className="text-sm text-gray-600 truncate">
                        {truncateMessage(chat.lastMessage)}
                      </p>
                      {chat.unreadCount > 0 && (
                        <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full min-w-[20px] text-center">
                          {chat.unreadCount > 99 ? '99+' : chat.unreadCount}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-8 text-center">
            <div className="text-4xl mb-4">💬</div>
            <h3 className="text-lg font-semibold text-gray-700 mb-2">
              لا توجد محادثات
            </h3>
            <p className="text-gray-500 text-sm">
              ابدأ محادثة جديدة مع الحرفيين أو العملاء
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ChatList;
