'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import MainLayout from '@/components/layout/MainLayout';
import { Button } from '@/components/ui/Button';
import { formatDate } from '@/lib/utils';

interface User {
  name: string;
  email: string;
  role: 'client' | 'craftsman';
}

const DashboardPage = () => {
  const [user, setUser] = useState<User | null>(null);

  useEffect(() => {
    // Get user from localStorage (in a real app, you'd get this from your auth system)
    const userData = localStorage.getItem('user');
    if (userData) {
      setUser(JSON.parse(userData));
    }
  }, []);

  const stats = [
    {
      title: 'المشاريع النشطة',
      value: '3',
      icon: '📋',
      color: 'bg-blue-50 text-blue-600',
    },
    {
      title: 'العروض المستلمة',
      value: '12',
      icon: '💼',
      color: 'bg-green-50 text-green-600',
    },
    {
      title: 'المشاريع المكتملة',
      value: '8',
      icon: '✅',
      color: 'bg-purple-50 text-purple-600',
    },
    {
      title: 'إجمالي الإنفاق',
      value: '2,450,000 ل.س',
      icon: '💰',
      color: 'bg-yellow-50 text-yellow-600',
    },
  ];

  const recentJobs = [
    {
      id: 1,
      title: 'تصميم وتنفيذ خزائن مطبخ',
      status: 'نشط',
      offers: 5,
      budget: '150,000 ل.س',
      deadline: '2024-02-15',
    },
    {
      id: 2,
      title: 'إصلاح نظام السباكة',
      status: 'في الانتظار',
      offers: 3,
      budget: '75,000 ل.س',
      deadline: '2024-02-10',
    },
    {
      id: 3,
      title: 'دهان شقة سكنية',
      status: 'مكتمل',
      offers: 8,
      budget: '200,000 ل.س',
      deadline: '2024-01-30',
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'نشط':
        return 'bg-green-100 text-green-800';
      case 'في الانتظار':
        return 'bg-yellow-100 text-yellow-800';
      case 'مكتمل':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  if (!user) {
    return (
      <MainLayout>
        <div className="min-h-screen bg-beige flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-navy mb-4">يرجى تسجيل الدخول</h1>
            <Link href="/login">
              <Button>تسجيل الدخول</Button>
            </Link>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="min-h-screen bg-beige py-8">
        <div className="container mx-auto px-4">
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-navy mb-2">
              مرحباً، {user.name}
            </h1>
            <p className="text-gray-600">
              {user.role === 'client' ? 'إدارة مشاريعك والعروض المستلمة' : 'إدارة خدماتك والعروض المقدمة'}
            </p>
          </div>

          {/* Quick Actions */}
          <div className="mb-8">
            <div className="flex flex-wrap gap-4">
              {user.role === 'client' ? (
                <>
                  <Link href="/post-job">
                    <Button>إنشاء مشروع جديد</Button>
                  </Link>
                  <Link href="/dashboard/jobs">
                    <Button variant="outline">مشاريعي</Button>
                  </Link>
                  <Link href="/dashboard/offers">
                    <Button variant="outline">العروض المستلمة</Button>
                  </Link>
                </>
              ) : (
                <>
                  <Link href="/jobs">
                    <Button>تصفح المشاريع</Button>
                  </Link>
                  <Link href="/dashboard/my-offers">
                    <Button variant="outline">عروضي</Button>
                  </Link>
                  <Link href="/dashboard/profile">
                    <Button variant="outline">ملفي الشخصي</Button>
                  </Link>
                </>
              )}
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6">
                <div className="flex items-center">
                  <div className={`p-3 rounded-full ${stat.color} text-2xl`}>
                    {stat.icon}
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-navy">{stat.value}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Recent Jobs */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-navy">
                {user.role === 'client' ? 'مشاريعي الأخيرة' : 'المشاريع المتاحة'}
              </h2>
              <Link href="/dashboard/jobs" className="text-teal hover:text-navy">
                عرض الكل
              </Link>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-right py-3 px-4 font-medium text-gray-700">المشروع</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">الحالة</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">العروض</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">الميزانية</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">الموعد النهائي</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-700">الإجراءات</th>
                  </tr>
                </thead>
                <tbody>
                  {recentJobs.map((job) => (
                    <tr key={job.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="font-medium text-navy">{job.title}</div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(job.status)}`}>
                          {job.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-600">{job.offers}</td>
                      <td className="py-3 px-4 text-gray-600">{job.budget}</td>
                      <td className="py-3 px-4 text-gray-600">{job.deadline}</td>
                      <td className="py-3 px-4">
                        <Link href={`/jobs/${job.id}`} className="text-teal hover:text-navy text-sm">
                          عرض التفاصيل
                        </Link>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default DashboardPage;
