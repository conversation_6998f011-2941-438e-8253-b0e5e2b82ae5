'use client';

import React from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import StatsCard from '@/components/dashboard/StatsCard';
import ActivityFeed from '@/components/dashboard/ActivityFeed';
import QuickActions from '@/components/dashboard/QuickActions';
import { useAuth } from '@/hooks/useAuth';

const DashboardPage = () => {
  const { user, isClient, isCraftsman, isAdmin } = useAuth();

  // إحصائيات ديناميكية حسب نوع المستخدم
  const getStatsData = () => {
    if (isClient) {
      return [
        {
          title: 'مشاريعي النشطة',
          value: '5',
          change: '+2 هذا الأسبوع',
          changeType: 'increase' as const,
          icon: (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          ),
          color: 'blue' as const
        },
        {
          title: 'العروض المستلمة',
          value: '23',
          change: '+8 جديدة',
          changeType: 'increase' as const,
          icon: (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
            </svg>
          ),
          color: 'green' as const
        },
        {
          title: 'المشاريع المكتملة',
          value: '12',
          change: '+3 هذا الشهر',
          changeType: 'increase' as const,
          icon: (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          color: 'purple' as const
        },
        {
          title: 'إجمالي الإنفاق',
          value: '₺25,000',
          change: 'هذا العام',
          changeType: 'neutral' as const,
          icon: (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          ),
          color: 'orange' as const
        }
      ];
    } else if (isCraftsman) {
      return [
        {
          title: 'المشاريع الجارية',
          value: '4',
          change: '+1 هذا الأسبوع',
          changeType: 'increase' as const,
          icon: (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
            </svg>
          ),
          color: 'blue' as const
        },
        {
          title: 'العروض المرسلة',
          value: '18',
          change: '+5 جديدة',
          changeType: 'increase' as const,
          icon: (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          ),
          color: 'orange' as const
        },
        {
          title: 'المشاريع المكتملة',
          value: '32',
          change: '+3 هذا الشهر',
          changeType: 'increase' as const,
          icon: (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          ),
          color: 'green' as const
        },
        {
          title: 'التقييم',
          value: '4.9',
          change: 'من 5 نجوم',
          changeType: 'neutral' as const,
          icon: (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
            </svg>
          ),
          color: 'teal' as const
        }
      ];
    } else if (isAdmin) {
      return [
        {
          title: 'إجمالي المستخدمين',
          value: '2,847',
          change: '+12% هذا الشهر',
          changeType: 'increase' as const,
          icon: (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          ),
          color: 'blue' as const
        },
        {
          title: 'المشاريع النشطة',
          value: '156',
          change: '+8% هذا الأسبوع',
          changeType: 'increase' as const,
          icon: (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          ),
          color: 'green' as const
        },
        {
          title: 'الإيرادات الشهرية',
          value: '₺125,000',
          change: '+15% من الشهر الماضي',
          changeType: 'increase' as const,
          icon: (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          ),
          color: 'purple' as const
        },
        {
          title: 'معدل الرضا',
          value: '4.8',
          change: '+0.2 من الشهر الماضي',
          changeType: 'increase' as const,
          icon: (
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
            </svg>
          ),
          color: 'teal' as const
        }
      ];
    }
    return [];
  };

  // الإجراءات السريعة حسب نوع المستخدم
  const getQuickActions = () => {
    if (isClient) {
      return [
        {
          id: 'create-project',
          title: 'إنشاء مشروع جديد',
          description: 'ابدأ مشروعك الجديد واحصل على عروض من الحرفيين',
          href: '/projects/create',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          ),
          color: 'blue' as const
        },
        {
          id: 'browse-craftsmen',
          title: 'تصفح الحرفيين',
          description: 'ابحث عن حرفيين مؤهلين في منطقتك',
          href: '/craftsmen',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          ),
          color: 'green' as const
        },
        {
          id: 'view-offers',
          title: 'العروض المستلمة',
          description: 'راجع العروض الجديدة على مشاريعك',
          href: '/client/offers',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
            </svg>
          ),
          color: 'purple' as const,
          badge: '12'
        },
        {
          id: 'messages',
          title: 'الرسائل',
          description: 'تواصل مع الحرفيين ومتابعة المحادثات',
          href: '/messages',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          ),
          color: 'orange' as const,
          badge: '3'
        }
      ];
    } else if (isCraftsman) {
      return [
        {
          id: 'browse-projects',
          title: 'تصفح المشاريع المتاحة',
          description: 'ابحث عن مشاريع جديدة تناسب مهاراتك',
          href: '/craftsman/projects',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          ),
          color: 'blue' as const
        },
        {
          id: 'my-offers',
          title: 'إدارة عروضي',
          description: 'تابع حالة العروض التي قدمتها',
          href: '/craftsman/offers',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
            </svg>
          ),
          color: 'green' as const
        },
        {
          id: 'portfolio',
          title: 'معرض أعمالي',
          description: 'أضف وأدر معرض أعمالك المكتملة',
          href: '/craftsman/portfolio',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2-2v16a2 2 0 002 2z" />
            </svg>
          ),
          color: 'purple' as const
        },
        {
          id: 'earnings',
          title: 'الأرباح',
          description: 'تتبع أرباحك ومدفوعاتك',
          href: '/craftsman/earnings',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          ),
          color: 'orange' as const
        }
      ];
    } else if (isAdmin) {
      return [
        {
          id: 'manage-users',
          title: 'إدارة المستخدمين',
          description: 'عرض وإدارة جميع المستخدمين',
          href: '/admin/users',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          ),
          color: 'blue' as const
        },
        {
          id: 'monitor-projects',
          title: 'مراقبة المشاريع',
          description: 'متابعة حالة جميع المشاريع',
          href: '/admin/projects',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          ),
          color: 'green' as const
        },
        {
          id: 'reports',
          title: 'التقارير المالية',
          description: 'عرض التقارير والإحصائيات',
          href: '/admin/reports',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          ),
          color: 'purple' as const
        },
        {
          id: 'settings',
          title: 'إعدادات النظام',
          description: 'تكوين إعدادات المنصة',
          href: '/admin/settings',
          icon: (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          ),
          color: 'orange' as const
        }
      ];
    }
    return [];
  };

  // بيانات الأنشطة الحديثة
  const getRecentActivities = () => {
    if (isClient) {
      return [
        {
          id: '1',
          type: 'offer_received' as const,
          title: 'عرض جديد على مشروعك',
          description: 'تم استلام عرض جديد على مشروع تجديد المطبخ',
          time: 'منذ 5 دقائق',
          user: 'أحمد النجار',
          href: '/client/offers'
        },
        {
          id: '2',
          type: 'project_completed' as const,
          title: 'تم إكمال مشروع',
          description: 'تم إكمال مشروع دهان الشقة بنجاح',
          time: 'منذ ساعتين',
          user: 'محمد الدهان'
        },
        {
          id: '3',
          type: 'message_received' as const,
          title: 'رسالة جديدة',
          description: 'رسالة من سارة الكهربائية حول مشروع الإضاءة',
          time: 'منذ 4 ساعات',
          user: 'سارة الكهربائية',
          href: '/messages'
        }
      ];
    } else if (isCraftsman) {
      return [
        {
          id: '1',
          type: 'project_created' as const,
          title: 'مشروع جديد متاح',
          description: 'مشروع تركيب خزائن مطبخ في دمشق',
          time: 'منذ 10 دقائق',
          href: '/craftsman/projects'
        },
        {
          id: '2',
          type: 'offer_received' as const,
          title: 'تم قبول عرضك',
          description: 'تم قبول عرضك على مشروع تجديد الحمام',
          time: 'منذ ساعة',
          user: 'فاطمة أحمد'
        },
        {
          id: '3',
          type: 'payment_completed' as const,
          title: 'تم استلام دفعة',
          description: 'تم استلام دفعة مشروع إصلاح السباكة',
          time: 'منذ 3 ساعات'
        }
      ];
    } else if (isAdmin) {
      return [
        {
          id: '1',
          type: 'user_registered' as const,
          title: 'مستخدم جديد',
          description: 'انضم مستخدم جديد إلى المنصة',
          time: 'منذ 2 دقائق',
          user: 'علي محمد'
        },
        {
          id: '2',
          type: 'project_completed' as const,
          title: 'مشروع مكتمل',
          description: 'تم إكمال مشروع تجديد المطبخ بنجاح',
          time: 'منذ 15 دقيقة'
        },
        {
          id: '3',
          type: 'payment_completed' as const,
          title: 'دفعة جديدة',
          description: 'تم استلام دفعة من عمولة المشروع',
          time: 'منذ 30 دقيقة'
        }
      ];
    }
    return [];
  };

  const statsData = getStatsData();
  const quickActions = getQuickActions();
  const recentActivities = getRecentActivities();

  return (
    <ProtectedRoute>
      <DashboardLayout
        title={`مرحباً، ${user?.name || 'مستخدم'}`}
        subtitle={`لوحة تحكم ${isClient ? 'العميل' : isCraftsman ? 'الحرفي' : 'المدير'}`}
      >
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {statsData.map((stat, index) => (
              <StatsCard
                key={index}
                title={stat.title}
                value={stat.value}
                change={stat.change}
                changeType={stat.changeType}
                icon={stat.icon}
                color={stat.color}
              />
            ))}
          </div>

          {/* Quick Actions */}
          <QuickActions actions={quickActions} />

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Recent Activity */}
            <ActivityFeed activities={recentActivities} />

            {/* Additional Content based on role */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">
                {isClient ? 'مشاريعي الأخيرة' : isCraftsman ? 'المشاريع المتاحة' : 'إحصائيات سريعة'}
              </h3>

              {isClient && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">تجديد المطبخ</h4>
                      <p className="text-sm text-gray-600">12 عرض مستلم</p>
                    </div>
                    <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">نشط</span>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">إصلاح السباكة</h4>
                      <p className="text-sm text-gray-600">8 عروض مستلمة</p>
                    </div>
                    <span className="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm">في الانتظار</span>
                  </div>
                </div>
              )}

              {isCraftsman && (
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">تركيب خزائن مطبخ</h4>
                      <p className="text-sm text-gray-600">دمشق - المزة</p>
                    </div>
                    <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">جديد</span>
                  </div>
                  <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">دهان شقة كاملة</h4>
                      <p className="text-sm text-gray-600">حلب - الفرقان</p>
                    </div>
                    <span className="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">متاح</span>
                  </div>
                </div>
              )}

              {isAdmin && (
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">89%</div>
                    <div className="text-sm text-gray-600">معدل إكمال المشاريع</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">4.8</div>
                    <div className="text-sm text-gray-600">متوسط التقييمات</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">156</div>
                    <div className="text-sm text-gray-600">مشاريع نشطة</div>
                  </div>
                  <div className="text-center p-4 bg-orange-50 rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">+12%</div>
                    <div className="text-sm text-gray-600">نمو شهري</div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default DashboardPage;