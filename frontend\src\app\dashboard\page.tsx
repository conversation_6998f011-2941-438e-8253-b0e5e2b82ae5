'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSimpleAuth } from '@/hooks/useSimpleAuth';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import supabase from '@/lib/supabase';

export default function DashboardPage() {
  const { user, loading, isAuthenticated } = useSimpleAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [activeTab, setActiveTab] = useState('overview');
  const [projects, setProjects] = useState<any[]>([]);
  const [bids, setBids] = useState<any[]>([]);
  const [dataLoading, setDataLoading] = useState(true);

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      router.push('/auth/login');
      return;
    }

    // تحديد التبويب النشط من URL
    const tab = searchParams.get('tab') || 'overview';
    setActiveTab(tab);

    if (isAuthenticated) {
      loadDashboardData();
    }
  }, [loading, isAuthenticated, router, searchParams]);

  const loadDashboardData = async () => {
    try {
      if (user?.role === 'CLIENT') {
        // تحميل مشاريع العميل
        const { data: projectsData, error: projectsError } = await supabase
          .from('projects')
          .select('*')
          .eq('client_id', user.id)
          .order('created_at', { ascending: false });

        if (projectsError) throw projectsError;
        setProjects(projectsData || []);

      } else if (user?.role === 'CRAFTSMAN') {
        // تحميل عروض الحرفي
        const { data: bidsData, error: bidsError } = await supabase
          .from('bids')
          .select(`
            *,
            project:projects(title, location, category, status)
          `)
          .eq('craftsman_id', user.id)
          .order('created_at', { ascending: false });

        if (bidsError) throw bidsError;
        setBids(bidsData || []);

        // تحميل المشاريع المتاحة
        const { data: availableProjects, error: availableError } = await supabase
          .from('projects')
          .select('*')
          .eq('status', 'OPEN')
          .order('created_at', { ascending: false })
          .limit(10);

        if (availableError) throw availableError;
        setProjects(availableProjects || []);
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setDataLoading(false);
    }
  };

  const navigateToTab = (tab: string) => {
    setActiveTab(tab);
    router.push(`/dashboard?tab=${tab}`);
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <DashboardLayout
      title={`مرحباً ${user?.name || 'بك'}`}
      subtitle={user?.role === 'CLIENT' ? 'لوحة تحكم العميل' : user?.role === 'CRAFTSMAN' ? 'لوحة تحكم الحرفي' : 'لوحة تحكم المدير'}
    >
      <div className="space-y-6">
        {/* التبويبات */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 space-x-reverse">
            {user?.role === 'CLIENT' && (
              <>
                <button
                  onClick={() => navigateToTab('overview')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'overview'
                      ? 'border-teal text-teal'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  نظرة عامة
                </button>
                <button
                  onClick={() => navigateToTab('projects')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'projects'
                      ? 'border-teal text-teal'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  مشاريعي
                </button>
                <button
                  onClick={() => navigateToTab('create-project')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'create-project'
                      ? 'border-teal text-teal'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  إنشاء مشروع
                </button>
              </>
            )}

            {user?.role === 'CRAFTSMAN' && (
              <>
                <button
                  onClick={() => navigateToTab('overview')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'overview'
                      ? 'border-teal text-teal'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  نظرة عامة
                </button>
                <button
                  onClick={() => navigateToTab('available-projects')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'available-projects'
                      ? 'border-teal text-teal'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  المشاريع المتاحة
                </button>
                <button
                  onClick={() => navigateToTab('my-bids')}
                  className={`py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === 'my-bids'
                      ? 'border-teal text-teal'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  عروضي
                </button>
              </>
            )}
          </nav>
        </div>

        {/* محتوى التبويبات */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>إحصائيات سريعة</CardTitle>
              </CardHeader>
              <CardContent>
                {user?.role === 'CLIENT' ? (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>إجمالي المشاريع:</span>
                      <span className="font-bold">{projects.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>المشاريع النشطة:</span>
                      <span className="font-bold text-green-600">
                        {projects.filter(p => p.status === 'OPEN' || p.status === 'IN_PROGRESS').length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>المشاريع المكتملة:</span>
                      <span className="font-bold text-blue-600">
                        {projects.filter(p => p.status === 'COMPLETED').length}
                      </span>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>إجمالي العروض:</span>
                      <span className="font-bold">{bids.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>العروض المقبولة:</span>
                      <span className="font-bold text-green-600">
                        {bids.filter(b => b.status === 'ACCEPTED').length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>في الانتظار:</span>
                      <span className="font-bold text-yellow-600">
                        {bids.filter(b => b.status === 'PENDING').length}
                      </span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>الإجراءات السريعة</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {user?.role === 'CLIENT' ? (
                    <>
                      <Button
                        onClick={() => navigateToTab('create-project')}
                        className="w-full bg-gradient-to-r from-teal to-navy"
                      >
                        إنشاء مشروع جديد
                      </Button>
                      <Button
                        onClick={() => navigateToTab('projects')}
                        variant="outline"
                        className="w-full"
                      >
                        عرض مشاريعي
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button
                        onClick={() => navigateToTab('available-projects')}
                        className="w-full bg-gradient-to-r from-teal to-navy"
                      >
                        تصفح المشاريع
                      </Button>
                      <Button
                        onClick={() => navigateToTab('my-bids')}
                        variant="outline"
                        className="w-full"
                      >
                        عرض عروضي
                      </Button>
                    </>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* تبويب مشاريعي للعملاء */}
        {activeTab === 'projects' && user?.role === 'CLIENT' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold text-navy">مشاريعي</h2>
              <Button
                onClick={() => navigateToTab('create-project')}
                className="bg-gradient-to-r from-teal to-navy"
              >
                إنشاء مشروع جديد
              </Button>
            </div>

            {projects.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <div className="text-6xl mb-4">📋</div>
                  <h3 className="text-xl font-bold text-gray-600 mb-2">لا توجد مشاريع بعد</h3>
                  <p className="text-gray-500 mb-4">ابدأ بإنشاء مشروعك الأول</p>
                  <Button
                    onClick={() => navigateToTab('create-project')}
                    className="bg-gradient-to-r from-teal to-navy"
                  >
                    إنشاء مشروع
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {projects.map((project) => (
                  <Card key={project.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <CardTitle className="text-lg">{project.title}</CardTitle>
                        <span className={`px-2 py-1 rounded text-xs font-medium ${
                          project.status === 'OPEN' ? 'bg-green-100 text-green-800' :
                          project.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                          project.status === 'COMPLETED' ? 'bg-purple-100 text-purple-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {project.status === 'OPEN' ? 'مفتوح' :
                           project.status === 'IN_PROGRESS' ? 'قيد التنفيذ' :
                           project.status === 'COMPLETED' ? 'مكتمل' : project.status}
                        </span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <p className="text-gray-600 text-sm">{project.description}</p>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">الفئة:</span>
                          <span className="font-medium">{project.category}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">الموقع:</span>
                          <span className="font-medium">{project.location}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">الميزانية:</span>
                          <span className="font-medium text-green-600">
                            {project.budget_min?.toLocaleString()} - {project.budget_max?.toLocaleString()} ل.س
                          </span>
                        </div>
                        <div className="pt-3 border-t">
                          <Button
                            onClick={() => router.push(`/projects/${project.id}`)}
                            variant="outline"
                            className="w-full"
                          >
                            عرض التفاصيل
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {/* تبويب المشاريع المتاحة للحرفيين */}
        {activeTab === 'available-projects' && user?.role === 'CRAFTSMAN' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-navy">المشاريع المتاحة</h2>

            {projects.length === 0 ? (
              <Card>
                <CardContent className="text-center py-12">
                  <div className="text-6xl mb-4">🔍</div>
                  <h3 className="text-xl font-bold text-gray-600 mb-2">لا توجد مشاريع متاحة حالياً</h3>
                  <p className="text-gray-500">تحقق مرة أخرى لاحقاً</p>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {projects.map((project) => (
                  <Card key={project.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <CardTitle className="text-lg">{project.title}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        <p className="text-gray-600 text-sm">{project.description}</p>
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <span className="text-gray-500">الفئة:</span>
                            <p className="font-medium">{project.category}</p>
                          </div>
                          <div>
                            <span className="text-gray-500">الموقع:</span>
                            <p className="font-medium">{project.location}</p>
                          </div>
                        </div>
                        <div className="text-sm">
                          <span className="text-gray-500">الميزانية:</span>
                          <p className="font-bold text-green-600">
                            {project.budget_min?.toLocaleString()} - {project.budget_max?.toLocaleString()} ل.س
                          </p>
                        </div>
                        <div className="pt-3 border-t flex space-x-2 space-x-reverse">
                          <Button
                            onClick={() => router.push(`/projects/${project.id}`)}
                            variant="outline"
                            className="flex-1"
                          >
                            عرض التفاصيل
                          </Button>
                          <Button
                            onClick={() => router.push(`/projects/${project.id}/submit-bid`)}
                            className="flex-1 bg-gradient-to-r from-teal to-navy"
                          >
                            تقديم عرض
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {/* باقي التبويبات */}
        {(activeTab === 'create-project' || activeTab === 'my-bids') && (
          <Card>
            <CardContent className="text-center py-12">
              <div className="text-6xl mb-4">🚧</div>
              <h3 className="text-xl font-bold text-gray-600 mb-2">قيد التطوير</h3>
              <p className="text-gray-500">هذا القسم قيد التطوير وسيكون متاحاً قريباً</p>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
}