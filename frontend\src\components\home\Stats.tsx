'use client';

import React, { useState, useEffect } from 'react';

const Stats = () => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    const element = document.getElementById('stats-section');
    if (element) {
      observer.observe(element);
    }

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, []);

  const stats = [
    {
      id: 1,
      number: 2500,
      suffix: '+',
      title: 'حرفي محترف',
      description: 'من جميع أنحاء سوريا',
      icon: '👷‍♂️',
      color: 'from-blue-500 to-blue-600',
      delay: 0
    },
    {
      id: 2,
      number: 15000,
      suffix: '+',
      title: 'مشروع مكتمل',
      description: 'بنجاح وجودة عالية',
      icon: '✅',
      color: 'from-green-500 to-green-600',
      delay: 200
    },
    {
      id: 3,
      number: 98,
      suffix: '%',
      title: 'رضا العملاء',
      description: 'تقييم ممتاز',
      icon: '⭐',
      color: 'from-yellow-500 to-yellow-600',
      delay: 400
    },
    {
      id: 4,
      number: 50,
      suffix: '+',
      title: 'مدينة سورية',
      description: 'نغطي جميع المحافظات',
      icon: '🏙️',
      color: 'from-purple-500 to-purple-600',
      delay: 600
    }
  ];

  const CountUpAnimation = ({ end, duration = 2000, suffix = '', delay = 0 }: {
    end: number;
    duration?: number;
    suffix?: string;
    delay?: number;
  }) => {
    const [count, setCount] = useState(0);
    const [mounted, setMounted] = useState(false);

    useEffect(() => {
      setMounted(true);
    }, []);

    useEffect(() => {
      if (!isVisible || !mounted) return;

      const timer = setTimeout(() => {
        let startTime: number;
        const animate = (currentTime: number) => {
          if (!startTime) startTime = currentTime;
          const progress = Math.min((currentTime - startTime) / duration, 1);

          const easeOutQuart = 1 - Math.pow(1 - progress, 4);
          setCount(Math.floor(easeOutQuart * end));

          if (progress < 1) {
            requestAnimationFrame(animate);
          }
        };
        requestAnimationFrame(animate);
      }, delay);

      return () => clearTimeout(timer);
    }, [isVisible, mounted, end, duration, delay]);

    if (!mounted) {
      return <span>0{suffix}</span>;
    }

    return <span>{count.toLocaleString()}{suffix}</span>;
  };

  return (
    <section id="stats-section" className="py-20 relative overflow-hidden">
      {/* خلفية متدرجة مع أنماط */}
      <div className="absolute inset-0 bg-gradient-to-br from-navy via-teal to-skyblue">
        <div className="absolute inset-0 bg-black/10"></div>
        {/* أنماط هندسية */}
        <div className="absolute top-0 left-0 w-full h-full opacity-10">
          <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-xl"></div>
          <div className="absolute top-32 right-20 w-24 h-24 bg-white rounded-full blur-xl"></div>
          <div className="absolute bottom-20 left-32 w-40 h-40 bg-white rounded-full blur-xl"></div>
          <div className="absolute bottom-32 right-10 w-28 h-28 bg-white rounded-full blur-xl"></div>
        </div>

        {/* شبكة نقطية */}
        <div className="absolute inset-0 opacity-20">
          <div className="w-full h-full" style={{
            backgroundImage: 'radial-gradient(circle, white 1px, transparent 1px)',
            backgroundSize: '30px 30px'
          }}></div>
        </div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="text-center mb-16">
          <div className="inline-block bg-white/20 backdrop-blur-sm rounded-full px-6 py-2 mb-6">
            <span className="text-white text-sm font-medium">📊 أرقام تتحدث عن نفسها</span>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            منصة دوزان في أرقام
          </h2>
          <p className="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
            نفخر بالثقة التي وضعها فينا آلاف العملاء والحرفيين في جميع أنحاء سوريا
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {stats.map((stat) => (
            <div
              key={stat.id}
              className={`group relative bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-500 hover:scale-105 hover:-translate-y-2`}
              style={{
                animationDelay: `${stat.delay}ms`,
                animation: isVisible ? 'fadeInUp 0.8s ease-out forwards' : 'none'
              }}
            >
              {/* تأثير الضوء */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

              {/* الأيقونة */}
              <div className="relative z-10 mb-6">
                <div className="w-16 h-16 mx-auto bg-white/20 rounded-2xl flex items-center justify-center text-3xl group-hover:scale-110 transition-transform duration-300">
                  {stat.icon}
                </div>
              </div>

              {/* الرقم */}
              <div className="relative z-10 text-center">
                <div className="text-4xl md:text-5xl font-bold text-white mb-2 group-hover:scale-110 transition-transform duration-300">
                  <CountUpAnimation
                    end={stat.number}
                    suffix={stat.suffix}
                    delay={stat.delay}
                  />
                </div>
                <h3 className="text-xl font-semibold text-white mb-2">
                  {stat.title}
                </h3>
                <p className="text-white/80 text-sm">
                  {stat.description}
                </p>
              </div>

              {/* تأثير الحدود المتوهجة */}
              <div className="absolute inset-0 rounded-2xl border-2 border-transparent bg-gradient-to-r from-white/30 via-transparent to-white/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>
          ))}
        </div>

        {/* شريط الشركاء المحسن - Partners Slider */}
        <div className="mt-20">
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-8 border border-white/20 overflow-hidden">
            <div className="text-center mb-8">
              <div className="inline-block bg-white/20 backdrop-blur-sm rounded-full px-6 py-2 mb-4">
                <span className="text-white text-sm font-medium">🤝 شركاؤنا</span>
              </div>
              <h3 className="text-2xl md:text-3xl font-bold text-white mb-2">
                موثوق من قبل أفضل الشركات في سوريا
              </h3>
              <p className="text-white/80 text-sm md:text-base">
                يثق بنا المئات من الشركات والمؤسسات الرائدة
              </p>
            </div>

            {/* Partners Slider */}
            <div className="relative">
              <div className="flex animate-scroll-right space-x-12 space-x-reverse">
                {[
                  { name: 'شركة البناء السورية', icon: '🏗️', specialty: 'البناء والإنشاءات' },
                  { name: 'مجموعة الإعمار', icon: '🏢', specialty: 'التطوير العقاري' },
                  { name: 'شركة التطوير العقاري', icon: '🏘️', specialty: 'المشاريع السكنية' },
                  { name: 'مؤسسة الحرفيين', icon: '👷‍♂️', specialty: 'الحرف التقليدية' },
                  { name: 'شركة الديكور الحديث', icon: '🎨', specialty: 'التصميم الداخلي' },
                  { name: 'مجموعة الصيانة', icon: '🔧', specialty: 'الصيانة العامة' }
                ].concat([
                  { name: 'شركة البناء السورية', icon: '🏗️', specialty: 'البناء والإنشاءات' },
                  { name: 'مجموعة الإعمار', icon: '🏢', specialty: 'التطوير العقاري' },
                  { name: 'شركة التطوير العقاري', icon: '🏘️', specialty: 'المشاريع السكنية' },
                  { name: 'مؤسسة الحرفيين', icon: '👷‍♂️', specialty: 'الحرف التقليدية' }
                ]).map((partner, index) => (
                  <div
                    key={index}
                    className="flex-shrink-0 group"
                  >
                    <div className="bg-white/15 backdrop-blur-sm rounded-xl p-6 border border-white/20 hover:bg-white/25 hover:border-white/40 transition-all duration-300 hover:scale-105 min-w-[280px]">
                      <div className="flex items-center space-x-4 space-x-reverse">
                        <div className="text-3xl group-hover:scale-110 transition-transform duration-300">
                          {partner.icon}
                        </div>
                        <div className="text-right">
                          <h4 className="text-white font-bold text-lg group-hover:text-yellow-300 transition-colors duration-300">
                            {partner.name}
                          </h4>
                          <p className="text-white/70 text-sm">
                            {partner.specialty}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        @keyframes scroll-right {
          0% {
            transform: translateX(0);
          }
          100% {
            transform: translateX(-50%);
          }
        }

        .animate-scroll-right {
          animation: scroll-right 30s linear infinite;
        }

        .animate-scroll-right:hover {
          animation-play-state: paused;
        }
      `}</style>
    </section>
  );
};

export default Stats;
