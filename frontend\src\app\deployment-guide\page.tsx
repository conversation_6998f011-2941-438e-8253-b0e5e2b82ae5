'use client';

import React, { useState } from 'react';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

const DeploymentGuidePage = () => {
  const [activeTab, setActiveTab] = useState('services');

  const externalServices = [
    {
      name: 'قاعدة البيانات',
      service: 'PostgreSQL',
      provider: 'Railway / Supabase',
      cost: 'مجاني للبداية',
      required: true,
      description: 'قاعدة بيانات PostgreSQL للإنتاج',
      setup: [
        'إنشاء حساب على Railway.app',
        'إنشاء مشروع PostgreSQL جديد',
        'نسخ DATABASE_URL',
        'تطبيق Prisma migrations'
      ],
      alternatives: [
        'Supabase (مجاني + ميزات إضافية)',
        'PlanetScale (MySQL)',
        'MongoDB Atlas'
      ]
    },
    {
      name: 'استضافة Frontend',
      service: 'Vercel',
      provider: 'Vercel',
      cost: 'مجاني',
      required: true,
      description: 'استضافة تطبيق Next.js',
      setup: [
        'ربط GitHub repository',
        'تكوين متغيرات البيئة',
        'نشر تلقائي عند كل commit'
      ],
      alternatives: [
        'Netlify',
        'GitHub Pages',
        'AWS Amplify'
      ]
    },
    {
      name: 'البريد الإلكتروني',
      service: 'Gmail SMTP',
      provider: 'Google',
      cost: 'مجاني (محدود)',
      required: true,
      description: 'إرسال رسائل التأكيد والإشعارات',
      setup: [
        'تفعيل 2FA على حساب Gmail',
        'إنشاء App Password',
        'تكوين SMTP settings'
      ],
      alternatives: [
        'SendGrid (مجاني حتى 100 رسالة/يوم)',
        'Mailgun',
        'Amazon SES'
      ]
    },
    {
      name: 'تسجيل الدخول بـ Google',
      service: 'Google OAuth',
      provider: 'Google Cloud',
      cost: 'مجاني',
      required: false,
      description: 'تسجيل دخول اختياري بحساب Google',
      setup: [
        'إنشاء مشروع في Google Cloud Console',
        'تفعيل Google+ API',
        'إنشاء OAuth 2.0 credentials',
        'تكوين authorized domains'
      ],
      alternatives: [
        'Facebook Login',
        'GitHub OAuth',
        'تعطيل الميزة مؤقتاً'
      ]
    },
    {
      name: 'تخزين الملفات',
      service: 'Local Storage',
      provider: 'محلي',
      cost: 'مجاني',
      required: false,
      description: 'تخزين الصور محلياً في البداية',
      setup: [
        'استخدام مجلد /public/uploads',
        'تكوين API route للرفع',
        'إعداد حدود الحجم والنوع'
      ],
      alternatives: [
        'Cloudinary (مجاني حتى 25GB)',
        'AWS S3',
        'Google Cloud Storage'
      ]
    }
  ];

  const deploymentSteps = [
    {
      phase: 'التحضير',
      steps: [
        'تنظيف الكود وإزالة console.log',
        'تحديث متغيرات البيئة للإنتاج',
        'اختبار شامل لجميع الميزات',
        'تحسين الأداء والصور',
        'إنشاء repository على GitHub'
      ]
    },
    {
      phase: 'قاعدة البيانات',
      steps: [
        'إنشاء حساب Railway/Supabase',
        'إنشاء قاعدة بيانات PostgreSQL',
        'نسخ DATABASE_URL',
        'تطبيق Prisma migrations',
        'إدخال البيانات الأولية'
      ]
    },
    {
      phase: 'البريد الإلكتروني',
      steps: [
        'إنشاء حساب Gmail مخصص للمشروع',
        'تفعيل 2FA',
        'إنشاء App Password',
        'تكوين SMTP settings',
        'اختبار إرسال البريد'
      ]
    },
    {
      phase: 'النشر',
      steps: [
        'ربط GitHub مع Vercel',
        'تكوين متغيرات البيئة في Vercel',
        'نشر أول إصدار',
        'اختبار الموقع المنشور',
        'تكوين domain مخصص (اختياري)'
      ]
    },
    {
      phase: 'المراقبة',
      steps: [
        'إعداد Google Analytics',
        'مراقبة الأخطاء والأداء',
        'إعداد النسخ الاحتياطية',
        'تكوين SSL certificates',
        'اختبار الأمان'
      ]
    }
  ];

  const localAlternatives = [
    {
      service: 'قاعدة البيانات',
      local: 'SQLite + Prisma',
      description: 'يمكن استخدام SQLite محلياً للتطوير والاختبار',
      pros: ['لا يحتاج إنترنت', 'سهل الإعداد', 'مجاني تماماً'],
      cons: ['محدود للمشاريع الصغيرة', 'لا يدعم المستخدمين المتعددين بكفاءة']
    },
    {
      service: 'البريد الإلكتروني',
      local: 'محاكاة في Console',
      description: 'طباعة رسائل البريد في console بدلاً من الإرسال',
      pros: ['لا يحتاج إعداد خارجي', 'سريع للتطوير'],
      cons: ['لا يعمل في الإنتاج', 'لا يمكن اختبار التجربة الحقيقية']
    },
    {
      service: 'تسجيل الدخول بـ Google',
      local: 'تعطيل مؤقت',
      description: 'إزالة زر Google وإبقاء تسجيل الدخول العادي فقط',
      pros: ['لا يحتاج إعداد', 'يقلل التعقيد'],
      cons: ['تجربة مستخدم أقل', 'فقدان ميزة مهمة']
    }
  ];

  const costBreakdown = [
    { service: 'Railway PostgreSQL', cost: '$0/شهر (مجاني)', limit: 'حتى 500MB' },
    { service: 'Vercel Hosting', cost: '$0/شهر (مجاني)', limit: 'حتى 100GB bandwidth' },
    { service: 'Gmail SMTP', cost: '$0/شهر (مجاني)', limit: 'حتى 500 رسالة/يوم' },
    { service: 'Google OAuth', cost: '$0/شهر (مجاني)', limit: 'بلا حدود' },
    { service: 'Domain (اختياري)', cost: '$10-15/سنة', limit: '.com domain' }
  ];

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-navy mb-4">
                🚀 دليل النشر والخدمات الخارجية
              </h1>
              <p className="text-xl text-gray-600">
                كل ما تحتاجه لنشر مشروع دوزان على الإنترنت
              </p>
            </div>

            {/* Tabs */}
            <div className="flex space-x-4 space-x-reverse mb-8">
              {[
                { id: 'services', label: 'الخدمات المطلوبة', icon: '🛠️' },
                { id: 'steps', label: 'خطوات النشر', icon: '📋' },
                { id: 'local', label: 'البدائل المحلية', icon: '💻' },
                { id: 'costs', label: 'التكاليف', icon: '💰' }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-teal text-white'
                      : 'bg-white text-gray-600 hover:bg-gray-50'
                  }`}
                >
                  {tab.icon} {tab.label}
                </button>
              ))}
            </div>

            {/* Services Tab */}
            {activeTab === 'services' && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {externalServices.map((service, index) => (
                    <Card key={index} className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                      <CardHeader>
                        <CardTitle className="flex items-center justify-between">
                          <span>{service.name}</span>
                          <div className="flex items-center space-x-2 space-x-reverse">
                            {service.required && (
                              <span className="bg-red-100 text-red-800 text-xs px-2 py-1 rounded">
                                مطلوب
                              </span>
                            )}
                            <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                              {service.cost}
                            </span>
                          </div>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <p className="text-gray-600 mb-4">{service.description}</p>
                        
                        <div className="mb-4">
                          <h4 className="font-semibold text-navy mb-2">خطوات الإعداد:</h4>
                          <ol className="list-decimal list-inside space-y-1 text-sm">
                            {service.setup.map((step, stepIndex) => (
                              <li key={stepIndex} className="text-gray-700">{step}</li>
                            ))}
                          </ol>
                        </div>

                        <div>
                          <h4 className="font-semibold text-navy mb-2">بدائل أخرى:</h4>
                          <ul className="space-y-1 text-sm">
                            {service.alternatives.map((alt, altIndex) => (
                              <li key={altIndex} className="text-gray-600">• {alt}</li>
                            ))}
                          </ul>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}

            {/* Steps Tab */}
            {activeTab === 'steps' && (
              <div className="space-y-6">
                {deploymentSteps.map((phase, index) => (
                  <Card key={index} className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                    <CardHeader>
                      <CardTitle className="flex items-center">
                        <span className="bg-teal text-white rounded-full w-8 h-8 flex items-center justify-center text-sm mr-3">
                          {index + 1}
                        </span>
                        {phase.phase}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {phase.steps.map((step, stepIndex) => (
                          <div key={stepIndex} className="bg-gray-50 p-4 rounded-lg">
                            <div className="flex items-start space-x-2 space-x-reverse">
                              <span className="text-teal font-bold">{stepIndex + 1}.</span>
                              <span className="text-gray-700">{step}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Local Alternatives Tab */}
            {activeTab === 'local' && (
              <div className="space-y-6">
                <Card className="border-0 bg-blue-50 border-blue-200">
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-navy mb-3">💡 هل يمكن تشغيل كل شيء محلياً؟</h3>
                    <p className="text-gray-700 mb-4">
                      نعم! يمكن تشغيل المشروع محلياً بالكامل للتطوير والاختبار، لكن للنشر العام ستحتاج بعض الخدمات الخارجية.
                    </p>
                    <div className="bg-white p-4 rounded-lg">
                      <h4 className="font-semibold text-navy mb-2">الوضع الحالي:</h4>
                      <p className="text-green-700">
                        ✅ المشروع يعمل محلياً بالكامل مع بيانات وهمية ومحاكاة للخدمات
                      </p>
                    </div>
                  </CardContent>
                </Card>

                {localAlternatives.map((alt, index) => (
                  <Card key={index} className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                    <CardHeader>
                      <CardTitle>{alt.service}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="mb-4">
                        <h4 className="font-semibold text-navy mb-2">البديل المحلي:</h4>
                        <p className="text-gray-700 mb-3">{alt.local}</p>
                        <p className="text-gray-600">{alt.description}</p>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-semibold text-green-600 mb-2">المزايا:</h4>
                          <ul className="space-y-1">
                            {alt.pros.map((pro, proIndex) => (
                              <li key={proIndex} className="text-green-700 text-sm">✅ {pro}</li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h4 className="font-semibold text-red-600 mb-2">العيوب:</h4>
                          <ul className="space-y-1">
                            {alt.cons.map((con, conIndex) => (
                              <li key={conIndex} className="text-red-700 text-sm">❌ {con}</li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}

            {/* Costs Tab */}
            {activeTab === 'costs' && (
              <div className="space-y-6">
                <Card className="border-0 bg-green-50 border-green-200">
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-navy mb-3">💰 ملخص التكاليف</h3>
                    <div className="text-center">
                      <div className="text-4xl font-bold text-green-600 mb-2">$0/شهر</div>
                      <p className="text-gray-700">التكلفة الإجمالية للبداية (مع الحدود المجانية)</p>
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-0 bg-white/90 backdrop-blur-md shadow-lg">
                  <CardHeader>
                    <CardTitle>تفصيل التكاليف</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {costBreakdown.map((item, index) => (
                        <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                          <div>
                            <h4 className="font-semibold text-navy">{item.service}</h4>
                            <p className="text-sm text-gray-600">{item.limit}</p>
                          </div>
                          <div className="text-right">
                            <span className="font-bold text-green-600">{item.cost}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                <Card className="border-0 bg-yellow-50 border-yellow-200">
                  <CardContent className="p-6">
                    <h3 className="font-semibold text-navy mb-3">📈 عند نمو المشروع</h3>
                    <p className="text-gray-700 mb-4">
                      عندما يزداد عدد المستخدمين، قد تحتاج للترقية:
                    </p>
                    <ul className="space-y-2 text-gray-700">
                      <li>• Railway Pro: $5/شهر (قاعدة بيانات أكبر)</li>
                      <li>• Vercel Pro: $20/شهر (bandwidth أكثر)</li>
                      <li>• SendGrid: $15/شهر (40,000 رسالة)</li>
                      <li>• Cloudinary: $89/شهر (تخزين صور احترافي)</li>
                    </ul>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Action Buttons */}
            <div className="mt-12 text-center">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Button 
                  onClick={() => window.open('/syria-simulation', '_blank')}
                  className="bg-gradient-to-r from-blue-500 to-blue-600"
                >
                  🇸🇾 محاكاة المستخدم السوري
                </Button>
                <Button 
                  onClick={() => window.open('/test-dashboard', '_blank')}
                  className="bg-gradient-to-r from-green-500 to-green-600"
                >
                  🧪 لوحة الاختبار الشاملة
                </Button>
                <Button 
                  onClick={() => window.open('/project-summary', '_blank')}
                  className="bg-gradient-to-r from-purple-500 to-purple-600"
                >
                  📊 ملخص المشروع
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default DeploymentGuidePage;
