{"version": 3, "sources": [], "sections": [{"offset": {"line": 55, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/g/Duzan%20Website/frontend/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { email, password, name, phone, role = 'CLIENT' } = body;\n\n    // التحقق من صحة البيانات\n    if (!email || !password || !name) {\n      return NextResponse.json(\n        { error: 'البريد الإلكتروني وكلمة المرور والاسم مطلوبة' },\n        { status: 400 }\n      );\n    }\n\n    // للتطوير: رفض إنشاء حسابات جديدة مؤقتاً\n    return NextResponse.json(\n      { error: 'إنشاء الحسابات الجديدة معطل مؤقتاً. استخدم الحسابات التجريبية.' },\n      { status: 400 }\n    );\n\n  } catch (error) {\n    console.error('Registration error:', error);\n    return NextResponse.json(\n      { error: 'خطأ في إنشاء الحساب' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,QAAQ,EAAE,GAAG;QAE1D,yBAAyB;QACzB,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,MAAM;YAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+C,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,yCAAyC;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiE,GAC1E;YAAE,QAAQ;QAAI;IAGlB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAsB,GAC/B;YAAE,QAAQ;QAAI;IAElB;AACF"}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}