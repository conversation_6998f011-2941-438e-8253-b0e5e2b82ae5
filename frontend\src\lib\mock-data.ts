// بيانات تجريبية للتطوير بدون قاعدة بيانات

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'ADMIN' | 'CLIENT' | 'CRAFTSMAN';
  phone?: string;
  location?: string;
  bio?: string;
  avatar?: string;
  isActive: boolean;
  isVerified: boolean;
  createdAt: Date;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  category: string;
  budget: string;
  deadline: Date;
  location: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH';
  status: 'OPEN' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  clientId: string;
  craftsmanId?: string;
  images: string[];
  materials?: string;
  workType?: string;
  requirements?: string;
  views: number;
  featured: boolean;
  createdAt: Date;
}

export interface Offer {
  id: string;
  projectId: string;
  craftsmanId: string;
  amount: number;
  currency: string;
  description: string;
  estimatedDuration: number;
  status: 'PENDING' | 'ACCEPTED' | 'REJECTED';
  createdAt: Date;
}

export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'INFO' | 'SUCCESS' | 'WARNING' | 'ERROR';
  isRead: boolean;
  createdAt: Date;
}

// بيانات المستخدمين التجريبية
export const mockUsers: User[] = [
  {
    id: 'admin',
    email: '<EMAIL>',
    name: 'مدير النظام',
    role: 'ADMIN',
    phone: '+963 11 123 4567',
    location: 'دمشق، سوريا',
    bio: 'مدير منصة دوزان',
    isActive: true,
    isVerified: true,
    createdAt: new Date('2024-01-01'),
  },
  {
    id: 'client',
    email: '<EMAIL>',
    name: 'أحمد محمد',
    role: 'CLIENT',
    phone: '+963 123 456 789',
    location: 'دمشق - المزة',
    bio: 'عميل يبحث عن حرفيين مؤهلين',
    isActive: true,
    isVerified: true,
    createdAt: new Date('2024-01-15'),
  },
  {
    id: 'craftsman1',
    email: '<EMAIL>',
    name: 'محمد النجار',
    role: 'CRAFTSMAN',
    phone: '+963 987 654 321',
    location: 'دمشق - كفرسوسة',
    bio: 'نجار محترف مع خبرة 10 سنوات',
    isActive: true,
    isVerified: true,
    createdAt: new Date('2024-01-10'),
  },
  {
    id: 'craftsman2',
    email: '<EMAIL>',
    name: 'سارة الكهربائية',
    role: 'CRAFTSMAN',
    phone: '+963 555 123 456',
    location: 'حلب - الفرقان',
    bio: 'كهربائية متخصصة في الأنظمة الذكية',
    isActive: true,
    isVerified: true,
    createdAt: new Date('2024-01-20'),
  },
];

// بيانات المشاريع التجريبية
export const mockProjects: Project[] = [
  {
    id: 'project1',
    title: 'تجديد مطبخ منزلي',
    description: 'أحتاج إلى تجديد مطبخ منزلي بالكامل مع تغيير الخزائن والأرضية',
    category: 'نجارة',
    budget: '₺6,000 - ₺8,000',
    deadline: new Date('2024-03-15'),
    location: 'دمشق - المزة',
    priority: 'HIGH',
    status: 'OPEN',
    clientId: 'client',
    images: [],
    materials: 'متوفرة جزئياً',
    workType: 'تجديد',
    requirements: 'يفضل استخدام خشب عالي الجودة',
    views: 15,
    featured: true,
    createdAt: new Date('2024-02-01'),
  },
  {
    id: 'project2',
    title: 'تركيب نظام إضاءة ذكي',
    description: 'تركيب نظام إضاءة ذكي في المنزل مع إمكانية التحكم عبر الهاتف',
    category: 'كهرباء',
    budget: '₺4,000 - ₺6,000',
    deadline: new Date('2024-03-10'),
    location: 'دمشق - المالكي',
    priority: 'MEDIUM',
    status: 'OPEN',
    clientId: 'client',
    images: [],
    materials: 'غير متوفرة',
    workType: 'تركيب',
    requirements: 'نظام متوافق مع الهواتف الذكية',
    views: 8,
    featured: false,
    createdAt: new Date('2024-02-05'),
  },
];

// بيانات العروض التجريبية
export const mockOffers: Offer[] = [
  {
    id: 'offer1',
    projectId: 'project1',
    craftsmanId: 'craftsman1',
    amount: 7000,
    currency: 'TRY',
    description: 'يمكنني تنفيذ هذا المشروع بجودة عالية وفي الوقت المحدد',
    estimatedDuration: 14,
    status: 'PENDING',
    createdAt: new Date('2024-02-02'),
  },
  {
    id: 'offer2',
    projectId: 'project2',
    craftsmanId: 'craftsman2',
    amount: 5000,
    currency: 'TRY',
    description: 'متخصصة في أنظمة الإضاءة الذكية مع ضمان سنة كاملة',
    estimatedDuration: 7,
    status: 'PENDING',
    createdAt: new Date('2024-02-06'),
  },
];

// بيانات الإشعارات التجريبية
export const mockNotifications: Notification[] = [
  {
    id: 'notif1',
    userId: 'client',
    title: 'عرض جديد على مشروعك',
    message: 'تلقيت عرضاً جديداً من محمد النجار على مشروع تجديد المطبخ',
    type: 'INFO',
    isRead: false,
    createdAt: new Date('2024-02-02'),
  },
  {
    id: 'notif2',
    userId: 'craftsman1',
    title: 'مشروع جديد متاح',
    message: 'يوجد مشروع نجارة جديد في منطقتك',
    type: 'INFO',
    isRead: true,
    createdAt: new Date('2024-02-01'),
  },
];

// دوال مساعدة للبحث والفلترة
export const getUserById = (id: string): User | undefined => {
  return mockUsers.find(user => user.id === id);
};

export const getUserByEmail = (email: string): User | undefined => {
  return mockUsers.find(user => user.email === email);
};

export const getProjectsByUserId = (userId: string): Project[] => {
  return mockProjects.filter(project => 
    project.clientId === userId || project.craftsmanId === userId
  );
};

export const getOffersByProjectId = (projectId: string): Offer[] => {
  return mockOffers.filter(offer => offer.projectId === projectId);
};

export const getOffersByCraftsmanId = (craftsmanId: string): Offer[] => {
  return mockOffers.filter(offer => offer.craftsmanId === craftsmanId);
};

export const getNotificationsByUserId = (userId: string): Notification[] => {
  return mockNotifications.filter(notification => notification.userId === userId);
};
