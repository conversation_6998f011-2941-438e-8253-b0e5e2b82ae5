'use client';

import React, { useState } from 'react';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';

const CraftsmanPortfolioPage = () => {
  const [activeFilter, setActiveFilter] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);

  const portfolioItems = [
    {
      id: 1,
      title: 'تجديد مطبخ عصري',
      category: 'نجارة',
      client: 'أحمد محمد',
      completedDate: '2024-01-15',
      duration: '12 يوم',
      budget: '₺7,500',
      rating: 5,
      description: 'تجديد كامل للمطبخ مع تركيب خزائن حديثة وأسطح عمل من الجرانيت',
      beforeImage: '/api/placeholder/400/300',
      afterImage: '/api/placeholder/400/300',
      tags: ['مطبخ', 'خزائن', 'تجديد'],
      featured: true
    },
    {
      id: 2,
      title: 'إصلاح نظام كهرباء منزلي',
      category: 'كهرباء',
      client: 'فاطمة أحمد',
      completedDate: '2024-02-10',
      duration: '5 أيام',
      budget: '₺2,800',
      rating: 4.8,
      description: 'إصلاح وتجديد التمديدات الكهربائية مع تركيب لوحة توزيع جديدة',
      beforeImage: '/api/placeholder/400/300',
      afterImage: '/api/placeholder/400/300',
      tags: ['كهرباء', 'إصلاح', 'لوحة توزيع'],
      featured: false
    },
    {
      id: 3,
      title: 'دهان شقة كاملة',
      category: 'دهان',
      client: 'محمد علي',
      completedDate: '2024-01-28',
      duration: '8 أيام',
      budget: '₺3,200',
      rating: 4.9,
      description: 'دهان شقة من 3 غرف بألوان عصرية مع تحضير الجدران',
      beforeImage: '/api/placeholder/400/300',
      afterImage: '/api/placeholder/400/300',
      tags: ['دهان', 'شقة', 'ألوان عصرية'],
      featured: true
    },
    {
      id: 4,
      title: 'تركيب نظام سباكة حديث',
      category: 'سباكة',
      client: 'سارة خالد',
      completedDate: '2024-02-05',
      duration: '10 أيام',
      budget: '₺5,200',
      rating: 4.7,
      description: 'تركيب نظام سباكة متكامل في حمامين مع أدوات صحية حديثة',
      beforeImage: '/api/placeholder/400/300',
      afterImage: '/api/placeholder/400/300',
      tags: ['سباكة', 'حمام', 'أدوات صحية'],
      featured: false
    }
  ];

  const categories = ['all', 'نجارة', 'كهرباء', 'سباكة', 'دهان', 'بناء'];

  const filteredItems = portfolioItems.filter(item => {
    if (activeFilter === 'all') return true;
    return item.category === activeFilter;
  });

  const stats = [
    {
      title: 'إجمالي المشاريع',
      value: portfolioItems.length.toString(),
      color: 'text-blue-600'
    },
    {
      title: 'المشاريع المميزة',
      value: portfolioItems.filter(item => item.featured).length.toString(),
      color: 'text-yellow-600'
    },
    {
      title: 'متوسط التقييم',
      value: (portfolioItems.reduce((sum, item) => sum + item.rating, 0) / portfolioItems.length).toFixed(1),
      color: 'text-green-600'
    },
    {
      title: 'إجمالي الأرباح',
      value: '₺18,700',
      color: 'text-purple-600'
    }
  ];

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <span key={i} className={`text-lg ${i < Math.floor(rating) ? 'text-yellow-400' : 'text-gray-300'}`}>
        ⭐
      </span>
    ));
  };

  return (
    <ProtectedRoute requiredRoles="craftsman">
      <DashboardLayout 
        title="معرض أعمالي"
        subtitle="عرض وإدارة مشاريعك المكتملة"
        actions={
          <Button 
            onClick={() => setShowAddModal(true)}
            className="bg-gradient-to-r from-navy to-teal"
          >
            إضافة مشروع جديد
          </Button>
        }
      >
        <div className="space-y-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {stats.map((stat, index) => (
              <div key={index} className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                  <div className={`text-3xl ${stat.color}`}>
                    🎨
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => (
                <button
                  key={category}
                  onClick={() => setActiveFilter(category)}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 ${
                    activeFilter === category
                      ? 'bg-navy text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category === 'all' ? 'جميع المشاريع' : category}
                </button>
              ))}
            </div>
          </div>

          {/* Portfolio Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredItems.map((item) => (
              <div key={item.id} className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
                {/* Featured Badge */}
                {item.featured && (
                  <div className="absolute top-4 right-4 z-10">
                    <span className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      مميز
                    </span>
                  </div>
                )}

                {/* Before/After Images */}
                <div className="relative">
                  <div className="grid grid-cols-2 h-48">
                    <div className="relative">
                      <img
                        src={item.beforeImage}
                        alt="قبل"
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                        قبل
                      </div>
                    </div>
                    <div className="relative">
                      <img
                        src={item.afterImage}
                        alt="بعد"
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute bottom-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-xs">
                        بعد
                      </div>
                    </div>
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="text-lg font-semibold text-gray-900">{item.title}</h3>
                    <span className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                      {item.category}
                    </span>
                  </div>

                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">{item.description}</p>

                  {/* Client & Rating */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <div className="w-6 h-6 bg-gradient-to-br from-navy to-teal rounded-full flex items-center justify-center text-white text-xs font-bold">
                        {item.client.charAt(0)}
                      </div>
                      <span className="text-sm text-gray-700">{item.client}</span>
                    </div>
                    <div className="flex items-center space-x-1 space-x-reverse">
                      {renderStars(item.rating)}
                      <span className="text-sm text-gray-600 mr-1">({item.rating})</span>
                    </div>
                  </div>

                  {/* Project Details */}
                  <div className="grid grid-cols-2 gap-4 mb-4 text-sm">
                    <div>
                      <span className="text-gray-500">المدة:</span>
                      <span className="font-medium text-gray-900 mr-2">{item.duration}</span>
                    </div>
                    <div>
                      <span className="text-gray-500">القيمة:</span>
                      <span className="font-medium text-gray-900 mr-2">{item.budget}</span>
                    </div>
                    <div className="col-span-2">
                      <span className="text-gray-500">تاريخ الإكمال:</span>
                      <span className="font-medium text-gray-900 mr-2">{item.completedDate}</span>
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {item.tags.map((tag, index) => (
                      <span key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-2 space-x-reverse">
                    <Button size="sm" variant="outline" className="flex-1 border-navy text-navy hover:bg-navy hover:text-white">
                      عرض التفاصيل
                    </Button>
                    <Button size="sm" variant="outline" className="border-gray-300 text-gray-700 hover:bg-gray-100">
                      تعديل
                    </Button>
                    <Button 
                      size="sm" 
                      variant="outline" 
                      className={`border-yellow-300 text-yellow-700 hover:bg-yellow-100 ${item.featured ? 'bg-yellow-100' : ''}`}
                    >
                      {item.featured ? '⭐' : '☆'}
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredItems.length === 0 && (
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
              <div className="text-6xl mb-4">🎨</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">لا توجد مشاريع</h3>
              <p className="text-gray-600 mb-6">لم يتم العثور على مشاريع بالفئة المحددة</p>
              <Button 
                onClick={() => setShowAddModal(true)}
                className="bg-gradient-to-r from-navy to-teal"
              >
                إضافة مشروع جديد
              </Button>
            </div>
          )}

          {/* Add Project Modal Placeholder */}
          {showAddModal && (
            <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
              <div className="bg-white rounded-xl p-8 max-w-md w-full mx-4">
                <h3 className="text-xl font-semibold text-gray-900 mb-4">إضافة مشروع جديد</h3>
                <p className="text-gray-600 mb-6">سيتم إضافة نموذج لإضافة مشروع جديد هنا</p>
                <div className="flex space-x-4 space-x-reverse">
                  <Button 
                    onClick={() => setShowAddModal(false)}
                    variant="outline"
                    className="flex-1 border-gray-300 text-gray-700"
                  >
                    إلغاء
                  </Button>
                  <Button className="flex-1 bg-gradient-to-r from-navy to-teal">
                    إضافة
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default CraftsmanPortfolioPage;
