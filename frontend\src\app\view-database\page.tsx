'use client';

import { useEffect, useState } from 'react';
import supabase from '@/lib/supabase';
import MainLayout from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

export default function ViewDatabase() {
  const [users, setUsers] = useState<any[]>([]);
  const [projects, setProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load users
      const { data: usersData, error: usersError } = await supabase
        .from('users')
        .select('*')
        .order('created_at', { ascending: false });

      if (usersError) {
        throw new Error(`خطأ في تحميل المستخدمين: ${usersError.message}`);
      }

      // Load projects
      const { data: projectsData, error: projectsError } = await supabase
        .from('projects')
        .select('*')
        .order('created_at', { ascending: false });

      if (projectsError) {
        throw new Error(`خطأ في تحميل المشاريع: ${projectsError.message}`);
      }

      setUsers(usersData || []);
      setProjects(projectsData || []);

    } catch (err: any) {
      setError(err.message);
    }

    setLoading(false);
  };

  useEffect(() => {
    loadData();
  }, []);

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-white via-skyblue/10 to-white py-8">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="text-4xl font-bold text-navy mb-4">
                📊 عرض بيانات قاعدة البيانات
              </h1>
              <p className="text-xl text-gray-600">
                عرض جميع البيانات المخزنة في Supabase
              </p>
            </div>

            {/* Controls */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">التحكم</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex justify-center">
                  <Button 
                    onClick={loadData}
                    disabled={loading}
                    className="bg-gradient-to-r from-teal to-navy"
                  >
                    {loading ? 'جاري التحميل...' : '🔄 إعادة تحميل البيانات'}
                  </Button>
                </div>
                
                {error && (
                  <div className="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <strong>خطأ:</strong> {error}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Users Data */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">
                  👥 المستخدمين ({users.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {users.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">لا توجد بيانات مستخدمين</p>
                ) : (
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b">
                          <th className="text-right p-2">الاسم</th>
                          <th className="text-right p-2">البريد الإلكتروني</th>
                          <th className="text-right p-2">الدور</th>
                          <th className="text-right p-2">الموقع</th>
                          <th className="text-right p-2">تاريخ الإنشاء</th>
                        </tr>
                      </thead>
                      <tbody>
                        {users.map((user) => (
                          <tr key={user.id} className="border-b hover:bg-gray-50">
                            <td className="p-2 font-medium">{user.name}</td>
                            <td className="p-2">{user.email}</td>
                            <td className="p-2">
                              <span className={`px-2 py-1 rounded text-xs ${
                                user.role === 'ADMIN' ? 'bg-red-100 text-red-800' :
                                user.role === 'CRAFTSMAN' ? 'bg-blue-100 text-blue-800' :
                                'bg-green-100 text-green-800'
                              }`}>
                                {user.role === 'ADMIN' ? 'مدير' :
                                 user.role === 'CRAFTSMAN' ? 'حرفي' : 'عميل'}
                              </span>
                            </td>
                            <td className="p-2">{user.location || '-'}</td>
                            <td className="p-2">
                              {new Date(user.created_at).toLocaleDateString('ar-SA')}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Projects Data */}
            <Card className="border-0 bg-white/90 backdrop-blur-md shadow-xl mb-8">
              <CardHeader>
                <CardTitle className="text-2xl text-navy">
                  🏗️ المشاريع ({projects.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {projects.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">لا توجد مشاريع</p>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {projects.map((project) => (
                      <div key={project.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <h3 className="font-bold text-navy mb-2">{project.title}</h3>
                        <p className="text-gray-600 text-sm mb-2">{project.description}</p>
                        <div className="space-y-1 text-xs">
                          <div><strong>الفئة:</strong> {project.category}</div>
                          <div><strong>الموقع:</strong> {project.location}</div>
                          <div><strong>الحالة:</strong> 
                            <span className={`ml-1 px-2 py-1 rounded ${
                              project.status === 'OPEN' ? 'bg-green-100 text-green-800' :
                              project.status === 'IN_PROGRESS' ? 'bg-blue-100 text-blue-800' :
                              project.status === 'COMPLETED' ? 'bg-purple-100 text-purple-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {project.status === 'OPEN' ? 'مفتوح' :
                               project.status === 'IN_PROGRESS' ? 'قيد التنفيذ' :
                               project.status === 'COMPLETED' ? 'مكتمل' : project.status}
                            </span>
                          </div>
                          {project.budget_min && project.budget_max && (
                            <div><strong>الميزانية:</strong> {project.budget_min} - {project.budget_max} ل.س</div>
                          )}
                          <div><strong>تاريخ الإنشاء:</strong> {new Date(project.created_at).toLocaleDateString('ar-SA')}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Summary */}
            <Card className="border-0 bg-gradient-to-r from-green-500 to-green-600 text-white">
              <CardHeader>
                <CardTitle>📈 ملخص قاعدة البيانات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold">{users.length}</div>
                    <div className="text-white/90">مستخدم</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold">{projects.length}</div>
                    <div className="text-white/90">مشروع</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold">
                      {users.filter(u => u.role === 'CRAFTSMAN').length}
                    </div>
                    <div className="text-white/90">حرفي</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Navigation */}
            <div className="text-center mt-8 space-x-4 space-x-reverse">
              <a 
                href="/test-dashboard"
                className="inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all"
              >
                العودة للوحة الاختبار
              </a>
              <a 
                href="/setup-database"
                className="inline-block bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-3 rounded-lg hover:shadow-lg transition-all"
              >
                اختبار قاعدة البيانات
              </a>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
