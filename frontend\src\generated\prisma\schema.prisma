// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums - التعدادات
enum UserRole {
  CLIENT
  CRAFTSMAN
  ADMIN
}

enum ProjectStatus {
  OPEN // مفتوح
  IN_PROGRESS // جاري التنفيذ
  COMPLETED // مكتمل
  CANCELLED // ملغي
  ON_HOLD // متوقف
}

enum ProjectPriority {
  LOW // منخفضة
  MEDIUM // متوسطة
  HIGH // عالية
  URGENT // عاجلة
}

enum OfferStatus {
  PENDING // في الانتظار
  ACCEPTED // مقبول
  REJECTED // مرفوض
  WITHDRAWN // مسحوب
}

enum PaymentStatus {
  PENDING // في الانتظار
  COMPLETED // مكتمل
  FAILED // فاشل
  REFUNDED // مسترد
}

enum MessageType {
  TEXT
  IMAGE
  FILE
  SYSTEM
}

enum NotificationType {
  OFFER_RECEIVED
  OFFER_ACCEPTED
  OFFER_REJECTED
  PROJECT_COMPLETED
  PAYMENT_RECEIVED
  MESSAGE_RECEIVED
  DEADLINE_REMINDER
  NEW_PROJECT
  SYSTEM_UPDATE
}

// User model - المستخدمين
model User {
  id       String   @id @default(cuid())
  email    String   @unique
  name     String
  phone    String?
  password String? // كلمة المرور المشفرة
  role     UserRole @default(CLIENT)
  avatar   String?
  location String?
  bio      String?

  // Security fields
  isActive        Boolean   @default(true)
  isVerified      Boolean   @default(false)
  emailVerifiedAt DateTime?
  phoneVerifiedAt DateTime?
  lastLoginAt     DateTime?
  loginAttempts   Int       @default(0)
  lockedUntil     DateTime?

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String? // من أنشأ الحساب (للمدراء)
  updatedBy String? // من حدث الحساب

  // Client specific fields
  clientProjects Project[] @relation("ClientProjects")
  clientOffers   Offer[]   @relation("ClientOffers")
  clientPayments Payment[] @relation("ClientPayments")
  clientReviews  Review[]  @relation("ClientReviews")

  // Craftsman specific fields
  craftsmanProfile  CraftsmanProfile?
  craftsmanOffers   Offer[]           @relation("CraftsmanOffers")
  craftsmanPayments Payment[]         @relation("CraftsmanPayments")
  craftsmanReviews  Review[]          @relation("CraftsmanReviews")

  // Messages
  sentMessages     Message[] @relation("SentMessages")
  receivedMessages Message[] @relation("ReceivedMessages")

  // Notifications
  notifications Notification[]

  // Security relations
  sessions           Session[]
  auditLogs          AuditLog[]
  passwordResets     PasswordReset[]
  emailVerifications EmailVerification[]

  @@map("users")
}

// Craftsman Profile - ملف الحرفي
model CraftsmanProfile {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  skills       String[] // المهارات
  experience   Int // سنوات الخبرة
  hourlyRate   Float? // السعر بالساعة
  availability String   @default("AVAILABLE") // متاح، مشغول، في إجازة
  workingHours String? // ساعات العمل
  languages    String[] // اللغات

  // Statistics
  rating            Float @default(0)
  totalProjects     Int   @default(0)
  completedProjects Int   @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  portfolioItems PortfolioItem[]

  @@map("craftsman_profiles")
}

// Portfolio Item - عنصر معرض الأعمال
model PortfolioItem {
  id          String           @id @default(cuid())
  craftsmanId String
  craftsman   CraftsmanProfile @relation(fields: [craftsmanId], references: [id], onDelete: Cascade)

  title        String
  description  String
  category     String
  beforeImages String[] // صور قبل
  afterImages  String[] // صور بعد
  tags         String[] // العلامات
  featured     Boolean  @default(false)

  // Project details
  duration      String? // مدة المشروع
  budget        Float? // الميزانية
  clientName    String? // اسم العميل (اختياري)
  completedDate DateTime

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("portfolio_items")
}

// Project - المشاريع
model Project {
  id           String          @id @default(cuid())
  title        String
  description  String
  category     String
  budget       String // نطاق الميزانية
  deadline     DateTime
  location     String
  priority     ProjectPriority @default(MEDIUM)
  materials    String          @default("NOT_SPECIFIED") // متوفرة، غير متوفرة، جزئياً
  workType     String? // نوع العمل
  requirements String? // متطلبات إضافية
  images       String[] // صور المشروع

  status ProjectStatus @default(OPEN)

  // Relations
  clientId String
  client   User   @relation("ClientProjects", fields: [clientId], references: [id], onDelete: Cascade)

  offers   Offer[]
  payments Payment[]
  reviews  Review[]
  messages Message[]

  // Audit fields
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  createdBy String? // من أنشأ المشروع
  updatedBy String? // من حدث المشروع

  // SEO and metadata
  slug     String? @unique // للروابط الودية
  views    Int     @default(0) // عدد المشاهدات
  featured Boolean @default(false) // مشروع مميز

  @@map("projects")
}

// Offer - العروض
model Offer {
  id        String  @id @default(cuid())
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  craftsmanId String
  craftsman   User   @relation("CraftsmanOffers", fields: [craftsmanId], references: [id], onDelete: Cascade)

  clientId String
  client   User   @relation("ClientOffers", fields: [clientId], references: [id], onDelete: Cascade)

  price       Float // السعر المقترح
  duration    String // المدة المقترحة
  description String // وصف العرض
  materials   String? // المواد (متضمنة، غير متضمنة، جزئياً)
  warranty    String? // فترة الضمان

  status OfferStatus @default(PENDING)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("offers")
}

// Payment - المدفوعات
model Payment {
  id        String  @id @default(cuid())
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  clientId String
  client   User   @relation("ClientPayments", fields: [clientId], references: [id], onDelete: Cascade)

  craftsmanId String
  craftsman   User   @relation("CraftsmanPayments", fields: [craftsmanId], references: [id], onDelete: Cascade)

  amount     Float // المبلغ الإجمالي
  commission Float // عمولة المنصة
  netAmount  Float // المبلغ الصافي للحرفي

  paymentMethod String? // طريقة الدفع
  description   String? // وصف الدفعة
  invoiceNumber String? // رقم الفاتورة

  status PaymentStatus @default(PENDING)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("payments")
}

// Review - التقييمات
model Review {
  id        String  @id @default(cuid())
  projectId String
  project   Project @relation(fields: [projectId], references: [id], onDelete: Cascade)

  clientId String
  client   User   @relation("ClientReviews", fields: [clientId], references: [id], onDelete: Cascade)

  craftsmanId String
  craftsman   User   @relation("CraftsmanReviews", fields: [craftsmanId], references: [id], onDelete: Cascade)

  rating  Int // التقييم من 1 إلى 5
  comment String? // التعليق

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("reviews")
}

// Message - الرسائل
model Message {
  id        String   @id @default(cuid())
  projectId String?
  project   Project? @relation(fields: [projectId], references: [id], onDelete: Cascade)

  senderId String
  sender   User   @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)

  receiverId String
  receiver   User   @relation("ReceivedMessages", fields: [receiverId], references: [id], onDelete: Cascade)

  content String // محتوى الرسالة
  type    MessageType @default(TEXT)
  fileUrl String? // رابط الملف (للصور والملفات)

  isRead Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("messages")
}

// Notification - الإشعارات
model Notification {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  type      NotificationType
  title     String // عنوان الإشعار
  message   String // محتوى الإشعار
  actionUrl String? // رابط الإجراء (اختياري)

  isRead Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("notifications")
}

// Session model - جلسات المستخدمين
model Session {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  token     String  @unique
  userAgent String?
  ipAddress String?
  location  String?

  isActive  Boolean  @default(true)
  expiresAt DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("sessions")
}

// AuditLog model - سجل التدقيق
model AuditLog {
  id     String  @id @default(cuid())
  userId String?
  user   User?   @relation(fields: [userId], references: [id], onDelete: SetNull)

  action    String // CREATE, UPDATE, DELETE, LOGIN, LOGOUT
  entity    String // User, Project, Offer, etc.
  entityId  String?
  oldValues Json?
  newValues Json?

  ipAddress String?
  userAgent String?

  createdAt DateTime @default(now())

  @@map("audit_logs")
}

// PasswordReset model - إعادة تعيين كلمة المرور
model PasswordReset {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  token     String   @unique
  expiresAt DateTime
  isUsed    Boolean  @default(false)

  createdAt DateTime @default(now())

  @@map("password_resets")
}

// EmailVerification model - تأكيد البريد الإلكتروني
model EmailVerification {
  id     String @id @default(cuid())
  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  token     String   @unique
  expiresAt DateTime
  isUsed    Boolean  @default(false)

  createdAt DateTime @default(now())

  @@map("email_verifications")
}

// Settings model - إعدادات النظام
model Settings {
  id    String @id @default(cuid())
  key   String @unique
  value String
  type  String @default("string") // string, number, boolean, json

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}

// Category model - فئات المشاريع
model Category {
  id          String  @id @default(cuid())
  name        String  @unique
  nameAr      String  @unique
  description String?
  icon        String?
  color       String?
  isActive    Boolean @default(true)
  sortOrder   Int     @default(0)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("categories")
}

// Location model - المواقع
model Location {
  id       String     @id @default(cuid())
  name     String
  nameAr   String
  type     String // city, district, area
  parentId String?
  parent   Location?  @relation("LocationHierarchy", fields: [parentId], references: [id])
  children Location[] @relation("LocationHierarchy")

  isActive Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("locations")
}
