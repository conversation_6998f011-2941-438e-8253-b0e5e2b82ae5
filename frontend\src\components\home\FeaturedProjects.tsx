'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';

const FeaturedProjects = () => {
  const [activeCategory, setActiveCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'جميع المشاريع', icon: '🏗️' },
    { id: 'kitchen', name: 'مطابخ', icon: '🍳' },
    { id: 'bathroom', name: 'حمامات', icon: '🚿' },
    { id: 'bedroom', name: 'غرف نوم', icon: '🛏️' },
    { id: 'office', name: 'مكاتب', icon: '💼' }
  ];

  const projects = [
    {
      id: 1,
      title: 'تجديد مطبخ عصري فاخر',
      category: 'kitchen',
      beforeImage: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop',
      afterImage: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=600&h=400&fit=crop&sat=2&brightness=1.1',
      craftsman: 'محمد النجار',
      location: 'دمشق، المزة',
      duration: '14 يوم',
      budget: '350,000 ل.س',
      rating: 5,
      description: 'تحويل مطبخ تقليدي إلى مطبخ عصري بخزائن خشبية فاخرة وجزيرة وسطية'
    },
    {
      id: 2,
      title: 'تصميم حمام مودرن',
      category: 'bathroom',
      beforeImage: 'https://images.unsplash.com/photo-1620626011761-996317b8d101?w=600&h=400&fit=crop',
      afterImage: 'https://images.unsplash.com/photo-1620626011761-996317b8d101?w=600&h=400&fit=crop&sat=2&brightness=1.1',
      craftsman: 'أحمد السباك',
      location: 'حلب، الفرقان',
      duration: '10 أيام',
      budget: '180,000 ل.س',
      rating: 5,
      description: 'تجديد حمام كامل بتصميم عصري وتركيبات حديثة'
    },
    {
      id: 3,
      title: 'غرفة نوم رئيسية فاخرة',
      category: 'bedroom',
      beforeImage: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop',
      afterImage: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop&sat=2&brightness=1.1',
      craftsman: 'علي النجار',
      location: 'دمشق، جرمانا',
      duration: '12 يوم',
      budget: '280,000 ل.س',
      rating: 5,
      description: 'تصميم غرفة نوم رئيسية مع خزائن مدمجة وإضاءة مخفية'
    },
    {
      id: 4,
      title: 'مكتب منزلي أنيق',
      category: 'office',
      beforeImage: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop&crop=faces',
      afterImage: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=600&h=400&fit=crop&crop=faces&sat=2&brightness=1.1',
      craftsman: 'خالد الحرفي',
      location: 'حمص، الوعر',
      duration: '8 أيام',
      budget: '150,000 ل.س',
      rating: 4,
      description: 'تحويل غرفة عادية إلى مكتب منزلي عملي وأنيق'
    }
  ];

  const filteredProjects = projects.filter(project =>
    activeCategory === 'all' || project.category === activeCategory
  );

  return (
    <section className="py-20 bg-gradient-to-b from-white via-beige/30 to-white relative overflow-hidden">
      {/* عناصر تصميمية في الخلفية */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-20 left-10 w-64 h-64 bg-navy rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-48 h-48 bg-teal rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        {/* العنوان */}
        <div className="text-center mb-16">
          <div className="inline-block bg-gradient-to-r from-navy to-teal text-white px-6 py-2 rounded-full text-sm font-medium mb-4">
            🏆 مشاريع مميزة
          </div>
          <h2 className="text-4xl md:text-5xl font-bold text-navy mb-6">
            أعمال تحكي قصص
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-teal to-navy">النجاح</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            اكتشف مجموعة من أفضل المشاريع التي تم إنجازها على منصة دوزان بجودة استثنائية
          </p>
        </div>

        {/* فلاتر الفئات */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center px-6 py-3 rounded-full text-sm font-medium transition-all duration-300 ${
                activeCategory === category.id
                  ? 'bg-gradient-to-r from-navy to-teal text-white shadow-lg scale-105'
                  : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-200 hover:border-teal hover:text-teal'
              }`}
            >
              <span className="text-lg ml-2">{category.icon}</span>
              {category.name}
            </button>
          ))}
        </div>

        {/* شبكة المشاريع */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {filteredProjects.map((project, index) => (
            <div
              key={project.id}
              className="group bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100 hover:border-teal/30 hover:scale-105"
              style={{
                animation: `fadeInUp 0.6s ease-out ${index * 0.1}s both`
              }}
            >
              {/* صورة واحدة للمشروع */}
              <div className="relative h-64 overflow-hidden">
                <img
                  src={project.afterImage}
                  alt={project.title}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />

                {/* تأثير التدرج */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>

                {/* شارة الفئة */}
                <div className="absolute top-4 right-4">
                  <div className="bg-white/90 backdrop-blur-sm text-navy px-3 py-1 rounded-full text-sm font-medium shadow-lg">
                    {categories.find(cat => cat.category === project.category)?.icon || '🏗️'}
                    {categories.find(cat => cat.id === project.category)?.name || 'مشروع'}
                  </div>
                </div>

                {/* تقييم المشروع */}
                <div className="absolute top-4 left-4">
                  <div className="bg-yellow-500/90 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg flex items-center">
                    <span className="text-xs mr-1">⭐</span>
                    {project.rating}
                  </div>
                </div>

                {/* معلومات سريعة في أسفل الصورة */}
                <div className="absolute bottom-4 left-4 right-4">
                  <div className="bg-white/90 backdrop-blur-sm rounded-xl p-3 shadow-lg">
                    <h3 className="font-bold text-navy text-lg mb-1 line-clamp-1">
                      {project.title}
                    </h3>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600 flex items-center">
                        <span className="text-teal mr-1">👨‍🔧</span>
                        {project.craftsman}
                      </span>
                      <span className="text-green-600 font-bold">
                        {project.budget}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* محتوى البطاقة */}
              <div className="p-6">
                <div className="mb-4">
                  <p className="text-gray-600 text-sm leading-relaxed line-clamp-2">
                    {project.description}
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-4">
                  <div className="bg-gradient-to-r from-navy/5 to-teal/5 p-3 rounded-xl border border-navy/10">
                    <div className="text-xs text-gray-500 mb-1 flex items-center">
                      <span className="text-navy mr-1">⏱️</span>
                      المدة
                    </div>
                    <div className="font-semibold text-navy text-sm">{project.duration}</div>
                  </div>
                  <div className="bg-gradient-to-r from-teal/5 to-navy/5 p-3 rounded-xl border border-teal/10">
                    <div className="text-xs text-gray-500 mb-1 flex items-center">
                      <span className="text-teal mr-1">📍</span>
                      الموقع
                    </div>
                    <div className="font-semibold text-navy text-sm">{project.location}</div>
                  </div>
                </div>

                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2 space-x-reverse">
                    <div className="w-8 h-8 bg-gradient-to-r from-navy to-teal rounded-full flex items-center justify-center text-white text-sm font-bold">
                      {project.craftsman.charAt(0)}
                    </div>
                    <div>
                      <div className="text-sm font-medium text-navy">{project.craftsman}</div>
                      <div className="text-xs text-gray-500">حرفي معتمد</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xs text-gray-500">الميزانية</div>
                    <div className="font-bold text-green-600 text-lg">{project.budget}</div>
                  </div>
                </div>

                {/* زر عرض التفاصيل */}
                <Link href={`/projects/${project.id}`}>
                  <Button
                    className="w-full bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90 text-white py-3 rounded-xl font-medium transition-all duration-300 group-hover:shadow-lg"
                  >
                    <span className="flex items-center justify-center">
                      عرض التفاصيل
                      <svg className="w-4 h-4 mr-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12l-3-3m0 0l-3 3m3-3v12" />
                      </svg>
                    </span>
                  </Button>
                </Link>
              </div>
            </div>
          ))}
        </div>

        {/* زر عرض المزيد */}
        <div className="text-center">
          <Link href="/jobs">
            <Button size="lg" className="bg-gradient-to-r from-navy to-teal hover:from-navy/90 hover:to-teal/90 px-8 py-4 text-lg">
              استكشف جميع المشاريع
              <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Button>
          </Link>
        </div>
      </div>

      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </section>
  );
};

export default FeaturedProjects;
