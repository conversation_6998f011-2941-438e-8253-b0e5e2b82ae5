'use client';

import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
// import { useAuth } from '@/hooks/useAuth';
import supabase from '@/lib/supabase';
import Link from 'next/link';

export default function CraftsmanDashboard() {
  // Mock user for development
  const user = { id: '1', name: 'محمد الحرفي', role: 'CRAFTSMAN' };
  const [stats, setStats] = useState({
    totalBids: 0,
    activeBids: 0,
    completedProjects: 0,
    totalEarnings: 0,
    rating: 0
  });
  const [availableProjects, setAvailableProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user?.id) {
      loadDashboardData();
    }
  }, [user?.id]);

  const loadDashboardData = async () => {
    try {
      // تحميل المشاريع المتاحة
      const { data: projects, error } = await supabase
        .from('projects')
        .select('*')
        .eq('status', 'OPEN')
        .order('created_at', { ascending: false })
        .limit(5);

      if (error) throw error;

      setAvailableProjects(projects || []);

      // إحصائيات وهمية للحرفي (يمكن تحديثها لاحقاً مع جداول العروض)
      setStats({
        totalBids: 12,
        activeBids: 3,
        completedProjects: 8,
        totalEarnings: 450000,
        rating: 4.8
      });

    } catch (error) {
      console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <DashboardLayout title="لوحة تحكم الحرفي">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal"></div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout 
      title="لوحة تحكم الحرفي"
      subtitle={`مرحباً ${user?.name}، إليك نظرة عامة على أعمالك`}
    >
      <div className="space-y-6">
        {/* الإحصائيات */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <Card className="border-0 bg-gradient-to-br from-blue-500 to-blue-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-blue-100 text-sm">إجمالي العروض</p>
                  <p className="text-3xl font-bold">{stats.totalBids}</p>
                </div>
                <div className="text-4xl opacity-80">📋</div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-green-500 to-green-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-green-100 text-sm">العروض النشطة</p>
                  <p className="text-3xl font-bold">{stats.activeBids}</p>
                </div>
                <div className="text-4xl opacity-80">🚀</div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-purple-500 to-purple-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-purple-100 text-sm">المشاريع المكتملة</p>
                  <p className="text-3xl font-bold">{stats.completedProjects}</p>
                </div>
                <div className="text-4xl opacity-80">✅</div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-orange-500 to-orange-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-orange-100 text-sm">إجمالي الأرباح</p>
                  <p className="text-2xl font-bold">{stats.totalEarnings.toLocaleString()} ل.س</p>
                </div>
                <div className="text-4xl opacity-80">💰</div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 bg-gradient-to-br from-yellow-500 to-yellow-600 text-white">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-yellow-100 text-sm">التقييم</p>
                  <p className="text-3xl font-bold">{stats.rating}</p>
                </div>
                <div className="text-4xl opacity-80">⭐</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* الإجراءات السريعة */}
        <Card>
          <CardHeader>
            <CardTitle>الإجراءات السريعة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Link href="/projects">
                <Button className="w-full h-20 bg-gradient-to-r from-teal to-navy text-lg">
                  <div className="text-center">
                    <div className="text-2xl mb-1">🔍</div>
                    <div>تصفح المشاريع</div>
                  </div>
                </Button>
              </Link>
              
              <Link href="/dashboard/craftsman/bids">
                <Button variant="outline" className="w-full h-20 text-lg">
                  <div className="text-center">
                    <div className="text-2xl mb-1">📋</div>
                    <div>عروضي</div>
                  </div>
                </Button>
              </Link>
              
              <Link href="/dashboard/craftsman/portfolio">
                <Button variant="outline" className="w-full h-20 text-lg">
                  <div className="text-center">
                    <div className="text-2xl mb-1">🎨</div>
                    <div>معرض الأعمال</div>
                  </div>
                </Button>
              </Link>

              <Link href="/dashboard/craftsman/profile">
                <Button variant="outline" className="w-full h-20 text-lg">
                  <div className="text-center">
                    <div className="text-2xl mb-1">👤</div>
                    <div>الملف الشخصي</div>
                  </div>
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* المشاريع المتاحة */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>المشاريع المتاحة</CardTitle>
              <Link href="/projects">
                <Button variant="outline" size="sm">عرض الكل</Button>
              </Link>
            </div>
          </CardHeader>
          <CardContent>
            {availableProjects.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-6xl mb-4">📭</div>
                <h3 className="text-xl font-semibold text-gray-600 mb-2">لا توجد مشاريع متاحة حالياً</h3>
                <p className="text-gray-500 mb-4">تحقق لاحقاً من المشاريع الجديدة</p>
                <Link href="/projects">
                  <Button>تصفح المشاريع</Button>
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {availableProjects.map((project) => (
                  <div key={project.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <div className="flex-1">
                      <h3 className="font-semibold text-navy">{project.title}</h3>
                      <p className="text-sm text-gray-600 mt-1">{project.description}</p>
                      <div className="flex items-center space-x-4 space-x-reverse mt-2">
                        <span className="text-sm text-gray-500">📍 {project.location}</span>
                        <span className="text-sm text-gray-500">🏷️ {project.category}</span>
                        {project.budget_max && (
                          <span className="text-sm font-medium text-green-600">
                            💰 حتى {project.budget_max.toLocaleString()} ل.س
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <span className="px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        متاح
                      </span>
                      <Link href={`/projects/${project.id}`}>
                        <Button size="sm">تقديم عرض</Button>
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* نصائح للحرفيين */}
        <Card className="border-0 bg-gradient-to-r from-teal/10 to-navy/10">
          <CardHeader>
            <CardTitle className="text-navy">💡 نصائح لزيادة فرص الفوز بالمشاريع</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-start space-x-3 space-x-reverse">
                  <div className="w-6 h-6 bg-teal rounded-full flex items-center justify-center text-white text-sm">1</div>
                  <p className="text-sm">اكتب وصف مفصل وواضح لعرضك</p>
                </div>
                <div className="flex items-start space-x-3 space-x-reverse">
                  <div className="w-6 h-6 bg-teal rounded-full flex items-center justify-center text-white text-sm">2</div>
                  <p className="text-sm">أضف صور من أعمالك السابقة</p>
                </div>
                <div className="flex items-start space-x-3 space-x-reverse">
                  <div className="w-6 h-6 bg-teal rounded-full flex items-center justify-center text-white text-sm">3</div>
                  <p className="text-sm">حدد سعر منافس ومعقول</p>
                </div>
              </div>
              <div className="space-y-3">
                <div className="flex items-start space-x-3 space-x-reverse">
                  <div className="w-6 h-6 bg-navy rounded-full flex items-center justify-center text-white text-sm">4</div>
                  <p className="text-sm">رد بسرعة على استفسارات العملاء</p>
                </div>
                <div className="flex items-start space-x-3 space-x-reverse">
                  <div className="w-6 h-6 bg-navy rounded-full flex items-center justify-center text-white text-sm">5</div>
                  <p className="text-sm">حافظ على تقييم عالي بجودة العمل</p>
                </div>
                <div className="flex items-start space-x-3 space-x-reverse">
                  <div className="w-6 h-6 bg-navy rounded-full flex items-center justify-center text-white text-sm">6</div>
                  <p className="text-sm">أكمل ملفك الشخصي بالكامل</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
}
